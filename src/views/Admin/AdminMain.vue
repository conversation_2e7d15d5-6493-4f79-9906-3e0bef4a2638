<template>
    <v-tabs v-model="activeTab" fixed-tabs background-color="primary" slider-color="info">
      <v-tab
        v-for="(item, i) in admin_items"
        :key="i"
        :value="item.entity"
        :title="item.text"
        @click="navigateTo(item.entity)">
        <v-icon>{{ item.icon }}</v-icon>&nbsp;&nbsp;{{ item.text }}
      </v-tab>
    </v-tabs>
    <v-card  v-if="this.$route.query.entity !== 'reports'"  width="90%" class="mx-auto mt-9" elevation="0">
      <AdminSearch :triggerSearch="trigSearch"
        @searchResults="searchEntity"/>
    </v-card>
    <v-card width="90%" class="mx-auto mt-9" elevation="0">
    </v-card>
    <v-card width="90%" class="mx-auto mt-9" elevation="0">
      <AdminResults @reloadResults="resultsRefresh"
        :searchResults="searchResults"/>
    </v-card>
</template>

<script>
  import AdminSearch from "./AdminSearch.vue";
  import AdminResults from "./AdminResults.vue";
  export default  {

    data:() => ({
    searchResults: [],
    trigSearch : false,
    activeTab: null,
    admin_items:[
       {
        icon: "mdi-account-multiple",
        text: "Clients",
        entity: "client",
      },
      {
    icon: "mdi-chart-bar",
    text: "Reports",
    entity: "reports",
  },
    ],
    }),
    methods: {
      searchEntity(searchResults) {
        this.searchResults = searchResults;
        this.trigSearch = false;
      },
      resultsRefresh(val){
        if(val == true){
          this.trigSearch = val
        }
      },
      navigateTo(entity) {
    try {
      const path = "#/admin?entity=";
      const destination = path + entity;
      if (window.location.pathname + window.location.search !== destination) {
        this.$router.push(destination);
      }
    } catch (error) {
      console.log(error);
    }
  }
    },
    computed: {

    },
    watch:{
      '$route.query.entity': {
        handler(newEntity) {
          this.activeTab = newEntity || 'client'; // Default to 'client' if no entity
        },
        immediate: true
      }
    },
    mounted() {
      this.resultsRefresh(true);
      // Set initial active tab based on current route
      this.activeTab = this.$route.query.entity || 'client';
    },
    components: {
      AdminSearch,
      AdminResults,
  },
}
</script>

