// src/services/commentsService.js (renamed from rulesService.js)
import { API } from "aws-amplify";

// READ: Fetch comments by `name`
// Example usage: getComments("barriers") or getComments("horseprofile")
export async function getComments(name) {
  try {
    // calls GET /comments?name=NAME
    const response = await API.get("MrCenApiGateway", `/comments?name=${name}`, {});
    return response; // or response.data if your backend returns { data: ... }
  } catch (error) {
    console.error("Error fetching comments:", error);
    throw error;
  }
}

// CREATE: Create a new comment
// Example usage: createComment({ Name: "...", active: true, ... })
export async function createComment(commentData) {
  try {
    // calls POST /comments
    const response = await API.post("MrCenApiGateway", "/comments", {
      body: commentData,
    });
    return response; // or response.data
  } catch (error) {
    console.error("Error creating comment:", error);
    throw error;
  }
}

// UPDATE: Update a specific comment by _id
// Example usage: updateComment("someMongoId", { Name: "New name", active: false })
export async function updateComment(commentId, commentData) {
  try {
    // calls PUT /comments/:commentId
    const response = await API.put("MrCenApiGateway", `/comments/${commentId}`, {
      body: commentData,
    });
    return response; // or response.data
  } catch (error) {
    console.error("Error updating comment:", error);
    throw error;
  }
}

// DELETE: Delete a specific comment by _id
// Example usage: deleteComment("someMongoId")
export async function deleteComment(commentId) {
  try {
    // calls DELETE /comments/:commentId
    const response = await API.del("MrCenApiGateway", `/comments/${commentId}`, {});
    return response; // or response.data
  } catch (error) {
    console.error("Error deleting comment:", error);
    throw error;
  }
}
