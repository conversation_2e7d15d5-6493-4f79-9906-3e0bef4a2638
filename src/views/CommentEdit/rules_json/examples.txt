
type:

compare // compare a field in a phase to another field or to a static value
count  // count form items, either matching specific criteria or not


phase:

currentHorse // horse entry in current processed meeting
currentRace // race level data from current race of processed meeting
currentMeeting // meeting level data from current processed meeting
dbHorse // horse entry in horse db table
dbForm // data relating to the 'form' array of the horse form db entry, with trials removed
dbTrialForm // data relating to trial form entries from the above form array
staticValue // a fixed value


compareFunc:

greaterThan
lessThan
equalTo
notEqualTo


compare1

phase
field // the key path in the phase object being referenced
number // in relation to dbForm and dbTrialForm items, 1 is most recent, 2 is next most recent etc.
?? isNotEmpty // is not a "0" or empty string, null, 0, or undefined
modifyOperator // * / - + mathematical operators to apply to the field value for comparison
modifyValue // the value to be used with the mathematical operator to apply to the field value for comparison
isJumps // used with count compare1 to return true if a form item is a jumps race


consecutive // used with count to denote a false if any items do not match before a min value is met

maxvalue // used with count to denote the max number of form items that can match before a false is returned

minvalue // used with count to denote the number of form items that are required to match before a true can be returned

campaign // used with dbForm or dbTrialForm, a camapign is all runs between spells (84 days wthout racing) options are "current", "previous" and "each". Use to assess whole campaigns or to pick a run number from the campaign using "campaignrun" 

campaignrun // when using phase:dbForm, only look at selected run from selected campaign. Unlike "number", 1 is the earliest campaign run, 2 is the next etc. can be an array. Ones that do not exist will be ignored.

campaignrunexclude // like campaignrun, but this is telling the system to look at all the runs not listed. can be an array. Ones that do not exist will be ignored.

ifExists // if the form item this would be referencing doesnt exist, return true


"ruleParameters": [
    // for the below, we are checking that the dbForm count (form.length)
    // has am minimum value of 2

    {
        "type": "count",
        "minvalue": 2,
        "compare1": {
            "phase": "dbForm"
        }
    },


    // for the below, we are looking at the horse.betting
    // value of the horse in the processed_meeting (currentHorse)
    // will return true if horse.betting valus is 
    // less than the staticValue of 5

    {
        "type": "compareFunc",
        "compare1": {
            "phase": "currentHorse",
            "field": "betting"
        },
        "compare2": {
            "phase": "staticValue",
            "value": 5.01
        },
        "compareFunc": "lessThan"
    },


    // for the below, we are checking that the horse.barrier
    // from the processed_meeting, is "lessThan"
    // the value of the "currentRace" starters from the
    // processed_meeting (race.starters) 
    // multiplied ("modifyOperator" : "*") by 0.34
    // ("modifyValue": 0.34)
    // for example, in a 20 horse race, 
    // barrier 1-6 would return true (20 * 0.34 = 6.8)

    {
        "type": "compare",
        "compare1": {
            "phase": "currentHorse",
            "field": "barrier"
        },
        "compare2": {
            "phase": "currentRace",
            "value": "starters",
            "modifyOperator": "*",
            "modifyValue": 0.34

        },
        "compareFunc": "lessThan"
    }


    // for the below, it checks the 1 form item only ("number" : 1)
    // it is comparing the "field" form[0].positions["@_settling_down"]
    // is "lessThan" the staticValue of 5
    // it will return true or false

    {
        "type": "compare",
        "compare1": {
            "phase": "dbForm",
            "number": 1,
            "field": "positions.@_settling_down"
        },
        "compare2": {
            "phase": "staticValue",
            "value": 5
        },
        "compare": "lessThan"
    },


    // for the below, it will cycle through the form items
    // in each, it will compare the "field" form.finish_position,
    // to see if it is "equalTo" the "staticValue" of 1
    // because consecutive is selected, if any do not match, false is returned
    // if the minvalue number of form items is checked, 
    // and false has not been returned, true is returned

    {
        "type": "count",
        "consecutive": true,
        "minvalue": 3,
        "compare1": {
            "phase": "dbForm",
            "field": "finish_position"
        },
        "compare2": {
            "phase": "staticValue",
            "value": 1
        },
        "compareFunc": "equalTo"
    }
]




