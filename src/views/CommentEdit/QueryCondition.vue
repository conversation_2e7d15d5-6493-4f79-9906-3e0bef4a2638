<template>
    <div class="p-d-flex p-ai-center p-mb-1">
      <!-- Choose a field -->
      <p-dropdown
        class="p-mr-1"
        :options="fieldOptions"
        optionLabel="label"
        optionValue="value"
        v-model="field"
        placeholder="Select Field"
      />
      <!-- Choose an operator -->
      <p-dropdown
        class="p-mr-1"
        :options="operatorOptions"
        v-model="operator"
        placeholder="Operator"
      />
      <!-- Input a value -->
      <p-inputtext
        class="p-mr-1"
        v-model="value"
        placeholder="Value"
      />
    </div>
  </template>
  
  <script>
  import { defineComponent, reactive, toRefs, watch, computed } from 'vue'
  import PDropdown from 'primevue/dropdown'
  import PInputText from 'primevue/inputtext'
  
  export default defineComponent({
    name: 'QueryCondition',
    components: {
      'p-dropdown': PDropdown,
      'p-inputtext': PInputText
    },
    props: {
      condition: {
        type: Object,
        required: true
      },
      availableFields: {
        type: Array,
        default: () => []
      }
    },
    emits: ['update:condition'],
    setup(props, { emit }) {
      // Local copy of condition
      const working = reactive({
        field: props.condition.field || null,
        operator: props.condition.operator || '=',
        value: props.condition.value || ''
      })
  
      // Build field options
      const fieldOptions = computed(() => props.availableFields)
  
      // Determine valid operators for the selected field
      const operatorOptions = computed(() => {
        const field = props.availableFields.find(f => f.value === working.field)
        return field
          ? field.operators
          : ['=', '!=', '>', '<', 'contains'] // fallback
      })
  
      // Emit whenever the condition changes
      watch(() => working, (val) => {
        emit('update:condition', { ...val })
      }, { deep: true })
  
      return {
        ...toRefs(working),
        fieldOptions,
        operatorOptions
      }
    }
  })
  </script>
  
  <style scoped>
  </style>
  