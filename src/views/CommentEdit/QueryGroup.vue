<template>
    <div
      :class="'query-group depth-' + depth"
      style="border:1px solid #ccc; margin:1rem 0; padding:1rem; border-radius:4px;"
    >
      <div class="p-d-flex p-jc-between p-ai-center p-mb-2">
        <!-- Operator (AND/OR) -->
        <p-dropdown
          v-model="logicalOperator"
          :options="logicalOptions"
          optionLabel="label"
          optionValue="value"
          class="p-mr-2"
        />
  
        <!-- Add Condition / Group -->
        <div>
          <p-button
            label="Add Condition"
            icon="pi pi-plus"
            class="p-button-sm p-button-outlined p-mr-2"
            @click="addCondition"
          />
          <p-button
            label="Add Group"
            icon="pi pi-plus-circle"
            class="p-button-sm p-button-outlined p-button-secondary"
            @click="addGroup"
          />
        </div>
      </div>
  
      <!-- Items (either sub-groups or conditions) -->
      <div class="group-items">
        <div
          v-for="(item, index) in conditions"
          :key="item.key"
          class="p-mb-2"
        >
          <!-- Distinguish group vs condition by checking item.conditions -->
          <template v-if="item.conditions">
            <!-- Nested Group -->
            <QueryGroup
              :group="item"
              :available-fields="availableFields"
              :depth="depth + 1"
              @update:group="val => onGroupUpdate(index, val)"
            />
            <!-- Move up/down / remove (actions for the group) -->
            <div class="p-text-right p-mt-1">
              <p-button
                icon="pi pi-angle-up"
                class="p-button-text p-button-sm p-mr-1"
                @click="moveUp(index)"
              />
              <p-button
                icon="pi pi-angle-down"
                class="p-button-text p-button-sm p-mr-1"
                @click="moveDown(index)"
              />
              <p-button
                icon="pi pi-trash"
                class="p-button-danger p-button-text p-button-sm"
                @click="removeItem(index)"
              />
            </div>
          </template>
          <template v-else>
            <!-- Single Condition -->
            <QueryCondition
              :condition="item"
              :available-fields="availableFields"
              @update:condition="val => onConditionUpdate(index, val)"
            />
            <!-- Move up/down / remove (actions for the condition) -->
            <div class="p-text-right">
              <p-button
                icon="pi pi-angle-up"
                class="p-button-text p-button-sm p-mr-1"
                @click="moveUp(index)"
              />
              <p-button
                icon="pi pi-angle-down"
                class="p-button-text p-button-sm p-mr-1"
                @click="moveDown(index)"
              />
              <p-button
                icon="pi pi-trash"
                class="p-button-danger p-button-text p-button-sm"
                @click="removeItem(index)"
              />
            </div>
          </template>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  /**
   * QueryGroup.vue
   * Allows creating nested AND/OR groups. Each group has an array of conditions or child groups.
   */
  import { defineComponent, reactive, toRefs, watch } from 'vue'
  import { v4 as uuidv4 } from 'uuid'
  import PButton from 'primevue/button'
  import PDropdown from 'primevue/dropdown'
  import QueryCondition from './QueryCondition.vue'
  
  export default defineComponent({
    name: 'QueryGroup',
    components: {
      'p-button': PButton,
      'p-dropdown': PDropdown,
      QueryCondition
    },
    props: {
      group: {
        type: Object,
        required: true
      },
      availableFields: {
        type: Array,
        default: () => []
      },
      depth: {
        type: Number,
        default: 1
      }
    },
    emits: ['update:group'],
    setup(props, { emit }) {
      // Local reactive copy
      const workingGroup = reactive({
        logicalOperator: props.group.logicalOperator || 'AND',
        conditions: props.group.conditions || []
      })
  
      const logicalOptions = [
        { label: 'AND', value: 'AND' },
        { label: 'OR',  value: 'OR' }
      ]
  
      // Watch for changes, emit up
      watch(() => workingGroup, () => {
        emitChange()
      }, { deep: true })
  
      // Add a condition
      const addCondition = () => {
        workingGroup.conditions.push({
          key: uuidv4(),
          field: null,
          operator: '=',
          value: ''
        })
      }
  
      // Add a nested group
      const addGroup = () => {
        workingGroup.conditions.push({
          key: uuidv4(),
          logicalOperator: 'AND',
          conditions: []
        })
      }
  
      // Remove an item
      const removeItem = (index) => {
        workingGroup.conditions.splice(index, 1)
      }
  
      // Move items up/down
      const moveUp = (index) => {
        if (index === 0) return
        const item = workingGroup.conditions.splice(index, 1)[0]
        workingGroup.conditions.splice(index - 1, 0, item)
      }
  
      const moveDown = (index) => {
        if (index === workingGroup.conditions.length - 1) return
        const item = workingGroup.conditions.splice(index, 1)[0]
        workingGroup.conditions.splice(index + 1, 0, item)
      }
  
      // Child update
      const onConditionUpdate = (index, updatedCond) => {
        workingGroup.conditions[index] = updatedCond
      }
      const onGroupUpdate = (index, updatedGroup) => {
        workingGroup.conditions[index] = updatedGroup
      }
  
      // Emit final value up
      function emitChange() {
        const newVal = {
          logicalOperator: workingGroup.logicalOperator,
          conditions: workingGroup.conditions
        }
        emit('update:group', newVal)
      }
  
      return {
        ...toRefs(workingGroup),
        logicalOptions,
        addCondition,
        addGroup,
        removeItem,
        moveUp,
        moveDown,
        onConditionUpdate,
        onGroupUpdate
      }
    }
  })
  </script>
  
  <style scoped>
  .group-items {
    margin-left: 1rem;
  }
  </style>
  