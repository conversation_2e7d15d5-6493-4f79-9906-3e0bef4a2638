# Environment Configuration

This directory contains environment-specific configuration for the Centaur application.

## Overview

The environment configuration system automatically detects the current deployment environment and provides appropriate configuration values. This allows the application to use different API endpoints and settings based on whether it's running in staging or production.

## How it works

1. **Environment Detection**: The system automatically detects the current environment by examining the AWS Amplify configuration (`aws-exports.js`)
2. **Configuration Selection**: Based on the detected environment, it selects the appropriate configuration
3. **Environment Variable Override**: Environment variables can override the default configuration

## Supported Environments

- `stg` - Staging environment
- `stgblue` - Staging Blue environment  
- `stgblack` - Staging Black environment
- `prd` - Production environment

## Configuration Files

### `environment.js`

Main configuration file that exports:

- `getEnvironmentConfig()` - Returns configuration for current environment
- `getCurrentEnvironmentName()` - Returns current environment name
- `isProduction()` - Returns true if running in production
- `isStaging()` - Returns true if running in any staging environment

## Environment Variables

You can override configuration using environment variables:

### `VUE_APP_REPORT_API_URL`

Override the report API URL for the current environment.

**Example:**
```bash
# For staging
VUE_APP_REPORT_API_URL=https://51erxl7hb6.execute-api.ap-southeast-2.amazonaws.com/report

# For production (when available)
VUE_APP_REPORT_API_URL=https://production-api.execute-api.ap-southeast-2.amazonaws.com/report
```

## Usage in Components

```javascript
import { getEnvironmentConfig } from "../config/environment";

export default {
  methods: {
    async callAPI() {
      const config = getEnvironmentConfig();
      const response = await axios.post(config.reportApiUrl, data);
      // ...
    }
  }
}
```

## AWS Amplify Integration

The configuration is automatically set during the AWS Amplify build process via `amplify.yml`. Different environments will receive different configuration values based on the `AWS_BRANCH` environment variable.

## Local Development

For local development, create a `.env.local` file in the project root:

```bash
# Copy from .env.example and modify as needed
VUE_APP_REPORT_API_URL=https://51erxl7hb6.execute-api.ap-southeast-2.amazonaws.com/report
```

## Adding New Configuration

To add new environment-specific configuration:

1. Add the new property to each environment in `environmentConfig` object
2. Update the TypeScript types if using TypeScript
3. Document the new configuration option in this README

## Troubleshooting

- Check browser console for environment detection logs
- Verify AWS configuration is properly loaded
- Ensure environment variables are prefixed with `VUE_APP_`
- Check that the build process is setting environment variables correctly
