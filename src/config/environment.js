/**
 * Environment configuration for the Centaur application
 * Determines the current environment and provides appropriate configuration
 */

import awsconfig from "../aws-exports.js";

/**
 * Determines the current environment based on AWS Amplify configuration
 * @returns {string} Environment name (stg, stgblue, stgblack, prd)
 */
function getCurrentEnvironment() {
  // Extract environment from the API Gateway endpoint
  const apiEndpoint = awsconfig.aws_cloud_logic_custom?.[0]?.endpoint;

  if (!apiEndpoint) {
    console.warn('No API endpoint found in AWS config, defaulting to stg');
    return 'stg';
  }

  // Parse environment from endpoint URL
  if (apiEndpoint.includes('/stgblack')) {
    return 'stgblack';
  } else if (apiEndpoint.includes('/stgblue')) {
    return 'stgblue';
  } else if (apiEndpoint.includes('/stg')) {
    return 'stg';
  } else if (apiEndpoint.includes('/prd')) {
    return 'prd';
  }

  // Fallback: try to determine from AmplifyAppId or other indicators
  const amplifyAppId = awsconfig.aws_cognito_identity_pool_id;
  if (amplifyAppId && amplifyAppId.includes('************')) {
    return 'prd'; // Production AWS account
  }

  console.warn('Could not determine environment from AWS config, defaulting to stg');
  return 'stg';
}

/**
 * Environment-specific configuration
 */
const environmentConfig = {
  stg: {
    reportApiUrl: 'https://51erxl7hb6.execute-api.ap-southeast-2.amazonaws.com/report',
    environment: 'staging',
    debug: true
  },
  stgblue: {
    reportApiUrl: 'https://51erxl7hb6.execute-api.ap-southeast-2.amazonaws.com/report',
    environment: 'staging-blue',
    debug: true
  },
  stgblack: {
    reportApiUrl: 'https://51erxl7hb6.execute-api.ap-southeast-2.amazonaws.com/report',
    environment: 'staging-black',
    debug: true
  },
  prd: {
    // Production environment - update this URL when production report API is available
    reportApiUrl: 'https://51erxl7hb6.execute-api.ap-southeast-2.amazonaws.com/report',
    environment: 'production',
    debug: false
  }
};

/**
 * Gets the configuration for the current environment
 * @returns {object} Environment configuration object
 */
export function getEnvironmentConfig() {
  const currentEnv = getCurrentEnvironment();
  let config = environmentConfig[currentEnv];

  if (!config) {
    console.error(`No configuration found for environment: ${currentEnv}`);
    config = environmentConfig.stg; // Fallback to staging
  }

  // Override with environment variables if available
  const envConfig = { ...config };

  // Check for environment variable overrides
  if (process.env.VUE_APP_REPORT_API_URL) {
    envConfig.reportApiUrl = process.env.VUE_APP_REPORT_API_URL;
    console.log('Using report API URL from environment variable:', envConfig.reportApiUrl);
  }

  if (config.debug) {
    console.log(`Environment detected: ${currentEnv}`, envConfig);
  }

  return envConfig;
}

/**
 * Gets the current environment name
 * @returns {string} Environment name
 */
export function getCurrentEnvironmentName() {
  return getCurrentEnvironment();
}

/**
 * Checks if the current environment is production
 * @returns {boolean} True if production environment
 */
export function isProduction() {
  return getCurrentEnvironment() === 'prd';
}

/**
 * Checks if the current environment is staging (any staging variant)
 * @returns {boolean} True if staging environment
 */
export function isStaging() {
  const env = getCurrentEnvironment();
  return env.startsWith('stg');
}
