import Vue from "vue";
import VueRouter from "vue-router";
import Inputs from "../components/InputsScreen.vue";
import Meetings from "../views/Meetings/MeetingOverview.vue";
import AutoComment from "../views/AutoComment/AutoComment.vue";
import Signin from "../views/SigninScreen.vue";
import Database from "../views/Database/DatabaseMain.vue";

Vue.use(VueRouter);

const routes = [
  {
    path: "/",
    name: "Signin",
    component: Signin,
  },
  {
    path: "/meetings",
    name: "Meetings",
    component: Meetings,
  },
  {
    path: "/db",
    name: "Database",
    component: Database,
  },
  {
    path: "/inputs",
    name: "Inputs",
    component: Inputs,
    // route level code-splitting
    // this generates a separate chunk (about.[hash].js) for this route
    // which is lazy-loaded when the route is visited.
    // component: function () {
    //   return import(/* webpackChunkName: "about" */ "../views/Inputs.vue");
    // },
  },
  {
    path: "/autocomment",
    name: "AutoComment",
    component: AutoComment,
  },
];

const router = new VueRouter({
  mode: "history",
  base: process.env.BASE_URL,
  routes,
});

export default router;
