{"name": "mediality-app", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "test": "jest"}, "dependencies": {"@aws-amplify/auth": "^4.5.2", "@aws-amplify/core": "^4.5.2", "@aws-amplify/ui-components": "^1.9.6", "@aws-amplify/ui-vue": "^2.3.2", "@braid/vue-formulate": "^2.5.3", "@formkit/themes": "^1.0.0-beta.6", "@formkit/vue": "^1.0.0-beta.6", "@mdi/font": "^6.6.96", "@mdi/js": "^6.6.96", "@vuelidate/core": "^2.0.0-alpha.41", "@vuelidate/validators": "^2.0.0-alpha.29", "aws-amplify": "^4.3.17", "aws-amplify-vue": "^2.1.5", "aws-sdk": "^2.1125.0", "axios": "^1.6.7", "core-js": "^3.8.3", "file-saver": "^2.0.5", "mdi-vue": "^3.0.12", "pg": "^8.14.0", "primeflex": "^3.1.3", "primeicons": "^5.0.0", "primevue": "^3.12.4", "roboto-fontface": "*", "tabulator-tables": "^5.1.7", "vue": "^3.2.13", "vue-router": "^4.0.14", "vue-uuid": "^3.0.0", "vuedraggable": "^4.1.0", "vuelidate": "^0.7.7", "vuetify": "npm:@vuetify/nightly@^3.0.0-next-20220328.0", "vuex": "^4.0.2", "webfontloader": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vitejs/plugin-vue": "^2.0.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vuetify/vite-plugin": "^1.0.0-alpha.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "moment": "^2.29.3", "sass": "^1.49.9", "sass-loader": "^10.0.0", "vite": "^2.0.0", "vue-cli-plugin-vuetify": "~2.4.8", "vuetify-loader": "^2.0.0-alpha.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}