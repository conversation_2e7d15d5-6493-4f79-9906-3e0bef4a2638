const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
  transpileDependencies: true,

  // Ensure environment variables are properly loaded
  configureWebpack: {
    // Enable source maps for debugging
    devtool: process.env.NODE_ENV === 'development' ? 'eval-source-map' : 'source-map',
  },

  pluginOptions: {
    vuetify: {
			// https://github.com/vuetifyjs/vuetify-loader/tree/next/packages/vuetify-loader
		}
  }
})
