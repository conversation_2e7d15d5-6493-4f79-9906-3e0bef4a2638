{"stgmanu": {"awscloudformation": {"AuthRoleName": "amplify-centaurapp-stgmanu-112341-authRole", "UnauthRoleArn": "arn:aws:iam::************:role/amplify-centaurapp-stgmanu-112341-unauthRole", "AuthRoleArn": "arn:aws:iam::************:role/amplify-centaurapp-stgmanu-112341-authRole", "Region": "ap-southeast-2", "DeploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "UnauthRoleName": "amplify-centaurapp-stgmanu-112341-unauthRole", "StackName": "amplify-centaurapp-stgmanu-112341", "StackId": "arn:aws:cloudformation:ap-southeast-2:************:stack/amplify-centaurapp-stgmanu-112341/8fdaf910-84df-11ee-bf8c-02706d8eea7d", "AmplifyAppId": "d3fh3w2m9jb4wi", "APIGatewayAuthURL": "https://s3.amazonaws.com/amplify-centaurapp-stgmanu-112341-deployment/amplify-cfn-templates/api/APIGatewayAuthStack.json"}, "categories": {"auth": {"centaurapp": {}}, "function": {"MrCenDocDbScheduler": {"deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MrCenDocDbScheduler-6c702f50665a56396267-build.zip"}, "MrCenLambdaApi": {"centaurSecrets": "*********************************", "awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MrCenLambdaApi-754c4c4e6b327645436f-build.zip"}, "MrCenLambdaApiAdmin": {"awsLocal": "false", "centaurSecrets": "*********************************", "customerApiGateway": "MrCenApiGatewayPublic", "deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MrCenLambdaApiAdmin-3574446e426445416838-build.zip"}, "MrCenLambdaClientDeliveryService": {"deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MrCenLambdaClientDeliveryService-486856726b7677304244-build.zip", "centaurSecrets": "*********************************", "centaurSecretsId": "*********************************-95P2aQ"}, "MrCenLambdaComment": {"secretsPathAmplifyAppId": "d3fh3w2m9jb4wi", "centaurSecrets": "*********************************", "awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MrCenLambdaComment-616b4f7849347448712f-build.zip"}, "MrCenLambdaDBcleanup": {"region": "AWS::Region", "deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MrCenLambdaDBcleanup-564d7244303658446f67-build.zip", "env": "env", "awsLocal": "awsLocal", "centaurSecrets": "*********************************"}, "MrCenLambdaDeliveryFunction": {"centaurSecrets": "*********************************", "deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MrCenLambdaDeliveryFunction-5a336b7361474d713339-build.zip"}, "MrCenLambdaGetDeliveryManifest": {"deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MrCenLambdaGetDeliveryManifest-71434a6f33433946357a-build.zip", "centaurSecrets": "*********************************", "centaurSecretsId": "*********************************-95P2aQ"}, "MrCenLambdaPollerFunction": {"timeDiff": "*********0", "awsLocal": "false", "mockSftpData": "false", "fileProcessLimit": "15", "deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MrCenLambdaPollerFunction-6132486c576276314942-build.zip"}, "MrCenLambdaPollerTIS": {"timeDiff": "*********0", "awsLocal": "false", "mockSftpData": "false", "fileProcessLimit": "15", "deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MrCenLambdaPollerTIS-793570347764425a7a36-build.zip"}, "MrCenLambdaPostProcessMeeting": {"deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MrCenLambdaPostProcessMeeting-6441466e76437433316e-build.zip"}, "MrCenLambdaProcessMeeting": {"centaurSecrets": "*********************************", "awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MrCenLambdaProcessMeeting-372b5a4d7a5a6d487845-build.zip"}, "MrCenLambdaProcessStats": {"deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MrCenLambdaProcessStats-2b535435544a4a614855-build.zip"}, "MrCenLambdaStepFunctionExecutor": {"awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MrCenLambdaStepFunctionExecutor-704a5a77586e6c684639-build.zip"}, "centaurappCentaurAppCommonLayer": {"deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/centaurappCentaurAppCommonLayer-LambdaLayerVersiond4af1281-build.zip"}, "MrCenDeliveryServiceXmlSourceStorage": {}, "MrCenDeliveryServiceXmlOutputStorage": {}, "MRCenLambdaHorseNameUpdate": {"deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MRCenLambdaHorseNameUpdate-7a716f473667386c317a-build.zip", "region": "AWS::Region", "env": "env", "awsLocal": "awsLocal"}, "notificationToGoogleChat": {}, "MRCenLambdaJockeyUpdate": {"deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MRCenLambdaJockeyUpdate-4b417a58485154704559-build.zip"}, "MRCenFileArchive": {"region": "ap-southeast-2", "deploymentBucketName": "amplify-centaurapp-stgmanu-112341-deployment", "s3Key": "amplify-builds/MRCenFileArchive-33387063544a30625366-build.zip"}, "MRCenLambdaCronSendFormXML": {"centaurSecrets": "*********************************"}}, "api": {"MrCenApiGateway": {}}, "custom": {"MrCenAlarmSnsTopic": {}, "MrCenDeadLetterQueue": {}, "MrCenDeliveryServiceXmlOutputStorage": {}, "MrCenDeliveryServiceXmlSourceStorage": {}, "MrCenEventBridge": {}, "MrCenFileStorage": {}, "MrCenProcessClientDeliveryServiceWorkflow": {}, "MrCenProcessStatsWorkflow": {}, "ServiceAccounts": {}, "MrCenDataUpdateStepFunction": {}}, "stepFunction": {"MrCenProcessMeetingWorkflow": {}}}}, "stgblue": {"awscloudformation": {"AuthRoleName": "amplify-centaurapp-stgblue-91145-authRole", "UnauthRoleArn": "arn:aws:iam::************:role/amplify-centaurapp-stgblue-91145-unauthRole", "AuthRoleArn": "arn:aws:iam::************:role/amplify-centaurapp-stgblue-91145-authRole", "Region": "ap-southeast-2", "DeploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "UnauthRoleName": "amplify-centaurapp-stgblue-91145-unauthRole", "StackName": "amplify-centaurapp-stgblue-91145", "StackId": "arn:aws:cloudformation:ap-southeast-2:************:stack/amplify-centaurapp-stgblue-91145/ccea2b80-1db8-11ed-870b-06595eb28628", "AmplifyAppId": "d3fh3w2m9jb4wi", "APIGatewayAuthURL": "https://s3.amazonaws.com/amplify-centaurapp-stgblue-91145-deployment/amplify-cfn-templates/api/APIGatewayAuthStack.json"}, "categories": {"function": {"MrCenLambdaPollerFunction": {"timeDiff": "*********0", "awsLocal": "false", "mockSftpData": "false", "fileProcessLimit": "15", "deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrCenLambdaPollerFunction-6132486c576276314942-build.zip"}, "MrCenLambdaStepFunctionExecutor": {"awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrCenLambdaStepFunctionExecutor-61526e7348576545305a-build.zip"}, "MrCenLambdaApi": {"centaurSecrets": "*********************************", "awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrCenLambdaApi-7232612b395465423432-build.zip"}, "MrCenLambdaProcessMeeting": {"centaurSecrets": "*********************************", "awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrCenLambdaProcessMeeting-3174567249484e7a5646-build.zip"}, "MrCenDocDbScheduler": {"deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrCenDocDbScheduler-6c702f50665a56396267-build.zip"}, "MrCenLambdaProcessStats": {"deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrCenLambdaProcessStats-4636707075394351424f-build.zip"}, "MrCenLambdaPostProcessMeeting": {"deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrCenLambdaPostProcessMeeting-4f414a41596e34712b37-build.zip"}, "MrCenLambdaApiAdmin": {"awsLocal": "false", "centaurSecrets": "*********************************", "customerApiGateway": "MrCenApiGatewayPublic", "deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrCenLambdaApiAdmin-5a555a31317335537564-build.zip"}, "MrCenLambdaDeliveryFunction": {"centaurSecrets": "*********************************", "deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrCenLambdaDeliveryFunction-4c45357177383531524a-build.zip"}, "centaurappCentaurAppCommonLayer": {"deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/centaurappCentaurAppCommonLayer-LambdaLayerVersionc9ec03a0-build.zip"}, "MrCenLambdaComment": {"secretsPathAmplifyAppId": "d3fh3w2m9jb4wi", "centaurSecrets": "*********************************", "awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrCenLambdaComment-2b304757554830346e6d-build.zip"}, "MrCenLambdaClientDeliveryService": {"deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrCenLambdaClientDeliveryService-654d7232782b55593467-build.zip", "centaurSecrets": "*********************************", "centaurSecretsId": "*********************************-95P2aQ"}, "MrCenLambdaGetDeliveryManifest": {"deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrCenLambdaGetDeliveryManifest-7a67746b4a4771665739-build.zip", "centaurSecrets": "*********************************", "centaurSecretsId": "*********************************-95P2aQ"}, "MrCenLambdaDBcleanup": {"region": "AWS::Region", "deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrCenLambdaDBcleanup-54687662496132444e33-build.zip", "env": "env", "awsLocal": "awsLocal", "centaurSecrets": "*********************************"}, "MrCenLambdaPollerTIS": {"timeDiff": "*********0", "awsLocal": "false", "mockSftpData": "false", "fileProcessLimit": "15", "deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrCenLambdaPollerTIS-4833594e6b616432325a-build.zip"}, "MrCenDeliveryServiceXmlSourceStorage": {}, "MrCenDeliveryServiceXmlOutputStorage": {}, "MRCenFileArchive": {"region": "ap-southeast-2", "deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MRCenFileArchive-724c676862534b757539-build.zip"}, "MRCenLambdaHorseNameUpdate": {"deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MRCenLambdaHorseNameUpdate-48754a77475961423063-build.zip"}, "MRCenLambdaJockeyUpdate": {"deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MRCenLambdaJockeyUpdate-6c7551764238724e3761-build.zip"}, "MRCenLambdaCronSendFormXML": {"deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MRCenLambdaCronSendFormXML-7a4a4141656a4e433736-build.zip"}, "MrManualTriggerUpdate": {"deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrManualTriggerUpdate-7554742f72784861674d-build.zip"}, "MrCenLambdaInputFileLoad": {"deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrCenLambdaInputFileLoad-4e714738574c2f486f6d-build.zip"}, "MRCentCompareMeeting": {"deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MRCentCompareMeeting-717253626461414a427a-build.zip"}, "MrCenEdmGenerate": {"deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrCenEdmGenerate-316a364b497a34447673-build.zip"}, "MRCenProcessRating": {"deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MRCenProcessRating-712f345a62422f475678-build.zip"}, "MrPegasusLambdaCommentGen": {"secretsPathAmplifyAppId": "d3fh3w2m9jb4wi", "centaurSecrets": "*********************************", "awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-stgblue-91145-deployment", "s3Key": "amplify-builds/MrPegasusLambdaCommentGen-7732362b503330707639-build.zip"}}, "auth": {"centaurapp": {}}, "api": {"MrCenApiGateway": {}}, "custom": {"MrCenDeadLetterQueue": {}, "MrCenFileStorage": {}, "MrCenProcessStatsWorkflow": {}, "MrCenAlarmSnsTopic": {}, "MrCenDeliveryServiceXmlOutputStorage": {}, "MrCenDeliveryServiceXmlSourceStorage": {}, "MrCenEventBridge": {}, "MrCenProcessClientDeliveryServiceWorkflow": {}, "ServiceAccounts": {}, "MrCenDataUpdateStepFunction": {}}, "stepFunction": {"MrCenProcessMeetingWorkflow": {}}}}, "prd": {"awscloudformation": {"AuthRoleName": "amplify-centaurapp-prd-44427-authRole", "UnauthRoleArn": "arn:aws:iam::************:role/amplify-centaurapp-prd-44427-unauthRole", "AuthRoleArn": "arn:aws:iam::************:role/amplify-centaurapp-prd-44427-authRole", "Region": "ap-southeast-2", "DeploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "UnauthRoleName": "amplify-centaurapp-prd-44427-unauthRole", "StackName": "amplify-centaurapp-prd-44427", "StackId": "arn:aws:cloudformation:ap-southeast-2:************:stack/amplify-centaurapp-prd-44427/3d98e4b0-b564-11ec-b3da-0a487f1440c6", "AmplifyAppId": "d1x89fm284b95l", "APIGatewayAuthURL": "https://s3.amazonaws.com/amplify-centaurapp-prd-44427-deployment/amplify-cfn-templates/api/APIGatewayAuthStack.json"}, "categories": {"function": {"MrCenLambdaPollerFunction": {"deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "s3Key": "amplify-builds/MrCenLambdaPollerFunction-6132486c576276314942-build.zip", "timeDiff": "*********0", "awsLocal": "false", "mockSftpData": "false", "fileProcessLimit": "10"}, "MrCenLambdaStepFunctionExecutor": {"deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "s3Key": "amplify-builds/MrCenLambdaStepFunctionExecutor-596d433752344d56535a-build.zip", "awsLocal": "false"}, "centaurappCentaurAppCommonLayer": {"deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "s3Key": "amplify-builds/centaurappCentaurAppCommonLayer-LambdaLayerVersiond72e26f1-build.zip"}, "MrCenLambdaApi": {"deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "s3Key": "amplify-builds/MrCenLambdaApi-306b47427a7258316448-build.zip", "centaurSecrets": "*********************************", "awsLocal": "false"}, "MrCenLambdaProcessMeeting": {"deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "s3Key": "amplify-builds/MrCenLambdaProcessMeeting-4c76564e30565257452f-build.zip", "centaurSecrets": "*********************************", "awsLocal": "false"}, "MrCenDocDbScheduler": {"deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "s3Key": "amplify-builds/MrCenDocDbScheduler-6c702f50665a56396267-build.zip"}, "MrCenLambdaProcessStats": {"deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "centaurSecrets": "*********************************", "s3Key": "amplify-builds/MrCenLambdaProcessStats-354e536c497675674f43-build.zip"}, "MrCenLambdaPostProcessMeeting": {"deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "s3Key": "amplify-builds/MrCenLambdaPostProcessMeeting-6441466e76437433316e-build.zip"}, "MrCenLambdaApiAdmin": {"deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "s3Key": "amplify-builds/MrCenLambdaApiAdmin-3552323946766b6f7036-build.zip", "awsLocal": "false", "centaurSecrets": "*********************************", "customerApiGateway": "MrCenApiGatewayPublic"}, "MrCenLambdaDeliveryFunction": {"centaurSecrets": "*********************************", "deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "s3Key": "amplify-builds/MrCenLambdaDeliveryFunction-514a72462f45477a5457-build.zip"}, "MrCenLambdaComment": {"secretsPathAmplifyAppId": "d1x89fm284b95l", "centaurSecrets": "*********************************", "awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "s3Key": "amplify-builds/MrCenLambdaComment-616b4f7849347448712f-build.zip"}, "MrCenLambdaClientDeliveryService": {"deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "s3Key": "amplify-builds/MrCenLambdaClientDeliveryService-486856726b7677304244-build.zip", "centaurSecrets": "*********************************", "centaurSecretsId": "*********************************-45ISVp"}, "MrCenLambdaGetDeliveryManifest": {"deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "s3Key": "amplify-builds/MrCenLambdaGetDeliveryManifest-71434a6f33433946357a-build.zip", "centaurSecrets": "*********************************", "centaurSecretsId": "*********************************-45ISVp"}, "MrCenLambdaPollerTIS": {"timeDiff": "*********0", "awsLocal": "false", "mockSftpData": "false", "fileProcessLimit": "15", "deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "s3Key": "amplify-builds/MrCenLambdaPollerTIS-4833594e6b616432325a-build.zip"}, "MrCenLambdaDBcleanup": {"region": "AWS::Region", "deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "s3Key": "amplify-builds/MrCenLambdaDBcleanup-564d7244303658446f67-build.zip", "env": "env", "awsLocal": "awsLocal", "centaurSecrets": "*********************************"}, "MRCenLambdaHorseNameUpdate": {"deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "s3Key": "amplify-builds/MRCenLambdaHorseNameUpdate-7a716f473667386c317a-build.zip"}, "MRCenLambdaJockeyUpdate": {"deploymentBucketName": "amplify-centaurapp-prd-44427-deployment", "s3Key": "amplify-builds/MRCenLambdaJockeyUpdate-4b417a58485154704559-build.zip"}, "MRCenFileArchive": {}, "MRCenLambdaCronSendFormXML": {}}, "auth": {"centaurapp": {}}}}, "stg": {"awscloudformation": {"AuthRoleName": "amplify-centaurapp-stg-212208-authRole", "UnauthRoleArn": "arn:aws:iam::************:role/amplify-centaurapp-stg-212208-unauthRole", "AuthRoleArn": "arn:aws:iam::************:role/amplify-centaurapp-stg-212208-authRole", "Region": "ap-southeast-2", "DeploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "UnauthRoleName": "amplify-centaurapp-stg-212208-unauthRole", "StackName": "amplify-centaurapp-stg-212208", "StackId": "arn:aws:cloudformation:ap-southeast-2:************:stack/amplify-centaurapp-stg-212208/79f7de60-8fdb-11ec-9612-0205ec467d34", "AmplifyAppId": "d3fh3w2m9jb4wi", "APIGatewayAuthURL": "https://s3.amazonaws.com/amplify-centaurapp-stg-212208-deployment/amplify-cfn-templates/api/APIGatewayAuthStack.json"}, "categories": {"function": {"MrCenLambdaPollerFunction": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MrCenLambdaPollerFunction-5958636679626a6f2f4b-build.zip", "timeDiff": "*********0", "awsLocal": "false", "mockSftpData": "false", "fileProcessLimit": "10"}, "MrCenLambdaStepFunctionExecutor": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MrCenLambdaStepFunctionExecutor-61526e7348576545305a-build.zip", "awsLocal": "false"}, "MrCenLambdaApi": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MrCenLambdaApi-4d64437a4c7558765637-build.zip", "centaurSecrets": "*********************************", "awsLocal": "false"}, "MrCenLambdaProcessMeeting": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MrCenLambdaProcessMeeting-2b626379413066413049-build.zip", "centaurSecrets": "*********************************", "awsLocal": "false"}, "MrCenDocDbScheduler": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MrCenDocDbScheduler-6c702f50665a56396267-build.zip"}, "MrCenLambdaProcessStats": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "centaurSecrets": "*********************************", "s3Key": "amplify-builds/MrCenLambdaProcessStats-47434f3267633338444d-build.zip"}, "centaurappCentaurAppCommonLayer": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/centaurappCentaurAppCommonLayer-LambdaLayerVersiond86feb4a-build.zip"}, "MrCenLambdaPostProcessMeeting": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MrCenLambdaPostProcessMeeting-4f414a41596e34712b37-build.zip"}, "MrCenLambdaApiAdmin": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MrCenLambdaApiAdmin-6a435864626d6536546b-build.zip", "awsLocal": "false", "centaurSecrets": "*********************************", "customerApiGateway": "MrCenApiGatewayPublic"}, "MrCenLambdaDeliveryFunction": {"centaurSecrets": "*********************************", "deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MrCenLambdaDeliveryFunction-5757473076316c303973-build.zip"}, "MrCenLambdaComment": {"secretsPathAmplifyAppId": "d3fh3w2m9jb4wi", "centaurSecrets": "*********************************", "awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MrCenLambdaComment-2b304757554830346e6d-build.zip"}, "MrCenLambdaClientDeliveryService": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MrCenLambdaClientDeliveryService-6b614b346c38396f7467-build.zip", "centaurSecrets": "*********************************", "centaurSecretsId": "*********************************-95P2aQ"}, "MrCenLambdaGetDeliveryManifest": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MrCenLambdaGetDeliveryManifest-7a67746b4a4771665739-build.zip", "centaurSecrets": "*********************************", "centaurSecretsId": "*********************************-95P2aQ"}, "MrCenLambdaPollerTIS": {"timeDiff": "*********0", "awsLocal": "false", "mockSftpData": "false", "fileProcessLimit": "15", "deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MrCenLambdaPollerTIS-6574746e764567443542-build.zip"}, "MrCenLambdaDBcleanup": {"region": "AWS::Region", "deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MrCenLambdaDBcleanup-514c6b53323472733578-build.zip", "env": "env", "awsLocal": "awsLocal", "centaurSecrets": "*********************************"}, "MRCenLambdaHorseNameUpdate": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MRCenLambdaHorseNameUpdate-4245783737686e4b6e30-build.zip"}, "MRCenLambdaJockeyUpdate": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MRCenLambdaJockeyUpdate-56634367333043542f74-build.zip"}, "MRCenFileArchive": {"region": "ap-southeast-2", "deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MRCenFileArchive-724c676862534b757539-build.zip"}, "MrManualTriggerUpdate": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MrManualTriggerUpdate-6a44774f7a384962772f-build.zip"}, "MRCenLambdaCronSendFormXML": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MRCenLambdaCronSendFormXML-37755546584b6b6e374f-build.zip"}, "MrPegasusLambdaCommentGen": {"secretsPathAmplifyAppId": "d3fh3w2m9jb4wi", "centaurSecrets": "*********************************", "awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-stg-212208-deployment"}, "MRCenProcessRating": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MRCenProcessRating-712f345a62422f475678-build.zip"}, "MRCentCompareMeeting": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MRCentCompareMeeting-46466e7a4a7a38564164-build.zip"}, "MrCenEdmGenerate": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MrCenEdmGenerate-316a364b497a34447673-build.zip"}, "MrCenLambdaInputFileLoad": {"deploymentBucketName": "amplify-centaurapp-stg-212208-deployment", "s3Key": "amplify-builds/MrCenLambdaInputFileLoad-4e714738574c2f486f6d-build.zip"}}, "auth": {"centaurapp": {}}, "api": {"MrCenApiGateway": {}}, "custom": {"MrCenAlarmSnsTopic": {}, "MrCenDataUpdateStepFunction": {}, "MrCenDeadLetterQueue": {}, "MrCenDeliveryServiceXmlOutputStorage": {}, "MrCenDeliveryServiceXmlSourceStorage": {}, "MrCenEventBridge": {}, "MrCenFileStorage": {}, "MrCenProcessClientDeliveryServiceWorkflow": {}, "MrCenProcessStatsWorkflow": {}, "ServiceAccounts": {}}, "stepFunction": {"MrCenProcessMeetingWorkflow": {}}}}, "stgblack": {"awscloudformation": {"AuthRoleName": "amplify-centaurapp-stgblack-101801-authRole", "UnauthRoleArn": "arn:aws:iam::************:role/amplify-centaurapp-stgblack-101801-unauthRole", "AuthRoleArn": "arn:aws:iam::************:role/amplify-centaurapp-stgblack-101801-authRole", "Region": "ap-southeast-2", "DeploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "UnauthRoleName": "amplify-centaurapp-stgblack-101801-unauthRole", "StackName": "amplify-centaurapp-stgblack-101801", "StackId": "arn:aws:cloudformation:ap-southeast-2:************:stack/amplify-centaurapp-stgblack-101801/71a42b50-0cd0-11ef-af1b-0a6b6d027f5f", "AmplifyAppId": "d3fh3w2m9jb4wi", "APIGatewayAuthURL": "https://s3.amazonaws.com/amplify-centaurapp-stgblack-101801-deployment/amplify-cfn-templates/api/APIGatewayAuthStack.json"}, "categories": {"auth": {"centaurapp": {}}, "function": {"MRCenFileArchive": {"region": "ap-southeast-2", "deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MRCenFileArchive-724c676862534b757539-build.zip"}, "MRCenLambdaCronSendFormXML": {"deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MRCenLambdaCronSendFormXML-37755546584b6b6e374f-build.zip"}, "MRCenLambdaHorseNameUpdate": {"deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MRCenLambdaHorseNameUpdate-4245783737686e4b6e30-build.zip"}, "MRCenLambdaJockeyUpdate": {"deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MRCenLambdaJockeyUpdate-56634367333043542f74-build.zip"}, "MrCenDocDbScheduler": {"deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenDocDbScheduler-6c702f50665a56396267-build.zip"}, "MrCenLambdaApi": {"centaurSecrets": "*********************************", "awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenLambdaApi-516d7756634f324e426c-build.zip"}, "MrCenLambdaApiAdmin": {"awsLocal": "false", "centaurSecrets": "*********************************", "customerApiGateway": "MrCenApiGatewayPublic", "deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenLambdaApiAdmin-6a435864626d6536546b-build.zip"}, "MrCenLambdaClientDeliveryService": {"deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenLambdaClientDeliveryService-6664464b4d5852686144-build.zip", "centaurSecrets": "*********************************", "centaurSecretsId": "*********************************-95P2aQ"}, "MrCenLambdaComment": {"secretsPathAmplifyAppId": "d3fh3w2m9jb4wi", "centaurSecrets": "*********************************", "awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenLambdaComment-2b304757554830346e6d-build.zip"}, "MrCenLambdaDBcleanup": {"region": "AWS::Region", "deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenLambdaDBcleanup-514c6b53323472733578-build.zip", "env": "env", "awsLocal": "awsLocal", "centaurSecrets": "*********************************"}, "MrCenLambdaDeliveryFunction": {"centaurSecrets": "*********************************", "deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenLambdaDeliveryFunction-5757473076316c303973-build.zip"}, "MrCenLambdaGetDeliveryManifest": {"deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenLambdaGetDeliveryManifest-7a67746b4a4771665739-build.zip", "centaurSecrets": "*********************************", "centaurSecretsId": "*********************************-95P2aQ"}, "MrCenLambdaPollerFunction": {"timeDiff": "*********0", "awsLocal": "false", "mockSftpData": "false", "fileProcessLimit": "15", "deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenLambdaPollerFunction-5958636679626a6f2f4b-build.zip"}, "MrCenLambdaPollerTIS": {"timeDiff": "*********0", "awsLocal": "false", "mockSftpData": "false", "fileProcessLimit": "15", "deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenLambdaPollerTIS-6574746e764567443542-build.zip"}, "MrCenLambdaPostProcessMeeting": {"deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenLambdaPostProcessMeeting-4f414a41596e34712b37-build.zip"}, "MrCenLambdaProcessMeeting": {"centaurSecrets": "*********************************", "awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenLambdaProcessMeeting-2b626379413066413049-build.zip"}, "MrCenLambdaProcessStats": {"deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenLambdaProcessStats-47434f3267633338444d-build.zip"}, "MrCenLambdaStepFunctionExecutor": {"awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenLambdaStepFunctionExecutor-61526e7348576545305a-build.zip"}, "MrManualTriggerUpdate": {"deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrManualTriggerUpdate-6a44774f7a384962772f-build.zip"}, "centaurappCentaurAppCommonLayer": {"deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/centaurappCentaurAppCommonLayer-LambdaLayerVersion37695f53-build.zip"}, "MrCenDeliveryServiceXmlSourceStorage": {}, "MrCenDeliveryServiceXmlOutputStorage": {}, "MRCentCompareMeeting": {"deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MRCentCompareMeeting-46466e7a4a7a38564164-build.zip"}, "MRCenProcessRating": {"deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MRCenProcessRating-712f345a62422f475678-build.zip"}, "MrPegasusLambdaCommentGen": {"secretsPathAmplifyAppId": "d3fh3w2m9jb4wi", "centaurSecrets": "*********************************", "awsLocal": "false", "deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrPegasusLambdaCommentGen-5a4e5638594676396679-build.zip"}, "MrCenEdmGenerate": {"deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenEdmGenerate-316a364b497a34447673-build.zip"}, "MrCenLambdaInputFileLoad": {"deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenLambdaInputFileLoad-4e714738574c2f486f6d-build.zip"}, "MrCenChargebeeAPI": {"deploymentBucketName": "amplify-centaurapp-stgblack-101801-deployment", "s3Key": "amplify-builds/MrCenChargebeeAPI-657a3149396e41314844-build.zip"}}, "api": {"MrCenApiGateway": {}}, "custom": {"MrCenAlarmSnsTopic": {}, "MrCenDataUpdateStepFunction": {}, "MrCenDeadLetterQueue": {}, "MrCenDeliveryServiceXmlOutputStorage": {}, "MrCenDeliveryServiceXmlSourceStorage": {}, "MrCenEventBridge": {}, "MrCenFileStorage": {}, "MrCenProcessClientDeliveryServiceWorkflow": {}, "MrCenProcessStatsWorkflow": {}, "ServiceAccounts": {}}, "stepFunction": {"MrCenProcessMeetingWorkflow": {}}}}}