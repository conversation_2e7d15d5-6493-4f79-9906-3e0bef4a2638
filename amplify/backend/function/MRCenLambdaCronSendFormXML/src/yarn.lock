# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@aws-crypto/crc32@3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/crc32/-/crc32-3.0.0.tgz#07300eca214409c33e3ff769cd5697b57fdd38fa"
  integrity sha512-IzSgsrxUcsrejQbPVilIKy16kAT52EwB6zSaI+M3xxIhKh5+aldEyvI+z6erM7TCLB2BJsFrtHjp6/4/sr+3dA==
  dependencies:
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^1.11.1"

"@aws-crypto/crc32c@3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/crc32c/-/crc32c-3.0.0.tgz#016c92da559ef638a84a245eecb75c3e97cb664f"
  integrity sha512-ENNPPManmnVJ4BTXlOjAgD7URidbAznURqD0KvfREyc4o20DPYdEldU1f5cQ7Jbj0CJJSPaMIk/9ZshdB3210w==
  dependencies:
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^1.11.1"

"@aws-crypto/ie11-detection@^3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/ie11-detection/-/ie11-detection-3.0.0.tgz#640ae66b4ec3395cee6a8e94ebcd9f80c24cd688"
  integrity sha512-341lBBkiY1DfDNKai/wXM3aujNBkXR7tq1URPQDL9wi3AUbI80NR74uF1TXHMm7po1AcnFk8iu2S2IeU/+/A+Q==
  dependencies:
    tslib "^1.11.1"

"@aws-crypto/sha1-browser@3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/sha1-browser/-/sha1-browser-3.0.0.tgz#f9083c00782b24714f528b1a1fef2174002266a3"
  integrity sha512-NJth5c997GLHs6nOYTzFKTbYdMNA6/1XlKVgnZoaZcQ7z7UJlOgj2JdbHE8tiYLS3fzXNCguct77SPGat2raSw==
  dependencies:
    "@aws-crypto/ie11-detection" "^3.0.0"
    "@aws-crypto/supports-web-crypto" "^3.0.0"
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-locate-window" "^3.0.0"
    "@aws-sdk/util-utf8-browser" "^3.0.0"
    tslib "^1.11.1"

"@aws-crypto/sha256-browser@3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/sha256-browser/-/sha256-browser-3.0.0.tgz#05f160138ab893f1c6ba5be57cfd108f05827766"
  integrity sha512-8VLmW2B+gjFbU5uMeqtQM6Nj0/F1bro80xQXCW6CQBWgosFWXTx77aeOF5CAIAmbOK64SdMBJdNr6J41yP5mvQ==
  dependencies:
    "@aws-crypto/ie11-detection" "^3.0.0"
    "@aws-crypto/sha256-js" "^3.0.0"
    "@aws-crypto/supports-web-crypto" "^3.0.0"
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-locate-window" "^3.0.0"
    "@aws-sdk/util-utf8-browser" "^3.0.0"
    tslib "^1.11.1"

"@aws-crypto/sha256-js@3.0.0", "@aws-crypto/sha256-js@^3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/sha256-js/-/sha256-js-3.0.0.tgz#f06b84d550d25521e60d2a0e2a90139341e007c2"
  integrity sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==
  dependencies:
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^1.11.1"

"@aws-crypto/supports-web-crypto@^3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/supports-web-crypto/-/supports-web-crypto-3.0.0.tgz#5d1bf825afa8072af2717c3e455f35cda0103ec2"
  integrity sha512-06hBdMwUAb2WFTuGG73LSC0wfPu93xWwo5vL2et9eymgmu3Id5vFAHBbajVWiGhPO37qcsdCap/FqXvJGJWPIg==
  dependencies:
    tslib "^1.11.1"

"@aws-crypto/util@^3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/util/-/util-3.0.0.tgz#1c7ca90c29293f0883468ad48117937f0fe5bfb0"
  integrity sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==
  dependencies:
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-utf8-browser" "^3.0.0"
    tslib "^1.11.1"

"@aws-lambda-powertools/commons@^1.18.1":
  version "1.18.1"
  resolved "https://registry.yarnpkg.com/@aws-lambda-powertools/commons/-/commons-1.18.1.tgz#fcfdef39639105a7b2b5363e4bcade9d277f5468"
  integrity sha512-gFRgQ2GJDghKvf+fXvT0kQVftgOT05W+hCa7RkfZj6HSjVAO+9DZZeJL3JK1HcsLAjWRj7W9ra0/MqB3Abf+PQ==

"@aws-lambda-powertools/logger@^1.5.1":
  version "1.18.1"
  resolved "https://registry.yarnpkg.com/@aws-lambda-powertools/logger/-/logger-1.18.1.tgz#6388ddbafca6b3f65277b7a364df45f426f5c592"
  integrity sha512-GsSMqaFXCSz+llSOn2CVNMoN+j/jNsS6JP2Opy9myU0tvg7PeuU3+rN24vKyibUwpxM466IzWFBSJkYdm0bqVw==
  dependencies:
    "@aws-lambda-powertools/commons" "^1.18.1"
    lodash.merge "^4.6.2"

"@aws-lambda-powertools/tracer@^1.5.1":
  version "1.18.1"
  resolved "https://registry.yarnpkg.com/@aws-lambda-powertools/tracer/-/tracer-1.18.1.tgz#9a6c618abb195d0e2cc25b2cd36469f9d63317bd"
  integrity sha512-bMLBtdEFNmLUR9RJvBULR6XJD0XopUhhS1mlpeQlm2BCPIN3gLbqAlJK8dMXyAw8GCpLpHaziCo2+7a/AIh7lA==
  dependencies:
    "@aws-lambda-powertools/commons" "^1.18.1"
    aws-xray-sdk-core "^3.5.3"

"@aws-sdk/client-api-gateway@^3.54.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-api-gateway/-/client-api-gateway-3.521.0.tgz#711e8b87d4a50c89afc2d2c710c5971fb1e258b5"
  integrity sha512-CROHQtjyR+Rp5yzjqbtlPvYYsfMCHZtiP5qsjeg1flxDLXhBNZiviSwnx9QI37NekLfgr3ZrtRvpsVPOwUxv2A==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/client-sts" "3.521.0"
    "@aws-sdk/core" "3.521.0"
    "@aws-sdk/credential-provider-node" "3.521.0"
    "@aws-sdk/middleware-host-header" "3.521.0"
    "@aws-sdk/middleware-logger" "3.521.0"
    "@aws-sdk/middleware-recursion-detection" "3.521.0"
    "@aws-sdk/middleware-sdk-api-gateway" "3.521.0"
    "@aws-sdk/middleware-user-agent" "3.521.0"
    "@aws-sdk/region-config-resolver" "3.521.0"
    "@aws-sdk/types" "3.521.0"
    "@aws-sdk/util-endpoints" "3.521.0"
    "@aws-sdk/util-user-agent-browser" "3.521.0"
    "@aws-sdk/util-user-agent-node" "3.521.0"
    "@smithy/config-resolver" "^2.1.2"
    "@smithy/core" "^1.3.3"
    "@smithy/fetch-http-handler" "^2.4.2"
    "@smithy/hash-node" "^2.1.2"
    "@smithy/invalid-dependency" "^2.1.2"
    "@smithy/middleware-content-length" "^2.1.2"
    "@smithy/middleware-endpoint" "^2.4.2"
    "@smithy/middleware-retry" "^2.1.2"
    "@smithy/middleware-serde" "^2.1.2"
    "@smithy/middleware-stack" "^2.1.2"
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/node-http-handler" "^2.4.0"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/smithy-client" "^2.4.0"
    "@smithy/types" "^2.10.0"
    "@smithy/url-parser" "^2.1.2"
    "@smithy/util-base64" "^2.1.1"
    "@smithy/util-body-length-browser" "^2.1.1"
    "@smithy/util-body-length-node" "^2.2.1"
    "@smithy/util-defaults-mode-browser" "^2.1.2"
    "@smithy/util-defaults-mode-node" "^2.2.1"
    "@smithy/util-endpoints" "^1.1.2"
    "@smithy/util-middleware" "^2.1.2"
    "@smithy/util-retry" "^2.1.2"
    "@smithy/util-stream" "^2.1.2"
    "@smithy/util-utf8" "^2.1.1"
    tslib "^2.5.0"

"@aws-sdk/client-cognito-identity@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-cognito-identity/-/client-cognito-identity-3.521.0.tgz#cf1995e302fcdc903b7449c91b60b64c14b3f799"
  integrity sha512-UomYWcCpM7OZUt1BDlY3guO6mnA4VXzMkNjFbVtWibKQkk4LhcIUXb6SxWSw/gujIrlOZywldjyj8bL6V374IQ==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/client-sts" "3.521.0"
    "@aws-sdk/core" "3.521.0"
    "@aws-sdk/credential-provider-node" "3.521.0"
    "@aws-sdk/middleware-host-header" "3.521.0"
    "@aws-sdk/middleware-logger" "3.521.0"
    "@aws-sdk/middleware-recursion-detection" "3.521.0"
    "@aws-sdk/middleware-user-agent" "3.521.0"
    "@aws-sdk/region-config-resolver" "3.521.0"
    "@aws-sdk/types" "3.521.0"
    "@aws-sdk/util-endpoints" "3.521.0"
    "@aws-sdk/util-user-agent-browser" "3.521.0"
    "@aws-sdk/util-user-agent-node" "3.521.0"
    "@smithy/config-resolver" "^2.1.2"
    "@smithy/core" "^1.3.3"
    "@smithy/fetch-http-handler" "^2.4.2"
    "@smithy/hash-node" "^2.1.2"
    "@smithy/invalid-dependency" "^2.1.2"
    "@smithy/middleware-content-length" "^2.1.2"
    "@smithy/middleware-endpoint" "^2.4.2"
    "@smithy/middleware-retry" "^2.1.2"
    "@smithy/middleware-serde" "^2.1.2"
    "@smithy/middleware-stack" "^2.1.2"
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/node-http-handler" "^2.4.0"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/smithy-client" "^2.4.0"
    "@smithy/types" "^2.10.0"
    "@smithy/url-parser" "^2.1.2"
    "@smithy/util-base64" "^2.1.1"
    "@smithy/util-body-length-browser" "^2.1.1"
    "@smithy/util-body-length-node" "^2.2.1"
    "@smithy/util-defaults-mode-browser" "^2.1.2"
    "@smithy/util-defaults-mode-node" "^2.2.1"
    "@smithy/util-endpoints" "^1.1.2"
    "@smithy/util-middleware" "^2.1.2"
    "@smithy/util-retry" "^2.1.2"
    "@smithy/util-utf8" "^2.1.1"
    tslib "^2.5.0"

"@aws-sdk/client-s3@^3.282.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-s3/-/client-s3-3.521.0.tgz#a8ff7a5bd5b07903885b0ecd4df15da9f24aac4f"
  integrity sha512-txSfcxezAIW72dgRfhX+plc/lMouilY/QFVne/Cv01SL8Tzclcyp7T7LtkV7aSO4Tb9CUScHdqwWOfjZzCm/yQ==
  dependencies:
    "@aws-crypto/sha1-browser" "3.0.0"
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/client-sts" "3.521.0"
    "@aws-sdk/core" "3.521.0"
    "@aws-sdk/credential-provider-node" "3.521.0"
    "@aws-sdk/middleware-bucket-endpoint" "3.521.0"
    "@aws-sdk/middleware-expect-continue" "3.521.0"
    "@aws-sdk/middleware-flexible-checksums" "3.521.0"
    "@aws-sdk/middleware-host-header" "3.521.0"
    "@aws-sdk/middleware-location-constraint" "3.521.0"
    "@aws-sdk/middleware-logger" "3.521.0"
    "@aws-sdk/middleware-recursion-detection" "3.521.0"
    "@aws-sdk/middleware-sdk-s3" "3.521.0"
    "@aws-sdk/middleware-signing" "3.521.0"
    "@aws-sdk/middleware-ssec" "3.521.0"
    "@aws-sdk/middleware-user-agent" "3.521.0"
    "@aws-sdk/region-config-resolver" "3.521.0"
    "@aws-sdk/signature-v4-multi-region" "3.521.0"
    "@aws-sdk/types" "3.521.0"
    "@aws-sdk/util-endpoints" "3.521.0"
    "@aws-sdk/util-user-agent-browser" "3.521.0"
    "@aws-sdk/util-user-agent-node" "3.521.0"
    "@aws-sdk/xml-builder" "3.521.0"
    "@smithy/config-resolver" "^2.1.2"
    "@smithy/core" "^1.3.3"
    "@smithy/eventstream-serde-browser" "^2.1.2"
    "@smithy/eventstream-serde-config-resolver" "^2.1.2"
    "@smithy/eventstream-serde-node" "^2.1.2"
    "@smithy/fetch-http-handler" "^2.4.2"
    "@smithy/hash-blob-browser" "^2.1.2"
    "@smithy/hash-node" "^2.1.2"
    "@smithy/hash-stream-node" "^2.1.2"
    "@smithy/invalid-dependency" "^2.1.2"
    "@smithy/md5-js" "^2.1.2"
    "@smithy/middleware-content-length" "^2.1.2"
    "@smithy/middleware-endpoint" "^2.4.2"
    "@smithy/middleware-retry" "^2.1.2"
    "@smithy/middleware-serde" "^2.1.2"
    "@smithy/middleware-stack" "^2.1.2"
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/node-http-handler" "^2.4.0"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/smithy-client" "^2.4.0"
    "@smithy/types" "^2.10.0"
    "@smithy/url-parser" "^2.1.2"
    "@smithy/util-base64" "^2.1.1"
    "@smithy/util-body-length-browser" "^2.1.1"
    "@smithy/util-body-length-node" "^2.2.1"
    "@smithy/util-defaults-mode-browser" "^2.1.2"
    "@smithy/util-defaults-mode-node" "^2.2.1"
    "@smithy/util-endpoints" "^1.1.2"
    "@smithy/util-retry" "^2.1.2"
    "@smithy/util-stream" "^2.1.2"
    "@smithy/util-utf8" "^2.1.1"
    "@smithy/util-waiter" "^2.1.2"
    fast-xml-parser "4.2.5"
    tslib "^2.5.0"

"@aws-sdk/client-secrets-manager@^3.282.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-secrets-manager/-/client-secrets-manager-3.521.0.tgz#e3965b2569950bc2cb187037451cd0ac80292b52"
  integrity sha512-C01O7Ep06lL3pEhvsEwLjCHTt4ARA5NmCqQlVg/NVPkWTA3RX/GgvaqKu+HMFeTRoipTdOvdril+weBLIWFKAg==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/client-sts" "3.521.0"
    "@aws-sdk/core" "3.521.0"
    "@aws-sdk/credential-provider-node" "3.521.0"
    "@aws-sdk/middleware-host-header" "3.521.0"
    "@aws-sdk/middleware-logger" "3.521.0"
    "@aws-sdk/middleware-recursion-detection" "3.521.0"
    "@aws-sdk/middleware-user-agent" "3.521.0"
    "@aws-sdk/region-config-resolver" "3.521.0"
    "@aws-sdk/types" "3.521.0"
    "@aws-sdk/util-endpoints" "3.521.0"
    "@aws-sdk/util-user-agent-browser" "3.521.0"
    "@aws-sdk/util-user-agent-node" "3.521.0"
    "@smithy/config-resolver" "^2.1.2"
    "@smithy/core" "^1.3.3"
    "@smithy/fetch-http-handler" "^2.4.2"
    "@smithy/hash-node" "^2.1.2"
    "@smithy/invalid-dependency" "^2.1.2"
    "@smithy/middleware-content-length" "^2.1.2"
    "@smithy/middleware-endpoint" "^2.4.2"
    "@smithy/middleware-retry" "^2.1.2"
    "@smithy/middleware-serde" "^2.1.2"
    "@smithy/middleware-stack" "^2.1.2"
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/node-http-handler" "^2.4.0"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/smithy-client" "^2.4.0"
    "@smithy/types" "^2.10.0"
    "@smithy/url-parser" "^2.1.2"
    "@smithy/util-base64" "^2.1.1"
    "@smithy/util-body-length-browser" "^2.1.1"
    "@smithy/util-body-length-node" "^2.2.1"
    "@smithy/util-defaults-mode-browser" "^2.1.2"
    "@smithy/util-defaults-mode-node" "^2.2.1"
    "@smithy/util-endpoints" "^1.1.2"
    "@smithy/util-middleware" "^2.1.2"
    "@smithy/util-retry" "^2.1.2"
    "@smithy/util-utf8" "^2.1.1"
    tslib "^2.5.0"
    uuid "^9.0.1"

"@aws-sdk/client-sso-oidc@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-sso-oidc/-/client-sso-oidc-3.521.0.tgz#455cf62ccc0bba8fabd00f0b540cd9e51a24cd93"
  integrity sha512-MhX0CjV/543MR7DRPr3lA4ZDpGGKopp8cyV4EkSGXB7LMN//eFKKDhuZDlpgWU+aFe2A3DIqlNJjqgs08W0cSA==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/client-sts" "3.521.0"
    "@aws-sdk/core" "3.521.0"
    "@aws-sdk/middleware-host-header" "3.521.0"
    "@aws-sdk/middleware-logger" "3.521.0"
    "@aws-sdk/middleware-recursion-detection" "3.521.0"
    "@aws-sdk/middleware-user-agent" "3.521.0"
    "@aws-sdk/region-config-resolver" "3.521.0"
    "@aws-sdk/types" "3.521.0"
    "@aws-sdk/util-endpoints" "3.521.0"
    "@aws-sdk/util-user-agent-browser" "3.521.0"
    "@aws-sdk/util-user-agent-node" "3.521.0"
    "@smithy/config-resolver" "^2.1.2"
    "@smithy/core" "^1.3.3"
    "@smithy/fetch-http-handler" "^2.4.2"
    "@smithy/hash-node" "^2.1.2"
    "@smithy/invalid-dependency" "^2.1.2"
    "@smithy/middleware-content-length" "^2.1.2"
    "@smithy/middleware-endpoint" "^2.4.2"
    "@smithy/middleware-retry" "^2.1.2"
    "@smithy/middleware-serde" "^2.1.2"
    "@smithy/middleware-stack" "^2.1.2"
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/node-http-handler" "^2.4.0"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/smithy-client" "^2.4.0"
    "@smithy/types" "^2.10.0"
    "@smithy/url-parser" "^2.1.2"
    "@smithy/util-base64" "^2.1.1"
    "@smithy/util-body-length-browser" "^2.1.1"
    "@smithy/util-body-length-node" "^2.2.1"
    "@smithy/util-defaults-mode-browser" "^2.1.2"
    "@smithy/util-defaults-mode-node" "^2.2.1"
    "@smithy/util-endpoints" "^1.1.2"
    "@smithy/util-middleware" "^2.1.2"
    "@smithy/util-retry" "^2.1.2"
    "@smithy/util-utf8" "^2.1.1"
    tslib "^2.5.0"

"@aws-sdk/client-sso@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-sso/-/client-sso-3.521.0.tgz#b28fd6a974f4c6ddca6151df0b7954bbf72dd6d3"
  integrity sha512-aEx8kEvWmTwCja6hvIZd5PvxHsI1HQZkckXhw1UrkDPnfcAwQoQAgselI7D+PVT5qQDIjXRm0NpsvBLaLj6jZw==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/core" "3.521.0"
    "@aws-sdk/middleware-host-header" "3.521.0"
    "@aws-sdk/middleware-logger" "3.521.0"
    "@aws-sdk/middleware-recursion-detection" "3.521.0"
    "@aws-sdk/middleware-user-agent" "3.521.0"
    "@aws-sdk/region-config-resolver" "3.521.0"
    "@aws-sdk/types" "3.521.0"
    "@aws-sdk/util-endpoints" "3.521.0"
    "@aws-sdk/util-user-agent-browser" "3.521.0"
    "@aws-sdk/util-user-agent-node" "3.521.0"
    "@smithy/config-resolver" "^2.1.2"
    "@smithy/core" "^1.3.3"
    "@smithy/fetch-http-handler" "^2.4.2"
    "@smithy/hash-node" "^2.1.2"
    "@smithy/invalid-dependency" "^2.1.2"
    "@smithy/middleware-content-length" "^2.1.2"
    "@smithy/middleware-endpoint" "^2.4.2"
    "@smithy/middleware-retry" "^2.1.2"
    "@smithy/middleware-serde" "^2.1.2"
    "@smithy/middleware-stack" "^2.1.2"
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/node-http-handler" "^2.4.0"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/smithy-client" "^2.4.0"
    "@smithy/types" "^2.10.0"
    "@smithy/url-parser" "^2.1.2"
    "@smithy/util-base64" "^2.1.1"
    "@smithy/util-body-length-browser" "^2.1.1"
    "@smithy/util-body-length-node" "^2.2.1"
    "@smithy/util-defaults-mode-browser" "^2.1.2"
    "@smithy/util-defaults-mode-node" "^2.2.1"
    "@smithy/util-endpoints" "^1.1.2"
    "@smithy/util-middleware" "^2.1.2"
    "@smithy/util-retry" "^2.1.2"
    "@smithy/util-utf8" "^2.1.1"
    tslib "^2.5.0"

"@aws-sdk/client-sts@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-sts/-/client-sts-3.521.0.tgz#d58a2b3c6b0b16c487e41fdcd41df43ec8b56fad"
  integrity sha512-f1J5NDbntcwIHJqhks89sQvk7UXPmN0X0BZ2mgpj6pWP+NlPqy+1t1bia8qRhEuNITaEigoq6rqe9xaf4FdY9A==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/core" "3.521.0"
    "@aws-sdk/middleware-host-header" "3.521.0"
    "@aws-sdk/middleware-logger" "3.521.0"
    "@aws-sdk/middleware-recursion-detection" "3.521.0"
    "@aws-sdk/middleware-user-agent" "3.521.0"
    "@aws-sdk/region-config-resolver" "3.521.0"
    "@aws-sdk/types" "3.521.0"
    "@aws-sdk/util-endpoints" "3.521.0"
    "@aws-sdk/util-user-agent-browser" "3.521.0"
    "@aws-sdk/util-user-agent-node" "3.521.0"
    "@smithy/config-resolver" "^2.1.2"
    "@smithy/core" "^1.3.3"
    "@smithy/fetch-http-handler" "^2.4.2"
    "@smithy/hash-node" "^2.1.2"
    "@smithy/invalid-dependency" "^2.1.2"
    "@smithy/middleware-content-length" "^2.1.2"
    "@smithy/middleware-endpoint" "^2.4.2"
    "@smithy/middleware-retry" "^2.1.2"
    "@smithy/middleware-serde" "^2.1.2"
    "@smithy/middleware-stack" "^2.1.2"
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/node-http-handler" "^2.4.0"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/smithy-client" "^2.4.0"
    "@smithy/types" "^2.10.0"
    "@smithy/url-parser" "^2.1.2"
    "@smithy/util-base64" "^2.1.1"
    "@smithy/util-body-length-browser" "^2.1.1"
    "@smithy/util-body-length-node" "^2.2.1"
    "@smithy/util-defaults-mode-browser" "^2.1.2"
    "@smithy/util-defaults-mode-node" "^2.2.1"
    "@smithy/util-endpoints" "^1.1.2"
    "@smithy/util-middleware" "^2.1.2"
    "@smithy/util-retry" "^2.1.2"
    "@smithy/util-utf8" "^2.1.1"
    fast-xml-parser "4.2.5"
    tslib "^2.5.0"

"@aws-sdk/core@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/core/-/core-3.521.0.tgz#56aaed5714a5145055983f08362c2dfeaf275769"
  integrity sha512-KovKmW7yg/P2HVG2dhV2DAJLyoeGelgsnSGHaktXo/josJ3vDGRNqqRSgVaqKFxnD98dPEMLrjkzZumNUNGvLw==
  dependencies:
    "@smithy/core" "^1.3.3"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/signature-v4" "^2.1.1"
    "@smithy/smithy-client" "^2.4.0"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-cognito-identity@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-cognito-identity/-/credential-provider-cognito-identity-3.521.0.tgz#af548bb3e74cb91cdf4777f7180f99ac67fdb0db"
  integrity sha512-HsLKT0MOQ1/3qM2smxgafuf7B9sbie/gsKEgQi9De7UhA8N9yGaXdo3HQFbyRbv4eZ0fj9Ja++UgFypUk4c3Kw==
  dependencies:
    "@aws-sdk/client-cognito-identity" "3.521.0"
    "@aws-sdk/types" "3.521.0"
    "@smithy/property-provider" "^2.1.1"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-env@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-env/-/credential-provider-env-3.521.0.tgz#abef98938e0013d4dcc34a546c50e1fd5593a9ca"
  integrity sha512-OwblTJNdDAoqYVwcNfhlKDp5z+DINrjBfC6ZjNdlJpTXgxT3IqzuilTJTlydQ+2eG7aXfV9OwTVRQWdCmzFuKA==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@smithy/property-provider" "^2.1.1"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-http@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-http/-/credential-provider-http-3.521.0.tgz#a189f2ced504bccedbe57cb911f64a8c1bb77b3c"
  integrity sha512-yJM1yNGj2XFH8v6/ffWrFY5nC3/2+8qZ8c4mMMwZru8bYXeuSV4+NNfE59HUWvkAF7xP76u4gr4I8kNrMPTlfg==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@smithy/fetch-http-handler" "^2.4.2"
    "@smithy/node-http-handler" "^2.4.0"
    "@smithy/property-provider" "^2.1.1"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/smithy-client" "^2.4.0"
    "@smithy/types" "^2.10.0"
    "@smithy/util-stream" "^2.1.2"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-ini@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-ini/-/credential-provider-ini-3.521.0.tgz#936201cc56ccc50a5a412f97f3a0867e3017d477"
  integrity sha512-HuhP1AlKgvBBxUIwxL/2DsDemiuwgbz1APUNSeJhDBF6JyZuxR0NU8zEZkvH9b4ukTcmcKGABpY0Wex4rAh3xw==
  dependencies:
    "@aws-sdk/client-sts" "3.521.0"
    "@aws-sdk/credential-provider-env" "3.521.0"
    "@aws-sdk/credential-provider-process" "3.521.0"
    "@aws-sdk/credential-provider-sso" "3.521.0"
    "@aws-sdk/credential-provider-web-identity" "3.521.0"
    "@aws-sdk/types" "3.521.0"
    "@smithy/credential-provider-imds" "^2.2.1"
    "@smithy/property-provider" "^2.1.1"
    "@smithy/shared-ini-file-loader" "^2.3.1"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-node@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-node/-/credential-provider-node-3.521.0.tgz#b999f382242a5b2ea5b35025f9a7e3b1c0ab6892"
  integrity sha512-N9SR4gWI10qh4V2myBcTw8IlX3QpsMMxa4Q8d/FHiAX6eNV7e6irXkXX8o7+J1gtCRy1AtBMqAdGsve4GVqYMQ==
  dependencies:
    "@aws-sdk/credential-provider-env" "3.521.0"
    "@aws-sdk/credential-provider-http" "3.521.0"
    "@aws-sdk/credential-provider-ini" "3.521.0"
    "@aws-sdk/credential-provider-process" "3.521.0"
    "@aws-sdk/credential-provider-sso" "3.521.0"
    "@aws-sdk/credential-provider-web-identity" "3.521.0"
    "@aws-sdk/types" "3.521.0"
    "@smithy/credential-provider-imds" "^2.2.1"
    "@smithy/property-provider" "^2.1.1"
    "@smithy/shared-ini-file-loader" "^2.3.1"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-process@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-process/-/credential-provider-process-3.521.0.tgz#8d163862607bd6ef3ac289ae89b4c7cf2e2f994a"
  integrity sha512-EcJjcrpdklxbRAFFgSLk6QGVtvnfZ80ItfZ47VL9LkhWcDAkQ1Oi0esHq+zOgvjb7VkCyD3Q9CyEwT6MlJsriA==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@smithy/property-provider" "^2.1.1"
    "@smithy/shared-ini-file-loader" "^2.3.1"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-sso@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-sso/-/credential-provider-sso-3.521.0.tgz#d4baf025c60d92dd4f3a27bbfaa83e4289010fcd"
  integrity sha512-GAfc0ji+fC2k9VngYM3zsS1J5ojfWg0WUOBzavvHzkhx/O3CqOt82Vfikg3PvemAp9yOgKPMaasTHVeipNLBBQ==
  dependencies:
    "@aws-sdk/client-sso" "3.521.0"
    "@aws-sdk/token-providers" "3.521.0"
    "@aws-sdk/types" "3.521.0"
    "@smithy/property-provider" "^2.1.1"
    "@smithy/shared-ini-file-loader" "^2.3.1"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-web-identity@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-web-identity/-/credential-provider-web-identity-3.521.0.tgz#a062dead8d50df1601c08d4925628d89584920b8"
  integrity sha512-ZPPJqdbPOE4BkdrPrYBtsWg0Zy5b+GY1sbMWLQt0tcISgN5EIoePCS2pGNWnBUmBT+mibMQCVv9fOQpqzRkvAw==
  dependencies:
    "@aws-sdk/client-sts" "3.521.0"
    "@aws-sdk/types" "3.521.0"
    "@smithy/property-provider" "^2.1.1"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/credential-providers@^3.186.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-providers/-/credential-providers-3.521.0.tgz#3cd74c5467d4599cc14d063919b9184b1e62db0a"
  integrity sha512-PYd93rIF99TtRYwFCKr/3G/eEMjQzEVFuX3lUoKWrNgDCd+Jeor/ol4HlDoeiSX/Y37HcFnvAFCKJwDGHOPsLw==
  dependencies:
    "@aws-sdk/client-cognito-identity" "3.521.0"
    "@aws-sdk/client-sso" "3.521.0"
    "@aws-sdk/client-sts" "3.521.0"
    "@aws-sdk/credential-provider-cognito-identity" "3.521.0"
    "@aws-sdk/credential-provider-env" "3.521.0"
    "@aws-sdk/credential-provider-http" "3.521.0"
    "@aws-sdk/credential-provider-ini" "3.521.0"
    "@aws-sdk/credential-provider-node" "3.521.0"
    "@aws-sdk/credential-provider-process" "3.521.0"
    "@aws-sdk/credential-provider-sso" "3.521.0"
    "@aws-sdk/credential-provider-web-identity" "3.521.0"
    "@aws-sdk/types" "3.521.0"
    "@smithy/credential-provider-imds" "^2.2.1"
    "@smithy/property-provider" "^2.1.1"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-bucket-endpoint@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-bucket-endpoint/-/middleware-bucket-endpoint-3.521.0.tgz#5d71cd7a73fbab1eac933d79194150b14a85ab39"
  integrity sha512-wUPSpzeEGwAic5OJmXQGt1RCbt5KHighZ1ubUeNV67FMPsxaEW+Y0Kd+L0vbbFoQptIui2GqP5JxuROr6J7SjA==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@aws-sdk/util-arn-parser" "3.495.0"
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/types" "^2.10.0"
    "@smithy/util-config-provider" "^2.2.1"
    tslib "^2.5.0"

"@aws-sdk/middleware-expect-continue@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-expect-continue/-/middleware-expect-continue-3.521.0.tgz#22845df7ea4940f836c439e2dbc14c6e055cf343"
  integrity sha512-6NBaPS+1b1QbsbJ74KI9MkqWbj8rnY6uKNEo0wkxgA8Q6u0aTn/jV+jrn5ZemdYmfS/y/VbaoY/hE+/QNp5vUw==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-flexible-checksums@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-flexible-checksums/-/middleware-flexible-checksums-3.521.0.tgz#375cf8332876dfa83069a2a91c61524db9b0bf88"
  integrity sha512-sWNN0wtdwImO2QqN4J1YVTpDhdii6Tp5p8jCkCE1Qe+afQ5u52PeRAS/9U56cJnqM5JLabO4kE10Mm5rufNs2A==
  dependencies:
    "@aws-crypto/crc32" "3.0.0"
    "@aws-crypto/crc32c" "3.0.0"
    "@aws-sdk/types" "3.521.0"
    "@smithy/is-array-buffer" "^2.1.1"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/types" "^2.10.0"
    "@smithy/util-utf8" "^2.1.1"
    tslib "^2.5.0"

"@aws-sdk/middleware-host-header@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-host-header/-/middleware-host-header-3.521.0.tgz#d826a4803c1479935cbc9b05e2399895497e55a1"
  integrity sha512-Bc4stnMtVAdqosYI1wedFK9tffclCuwpOK/JA4bxbnvSyP1kz4s1HBVT9OOMzdLRLWLwVj/RslXKfSbzOUP7ug==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-location-constraint@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-location-constraint/-/middleware-location-constraint-3.521.0.tgz#bf9446bc8652a25176757123be4864e78bcd9e05"
  integrity sha512-XlGst6F3+20mhMVk+te7w8Yvrm9i9JGpgRdxdMN1pnXtGn/aAKF9lFFm4bOu47PR/XHun2PLmKlLnlZd7NAP2Q==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-logger@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-logger/-/middleware-logger-3.521.0.tgz#499d93a1b74dc4f37c508567aff9290449c730bf"
  integrity sha512-JJ4nyYvLu3RyyNHo74Rlx6WKxJsAixWCEnnFb6IGRUHvsG+xBGU7HF5koY2log8BqlDLrt4ZUaV/CGy5Dp8Mfg==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-recursion-detection@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.521.0.tgz#77e2917e8b7040b8f3dacea3f29a65f885c69f98"
  integrity sha512-1m5AsC55liTlaYMjc4pIQfjfBHG9LpWgubSl4uUxJSdI++zdA/SRBwXl40p7Ac/y5esweluhWabyiv1g/W4+Xg==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-sdk-api-gateway@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-sdk-api-gateway/-/middleware-sdk-api-gateway-3.521.0.tgz#01e01e8e9968619832c3c0b5056b6d5a2a629f94"
  integrity sha512-mqn7Ygfa53ui49fOHGsKimpeh1EuznphgP1cXDap4VGMovkWtMMk8bvS50UajMzTpkWBhaNMzUdvOQPZIFQWnQ==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-sdk-s3@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-sdk-s3/-/middleware-sdk-s3-3.521.0.tgz#ccf020ba7a8a2bbc1527fc672e9d02c6915e40f2"
  integrity sha512-aDeOScfzGGHZ7oEDx+EPzz+JVa8/B88CPeDRaDmO5dFNv2/5PFumHfh0gc6XFl4nJWPPOrJyZ1UYU/9VdDfSyQ==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@aws-sdk/util-arn-parser" "3.495.0"
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/signature-v4" "^2.1.1"
    "@smithy/smithy-client" "^2.4.0"
    "@smithy/types" "^2.10.0"
    "@smithy/util-config-provider" "^2.2.1"
    tslib "^2.5.0"

"@aws-sdk/middleware-signing@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-signing/-/middleware-signing-3.521.0.tgz#87267770454f66d3ea46d12a3cb71b0131b699fa"
  integrity sha512-OW1jKeN6Eh3/OItXBtyNRFOv1MuZQBeHpEbywgYwtaqxTGxm9gFj//9wFsCXK4zg1+ghun8iC0buNbyOvCUf9A==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@smithy/property-provider" "^2.1.1"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/signature-v4" "^2.1.1"
    "@smithy/types" "^2.10.0"
    "@smithy/util-middleware" "^2.1.2"
    tslib "^2.5.0"

"@aws-sdk/middleware-ssec@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-ssec/-/middleware-ssec-3.521.0.tgz#5d1e494d04c9c479ece7673ac874ff90d3ba87f1"
  integrity sha512-O9vlns8bFxkZA71CyjQbiB2tm3v+925C37Z3wzn9sj2x0FTB3njgSR23w05d8HP2ve1GPuqoVD0T0pa+jG0Zbw==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-user-agent@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.521.0.tgz#c2362f97394143d86ba9f5ab9f929d337b18c5ce"
  integrity sha512-+hmQjWDG93wCcJn5QY2MkzAL1aG5wl3FJ/ud2nQOu/Gx7d4QVT/B6VJwoG6GSPVuVPZwzne5n9zPVst6RmWJGA==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@aws-sdk/util-endpoints" "3.521.0"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/region-config-resolver@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/region-config-resolver/-/region-config-resolver-3.521.0.tgz#a8313f9d7e2df55662418cfb8a04fd055624cb29"
  integrity sha512-eC2T62nFgQva9Q0Sqoc9xsYyyH9EN2rJtmUKkWsBMf77atpmajAYRl5B/DzLwGHlXGsgVK2tJdU5wnmpQCEwEQ==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/types" "^2.10.0"
    "@smithy/util-config-provider" "^2.2.1"
    "@smithy/util-middleware" "^2.1.2"
    tslib "^2.5.0"

"@aws-sdk/signature-v4-multi-region@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/signature-v4-multi-region/-/signature-v4-multi-region-3.521.0.tgz#74f74de15cc4dc94a42c469dd70c7ca29a69749b"
  integrity sha512-JVMGQEE6+MQ5Enc/NDQNw8cmy/soALH/Ky00SVQvrfb9ec4H40eDQbbn/d7lua52UCcvUv1w+Ppk00WzbqDAcQ==
  dependencies:
    "@aws-sdk/middleware-sdk-s3" "3.521.0"
    "@aws-sdk/types" "3.521.0"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/signature-v4" "^2.1.1"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/token-providers@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/token-providers/-/token-providers-3.521.0.tgz#557fa6e5535dc680c8589cca611ac2bd4426a9dd"
  integrity sha512-63XxPOn13j87yPWKm6UXOPdMZIMyEyCDJzmlxnIACP8m20S/c6b8xLJ4fE/PUlD0MTKxpFeQbandq5OhnLsWSQ==
  dependencies:
    "@aws-sdk/client-sso-oidc" "3.521.0"
    "@aws-sdk/types" "3.521.0"
    "@smithy/property-provider" "^2.1.1"
    "@smithy/shared-ini-file-loader" "^2.3.1"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/types@3.521.0", "@aws-sdk/types@^3.222.0", "@aws-sdk/types@^3.4.1":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/types/-/types-3.521.0.tgz#63696760837a1f505b6ef49a668bbff8c827dd2d"
  integrity sha512-H9I3Lut0F9d+kTibrhnTRqDRzhxf/vrDu12FUdTXVZEvVAQ7w9yrVHAZx8j2e8GWegetsQsNitO3KMrj4dA4pw==
  dependencies:
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/util-arn-parser@3.495.0":
  version "3.495.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-arn-parser/-/util-arn-parser-3.495.0.tgz#539f2d6dfef343a80324348f1f9a1b7eed2390f3"
  integrity sha512-hwdA3XAippSEUxs7jpznwD63YYFR+LtQvlEcebPTgWR9oQgG9TfS+39PUfbnEeje1ICuOrN3lrFqFbmP9uzbMg==
  dependencies:
    tslib "^2.5.0"

"@aws-sdk/util-endpoints@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-endpoints/-/util-endpoints-3.521.0.tgz#607edd5429ed971ad4d3a0331d335f430a23d555"
  integrity sha512-lO5+1LeAZycDqgNjQyZdPSdXFQKXaW5bRuQ3UIT3bOCcUAbDI0BYXlPm1huPNTCEkI9ItnDCbISbV0uF901VXw==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@smithy/types" "^2.10.0"
    "@smithy/util-endpoints" "^1.1.2"
    tslib "^2.5.0"

"@aws-sdk/util-locate-window@^3.0.0":
  version "3.495.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-locate-window/-/util-locate-window-3.495.0.tgz#9034fd8db77991b28ed20e067acdd53e8b8f824b"
  integrity sha512-MfaPXT0kLX2tQaR90saBT9fWQq2DHqSSJRzW+MZWsmF+y5LGCOhO22ac/2o6TKSQm7h0HRc2GaADqYYYor62yg==
  dependencies:
    tslib "^2.5.0"

"@aws-sdk/util-user-agent-browser@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.521.0.tgz#20f10df57a5499ace0b955b7b76dccebb530bf1f"
  integrity sha512-2t3uW6AXOvJ5iiI1JG9zPqKQDc/TRFa+v13aqT5KKw9h3WHFyRUpd4sFQL6Ul0urrq2Zg9cG4NHBkei3k9lsHA==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@smithy/types" "^2.10.0"
    bowser "^2.11.0"
    tslib "^2.5.0"

"@aws-sdk/util-user-agent-node@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.521.0.tgz#5f0337af400037363676e7f45136b0463de412d8"
  integrity sha512-g4KMEiyLc8DG21eMrp6fJUdfQ9F0fxfCNMDRgf0SE/pWI/u4vuWR2n8obLwq1pMVx7Ksva1NO3dc+a3Rgr0hag==
  dependencies:
    "@aws-sdk/types" "3.521.0"
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@aws-sdk/util-utf8-browser@^3.0.0":
  version "3.259.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-utf8-browser/-/util-utf8-browser-3.259.0.tgz#3275a6f5eb334f96ca76635b961d3c50259fd9ff"
  integrity sha512-UvFa/vR+e19XookZF8RzFZBrw2EUkQWxiBW0yYQAhvk3C+QVGl0H3ouca8LDBlBfQKXwmW3huo/59H8rwb1wJw==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/xml-builder@3.521.0":
  version "3.521.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/xml-builder/-/xml-builder-3.521.0.tgz#628d5f38aa17ac5c6da70e10e40e2eef9b517b17"
  integrity sha512-ahaG39sgpBN/UOKzOW9Ey6Iuy6tK8vh2D+/tsLFLQ59PXoCvU06xg++TGXKpxsYMJGIzBvZMDC1aBhGmm/HsaA==
  dependencies:
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@dazn/lambda-powertools-correlation-ids@^1.28.1":
  version "1.28.1"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-correlation-ids/-/lambda-powertools-correlation-ids-1.28.1.tgz#ff0b94de104154cbdf5825e9f2a5a789c4cb6e92"
  integrity sha512-/RZUT5ZlVcQxsWi+OolEwXUIsXHqffNeZ+eY4Je23s9VcztuqHuHeyrlJh1m5Kg76EsvQTq+5b1xvjf3J/6A1Q==

"@dazn/lambda-powertools-logger@^1.28.1":
  version "1.28.1"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-logger/-/lambda-powertools-logger-1.28.1.tgz#ac638e6e13552ac4e3a35613436f8a132e9bfe05"
  integrity sha512-vfnKgEwW/jv4PSkqRsEWPWLY5fkcjCnLrSZpca2Exh7pIUWZQN3FqLdpqs23caa+qtOCJ2JM8toa21uwSzYMLw==
  dependencies:
    "@dazn/lambda-powertools-correlation-ids" "^1.28.1"

"@dazn/lambda-powertools-middleware-correlation-ids@^1.29.0":
  version "1.29.0"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-middleware-correlation-ids/-/lambda-powertools-middleware-correlation-ids-1.29.0.tgz#e03e8184e2a3673a77d18d02576b44646caa060c"
  integrity sha512-kXOOKzEMKz6nYHUQo2GUvTqnQeXo1U6/RI87xUjSeztcjHaDZ0Jw6plUepZD+YawjfsVIrHKfnZrlN909utttA==
  dependencies:
    "@dazn/lambda-powertools-correlation-ids" "^1.28.1"
    "@dazn/lambda-powertools-logger" "^1.28.1"

"@dazn/lambda-powertools-middleware-log-timeout@^1.29.0":
  version "1.29.0"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-middleware-log-timeout/-/lambda-powertools-middleware-log-timeout-1.29.0.tgz#027b9fcaa0413b5d0e0261f16721be31079f9e9c"
  integrity sha512-BJv3DQdcuOCBfp93cFv3LgCcCBhwh4s8COmw4x+c3cEdkY6zajo9tHAikFea8Fv9ShDXAcUgnPpkv8EFMbAH+w==
  dependencies:
    "@dazn/lambda-powertools-logger" "^1.28.1"

"@dazn/lambda-powertools-middleware-sample-logging@^1.29.0":
  version "1.29.0"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-middleware-sample-logging/-/lambda-powertools-middleware-sample-logging-1.29.0.tgz#a0b403f7387e202b47df1bdc975c5e0ba09fd46f"
  integrity sha512-VHe3bSw0ch5Ql5tA3XvCta8db1Nr6NaSJ0Oj2oqQU+F15WJfqPD+reeKMgj3F1z8lJqXWAea3aD4nQT0PCTt6Q==
  dependencies:
    "@dazn/lambda-powertools-correlation-ids" "^1.28.1"
    "@dazn/lambda-powertools-logger" "^1.28.1"

"@dazn/lambda-powertools-pattern-basic@^1.29.0":
  version "1.29.0"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-pattern-basic/-/lambda-powertools-pattern-basic-1.29.0.tgz#d97d47730588cb93dc115402fbab12e4492c6948"
  integrity sha512-HYmu9eKVRYNu5Q2CYuOl3UmBMAfpHzvNJFRdR8f8F5DJLktsexapk1sDjZZq4bP1ZmduuSbG/mUN9nmtkCRWYw==
  dependencies:
    "@dazn/lambda-powertools-middleware-correlation-ids" "^1.29.0"
    "@dazn/lambda-powertools-middleware-log-timeout" "^1.29.0"
    "@dazn/lambda-powertools-middleware-sample-logging" "^1.29.0"
    "@middy/core" "^2.1.0"

"@mediality/centaur@../../centaurappCentaurAppCommonLayer/lib/nodejs":
  version "1.0.31"
  dependencies:
    "@aws-lambda-powertools/logger" "^1.5.1"
    "@aws-lambda-powertools/tracer" "^1.5.1"
    "@aws-sdk/client-api-gateway" "^3.54.0"
    "@aws-sdk/client-s3" "^3.282.0"
    "@aws-sdk/client-secrets-manager" "^3.282.0"
    "@dazn/lambda-powertools-logger" "^1.28.1"
    "@dazn/lambda-powertools-pattern-basic" "^1.29.0"
    "@mediality/centaur" "./"
    aws-sdk "^2.1324.0"
    aws-xray-sdk "^3.3.4"
    aws-xray-sdk-core "^3.3.4"
    axios "^1.6.7"
    basic-ftp "^5.0.1"
    fast-xml-parser "^4.0.1"
    fs "^0.0.1-security"
    fs-extra "^10.0.0"
    install "^0.13.0"
    moment "^2.29.1"
    mongoose "^6.1.3"
    pify "^5.0.0"
    uuid "^8.3.2"
    uuid-by-string "^3.0.4"
    validator "^13.7.0"
    xml2js "^0.4.23"
    xmlbuilder2 "^3.0.2"

"@mediality/centaur@./":
  version "2.0.0"

"@middy/core@^2.1.0":
  version "2.5.7"
  resolved "https://registry.yarnpkg.com/@middy/core/-/core-2.5.7.tgz#a1b3eff68881ff66b14b5051255791f7cbd3b471"
  integrity sha512-KX5Ud0SP+pol6PGkYtMCH4goHobs1XJo3OvEUwdiZUIjZgo56Q08nLu5N7Bs6P+FwGTQHA+hlQ3I5SZbfpO/jg==

"@mongodb-js/saslprep@^1.1.0":
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/@mongodb-js/saslprep/-/saslprep-1.1.4.tgz#24ec1c4915a65f5c506bb88c081731450d91bb1c"
  integrity sha512-8zJ8N1x51xo9hwPh6AWnKdLGEC5N3lDa6kms1YHmFBoRhTpJR6HG8wWk0td1MVCu9cD4YBrvjZEtd5Obw0Fbnw==
  dependencies:
    sparse-bitfield "^3.0.3"

"@oozcitak/dom@1.15.10":
  version "1.15.10"
  resolved "https://registry.yarnpkg.com/@oozcitak/dom/-/dom-1.15.10.tgz#dca7289f2b292cff2a901ea4fbbcc0a1ab0b05c2"
  integrity sha512-0JT29/LaxVgRcGKvHmSrUTEvZ8BXvZhGl2LASRUgHqDTC1M5g1pLmVv56IYNyt3bG2CUjDkc67wnyZC14pbQrQ==
  dependencies:
    "@oozcitak/infra" "1.0.8"
    "@oozcitak/url" "1.0.4"
    "@oozcitak/util" "8.3.8"

"@oozcitak/infra@1.0.8":
  version "1.0.8"
  resolved "https://registry.yarnpkg.com/@oozcitak/infra/-/infra-1.0.8.tgz#b0b089421f7d0f6878687608301fbaba837a7d17"
  integrity sha512-JRAUc9VR6IGHOL7OGF+yrvs0LO8SlqGnPAMqyzOuFZPSZSXI7Xf2O9+awQPSMXgIWGtgUf/dA6Hs6X6ySEaWTg==
  dependencies:
    "@oozcitak/util" "8.3.8"

"@oozcitak/url@1.0.4":
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/@oozcitak/url/-/url-1.0.4.tgz#ca8b1c876319cf5a648dfa1123600a6aa5cda6ba"
  integrity sha512-kDcD8y+y3FCSOvnBI6HJgl00viO/nGbQoCINmQ0h98OhnGITrWR3bOGfwYCthgcrV8AnTJz8MzslTQbC3SOAmw==
  dependencies:
    "@oozcitak/infra" "1.0.8"
    "@oozcitak/util" "8.3.8"

"@oozcitak/util@8.3.8":
  version "8.3.8"
  resolved "https://registry.yarnpkg.com/@oozcitak/util/-/util-8.3.8.tgz#10f65fe1891fd8cde4957360835e78fd1936bfdd"
  integrity sha512-T8TbSnGsxo6TDBJx/Sgv/BlVJL3tshxZP7Aq5R1mSnM5OcHY2dQaxLMu2+E8u3gN0MLOzdjurqN4ZRVuzQycOQ==

"@smithy/abort-controller@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/abort-controller/-/abort-controller-2.1.2.tgz#8d865c28ad0d6a39ed0fdf3c361d0e0d722182e3"
  integrity sha512-iwUxrFm/ZFCXhzhtZ6JnoJzAsqUrVfBAZUTQj8ypXGtIjwXZpKqmgYiuqrDERiydDI5gesqvsC4Rqe57GGhbVg==
  dependencies:
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/chunked-blob-reader-native@^2.1.1":
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/chunked-blob-reader-native/-/chunked-blob-reader-native-2.1.1.tgz#6b98479c8f6ea94832dd6a6e5ca78969a44eafe1"
  integrity sha512-zNW+43dltfNMUrBEYLMWgI8lQr0uhtTcUyxkgC9EP4j17WREzgSFMPUFVrVV6Rc2+QtWERYjb4tzZnQGa7R9fQ==
  dependencies:
    "@smithy/util-base64" "^2.1.1"
    tslib "^2.5.0"

"@smithy/chunked-blob-reader@^2.1.1":
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/chunked-blob-reader/-/chunked-blob-reader-2.1.1.tgz#997faba8e197e0cb9824dad30ae581466e386e57"
  integrity sha512-NjNFCKxC4jVvn+lUr3Yo4/PmUJj3tbyqH6GNHueyTGS5Q27vlEJ1MkNhUDV8QGxJI7Bodnc2pD18lU2zRfhHlQ==
  dependencies:
    tslib "^2.5.0"

"@smithy/config-resolver@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/config-resolver/-/config-resolver-2.1.2.tgz#68d8e175ba9b1112d74dbfdccd03dfa38b96c718"
  integrity sha512-ZDMY63xJVsJl7ei/yIMv9nx8OiEOulwNnQOUDGpIvzoBrcbvYwiMjIMe5mP5J4fUmttKkpiTKwta/7IUriAn9w==
  dependencies:
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/types" "^2.10.0"
    "@smithy/util-config-provider" "^2.2.1"
    "@smithy/util-middleware" "^2.1.2"
    tslib "^2.5.0"

"@smithy/core@^1.3.3":
  version "1.3.3"
  resolved "https://registry.yarnpkg.com/@smithy/core/-/core-1.3.3.tgz#383da328c514fb916041380196df6fc190a5a996"
  integrity sha512-8cT/swERvU1EUMuJF914+psSeVy4+NcNhbRe1WEKN1yIMPE5+Tq5EaPq1HWjKCodcdBIyU9ViTjd62XnebXMHA==
  dependencies:
    "@smithy/middleware-endpoint" "^2.4.2"
    "@smithy/middleware-retry" "^2.1.2"
    "@smithy/middleware-serde" "^2.1.2"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/smithy-client" "^2.4.0"
    "@smithy/types" "^2.10.0"
    "@smithy/util-middleware" "^2.1.2"
    tslib "^2.5.0"

"@smithy/credential-provider-imds@^2.2.1", "@smithy/credential-provider-imds@^2.2.2":
  version "2.2.2"
  resolved "https://registry.yarnpkg.com/@smithy/credential-provider-imds/-/credential-provider-imds-2.2.2.tgz#58d5e38a8c50ae5119e94c0580421ea65789b13b"
  integrity sha512-a2xpqWzhzcYwImGbFox5qJLf6i5HKdVeOVj7d6kVFElmbS2QW2T4HmefRc5z1huVArk9bh5Rk1NiFp9YBCXU3g==
  dependencies:
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/property-provider" "^2.1.2"
    "@smithy/types" "^2.10.0"
    "@smithy/url-parser" "^2.1.2"
    tslib "^2.5.0"

"@smithy/eventstream-codec@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-codec/-/eventstream-codec-2.1.2.tgz#b527902b7813c5d9d23fb1351b6e84046f2e00df"
  integrity sha512-2PHrVRixITHSOj3bxfZmY93apGf8/DFiyhRh9W0ukfi07cvlhlRonZ0fjgcqryJjUZ5vYHqqmfIE/Qe1HM9mlw==
  dependencies:
    "@aws-crypto/crc32" "3.0.0"
    "@smithy/types" "^2.10.0"
    "@smithy/util-hex-encoding" "^2.1.1"
    tslib "^2.5.0"

"@smithy/eventstream-serde-browser@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-browser/-/eventstream-serde-browser-2.1.2.tgz#993f0c92bc0f5fcf734dea1217531f556efe62e6"
  integrity sha512-2N11IFHvOmKuwK6hLVkqM8ge8oiQsFkflr4h07LToxo3rX+njkx/5eRn6RVcyNmpbdbxYYt0s0Pf8u+yhHmOKg==
  dependencies:
    "@smithy/eventstream-serde-universal" "^2.1.2"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/eventstream-serde-config-resolver@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-config-resolver/-/eventstream-serde-config-resolver-2.1.2.tgz#6423b5fb1140448286803dae1d444f3bf96d166e"
  integrity sha512-nD/+k3mK+lMMwf2AItl7uWma+edHLqiE6LyIYXYnIBlCJcIQnA/vTHjHFoSJFCfG30sBJnU/7u4X5j/mbs9uKg==
  dependencies:
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/eventstream-serde-node@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-node/-/eventstream-serde-node-2.1.2.tgz#283adddc9898689cd231a0e6efcdf9bdcec81333"
  integrity sha512-zNE6DhbwDEWTKl4mELkrdgXBGC7UsFg1LDkTwizSOFB/gd7G7la083wb0JgU+xPt+TYKK0AuUlOM0rUZSJzqeA==
  dependencies:
    "@smithy/eventstream-serde-universal" "^2.1.2"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/eventstream-serde-universal@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-universal/-/eventstream-serde-universal-2.1.2.tgz#2ecbe6bffc7a40add81dbee04654c943bb602ec7"
  integrity sha512-Upd/zy+dNvvIDPU1HGhW9ivNjvJQ0W4UkkQOzr5Mo0hz2lqnZAyOuit4TK2JAEg/oo+V1gUY4XywDc7zNbCF0g==
  dependencies:
    "@smithy/eventstream-codec" "^2.1.2"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/fetch-http-handler@^2.4.2":
  version "2.4.2"
  resolved "https://registry.yarnpkg.com/@smithy/fetch-http-handler/-/fetch-http-handler-2.4.2.tgz#5ff26c1ef24c6e1d0acd189f6bc064f110fc446f"
  integrity sha512-sIGMVwa/8h6eqNjarI3F07gvML3mMXcqBe+BINNLuKsVKXMNBN6wRzeZbbx7lfiJDEHAP28qRns8flHEoBB7zw==
  dependencies:
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/querystring-builder" "^2.1.2"
    "@smithy/types" "^2.10.0"
    "@smithy/util-base64" "^2.1.1"
    tslib "^2.5.0"

"@smithy/hash-blob-browser@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/hash-blob-browser/-/hash-blob-browser-2.1.2.tgz#0e57a302587f9833e45a036479149990f414cedc"
  integrity sha512-f8QHgOVSXeYsc4BLKWdfXRowKa2g9byAkAX5c7Ku89bi9uBquWLEVmKlYXFBlkX562Fkmp2YSeciv+zZuOrIOQ==
  dependencies:
    "@smithy/chunked-blob-reader" "^2.1.1"
    "@smithy/chunked-blob-reader-native" "^2.1.1"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/hash-node@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/hash-node/-/hash-node-2.1.2.tgz#3dba95fc89d4758cb6189f2029d846677ac1364e"
  integrity sha512-3Sgn4s0g4xud1M/j6hQwYCkz04lVJ24wvCAx4xI26frr3Ao6v0o2VZkBpUySTeQbMUBp2DhuzJ0fV1zybzkckw==
  dependencies:
    "@smithy/types" "^2.10.0"
    "@smithy/util-buffer-from" "^2.1.1"
    "@smithy/util-utf8" "^2.1.1"
    tslib "^2.5.0"

"@smithy/hash-stream-node@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/hash-stream-node/-/hash-stream-node-2.1.2.tgz#85f940809bf646e4f7c485c2f23a7b3f04ac0fb3"
  integrity sha512-UB6xo+KN3axrLO+MfnWb8mtdeep4vjGUcjYCVFdk9h+OqUb7JYWZZLRcupRPZx28cNBCBEUtc9wVZDI71JDdQA==
  dependencies:
    "@smithy/types" "^2.10.0"
    "@smithy/util-utf8" "^2.1.1"
    tslib "^2.5.0"

"@smithy/invalid-dependency@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/invalid-dependency/-/invalid-dependency-2.1.2.tgz#45c0b34ca9dee56920b9313d88fa5a9e78c7bf41"
  integrity sha512-qdgKhkFYxDJnKecx2ANwz3JRkXjm0qDgEnAs5BIfb2z/XqA2l7s9BTH7GTC/RR4E8h6EDCeb5rM2rnARxviqIg==
  dependencies:
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/is-array-buffer@^2.1.1":
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/is-array-buffer/-/is-array-buffer-2.1.1.tgz#07b4c77ae67ed58a84400c76edd482271f9f957b"
  integrity sha512-xozSQrcUinPpNPNPds4S7z/FakDTh1MZWtRP/2vQtYB/u3HYrX2UXuZs+VhaKBd6Vc7g2XPr2ZtwGBNDN6fNKQ==
  dependencies:
    tslib "^2.5.0"

"@smithy/md5-js@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/md5-js/-/md5-js-2.1.2.tgz#205253479128980d3313189dd79d23f63ec757a1"
  integrity sha512-C/FWR5ooyDNDfc1Opx3n0QFO5p4G0gldIbk2VU9mPGnZVTjzXcWM5jUQp33My5UK305tKYpG5/kZdQSNVh+tLw==
  dependencies:
    "@smithy/types" "^2.10.0"
    "@smithy/util-utf8" "^2.1.1"
    tslib "^2.5.0"

"@smithy/middleware-content-length@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-content-length/-/middleware-content-length-2.1.2.tgz#c114f955d2b0fd3b61b1068908dd8d87ed070107"
  integrity sha512-XEWtul1tHP31EtUIobEyN499paUIbnCTRtjY+ciDCEXW81lZmpjrDG3aL0FxJDPnvatVQuMV1V5eg6MCqTFaLQ==
  dependencies:
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/middleware-endpoint@^2.4.2":
  version "2.4.2"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-endpoint/-/middleware-endpoint-2.4.2.tgz#dc229e8ee59e9f73ffd1ab4e020b2fc25cf2e7fd"
  integrity sha512-72qbmVwaWcLOd/OT52fszrrlXywPwciwpsRiIk/dIvpcwkpGE9qrYZ2bt/SYcA/ma8Rz9Ni2AbBuSXLDYISS+A==
  dependencies:
    "@smithy/middleware-serde" "^2.1.2"
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/shared-ini-file-loader" "^2.3.2"
    "@smithy/types" "^2.10.0"
    "@smithy/url-parser" "^2.1.2"
    "@smithy/util-middleware" "^2.1.2"
    tslib "^2.5.0"

"@smithy/middleware-retry@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-retry/-/middleware-retry-2.1.2.tgz#39762d83970b0458db3ad3469349d455ac6af4a4"
  integrity sha512-tlvSK+v9bPHHb0dLWvEaFW2Iz0IeA57ISvSaso36I33u8F8wYqo5FCvenH7TgMVBx57jyJBXOmYCZa9n5gdJIg==
  dependencies:
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/service-error-classification" "^2.1.2"
    "@smithy/smithy-client" "^2.4.0"
    "@smithy/types" "^2.10.0"
    "@smithy/util-middleware" "^2.1.2"
    "@smithy/util-retry" "^2.1.2"
    tslib "^2.5.0"
    uuid "^8.3.2"

"@smithy/middleware-serde@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-serde/-/middleware-serde-2.1.2.tgz#15b8258b806ecffd0a4c3fec3e56458cdef7ae66"
  integrity sha512-XNU6aVIhlSbjuo2XsfZ7rd4HhjTXDlNWxAmhlBfViTW1TNK02CeWdeEntp5XtQKYD//pyTIbYi35EQvIidAkOw==
  dependencies:
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/middleware-stack@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-stack/-/middleware-stack-2.1.2.tgz#17dbb56d85f51cb2c86c13dbad7fca35c843c61c"
  integrity sha512-EPGaHGd4XmZcaRYjbhyqiqN/Q/ESxXu5e5TK24CTZUe99y8/XCxmiX8VLMM4H0DI7K3yfElR0wPAAvceoSkTgw==
  dependencies:
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/node-config-provider@^2.2.2":
  version "2.2.2"
  resolved "https://registry.yarnpkg.com/@smithy/node-config-provider/-/node-config-provider-2.2.2.tgz#9422a0764dea8dec4a24f9aa570771d921dc657b"
  integrity sha512-QXvpqHSijAm13ZsVkUo92b085UzDvYP1LblWTb3uWi9WilhDvYnVyPLXaryLhOWZ2YvdhK2170T3ZBqtg+quIQ==
  dependencies:
    "@smithy/property-provider" "^2.1.2"
    "@smithy/shared-ini-file-loader" "^2.3.2"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/node-http-handler@^2.4.0":
  version "2.4.0"
  resolved "https://registry.yarnpkg.com/@smithy/node-http-handler/-/node-http-handler-2.4.0.tgz#21e48aa56ab334eee8afc69bb05f38f3883c3e95"
  integrity sha512-Mf2f7MMy31W8LisJ9O+7J5cKiNwBwBBLU6biQ7/sFSFdhuOxPN7hOPoZ8vlaFjvrpfOUJw9YOpjGyNTKuvomOQ==
  dependencies:
    "@smithy/abort-controller" "^2.1.2"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/querystring-builder" "^2.1.2"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/property-provider@^2.1.1", "@smithy/property-provider@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/property-provider/-/property-provider-2.1.2.tgz#16c630ae0354c05595c99c6ab70a877ee9a180e4"
  integrity sha512-yaXCVFKzxbSXqOoyA7AdAgXhwdjiLeui7n2P6XLjBCz/GZFdLUJgSY6KL1PevaxT4REMwUSs/bSHAe/0jdzEHw==
  dependencies:
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/protocol-http@^3.2.0":
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/protocol-http/-/protocol-http-3.2.0.tgz#1b9ed9eb18cd256e0d7872ec2851f5d12ba37d87"
  integrity sha512-VRp0YITYIQum+rX4zeZ3cW1wl9r90IQzQN+VLS1NxdSMt6NLsJiJqR9czTxlaeWNrLHsFAETmjmdrS48Ug1liA==
  dependencies:
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/querystring-builder@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/querystring-builder/-/querystring-builder-2.1.2.tgz#78f028c25253e514915247b25c20b3cf0d6a035b"
  integrity sha512-wk6QpuvBBLJF5w8aADsZOtxaHY9cF5MZe1Ry3hSqqBxARdUrMoXi/jukUz5W0ftXGlbA398IN8dIIUj3WXqJXg==
  dependencies:
    "@smithy/types" "^2.10.0"
    "@smithy/util-uri-escape" "^2.1.1"
    tslib "^2.5.0"

"@smithy/querystring-parser@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/querystring-parser/-/querystring-parser-2.1.2.tgz#3883dfec5760f0f8cdf9acc837bdc631069df576"
  integrity sha512-z1yL5Iiagm/UxVy1tcuTFZdfOBK/QtYeK6wfClAJ7cOY7kIaYR6jn1cVXXJmhAQSh1b2ljP4xiZN4Ybj7Tbs5w==
  dependencies:
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/service-error-classification@^2.0.4", "@smithy/service-error-classification@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/service-error-classification/-/service-error-classification-2.1.2.tgz#b8b5c23a784bcb1eb229a921d7040575e29e38ed"
  integrity sha512-R+gL1pAPuWkH6unFridk57wDH5PFY2IlVg2NUjSAjoaIaU+sxqKf/7AOWIcx9Bdn+xY0/4IRQ69urlC+F3I9gg==
  dependencies:
    "@smithy/types" "^2.10.0"

"@smithy/shared-ini-file-loader@^2.3.1", "@smithy/shared-ini-file-loader@^2.3.2":
  version "2.3.2"
  resolved "https://registry.yarnpkg.com/@smithy/shared-ini-file-loader/-/shared-ini-file-loader-2.3.2.tgz#3e4943b534eaabda15372e611cdb428dfdd88362"
  integrity sha512-idHGDJB+gBh+aaIjmWj6agmtNWftoyAenErky74hAtKyUaCvfocSBgEJ2pQ6o68svBluvGIj4NGFgJu0198mow==
  dependencies:
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/signature-v4@^2.1.1":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/signature-v4/-/signature-v4-2.1.2.tgz#a658df8a5fcb57160e1c364d43b46e0d14f5995f"
  integrity sha512-DdPWaNGIbxzyocR3ncH8xlxQgsqteRADEdCPoivgBzwv17UzKy2obtdi2vwNc5lAJ955bGEkkWef9O7kc1Eocg==
  dependencies:
    "@smithy/eventstream-codec" "^2.1.2"
    "@smithy/is-array-buffer" "^2.1.1"
    "@smithy/types" "^2.10.0"
    "@smithy/util-hex-encoding" "^2.1.1"
    "@smithy/util-middleware" "^2.1.2"
    "@smithy/util-uri-escape" "^2.1.1"
    "@smithy/util-utf8" "^2.1.1"
    tslib "^2.5.0"

"@smithy/smithy-client@^2.4.0":
  version "2.4.0"
  resolved "https://registry.yarnpkg.com/@smithy/smithy-client/-/smithy-client-2.4.0.tgz#f4cef6f63cdc267a32ded8446ca3db0ebb8fe64d"
  integrity sha512-6/jxk0om9l2s9BcgHtrBn+Hd3xcFGDzxfEJ2FvGpZxIz0S7bgvZg1gyR66O1xf1w9WZBH+W7JClhfSn2gETINw==
  dependencies:
    "@smithy/middleware-endpoint" "^2.4.2"
    "@smithy/middleware-stack" "^2.1.2"
    "@smithy/protocol-http" "^3.2.0"
    "@smithy/types" "^2.10.0"
    "@smithy/util-stream" "^2.1.2"
    tslib "^2.5.0"

"@smithy/types@^2.10.0":
  version "2.10.0"
  resolved "https://registry.yarnpkg.com/@smithy/types/-/types-2.10.0.tgz#1cc16e3c04d56c49ecb88efa1b7fa9ca3a90d667"
  integrity sha512-QYXQmpIebS8/jYXgyJjCanKZbI4Rr8tBVGBAIdDhA35f025TVjJNW69FJ0TGiDqt+lIGo037YIswq2t2Y1AYZQ==
  dependencies:
    tslib "^2.5.0"

"@smithy/url-parser@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/url-parser/-/url-parser-2.1.2.tgz#915590d97a7c6beb0dcebc9e9458345cf6bf7f48"
  integrity sha512-KBPi740ciTujUaY+RfQuPABD0QFmgSBN5qNVDCGTryfsbG4jkwC0YnElSzi72m24HegMyxzZDLG4Oh4/97mw2g==
  dependencies:
    "@smithy/querystring-parser" "^2.1.2"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/util-base64@^2.1.1":
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-base64/-/util-base64-2.1.1.tgz#af729085cc9d92ebd54a5d2c5d0aa5a0c31f83bf"
  integrity sha512-UfHVpY7qfF/MrgndI5PexSKVTxSZIdz9InghTFa49QOvuu9I52zLPLUHXvHpNuMb1iD2vmc6R+zbv/bdMipR/g==
  dependencies:
    "@smithy/util-buffer-from" "^2.1.1"
    tslib "^2.5.0"

"@smithy/util-body-length-browser@^2.1.1":
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-body-length-browser/-/util-body-length-browser-2.1.1.tgz#1fc77072768013ae646415eedb9833cd252d055d"
  integrity sha512-ekOGBLvs1VS2d1zM2ER4JEeBWAvIOUKeaFch29UjjJsxmZ/f0L3K3x0dEETgh3Q9bkZNHgT+rkdl/J/VUqSRag==
  dependencies:
    tslib "^2.5.0"

"@smithy/util-body-length-node@^2.2.1":
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-body-length-node/-/util-body-length-node-2.2.1.tgz#a6f5c9911f1c3e23efb340d5ce7a590b62f2056e"
  integrity sha512-/ggJG+ta3IDtpNVq4ktmEUtOkH1LW64RHB5B0hcr5ZaWBmo96UX2cIOVbjCqqDickTXqBWZ4ZO0APuaPrD7Abg==
  dependencies:
    tslib "^2.5.0"

"@smithy/util-buffer-from@^2.1.1":
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-buffer-from/-/util-buffer-from-2.1.1.tgz#f9346bf8b23c5ba6f6bdb61dd9db779441ba8d08"
  integrity sha512-clhNjbyfqIv9Md2Mg6FffGVrJxw7bgK7s3Iax36xnfVj6cg0fUG7I4RH0XgXJF8bxi+saY5HR21g2UPKSxVCXg==
  dependencies:
    "@smithy/is-array-buffer" "^2.1.1"
    tslib "^2.5.0"

"@smithy/util-config-provider@^2.2.1":
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-config-provider/-/util-config-provider-2.2.1.tgz#aea0a80236d6cedaee60473802899cff4a8cc0ba"
  integrity sha512-50VL/tx9oYYcjJn/qKqNy7sCtpD0+s8XEBamIFo4mFFTclKMNp+rsnymD796uybjiIquB7VCB/DeafduL0y2kw==
  dependencies:
    tslib "^2.5.0"

"@smithy/util-defaults-mode-browser@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-2.1.2.tgz#5f4c328605635656dee624a1686c7616aadccf4d"
  integrity sha512-YmojdmsE7VbvFGJ/8btn/5etLm1HOQkgVX6nMWlB0yBL/Vb//s3aTebUJ66zj2+LNrBS3B9S+18+LQU72Yj0AQ==
  dependencies:
    "@smithy/property-provider" "^2.1.2"
    "@smithy/smithy-client" "^2.4.0"
    "@smithy/types" "^2.10.0"
    bowser "^2.11.0"
    tslib "^2.5.0"

"@smithy/util-defaults-mode-node@^2.2.1":
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-2.2.1.tgz#034918f2f945974e7414c092cb250f2d45fe0ceb"
  integrity sha512-kof7M9Q2qP5yaQn8hHJL3KwozyvIfLe+ys7feifSul6gBAAeoraibo/MWqotb/I0fVLMlCMDwn7WXFsGUwnsew==
  dependencies:
    "@smithy/config-resolver" "^2.1.2"
    "@smithy/credential-provider-imds" "^2.2.2"
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/property-provider" "^2.1.2"
    "@smithy/smithy-client" "^2.4.0"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/util-endpoints@^1.1.2":
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-endpoints/-/util-endpoints-1.1.2.tgz#92f743ac8c2c3a99b1558a1c956864b565aa23e7"
  integrity sha512-2/REfdcJ20y9iF+9kSBRBsaoGzjT5dZ3E6/TA45GHJuJAb/vZTj76VLTcrl2iN3fWXiDK1B8RxchaLGbr7RxxA==
  dependencies:
    "@smithy/node-config-provider" "^2.2.2"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/util-hex-encoding@^2.1.1":
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-hex-encoding/-/util-hex-encoding-2.1.1.tgz#978252b9fb242e0a59bae4ead491210688e0d15f"
  integrity sha512-3UNdP2pkYUUBGEXzQI9ODTDK+Tcu1BlCyDBaRHwyxhA+8xLP8agEKQq4MGmpjqb4VQAjq9TwlCQX0kP6XDKYLg==
  dependencies:
    tslib "^2.5.0"

"@smithy/util-middleware@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-middleware/-/util-middleware-2.1.2.tgz#5e2e13c96e95b65ae5980a658e1b10e222a42482"
  integrity sha512-lvSOnwQ7iAajtWb1nAyy0CkOIn8d+jGykQOtt2NXDsPzOTfejZM/Uph+O/TmVgWoXdcGuw5peUMG2f5xEIl6UQ==
  dependencies:
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/util-retry@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-retry/-/util-retry-2.1.2.tgz#4b7d3ac79ad9a3b3cb01d21d8fe5ea0b99390b90"
  integrity sha512-pqifOgRqwLfRu+ks3awEKKqPeYxrHLwo4Yu2EarGzeoarTd1LVEyyf5qLE6M7IiCsxnXRhn9FoWIdZOC+oC/VQ==
  dependencies:
    "@smithy/service-error-classification" "^2.1.2"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@smithy/util-stream@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-stream/-/util-stream-2.1.2.tgz#c1ab318fa2f14ef044bdec7cb93a9ffc36388f85"
  integrity sha512-AbGjvoSok7YeUKv9WRVRSChQfsufLR54YCAabTbaABRdIucywRQs29em0uAP6r4RLj+4aFZStWGYpFgT0P8UlQ==
  dependencies:
    "@smithy/fetch-http-handler" "^2.4.2"
    "@smithy/node-http-handler" "^2.4.0"
    "@smithy/types" "^2.10.0"
    "@smithy/util-base64" "^2.1.1"
    "@smithy/util-buffer-from" "^2.1.1"
    "@smithy/util-hex-encoding" "^2.1.1"
    "@smithy/util-utf8" "^2.1.1"
    tslib "^2.5.0"

"@smithy/util-uri-escape@^2.1.1":
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-uri-escape/-/util-uri-escape-2.1.1.tgz#7eedc93b73ecda68f12fb9cf92e9fa0fbbed4d83"
  integrity sha512-saVzI1h6iRBUVSqtnlOnc9ssU09ypo7n+shdQ8hBTZno/9rZ3AuRYvoHInV57VF7Qn7B+pFJG7qTzFiHxWlWBw==
  dependencies:
    tslib "^2.5.0"

"@smithy/util-utf8@^2.1.1":
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-utf8/-/util-utf8-2.1.1.tgz#690018dd28f47f014114497735e51417ea5900a6"
  integrity sha512-BqTpzYEcUMDwAKr7/mVRUtHDhs6ZoXDi9NypMvMfOr/+u1NW7JgqodPDECiiLboEm6bobcPcECxzjtQh865e9A==
  dependencies:
    "@smithy/util-buffer-from" "^2.1.1"
    tslib "^2.5.0"

"@smithy/util-waiter@^2.1.2":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-waiter/-/util-waiter-2.1.2.tgz#194f8cbd9c8c7c6e03d57c22eb057fb6f30e0b44"
  integrity sha512-yxLC57GBDmbDmrnH+vJxsrbV4/aYUucBONkSRLZyJIVFAl/QJH+O/h+phITHDaxVZCYZAcudYJw4ERE32BJM7g==
  dependencies:
    "@smithy/abort-controller" "^2.1.2"
    "@smithy/types" "^2.10.0"
    tslib "^2.5.0"

"@types/aws-lambda@^8.10.92":
  version "8.10.134"
  resolved "https://registry.yarnpkg.com/@types/aws-lambda/-/aws-lambda-8.10.134.tgz#8f65d86736839889194f7892b7bec6b8a7ec6fc3"
  integrity sha512-cfv422ivDMO+EeA3N4YcshbTHBL+5lLXe+Uz+4HXvIcsCuWvqNFpOs28ZprL8NA3qRCzt95ETiNAJDn4IcC/PA==

"@types/body-parser@*":
  version "1.19.5"
  resolved "https://registry.yarnpkg.com/@types/body-parser/-/body-parser-1.19.5.tgz#04ce9a3b677dc8bd681a17da1ab9835dc9d3ede4"
  integrity sha512-fB3Zu92ucau0iQ0JMCFQE7b/dv8Ot07NI3KaZIkIUNXq82k4eBAqUaneXfleGY9JWskeS9y+u0nXMyspcuQrCg==
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/cls-hooked@^4.3.3":
  version "4.3.8"
  resolved "https://registry.yarnpkg.com/@types/cls-hooked/-/cls-hooked-4.3.8.tgz#ece275711b34eca51b3bc3899b13add7d8aff250"
  integrity sha512-tf/7H883gFA6MPlWI15EQtfNZ+oPL0gLKkOlx9UHFrun1fC/FkuyNBpTKq1B5E3T4fbvjId6WifHUdSGsMMuPg==
  dependencies:
    "@types/node" "*"

"@types/connect@*":
  version "3.4.38"
  resolved "https://registry.yarnpkg.com/@types/connect/-/connect-3.4.38.tgz#5ba7f3bc4fbbdeaff8dded952e5ff2cc53f8d858"
  integrity sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==
  dependencies:
    "@types/node" "*"

"@types/express-serve-static-core@^4.17.33":
  version "4.17.43"
  resolved "https://registry.yarnpkg.com/@types/express-serve-static-core/-/express-serve-static-core-4.17.43.tgz#10d8444be560cb789c4735aea5eac6e5af45df54"
  integrity sha512-oaYtiBirUOPQGSWNGPWnzyAFJ0BP3cwvN4oWZQY+zUBwpVIGsKUkpBpSztp74drYcjavs7SKFZ4DX1V2QeN8rg==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*":
  version "4.17.21"
  resolved "https://registry.yarnpkg.com/@types/express/-/express-4.17.21.tgz#c26d4a151e60efe0084b23dc3369ebc631ed192d"
  integrity sha512-ejlPM315qwLpaQlQDTjPdsUFSc6ZsP4AN6AlWnogPjQ7CVi7PYF3YVz+CY3jE2pwYf7E/7HlDAN0rV2GxTG0HQ==
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/http-errors@*":
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/@types/http-errors/-/http-errors-2.0.4.tgz#7eb47726c391b7345a6ec35ad7f4de469cf5ba4f"
  integrity sha512-D0CFMMtydbJAegzOyHjtiKPLlvnm3iTZyZRSZoLq2mRhDdmLfIWOCYPfQJ4cu2erKghU++QvjcUjp/5h7hESpA==

"@types/mime@*":
  version "3.0.4"
  resolved "https://registry.yarnpkg.com/@types/mime/-/mime-3.0.4.tgz#2198ac274de6017b44d941e00261d5bc6a0e0a45"
  integrity sha512-iJt33IQnVRkqeqC7PzBHPTC6fDlRNRW8vjrgqtScAhrmMwe8c4Eo7+fUGTa+XdWrpEgpyKWMYmi2dIwMAYRzPw==

"@types/mime@^1":
  version "1.3.5"
  resolved "https://registry.yarnpkg.com/@types/mime/-/mime-1.3.5.tgz#1ef302e01cf7d2b5a0fa526790c9123bf1d06690"
  integrity sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==

"@types/mysql@*":
  version "2.15.25"
  resolved "https://registry.yarnpkg.com/@types/mysql/-/mysql-2.15.25.tgz#a1dd241f2c95b1695ced5c336d567d5431aff698"
  integrity sha512-pKjbzNu/xvD2xOx4psIfxu9CBg+GovLvQFk8NYTW3oT7Gf5QY65MvNgQNFvVb0nC3l9DCKGqBFYhujVrDqii4A==
  dependencies:
    "@types/node" "*"

"@types/node@*":
  version "20.11.20"
  resolved "https://registry.yarnpkg.com/@types/node/-/node-20.11.20.tgz#f0a2aee575215149a62784210ad88b3a34843659"
  integrity sha512-7/rR21OS+fq8IyHTgtLkDK949uzsa6n8BkziAKtPVpugIkO6D+/ooXMvzXxDnZrmtXVfjb1bKQafYpb8s89LOg==
  dependencies:
    undici-types "~5.26.4"

"@types/pg@*":
  version "8.11.1"
  resolved "https://registry.yarnpkg.com/@types/pg/-/pg-8.11.1.tgz#2384c64feeb349841d74e4d5539c02a127395c12"
  integrity sha512-tUMU6HRvsosqH0qov7YH0Kj6EnziKoqmHUcREBPlDEQLWT+TxDUBT1MmQ53byHXs171xn3YFPIPwVTkVb16mVg==
  dependencies:
    "@types/node" "*"
    pg-protocol "*"
    pg-types "^4.0.1"

"@types/qs@*":
  version "6.9.11"
  resolved "https://registry.yarnpkg.com/@types/qs/-/qs-6.9.11.tgz#208d8a30bc507bd82e03ada29e4732ea46a6bbda"
  integrity sha512-oGk0gmhnEJK4Yyk+oI7EfXsLayXatCWPHary1MtcmbAifkobT9cM9yutG/hZKIseOU0MqbIwQ/u2nn/Gb+ltuQ==

"@types/range-parser@*":
  version "1.2.7"
  resolved "https://registry.yarnpkg.com/@types/range-parser/-/range-parser-1.2.7.tgz#50ae4353eaaddc04044279812f52c8c65857dbcb"
  integrity sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==

"@types/send@*":
  version "0.17.4"
  resolved "https://registry.yarnpkg.com/@types/send/-/send-0.17.4.tgz#6619cd24e7270793702e4e6a4b958a9010cfc57a"
  integrity sha512-x2EM6TJOybec7c52BX0ZspPodMsQUd5L6PRwOunVyVUhXiBSKf3AezDL8Dgvgt5o0UfKNfuA0eMLr2wLT4AiBA==
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-static@*":
  version "1.15.5"
  resolved "https://registry.yarnpkg.com/@types/serve-static/-/serve-static-1.15.5.tgz#15e67500ec40789a1e8c9defc2d32a896f05b033"
  integrity sha512-PDRk21MnK70hja/YF8AHfC7yIsiQHn1rcXx7ijCFBX/k+XQJhQT/gw3xekXKJvx+5SXaMMS8oqQy09Mzvz2TuQ==
  dependencies:
    "@types/http-errors" "*"
    "@types/mime" "*"
    "@types/node" "*"

"@types/webidl-conversions@*":
  version "7.0.3"
  resolved "https://registry.yarnpkg.com/@types/webidl-conversions/-/webidl-conversions-7.0.3.tgz#1306dbfa53768bcbcfc95a1c8cde367975581859"
  integrity sha512-CiJJvcRtIgzadHCYXw7dqEnMNRjhGZlYK05Mj9OyktqV8uVT8fD2BFOB7S1uwBE3Kj2Z+4UyPmFw/Ixgw/LAlA==

"@types/whatwg-url@^8.2.1":
  version "8.2.2"
  resolved "https://registry.yarnpkg.com/@types/whatwg-url/-/whatwg-url-8.2.2.tgz#749d5b3873e845897ada99be4448041d4cc39e63"
  integrity sha512-FtQu10RWgn3D9U4aazdwIE2yzphmTJREDqNdODHrbrZmmMqI0vMheC/6NE/J1Yveaj8H+ela+YwWTjq5PGmuhA==
  dependencies:
    "@types/node" "*"
    "@types/webidl-conversions" "*"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.yarnpkg.com/argparse/-/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==
  dependencies:
    sprintf-js "~1.0.2"

async-hook-jl@^1.7.6:
  version "1.7.6"
  resolved "https://registry.yarnpkg.com/async-hook-jl/-/async-hook-jl-1.7.6.tgz#4fd25c2f864dbaf279c610d73bf97b1b28595e68"
  integrity sha512-gFaHkFfSxTjvoxDMYqDuGHlcRyUuamF8s+ZTtJdDzqjws4mCt7v0vuV79/E2Wr2/riMQgtG4/yUtXWs1gZ7JMg==
  dependencies:
    stack-chain "^1.3.7"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

atomic-batcher@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/atomic-batcher/-/atomic-batcher-1.0.2.tgz#d16901d10ccec59516c197b9ccd8930689b813b4"
  integrity sha512-EFGCRj4kLX1dHv1cDzTk+xbjBFj1GnJDpui52YmEcxxHHEWjYyT6l51U7n6WQ28osZH4S9gSybxe56Vm7vB61Q==

available-typed-arrays@^1.0.6:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz#a5cc375d6a03c2efc87a553f3e0b1522def14846"
  integrity sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==
  dependencies:
    possible-typed-array-names "^1.0.0"

aws-sdk@^2.1324.0:
  version "2.1565.0"
  resolved "https://registry.yarnpkg.com/aws-sdk/-/aws-sdk-2.1565.0.tgz#a8eb27473015e5a1cc94b92aa37e19f24c6e80b9"
  integrity sha512-5+qkIUd6GfzybqWP43RCMqCNL5Ry3ztCWcC5d9Mq0O9cqQQM9eoUfktdgiu1eFc3zAM01dFNUR6fZfXnmZgkuQ==
  dependencies:
    buffer "4.9.2"
    events "1.1.1"
    ieee754 "1.1.13"
    jmespath "0.16.0"
    querystring "0.2.0"
    sax "1.2.1"
    url "0.10.3"
    util "^0.12.4"
    uuid "8.0.0"
    xml2js "0.6.2"

aws-xray-sdk-core@3.5.4, aws-xray-sdk-core@^3.3.4, aws-xray-sdk-core@^3.5.3:
  version "3.5.4"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk-core/-/aws-xray-sdk-core-3.5.4.tgz#19260291b4e95b3f71684187f43fa08ba49396c4"
  integrity sha512-L4yt2OstsebH8rNAIykDrr+BKjHGxwg5+1FUZAgqDBnaL98sfjPXNYVQNYzg+2ITOjfDbd8NtR1vCQXYVepm+g==
  dependencies:
    "@aws-sdk/types" "^3.4.1"
    "@smithy/service-error-classification" "^2.0.4"
    "@types/cls-hooked" "^4.3.3"
    atomic-batcher "^1.0.2"
    cls-hooked "^4.2.2"
    semver "^7.5.3"

aws-xray-sdk-express@3.5.4:
  version "3.5.4"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk-express/-/aws-xray-sdk-express-3.5.4.tgz#4eb940d83f1a77c2981ed8a8e0b6edc07714f87f"
  integrity sha512-8+czfml0AmqAhUshzfyrQweryJItn8Fc6g7T+eH0Q/ugi1sHaIC0oGn7W56kTSWfemdce2RjNw+E8vRvGcflXQ==
  dependencies:
    "@types/express" "*"

aws-xray-sdk-mysql@3.5.4:
  version "3.5.4"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk-mysql/-/aws-xray-sdk-mysql-3.5.4.tgz#2ce42f1ed96a1ef386c2798ca07239f338fe02c8"
  integrity sha512-Tqj6JeHJ+o+9qiLQ+QdhQ8056RhLBN4zhRF7RhmdfM/FOcaar4u1ayahQB0GquMOSsydB504vXL/vpLzEyN0dw==
  dependencies:
    "@types/mysql" "*"

aws-xray-sdk-postgres@3.5.4:
  version "3.5.4"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk-postgres/-/aws-xray-sdk-postgres-3.5.4.tgz#44434057b10e05b02deea20af1e18fc2b3cd311a"
  integrity sha512-Ztd6f8tQiCuQ4Zg64Bf50mv/E7EG6D67Mc8rlPCrOhWRvKW32Ka0b+/Cihb0e1cnWHjM1JFJ2ePgejv8SKNMmA==
  dependencies:
    "@types/pg" "*"

aws-xray-sdk@^3.3.4:
  version "3.5.4"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk/-/aws-xray-sdk-3.5.4.tgz#8b65f24a21600400f78eccaf1eb038c1dd8c4bfb"
  integrity sha512-1I/Tt1LSNqIRnPLfjq0aD0vtHReguee7d1hGXZhxSHB+2ZIP/bKcmsoj4ic8Gz2jJnfaRuHLq9KUwJZNd90ULQ==
  dependencies:
    aws-xray-sdk-core "3.5.4"
    aws-xray-sdk-express "3.5.4"
    aws-xray-sdk-mysql "3.5.4"
    aws-xray-sdk-postgres "3.5.4"

axios@^1.6.7:
  version "1.6.8"
  resolved "https://registry.yarnpkg.com/axios/-/axios-1.6.8.tgz#66d294951f5d988a00e87a0ffb955316a619ea66"
  integrity sha512-v/ZHtJDU39mDpyBoFVkETcd/uNdxrWRrg3bKpOKzXFA6Bvqopts6ALSMU3y6ijYxbw2B+wPrIv46egTzJXCLGQ==
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

base64-js@^1.0.2, base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://registry.yarnpkg.com/base64-js/-/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

basic-ftp@^5.0.1:
  version "5.0.4"
  resolved "https://registry.yarnpkg.com/basic-ftp/-/basic-ftp-5.0.4.tgz#28aeab7bfbbde5f5d0159cd8bb3b8e633bbb091d"
  integrity sha512-8PzkB0arJFV4jJWSGOYR+OEic6aeKMu/osRhBULN6RY0ykby6LKhbmuQ5ublvaas5BOwboah5D87nrHyuh8PPA==

bowser@^2.11.0:
  version "2.11.0"
  resolved "https://registry.yarnpkg.com/bowser/-/bowser-2.11.0.tgz#5ca3c35757a7aa5771500c70a73a9f91ef420a8f"
  integrity sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==

bson@^4.7.2:
  version "4.7.2"
  resolved "https://registry.yarnpkg.com/bson/-/bson-4.7.2.tgz#320f4ad0eaf5312dd9b45dc369cc48945e2a5f2e"
  integrity sha512-Ry9wCtIZ5kGqkJoi6aD8KjxFZEx78guTQDnpXWiNthsxzrxAK/i8E6pCHAIZTbaEFWcOCvbecMukfK7XUvyLpQ==
  dependencies:
    buffer "^5.6.0"

buffer@4.9.2:
  version "4.9.2"
  resolved "https://registry.yarnpkg.com/buffer/-/buffer-4.9.2.tgz#230ead344002988644841ab0244af8c44bbe3ef8"
  integrity sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

buffer@^5.6.0:
  version "5.7.1"
  resolved "https://registry.yarnpkg.com/buffer/-/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

call-bind@^1.0.2, call-bind@^1.0.5:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/call-bind/-/call-bind-1.0.7.tgz#06016599c40c56498c18769d2730be242b6fa3b9"
  integrity sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

cls-hooked@^4.2.2:
  version "4.2.2"
  resolved "https://registry.yarnpkg.com/cls-hooked/-/cls-hooked-4.2.2.tgz#ad2e9a4092680cdaffeb2d3551da0e225eae1908"
  integrity sha512-J4Xj5f5wq/4jAvcdgoGsL3G103BtWpZrMo8NEinRltN+xpTZdI+M38pyQqhuFU/P792xkMFvnKSf+Lm81U1bxw==
  dependencies:
    async-hook-jl "^1.7.6"
    emitter-listener "^1.0.1"
    semver "^5.4.1"

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.yarnpkg.com/combined-stream/-/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

debug@4.x:
  version "4.3.4"
  resolved "https://registry.yarnpkg.com/debug/-/debug-4.3.4.tgz#1319f6579357f2338d3337d2cdd4914bb5dcc865"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

define-data-property@^1.1.2:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/define-data-property/-/define-data-property-1.1.4.tgz#894dc141bb7d3060ae4366f6a0107e68fbe48c5e"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

emitter-listener@^1.0.1:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/emitter-listener/-/emitter-listener-1.1.2.tgz#56b140e8f6992375b3d7cb2cab1cc7432d9632e8"
  integrity sha512-Bt1sBAGFHY9DKY+4/2cV6izcKJUf5T7/gkdmkxzX/qv9CcGH8xSwVRW5mtX03SWJtRTWSOpzCuWN9rBFYZepZQ==
  dependencies:
    shimmer "^1.2.0"

es-define-property@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/es-define-property/-/es-define-property-1.0.0.tgz#c7faefbdff8b2696cf5f46921edfb77cc4ba3845"
  integrity sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==
  dependencies:
    get-intrinsic "^1.2.4"

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/es-errors/-/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/esprima/-/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==

events@1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/events/-/events-1.1.1.tgz#9ebdb7635ad099c70dcc4c2a1f5004288e8bd924"
  integrity sha512-kEcvvCBByWXGnZy6JUlgAp2gBIUjfCAV6P6TgT1/aaQKcmuAEC4OZTV1I4EWQLz2gxZw76atuVyvHhTxvi0Flw==

fast-xml-parser@4.2.5:
  version "4.2.5"
  resolved "https://registry.yarnpkg.com/fast-xml-parser/-/fast-xml-parser-4.2.5.tgz#a6747a09296a6cb34f2ae634019bf1738f3b421f"
  integrity sha512-B9/wizE4WngqQftFPmdaMYlXoJlJOYxGQOanC77fq9k8+Z0v5dDSVh+3glErdIROP//s/jgb7ZuxKfB8nVyo0g==
  dependencies:
    strnum "^1.0.5"

fast-xml-parser@^4.0.1:
  version "4.3.5"
  resolved "https://registry.yarnpkg.com/fast-xml-parser/-/fast-xml-parser-4.3.5.tgz#e2f2a2ae8377e9c3dc321b151e58f420ca7e5ccc"
  integrity sha512-sWvP1Pl8H03B8oFJpFR3HE31HUfwtX7Rlf9BNsvdpujD4n7WMhfmu8h9wOV2u+c1k0ZilTADhPqypzx2J690ZQ==
  dependencies:
    strnum "^1.0.5"

follow-redirects@^1.15.6:
  version "1.15.6"
  resolved "https://registry.yarnpkg.com/follow-redirects/-/follow-redirects-1.15.6.tgz#7f815c0cda4249c74ff09e95ef97c23b5fd0399b"
  integrity sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==

for-each@^0.3.3:
  version "0.3.3"
  resolved "https://registry.yarnpkg.com/for-each/-/for-each-0.3.3.tgz#69b447e88a0a5d32c3e7084f3f1710034b21376e"
  integrity sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==
  dependencies:
    is-callable "^1.1.3"

form-data@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/form-data/-/form-data-4.0.0.tgz#93919daeaf361ee529584b9b31664dc12c9fa452"
  integrity sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

fs-extra@^10.0.0:
  version "10.1.0"
  resolved "https://registry.yarnpkg.com/fs-extra/-/fs-extra-10.1.0.tgz#02873cfbc4084dde127eaa5f9905eef2325d1abf"
  integrity sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs@^0.0.1-security:
  version "0.0.1-security"
  resolved "https://registry.yarnpkg.com/fs/-/fs-0.0.1-security.tgz#8a7bd37186b6dddf3813f23858b57ecaaf5e41d4"
  integrity sha512-3XY9e1pP0CVEUCdj5BmfIZxRBTSDycnbqhIOGec9QYtmVH2fbLpj86CFWkrNOkt/Fvty4KZG5lTglL9j/gJ87w==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/function-bind/-/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

get-intrinsic@^1.1.3, get-intrinsic@^1.2.3, get-intrinsic@^1.2.4:
  version "1.2.4"
  resolved "https://registry.yarnpkg.com/get-intrinsic/-/get-intrinsic-1.2.4.tgz#e385f5a4b5227d449c3eabbad05494ef0abbeadd"
  integrity sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

gopd@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/gopd/-/gopd-1.0.1.tgz#29ff76de69dac7489b7c0918a5788e56477c332c"
  integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
  dependencies:
    get-intrinsic "^1.1.3"

graceful-fs@^4.1.6, graceful-fs@^4.2.0:
  version "4.2.11"
  resolved "https://registry.yarnpkg.com/graceful-fs/-/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

has-property-descriptors@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz#963ed7d071dc7bf5f084c5bfbe0d1b6222586854"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/has-proto/-/has-proto-1.0.3.tgz#b31ddfe9b0e6e9914536a6ab286426d0214f77fd"
  integrity sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==

has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/has-symbols/-/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"
  integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==

has-tostringtag@^1.0.0, has-tostringtag@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/has-tostringtag/-/has-tostringtag-1.0.2.tgz#2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc"
  integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/hasown/-/hasown-2.0.1.tgz#26f48f039de2c0f8d3356c223fb8d50253519faa"
  integrity sha512-1/th4MHjnwncwXsIW6QMzlvYL9kG5e/CpVvLRZe4XPa8TOUNbCELqmvhDmnkNsAjwaG4+I8gJJL0JBvTTLO9qA==
  dependencies:
    function-bind "^1.1.2"

ieee754@1.1.13:
  version "1.1.13"
  resolved "https://registry.yarnpkg.com/ieee754/-/ieee754-1.1.13.tgz#ec168558e95aa181fd87d37f55c32bbcb6708b84"
  integrity sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg==

ieee754@^1.1.13, ieee754@^1.1.4:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/ieee754/-/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==

inherits@^2.0.3:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

install@^0.13.0:
  version "0.13.0"
  resolved "https://registry.yarnpkg.com/install/-/install-0.13.0.tgz#6af6e9da9dd0987de2ab420f78e60d9c17260776"
  integrity sha512-zDml/jzr2PKU9I8J/xyZBQn8rPCAY//UOYNmR01XwNwyfhEWObo2SWfSl1+0tm1u6PhxLwDnfsT/6jB7OUxqFA==

ip-address@^9.0.5:
  version "9.0.5"
  resolved "https://registry.yarnpkg.com/ip-address/-/ip-address-9.0.5.tgz#117a960819b08780c3bd1f14ef3c1cc1d3f3ea5a"
  integrity sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g==
  dependencies:
    jsbn "1.1.0"
    sprintf-js "^1.1.3"

is-arguments@^1.0.4:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/is-arguments/-/is-arguments-1.1.1.tgz#15b3f88fda01f2a97fec84ca761a560f123efa9b"
  integrity sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-callable@^1.1.3:
  version "1.2.7"
  resolved "https://registry.yarnpkg.com/is-callable/-/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==

is-generator-function@^1.0.7:
  version "1.0.10"
  resolved "https://registry.yarnpkg.com/is-generator-function/-/is-generator-function-1.0.10.tgz#f1558baf1ac17e0deea7c0415c438351ff2b3c72"
  integrity sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==
  dependencies:
    has-tostringtag "^1.0.0"

is-typed-array@^1.1.3:
  version "1.1.13"
  resolved "https://registry.yarnpkg.com/is-typed-array/-/is-typed-array-1.1.13.tgz#d6c5ca56df62334959322d7d7dd1cca50debe229"
  integrity sha512-uZ25/bUAlUY5fR4OKT4rZQEBrzQWYV9ZJYGGsUmEJ6thodVJ1HX64ePQ6Z0qPWP+m+Uq6e9UugrE38jeYsDSMw==
  dependencies:
    which-typed-array "^1.1.14"

isarray@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==

jmespath@0.16.0:
  version "0.16.0"
  resolved "https://registry.yarnpkg.com/jmespath/-/jmespath-0.16.0.tgz#b15b0a85dfd4d930d43e69ed605943c802785076"
  integrity sha512-9FzQjJ7MATs1tSpnco1K6ayiYE3figslrXA72G2HQ/n76RzvYlofyi5QM+iX4YRs/pu3yzxlVQSST23+dMDknw==

js-md5@^0.7.3:
  version "0.7.3"
  resolved "https://registry.yarnpkg.com/js-md5/-/js-md5-0.7.3.tgz#b4f2fbb0b327455f598d6727e38ec272cd09c3f2"
  integrity sha512-ZC41vPSTLKGwIRjqDh8DfXoCrdQIyBgspJVPXHBGu4nZlAEvG3nf+jO9avM9RmLiGakg7vz974ms99nEV0tmTQ==

js-sha1@^0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/js-sha1/-/js-sha1-0.6.0.tgz#adbee10f0e8e18aa07cdea807cf08e9183dbc7f9"
  integrity sha512-01gwBFreYydzmU9BmZxpVk6svJJHrVxEN3IOiGl6VO93bVKYETJ0sIth6DASI6mIFdt7NmfX9UiByRzsYHGU9w==

js-yaml@3.14.1:
  version "3.14.1"
  resolved "https://registry.yarnpkg.com/js-yaml/-/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/jsbn/-/jsbn-1.1.0.tgz#b01307cb29b618a1ed26ec79e911f803c4da0040"
  integrity sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A==

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.yarnpkg.com/jsonfile/-/jsonfile-6.1.0.tgz#bc55b2634793c679ec6403094eb13698a6ec0aae"
  integrity sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

kareem@2.5.1:
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/kareem/-/kareem-2.5.1.tgz#7b8203e11819a8e77a34b3517d3ead206764d15d"
  integrity sha512-7jFxRVm+jD+rkq3kY0iZDJfsO2/t4BBPeEb2qKn2lR/9KhuksYk5hxzfRYWMPV8P/x2d0kHD306YyWLzjjH+uA==

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.yarnpkg.com/lodash.merge/-/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.yarnpkg.com/lru-cache/-/lru-cache-6.0.0.tgz#6d6fe6570ebd96aaf90fcad1dafa3b2566db3a94"
  integrity sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==
  dependencies:
    yallist "^4.0.0"

memory-pager@^1.0.2:
  version "1.5.0"
  resolved "https://registry.yarnpkg.com/memory-pager/-/memory-pager-1.5.0.tgz#d8751655d22d384682741c972f2c3d6dfa3e66b5"
  integrity sha512-ZS4Bp4r/Zoeq6+NLJpP+0Zzm0pR8whtGPf1XExKLJBAczGMnSi3It14OiNCStjQjM6NU1okjQGSxgEZN8eBYKg==

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12:
  version "2.1.35"
  resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

moment@^2.29.1:
  version "2.30.1"
  resolved "https://registry.yarnpkg.com/moment/-/moment-2.30.1.tgz#f8c91c07b7a786e30c59926df530b4eac96974ae"
  integrity sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==

mongodb-connection-string-url@^2.6.0:
  version "2.6.0"
  resolved "https://registry.yarnpkg.com/mongodb-connection-string-url/-/mongodb-connection-string-url-2.6.0.tgz#57901bf352372abdde812c81be47b75c6b2ec5cf"
  integrity sha512-WvTZlI9ab0QYtTYnuMLgobULWhokRjtC7db9LtcVfJ+Hsnyr5eo6ZtNAt3Ly24XZScGMelOcGtm7lSn0332tPQ==
  dependencies:
    "@types/whatwg-url" "^8.2.1"
    whatwg-url "^11.0.0"

mongodb@4.17.2:
  version "4.17.2"
  resolved "https://registry.yarnpkg.com/mongodb/-/mongodb-4.17.2.tgz#237c0534e36a3449bd74c6bf6d32f87a1ca7200c"
  integrity sha512-mLV7SEiov2LHleRJPMPrK2PMyhXFZt2UQLC4VD4pnth3jMjYKHhtqfwwkkvS/NXuo/Fp3vbhaNcXrIDaLRb9Tg==
  dependencies:
    bson "^4.7.2"
    mongodb-connection-string-url "^2.6.0"
    socks "^2.7.1"
  optionalDependencies:
    "@aws-sdk/credential-providers" "^3.186.0"
    "@mongodb-js/saslprep" "^1.1.0"

mongoose@^6.1.3:
  version "6.12.6"
  resolved "https://registry.yarnpkg.com/mongoose/-/mongoose-6.12.6.tgz#7474de5f125f88d372ac759116dc57aed5a22cb1"
  integrity sha512-VFxDnWj8esgswwplmpQYMT+lYcvuIhl76WDLz/vgp41/FOhBPM/n3GjyztK8R3r2ljsM6kudvKgqLhfcZEih1Q==
  dependencies:
    bson "^4.7.2"
    kareem "2.5.1"
    mongodb "4.17.2"
    mpath "0.9.0"
    mquery "4.0.3"
    ms "2.1.3"
    sift "16.0.1"

mpath@0.9.0:
  version "0.9.0"
  resolved "https://registry.yarnpkg.com/mpath/-/mpath-0.9.0.tgz#0c122fe107846e31fc58c75b09c35514b3871904"
  integrity sha512-ikJRQTk8hw5DEoFVxHG1Gn9T/xcjtdnOKIU1JTmGjZZlg9LST2mBLmcX3/ICIbgJydT2GOc15RnNy5mHmzfSew==

mquery@4.0.3:
  version "4.0.3"
  resolved "https://registry.yarnpkg.com/mquery/-/mquery-4.0.3.tgz#4d15f938e6247d773a942c912d9748bd1965f89d"
  integrity sha512-J5heI+P08I6VJ2Ky3+33IpCdAvlYGTSUjwTPxkAr8i8EoduPMBX2OY/wa3IKZIQl7MU4SbFk8ndgSKyB/cl1zA==
  dependencies:
    debug "4.x"

ms@2.1.2:
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/ms/-/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

ms@2.1.3:
  version "2.1.3"
  resolved "https://registry.yarnpkg.com/ms/-/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

obuf@~1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/obuf/-/obuf-1.1.2.tgz#09bea3343d41859ebd446292d11c9d4db619084e"
  integrity sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==

pg-int8@1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/pg-int8/-/pg-int8-1.0.1.tgz#943bd463bf5b71b4170115f80f8efc9a0c0eb78c"
  integrity sha512-WCtabS6t3c8SkpDBUlb1kjOs7l66xsGdKpIPZsg4wR+B3+u9UAum2odSsF9tnvxg80h4ZxLWMy4pRjOsFIqQpw==

pg-numeric@1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/pg-numeric/-/pg-numeric-1.0.2.tgz#816d9a44026086ae8ae74839acd6a09b0636aa3a"
  integrity sha512-BM/Thnrw5jm2kKLE5uJkXqqExRUY/toLHda65XgFTBTFYZyopbKjBe29Ii3RbkvlsMoFwD+tHeGaCjjv0gHlyw==

pg-protocol@*:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/pg-protocol/-/pg-protocol-1.6.0.tgz#4c91613c0315349363af2084608db843502f8833"
  integrity sha512-M+PDm637OY5WM307051+bsDia5Xej6d9IR4GwJse1qA1DIhiKlksvrneZOYQq42OM+spubpcNYEo2FcKQrDk+Q==

pg-types@^4.0.1:
  version "4.0.2"
  resolved "https://registry.yarnpkg.com/pg-types/-/pg-types-4.0.2.tgz#399209a57c326f162461faa870145bb0f918b76d"
  integrity sha512-cRL3JpS3lKMGsKaWndugWQoLOCoP+Cic8oseVcbr0qhPzYD5DWXK+RZ9LY9wxRf7RQia4SCwQlXk0q6FCPrVng==
  dependencies:
    pg-int8 "1.0.1"
    pg-numeric "1.0.2"
    postgres-array "~3.0.1"
    postgres-bytea "~3.0.0"
    postgres-date "~2.1.0"
    postgres-interval "^3.0.0"
    postgres-range "^1.1.1"

pify@^5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/pify/-/pify-5.0.0.tgz#1f5eca3f5e87ebec28cc6d54a0e4aaf00acc127f"
  integrity sha512-eW/gHNMlxdSP6dmG6uJip6FXN0EQBwm2clYYd8Wul42Cwu/DK8HEftzsapcNdYe2MfLiIwZqsDk2RDEsTE79hA==

possible-typed-array-names@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/possible-typed-array-names/-/possible-typed-array-names-1.0.0.tgz#89bb63c6fada2c3e90adc4a647beeeb39cc7bf8f"
  integrity sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==

postgres-array@~3.0.1:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/postgres-array/-/postgres-array-3.0.2.tgz#68d6182cb0f7f152a7e60dc6a6889ed74b0a5f98"
  integrity sha512-6faShkdFugNQCLwucjPcY5ARoW1SlbnrZjmGl0IrrqewpvxvhSLHimCVzqeuULCbG0fQv7Dtk1yDbG3xv7Veog==

postgres-bytea@~3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/postgres-bytea/-/postgres-bytea-3.0.0.tgz#9048dc461ac7ba70a6a42d109221619ecd1cb089"
  integrity sha512-CNd4jim9RFPkObHSjVHlVrxoVQXz7quwNFpz7RY1okNNme49+sVyiTvTRobiLV548Hx/hb1BG+iE7h9493WzFw==
  dependencies:
    obuf "~1.1.2"

postgres-date@~2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/postgres-date/-/postgres-date-2.1.0.tgz#b85d3c1fb6fb3c6c8db1e9942a13a3bf625189d0"
  integrity sha512-K7Juri8gtgXVcDfZttFKVmhglp7epKb1K4pgrkLxehjqkrgPhfG6OO8LHLkfaqkbpjNRnra018XwAr1yQFWGcA==

postgres-interval@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/postgres-interval/-/postgres-interval-3.0.0.tgz#baf7a8b3ebab19b7f38f07566c7aab0962f0c86a"
  integrity sha512-BSNDnbyZCXSxgA+1f5UU2GmwhoI0aU5yMxRGO8CdFEcY2BQF9xm/7MqKnYoM1nJDk8nONNWDk9WeSmePFhQdlw==

postgres-range@^1.1.1:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/postgres-range/-/postgres-range-1.1.4.tgz#a59c5f9520909bcec5e63e8cf913a92e4c952863"
  integrity sha512-i/hbxIE9803Alj/6ytL7UHQxRvZkI9O4Sy+J3HGc4F4oo/2eQAjTSNJ0bfxyse3bH0nuVesCk+3IRLaMtG3H6w==

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz#e102f16ca355424865755d2c9e8ea4f24d58c3e2"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"
  integrity sha512-RofWgt/7fL5wP1Y7fxE7/EmTLzQVnB0ycyibJ0OOHIlJqTNzglYFxVwETOcIoJqJmpDXJ9xImDv+Fq34F/d4Dw==

punycode@^2.1.1:
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==

querystring@0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/querystring/-/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"
  integrity sha512-X/xY82scca2tau62i9mDyU9K+I+djTMUsvwf7xnUX5GLvVzgJybOJf4Y6o9Zx3oJK/LSXg5tTZBjwzqVPaPO2g==

sax@1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/sax/-/sax-1.2.1.tgz#7b8e656190b228e81a66aea748480d828cd2d37a"
  integrity sha512-8I2a3LovHTOpm7NV5yOyO8IHqgVsfK4+UuySrXU8YXkSRX7k6hCV9b3HrkKCr3nMpgj+0bmocaJJWpvp1oc7ZA==

sax@>=0.6.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/sax/-/sax-1.3.0.tgz#a5dbe77db3be05c9d1ee7785dbd3ea9de51593d0"
  integrity sha512-0s+oAmw9zLl1V1cS9BtZN7JAd0cW5e0QH4W3LWEK6a4LaLEA2OTpGYWDY+6XasBLtz6wkm3u1xRw95mRuJ59WA==

semver@^5.4.1:
  version "5.7.2"
  resolved "https://registry.yarnpkg.com/semver/-/semver-5.7.2.tgz#48d55db737c3287cd4835e17fa13feace1c41ef8"
  integrity sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==

semver@^7.5.3:
  version "7.6.0"
  resolved "https://registry.yarnpkg.com/semver/-/semver-7.6.0.tgz#1a46a4db4bffcccd97b743b5005c8325f23d4e2d"
  integrity sha512-EnwXhrlwXMk9gKu5/flx5sv/an57AkRplG3hTK68W7FRDN+k+OWBj65M7719OkA82XLBxrcX0KSHj+X5COhOVg==
  dependencies:
    lru-cache "^6.0.0"

set-function-length@^1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/set-function-length/-/set-function-length-1.2.1.tgz#47cc5945f2c771e2cf261c6737cf9684a2a5e425"
  integrity sha512-j4t6ccc+VsKwYHso+kElc5neZpjtq9EnRICFZtWyBsLojhmeF/ZBd/elqm22WJh/BziDe/SBiOeAt0m2mfLD0g==
  dependencies:
    define-data-property "^1.1.2"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.3"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.1"

shimmer@^1.2.0:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/shimmer/-/shimmer-1.2.1.tgz#610859f7de327b587efebf501fb43117f9aff337"
  integrity sha512-sQTKC1Re/rM6XyFM6fIAGHRPVGvyXfgzIDvzoq608vM+jeyVD0Tu1E6Np0Kc2zAIFWIj963V2800iF/9LPieQw==

sift@16.0.1:
  version "16.0.1"
  resolved "https://registry.yarnpkg.com/sift/-/sift-16.0.1.tgz#e9c2ccc72191585008cf3e36fc447b2d2633a053"
  integrity sha512-Wv6BjQ5zbhW7VFefWusVP33T/EM0vYikCaQ2qR8yULbsilAT8/wQaXvuQ3ptGLpoKx+lihJE3y2UTgKDyyNHZQ==

smart-buffer@^4.2.0:
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/smart-buffer/-/smart-buffer-4.2.0.tgz#6e1d71fa4f18c05f7d0ff216dd16a481d0e8d9ae"
  integrity sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==

socks@^2.7.1:
  version "2.8.1"
  resolved "https://registry.yarnpkg.com/socks/-/socks-2.8.1.tgz#22c7d9dd7882649043cba0eafb49ae144e3457af"
  integrity sha512-B6w7tkwNid7ToxjZ08rQMT8M9BJAf8DKx8Ft4NivzH0zBUfd6jldGcisJn/RLgxcX3FPNDdNQCUEMMT79b+oCQ==
  dependencies:
    ip-address "^9.0.5"
    smart-buffer "^4.2.0"

sparse-bitfield@^3.0.3:
  version "3.0.3"
  resolved "https://registry.yarnpkg.com/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz#ff4ae6e68656056ba4b3e792ab3334d38273ca11"
  integrity sha512-kvzhi7vqKTfkh0PZU+2D2PIllw2ymqJKujUcyPMd9Y75Nv4nPbGJZXNhxsgdQab2BmlDct1YnfQCguEvHr7VsQ==
  dependencies:
    memory-pager "^1.0.2"

sprintf-js@^1.1.3:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/sprintf-js/-/sprintf-js-1.1.3.tgz#4914b903a2f8b685d17fdf78a70e917e872e444a"
  integrity sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==

stack-chain@^1.3.7:
  version "1.3.7"
  resolved "https://registry.yarnpkg.com/stack-chain/-/stack-chain-1.3.7.tgz#d192c9ff4ea6a22c94c4dd459171e3f00cea1285"
  integrity sha512-D8cWtWVdIe/jBA7v5p5Hwl5yOSOrmZPWDPe2KxQ5UAGD+nxbxU0lKXA4h85Ta6+qgdKVL3vUxsbIZjc1kBG7ug==

strnum@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/strnum/-/strnum-1.0.5.tgz#5c4e829fe15ad4ff0d20c3db5ac97b73c9b072db"
  integrity sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==

tr46@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/tr46/-/tr46-3.0.0.tgz#555c4e297a950617e8eeddef633c87d4d9d6cbf9"
  integrity sha512-l7FvfAHlcmulp8kr+flpQZmVwtu7nfRV7NZujtN0OqES8EL4O4e0qqzL0DC5gAvx/ZC/9lk6rhcUwYvkBnBnYA==
  dependencies:
    punycode "^2.1.1"

tslib@^1.11.1:
  version "1.14.1"
  resolved "https://registry.yarnpkg.com/tslib/-/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==

tslib@^2.3.1, tslib@^2.5.0:
  version "2.6.2"
  resolved "https://registry.yarnpkg.com/tslib/-/tslib-2.6.2.tgz#703ac29425e7b37cd6fd456e92404d46d1f3e4ae"
  integrity sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q==

undici-types@~5.26.4:
  version "5.26.5"
  resolved "https://registry.yarnpkg.com/undici-types/-/undici-types-5.26.5.tgz#bcd539893d00b56e964fd2657a4866b221a65617"
  integrity sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/universalify/-/universalify-2.0.1.tgz#168efc2180964e6386d061e094df61afe239b18d"
  integrity sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==

url@0.10.3:
  version "0.10.3"
  resolved "https://registry.yarnpkg.com/url/-/url-0.10.3.tgz#021e4d9c7705f21bbf37d03ceb58767402774c64"
  integrity sha512-hzSUW2q06EqL1gKM/a+obYHLIO6ct2hwPuviqTTOcfFVc61UbfJ2Q32+uGL/HCPxKqrdGB5QUwIe7UqlDgwsOQ==
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

util@^0.12.4:
  version "0.12.5"
  resolved "https://registry.yarnpkg.com/util/-/util-0.12.5.tgz#5f17a6059b73db61a875668781a1c2b136bd6fbc"
  integrity sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

uuid-by-string@^3.0.4:
  version "3.0.7"
  resolved "https://registry.yarnpkg.com/uuid-by-string/-/uuid-by-string-3.0.7.tgz#3c9b7e60c3d4a1bf5da5dfb2601721acc813d8fc"
  integrity sha512-9xf+GAcwzLLGL2Z2Vb7hmi7jWIAKSiuaI5cLFsKw1IIlm7S5VpqvdJ5S7N36hqdy0v7DAwnnENJVAeev57/H1A==
  dependencies:
    js-md5 "^0.7.3"
    js-sha1 "^0.6.0"

uuid@8.0.0:
  version "8.0.0"
  resolved "https://registry.yarnpkg.com/uuid/-/uuid-8.0.0.tgz#bc6ccf91b5ff0ac07bbcdbf1c7c4e150db4dbb6c"
  integrity sha512-jOXGuXZAWdsTH7eZLtyXMqUb9EcWMGZNbL9YcGBJl4MH4nrxHmZJhEHvyLFrkxo+28uLb/NYRcStH48fnD0Vzw==

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://registry.yarnpkg.com/uuid/-/uuid-8.3.2.tgz#80d5b5ced271bb9af6c445f21a1a04c606cefbe2"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

uuid@^9.0.1:
  version "9.0.1"
  resolved "https://registry.yarnpkg.com/uuid/-/uuid-9.0.1.tgz#e188d4c8853cc722220392c424cd637f32293f30"
  integrity sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==

validator@^13.7.0:
  version "13.11.0"
  resolved "https://registry.yarnpkg.com/validator/-/validator-13.11.0.tgz#23ab3fd59290c61248364eabf4067f04955fbb1b"
  integrity sha512-Ii+sehpSfZy+At5nPdnyMhx78fEoPDkR2XW/zimHEL3MyGJQOCQ7WeP20jPYRz7ZCpcKLB21NxuXHF3bxjStBQ==

webidl-conversions@^7.0.0:
  version "7.0.0"
  resolved "https://registry.yarnpkg.com/webidl-conversions/-/webidl-conversions-7.0.0.tgz#256b4e1882be7debbf01d05f0aa2039778ea080a"
  integrity sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==

whatwg-url@^11.0.0:
  version "11.0.0"
  resolved "https://registry.yarnpkg.com/whatwg-url/-/whatwg-url-11.0.0.tgz#0a849eebb5faf2119b901bb76fd795c2848d4018"
  integrity sha512-RKT8HExMpoYx4igMiVMY83lN6UeITKJlBQ+vR/8ZJ8OCdSiN3RwCq+9gH0+Xzj0+5IrM6i4j/6LuvzbZIQgEcQ==
  dependencies:
    tr46 "^3.0.0"
    webidl-conversions "^7.0.0"

which-typed-array@^1.1.14, which-typed-array@^1.1.2:
  version "1.1.14"
  resolved "https://registry.yarnpkg.com/which-typed-array/-/which-typed-array-1.1.14.tgz#1f78a111aee1e131ca66164d8bdc3ab062c95a06"
  integrity sha512-VnXFiIW8yNn9kIHN88xvZ4yOWchftKDsRJ8fEPacX/wl1lOvBrhsJ/OeJCXq7B0AaijRuqgzSKalJoPk+D8MPg==
  dependencies:
    available-typed-arrays "^1.0.6"
    call-bind "^1.0.5"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.1"

xml2js@0.6.2:
  version "0.6.2"
  resolved "https://registry.yarnpkg.com/xml2js/-/xml2js-0.6.2.tgz#dd0b630083aa09c161e25a4d0901e2b2a929b499"
  integrity sha512-T4rieHaC1EXcES0Kxxj4JWgaUQHDk+qwHcYOCFHfiwKz7tOVPLq7Hjq9dM1WCMhylqMEfP7hMcOIChvotiZegA==
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xml2js@^0.4.23:
  version "0.4.23"
  resolved "https://registry.yarnpkg.com/xml2js/-/xml2js-0.4.23.tgz#a0c69516752421eb2ac758ee4d4ccf58843eac66"
  integrity sha512-ySPiMjM0+pLDftHgXY4By0uswI3SPKLDw/i3UXbnO8M/p28zqexCUoPmQFrYD+/1BzhGJSs2i1ERWKJAtiLrug==
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder2@^3.0.2:
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/xmlbuilder2/-/xmlbuilder2-3.1.1.tgz#b977ef8a6fb27a1ea7ffa7d850d2c007ff343bc0"
  integrity sha512-WCSfbfZnQDdLQLiMdGUQpMxxckeQ4oZNMNhLVkcekTu7xhD4tuUDyAPoY8CwXvBYE6LwBHd6QW2WZXlOWr1vCw==
  dependencies:
    "@oozcitak/dom" "1.15.10"
    "@oozcitak/infra" "1.0.8"
    "@oozcitak/util" "8.3.8"
    js-yaml "3.14.1"

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "https://registry.yarnpkg.com/xmlbuilder/-/xmlbuilder-11.0.1.tgz#be9bae1c8a046e76b31127726347d0ad7002beb3"
  integrity sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/yallist/-/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==
