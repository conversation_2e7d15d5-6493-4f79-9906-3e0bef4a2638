const AWS = require("aws-sdk"); 
const lambda = new AWS.Lambda(); 
const centaur = require("@mediality/centaur");
const helper = require("./helper");

const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

exports.handler = async (event) => {
    console.log(`EVENT: ${JSON.stringify(event)}`);
    let con;
    try {
        let today = new Date();
        let yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        yesterday.setHours(0, 0, 0, 0);  // Set to beginning of yesterday
        console.log('yesterday', yesterday);
        con = await helper.openDBConnection();
        const processedMeetings = await centaur.processed_meetings
          .find({
            meetingDate: { $gte: yesterday },
            meetingStage: {
              $nin: ["Results", "InterimResults", "Abandoned", "DELETED","Postponed"],
            },
            validated: true,
            meetingErrorCount: { $lt: 1 }
          }, { _id: 1 })
          .lean();
        console.log("processedMeetings:", processedMeetings);

        // Function to invoke Lambda for a single meeting
        const invokeLambda = async (meeting) => {
            console.log(meeting._id);
            let payload = {
                queryStringParameters: {
                    id: meeting._id,
                    if_validated: false,
                    attempt_auto_delivery: true,
                },
            };
            let params = {
                FunctionName: "MrCenLambdaDeliveryFunction-" + process.env.ENV,
                InvocationType: "Event",
                Payload: JSON.stringify(payload),
            };
            await lambda.invoke(params).promise(); 
            await delay(5000);
        };

        for (let meeting of processedMeetings) {
            await invokeLambda(meeting);
        }

    } catch (error) {
        console.log("Error:", error);
    } finally {
        if (con) await helper.closeDBConnection(con);
    }
    return {
        statusCode: 200,
        body: JSON.stringify("Process completed successfully"),
    };
};
