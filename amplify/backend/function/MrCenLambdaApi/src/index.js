const mongoose = require("mongoose");
const getUuid = require("uuid-by-string");
const helper = require("./library/helper");
const AWSXRay = require("aws-xray-sdk-core");
const api = require("./library/api");
const horseAPI = require("./library/crud_horse");
const trainerAPI = require("./library/crud_trainer");
const trackAPI = require("./library/crud_track");
const jockeyAPI = require("./library/crud_jockey");
const clubAPI = require("./library/crud_club");
const fileAPI = require("./library/file_handler");
const formIndexAPI = require("./library/curd_form_index");
const horseFormAPI = require("./library/crud_horse_form");
const processedMeetingAPI = require("./library/crud_processedmeeting");
const commentsAPI = require("./library/curd_comment_rules");
const load_end = require("./library/load_end");
const stats = require("./library/stats");
const wrap = require("@dazn/lambda-powertools-pattern-basic");

var con;

AWSXRay.enableAutomaticMode();

const apiHandler = async (req) => {
  var data;

  // Handler uses switch case on the api query path to decide how to process the call
  switch (req.path) {
    case "/horse":
      // for accessing and manipulating the horses collection
      var segment = new AWSXRay.Segment("Horse API Handler");
      console.log("Inside Horse Handler");
      data = await horseAPI.horseAPI(req);
      segment.close();
      return { data: data };

    case "/trainer":
      // for accessing and manipulating the trainers collection
      var segment = new AWSXRay.Segment("Trainer API Handler");
      console.log("Inside Trainer Handler");
      data = await trainerAPI.trainerAPI(req);
      segment.close();
      return { data: data };

    case "/jockey":
      // for accessing and manipulating the jockeys collection
      var segment = new AWSXRay.Segment("Jockey API Handler");
      console.log("Inside Jockey Handler");
      data = await jockeyAPI.jockeyAPI(req);
      segment.close();
      return { data: data };

    case "/track":
      // for accessing and manipulating the tracks collection
      var segment = new AWSXRay.Segment("Track API Handler");
      console.log("Inside Track Handler");
      data = await trackAPI.trackAPI(req);
      segment.close();
      return { data: data };

    case "/club":
      // for accessing and manipulating the clubs collection
      var segment = new AWSXRay.Segment("Club API Handler");
      console.log("Inside Club Handler");
      data = await clubAPI.clubAPI(req);
      segment.close();
      return { data: data };

    case "/horse-form":
      // for accessing and manipulating the form collection
      var segment = new AWSXRay.Segment("HorseForm API Handler");
      console.log("Inside Horse Form Handler");
      console.log(req);
      data = await horseFormAPI.horseFormAPI(req);
      segment.close();
      return { data: data };

    case "/forms":
      // this only does a "GET" on the form collection, need to investigate if/where it is used and why
      var segment = new AWSXRay.Segment("Forms API Handler");
      console.log("Inside Forms Handler");
      data = await api.horseFormsAPI(req);
      segment.close();
      return { data: data };

    case "/all":
      // this does a "GET" to retreive the whole collections, as set: tracks, clubs, countries, jockeys, trainers, race_classes
      var segment = new AWSXRay.Segment("All API Handler");
      console.log("Inside All Handler");
      data = await api.allAPI(req);
      segment.close();
      return { data: data };

    case "/inputmeetings":
      // Pretty sure this is unused, its set to "GET" the last 50 input files, but it wouldnt even work very well. investigate
      var segment = new AWSXRay.Segment("Input Meetings API Handler");
      console.log("Inside InputMeetings Handler");
      data = await api.inputMeetingsAPI(req);
      segment.close();
      return { data: data };

    case "/processedmeetings":
      // for accessing and manipulating the processed_meetings collection
      var segment = new AWSXRay.Segment("Processed Meetings API Handler");
      console.log("Inside ProcessedMeetings Handler");
      data = await processedMeetingAPI.processedMeetingAPI(req);
      segment.close();
      return { data: data };

    case "/meeting":
      // for "GET" multiple meetings from the processed_meetings collection
      var segment = new AWSXRay.Segment("Meeting API Handler");
      console.log("Inside Meeting Handler");
      data = await api.meetingAPI(req);
      segment.close();
      return { data: data };

    case "/registrations":
      // to get an entry from the registrations collection by date
      var segment = new AWSXRay.Segment("registrations API Handler");
      console.log("Inside Rego API Handler");
      data = await api.registrationsAPI(req);
      segment.close();
      return { data: data };

    case "/meeting-status":
      // Definitely not being used, review and remove. DO NOT USE.
      var segment = new AWSXRay.Segment("Meeting Status API Handler");
      console.log("'Meeting Status API Handler - You shouldnt be here");
      // data = await api.meetingStatusAPI(req)
      data = "Call failed, bad gateway.";
      segment.close();
      return { data: data };

    case "/meeting-horses-forms":
      var segment = new AWSXRay.Segment("Meeting Horses Forms API Handler");
      console.log("Inside Meeting Horses Forms Handler");
      // data = await api.meetingHorsesFormsAPI(req)
      data = "Call failed, bad gateway.";
      segment.close();
      return { data: data };

    case "/queues":
      // Retreived the failed/succeeded Processing and dead letter queue items, we havent used this ever.
      var segment = new AWSXRay.Segment("Queues API Handler");
      console.log("Inside Queues Handler");
      data = await api.queuesAPI(req);
      segment.close();
      return { data: data };

    case "/s3download":
      // Enables the download function from the inputs screen. Generates the unique download url for an S3 key path
      var segment = new AWSXRay.Segment("S3Downoad API Handler");
      console.log("Inside S3Download Handler");
      data = await api.getS3Url(req);
      segment.close();
      return { data: data };

    case "/generatestats":
      // Starts the 'generate stats' part of the meeting post load, no current way to manually trigger
      var segment = new AWSXRay.Segment("Generate Stats API Handler");
      console.log("Inside Generate Stats Handler");
      data = await api.generateStatsAPI(req);
      segment.close();
      return { data: data };

    case "/gen-meeting-error-count":
      // Counts the errors within a meeting to display at meeting level
      var segment = new AWSXRay.Segment(
        "Generate MeetingErrorCount API Handler"
      );
      console.log("Inside Generate MeetingErrorCount Handler");
      data = await api.generateMeetingErrorAPI(req);
      segment.close();
      return { data: data };

    case "/compare-meeting":
      // Starts the Load end sequence, which includes the meeting comparison, race verify and automated distribution
      // This needs to be its own lambda, probably as part of a new step function
      var segment = new AWSXRay.Segment("Compare Meeting API Handler");
      console.log("Compare Meeting Handler");
      data = await load_end.compareMeetingAPI(req);
      segment.close();
      return { data: data };

    case "/changelog":
      // for retrieving the meeting changelogs from the changelog collection
      var segment = new AWSXRay.Segment("Retrieve changelog API Handler");
      console.log("Retrieve changelog Handler");
      data = await api.getChangelog(req);
      segment.close();
      return { data: data };

    case "/locks":
      // for checking what meetings are claimed and what meetings are errored, by date
      var segment = new AWSXRay.Segment("Check meeting locks API Handler");
      console.log("Check meeting locks Handler");
      data = await api.checkMeetingLocks(req);
      segment.close();
      return { data: data };

    case "/claim":
      // for claiming or saving a meeting via the frontend buttons
      var segment = new AWSXRay.Segment("Claim meeting API Handler");
      console.log("Claim meeting Handler");
      data = await api.claimMeeting(req);
      segment.close();
      return { data: data };

    case "/transfer-meeting":
      // to activate the transfer meeting functionality from the frontend
      var segment = new AWSXRay.Segment("Transfer Meeting API Handler");
      console.log("Inside Transfer Meeting Handler");
      data = await api.transferMeetingAPI(req);
      segment.close();
      return { data: data };

    case "/stats":
      // for generation of 1y stats for a track, to expand later
      var segment = new AWSXRay.Segment("Meeting API Handler");
      console.log("Inside Stats Handler");
      data = await stats.handleStatsGen(req);
      segment.close();
      return { data: data };

    case "/fileHandler":
      // Downloads a file by the unique S3 keypath
      var segment = new AWSXRay.Segment("fileHandler API Handler");
      console.log("Inside fileHandler Handler");
      data = await fileAPI.fileAPI(req);
      segment.close();
      return { data: data };
    case "/formIndex":
      // form_index curd operations
      var segment = new AWSXRay.Segment("form_index API Handler");
      console.log("Inside form_index Handler");
      data = await formIndexAPI.formIndexAPI(req);
      segment.close();
      return { data: data };
    case "/comments":
      // comments curd operations
      var segment = new AWSXRay.Segment("comments API Handler");
      console.log("Inside comments Handler");
      data = await commentsAPI.commentsAPI(req);
      segment.close();
      return { data: data };
    default:
      console.log("no api path found");
      console.log(req);
      return { msg: "No API Handler" };
  }
};

exports.handler = wrap(async (event) => {
  // make sure the database connection is active for the duration of the API call
  try {
    con = await helper.openDBConnection();
    var req = {
      path: event.path,
      method: event.httpMethod,
      params: event.queryStringParameters,
      body: event.body,
    };

    var body = await apiHandler(req);

    const response = {
      statusCode: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "*",
      },
      body: JSON.stringify(body),
    };
    return response;
  } catch (err) {
    console.log("Error in Handler: " + err);
    await helper.closeDBConnection(con);
  } finally {
    await helper.closeDBConnection(con);
  }
});

// const localTesting = async (event) => {
//     con = await helper.openDBConnection('local')
//     process.env.ENV = 'local'
//     mongoose.set("debug", true)
//     var meeting_req = {
//         "path": "/processedmeetings",
//         "method": "PUT",
//         // "params": {
//         //   "date": "2021-11-13"
//         // },
//         "body": {
//             "level": "processed_meeting",
//             "meetingId": "dabae74f-d6a2-5162-9334-e524a0ec02b4",
//             "raceIndex": "0",
//             "stage": "R",
//             "rail_position": "+10m 1200m-W/Post, +4m 2000m-1800m, +7m Remainder. Sectional 613m",
//             "tab_indicator": "T",
//             "dual_track": "N"

//         }
//     }

//     var race_req = {
//         "path": "/processedmeetings",
//         "method": "PUT",
//         "body": {
//             "level": "processed_race",
//             "raceIndex": "0",
//             "meetingId": "dabae74f-d6a2-5162-9334-e524a0ec02b4",
//             "number": "11",
//             "name": 'RaceName', // Store in InputData
//             "distance": "1100",
//             "restrictions_age": "33yo",
//             "restrictions_jockey": "AApprentices Can Claim",
//             "restrictions_sex": "nno",
//             "weight_type": "HHandicap",
//             "min_hcp_weight": "554",
//             "class": "BBenchmark 68",
//             "race_type": "FFlat",
//             "start_time": "33am"
//         }
//     }

//     var horse_req = {
//         "path": "/processedmeetings",
//         "method": "PUT",
//         "body": {
//             "level": "processed_horse",
//             "raceIndex": "0",
//             "horseIndex": "0",
//             "meetingId": "dabae74f-d6a2-5162-9334-e524a0ec02b4",
//             "weights_allocated": "11",
//             "weights_total": "1100",
//             "weights_penalty": "33yo",
//             "jockeyId": "16248",
//             "jockey_allowance_weight": "123",
//             "jockey_riding_weight": "123",
//             "barrier_number": "55",
//             "colours": "colours"
//         }
//     }

//     var race_req_inputData = {
//         "path": "/processedmeetings",
//         "method": "PUT",
//         "body": {
//             "level": "input_race",
//             "raceIndex": "0",
//             "meetingId": "ed497b6a-b006-5f84-96d7-bcbcdf8b7166",
//             "name": 'RaceName',
//         }
//     }

//     var horse_req_inputData = {
//         "path": "/processedmeetings",
//         "method": "PUT",
//         "body": {
//             "level": "input_horse",
//             "raceIndex": "0",
//             "horseIndex": "0",
//             "meetingId": "dabae74f-d6a2-5162-9334-e524a0ec02b4",
//             "tip": "11",
//             "market": "1100",
//             "comment": "33yo",
//             "colours": "colours"
//         }
//     }

//     var transfer_meeting = {
//         "path": "/transfer-meeting",
//         "method": "GET",
//         "params": {
//             "source": "6243a798ee8a4d2f8c839841",
//             "destination": "6243a88aee8a4d2f8c839842"
//         },
//     }

//     var data = await api.transferMeetingAPI(transfer_meeting)

//     console.log(data)
//     await helper.closeDBConnection(con)
// };

// const fs = require('fs');
// var sf_input = JSON.parse(fs.readFileSync('sample/event_getHorseData.json'));
//localTesting();
