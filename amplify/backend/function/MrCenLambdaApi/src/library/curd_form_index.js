const moment = require("moment");
const centaur = require("@mediality/centaur");

/**
 * <PERSON>les HTTP GET requests to fetch track data based on the request parameters.
 * Delegates data retrieval to the `formIndexRead` function.
 * @param {Object} req - An object representing the HTTP request, containing the method and parameters.
 * @returns {Promise<Object>} - Fetched data or an error message if the request method is not supported.
 */
const formIndexAPI = async (req) => {
  switch (req.method) {
    case "GET":
      console.log("Inside Track GET Handler");
      return await formIndexRead(req);
    default:
      return { msg: "No API Handler" };
  }
};

const formIndexRead = async (payload) => {
  console.log("Inside formIndexRead");
  console.log(payload);
  let response = [];
  if (payload.params && "meeting_date" in payload.params && "track_id" in payload.params) {
    const formattedDate = moment(payload.params.meeting_date, "YYYY-MM-DD")
      .startOf("day")
      .toDate();
    const data = await centaur.form_index
      .find({
        track_id: Number(payload.params.track_id),
        meeting_date: formattedDate,
      })
      .lean();
    if (data && data.length > 0) {
      const promises = data.map(async (item) => {
        try {
          const formDoc = await centaur.form.findOne({ horse_id: item.horse_id }).lean();
          if (formDoc && formDoc.form) {
            const race = formDoc.form.find(
              (f) =>
                moment(f.meeting_date).startOf("day").toDate().toISOString() === formattedDate.toISOString() &&
                f.track["@_id"] === Number(payload.params.track_id)
            );
            if (race) {
              return {
                horse_name: formDoc.horse_name,
                horse_id: formDoc.horse_id,
                race_number: race.race["@_number"],
                race_name: race.race["@_name"],
                finish_position: race.finish_position,
              };
            }
          }
        } catch (error) {
          console.error(`Error processing horse_id ${item.horse_id}:`, error);
        }
        return null;
      });
      response = (await Promise.all(promises)).filter(result => result !== null);
    }
  }
  return response;
};

module.exports = {
  formIndexAPI,
  formIndexRead,
};
