const helper = require("./helper")
const moment = require('moment')
// Import Models
const centaur = require('@mediality/centaur')

const handleStatsGen = async (payload) =>{
    var data = {}
    if (payload.params){
        if (payload.params.id){
            data = await generateTrackStats(payload.params.id)
        }
    }
    return data
}

const generateTrackStats = async (meetingID) =>{
    var data = {}
    var meeting = await centaur.processed_meetings.findOne({_id:meetingID}).lean()
    if (meeting){
        var barrierdistances = {}
        var fav = {win:0, not_win:0}
        var track_id = meeting.processedMeetingData.meeting.track['@_id']
        data.track = await centaur.tracks.findOne({TRK_TRACK_DB_ID:track_id}).lean()
        var fromDate = moment(meeting.meetingDate).subtract(3, 'years')
        var trackwins3y = await centaur.form_index.find({'$and':[{meeting_date:{$gt:fromDate}},{track_id:track_id},{finish_pos:1},{weight_carried:{$gt:0}}]}).lean()
        for (item of trackwins3y){
            var horseForm = await centaur.form.findOne({horse_id:item.horse_id}).lean()
            if (horseForm.form) for (race of horseForm.form){
                if (race.event_id == item.event_id) {
                    if (!(barrierdistances[(race.distance['@_metres']).toString()])) {
                        barrierdistances[(race.distance['@_metres']).toString()] = {}
                        barrierdistances[(race.distance['@_metres']).toString()].total = 0
                    }
                    if (!(barrierdistances[(race.distance['@_metres']).toString()][race.barrier])) barrierdistances[(race.distance['@_metres']).toString()][race.barrier] = 0 
                    barrierdistances[(race.distance['@_metres']).toString()][race.barrier]++
                    barrierdistances[(race.distance['@_metres']).toString()].total++
                    
                    if (race.favourite_indicator){
                        fav.win++
                        if (!(barrierdistances[(race.distance['@_metres']).toString()].fav)) barrierdistances[(race.distance['@_metres']).toString()].fav = 0 
                        barrierdistances[(race.distance['@_metres']).toString()].fav++
                    } else {
                        fav.not_win++
                    }
                }
            }
        }
        data.barriers = barrierdistances
        data.favourites = fav
        var meetingjox = {}
        var meetingtrains = {}
        var meetingtrains = {}
        var trainerchanges = []
        var fromDate1y = moment(meeting.meetingDate).subtract(1, 'years')
        console.log(fromDate1y,track_id)
        for (race of meeting.processedMeetingData.meeting.races.race){
            for (horse of race.horses.horse){
                if (horse.jockey && horse.jockey['@_id'] && !(meetingjox[(horse.jockey['@_id']).toString()])) meetingjox[(horse.jockey['@_id']).toString()] = {"@_name":horse.jockey['@_name'],"wins":0}
                if (horse.trainer && horse.trainer['@_id'] && !(meetingtrains[(horse.trainer['@_id']).toString()])) meetingtrains[(horse.trainer['@_id']).toString()]  = {"@_name":horse.trainer['@_name'],"wins":0}
                if (horse.trainer && horse.trainer['@_id'] && horse.trainer['@_previous_trainer'] && horse.trainer['@_previous_trainer_id']) trainerchanges.push({"race":race['@_number'],"horse":horse.tab_number,trainer_name:horse.trainer['@_name'],previous_trainer_name:horse.trainer['@_previous_trainer'] })
            } 
        }
        for (jockey_id in meetingjox) {
            var query = {'$and':[{meeting_date:{$gt:fromDate1y}},{track_id:track_id},{jockey_id:jockey_id},{finish_pos:1},{weight_carried:{$gt:0}}]}
            var jockeywins =  await centaur.form_index.find(query).count().lean()
            meetingjox[jockey_id].wins = jockeywins
        }
        for (trainer_id in meetingtrains) {
            var query = {'$and':[{meeting_date:{$gt:fromDate1y}},{track_id:track_id},{trainer_id: trainer_id},{finish_pos:1},{weight_carried:{$gt:0}}]}
            var trainerwins =  await centaur.form_index.find(query).count().lean()
            meetingtrains[trainer_id].wins = trainerwins
        }
        data.jockeys = meetingjox
        data.trainers = meetingtrains
        data.trainer_changes = trainerchanges
    }

    return data
}

module.exports = {
    handleStatsGen,
    generateTrackStats
};
