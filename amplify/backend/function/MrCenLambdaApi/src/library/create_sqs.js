const AWS = require('aws-sdk');

/**
 * Create an SQS Queue
 * @param {string} queueName - Name of the queue to create.
 * @param {string} region - AWS region for the SQS service.
 * @returns {Promise} - Promise object represents the operation result.
 */
const createQueue = (queueName, region) => {
  return new Promise((resolve, reject) => {
    // Set the AWS region
    AWS.config.update({ region });

    // Create an SQS service object
    const sqs = new AWS.SQS({ apiVersion: '2012-11-05' });

    const params = {
      QueueName: queueName,
      Attributes: {
        'FifoQueue': 'true',
      }
    };

    // Create the SQS queue
    sqs.createQueue(params, (err, data) => {
      if (err) {
        console.error("Error", err);
        reject(err);
      } else {
        console.log("Queue Created Successfully", data.QueueUrl);
        resolve(data.QueueUrl);
      }
    });
  });
};

module.exports = {
  createQueue
};
