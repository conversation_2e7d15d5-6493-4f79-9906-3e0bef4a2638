const mongoose = require("mongoose");
const moment = require("moment");
const helper = require("./helper");
const uuid = require("uuid");
const centaur = require("@mediality/centaur");
const results = require("./results");
const AWS = require("aws-sdk");

const processedMeetingAPI = async (req) => {
  console.log(req);
  switch (req.method) {
    case "GET":
      console.log("Inside processedMeeting GET Handler");
      data = await processedMeetingRead(req);
      return data;
      break;
    case "PUT":
      console.log("Inside processedMeeting PUT Handler");
      data = await processedMeetingUpdate(req);
      return data;
      break;
    case "POST":
      console.log("Inside processedMeeting POST Handler");
      data = await processedMeetingReload(req);
      return data;
      break;
    case "DELETE":
      console.log("Inside processedMeeting DELETE Handler");
      data = await processedMeetingDelete(req);
      return data;
      break;
    default:
      if (req.body) {
        var body = req.body;
        if (body.level == "approve_results") {
          var meetingId = body.meetingId;
          var theResult = await results.genResultsByMeetingId(meetingId);
          console.log(`Race Results for ${meetingId} Updated: `);
          console.log(theResult);
          return { msg: "Results internally approved" };
        }
      }
      console.log("no method found");
      console.log(req);
      return { msg: "No API Handler" };
  }
};

const processedMeetingRead = async (payload) => {
  var response = "";
  if (payload.params) {
    if (payload.params.date) {
      var date = new Date(payload.params.date);
      if (date) {
        var data = await centaur.processed_meetings
          .find({ meetingDate: date })
          .sort({ meetingDate: 1 })
          .select("-processedMeetingData.meeting.races")
          .select("-inputMeetingData")
          .lean();
        if (data) {
          response = data;
        } else {
          response = "";
        }
      } else {
        response = "Invalid date format";
      }
    } else if (payload.params.from && payload.params.updatedAfter) {
      var from = new Date(payload.params.from);
      var updated = new Date(payload.params.updatedAfter);

      var data = await centaur.processed_meetings
        .find({ meetingDate: { $gte: from }, updatedAt: { $gte: updated } })
        .sort({ meetingDate: 1 })
        .select("-processedMeetingData.meeting.races")
        .select("-inputMeetingData")
        .lean();
      if (data) {
        response = data;
      } else {
        response = "";
      }
    } else if (payload.params.from && payload.params.to) {
      var from = new Date(payload.params.from);
      var to = new Date(payload.params.to);

      var data = await centaur.processed_meetings
        .find({ meetingDate: { $gte: from, $lte: to } })
        .sort({ meetingDate: 1 })
        .select("-processedMeetingData.meeting.races")
        .select("-inputMeetingData")
        .lean();
      if (data) {
        response = data;
      } else {
        response = "";
      }
    }
  }
  return response;
};

const checkRaceandHorseId = async (
  meeting_id,
  race_id = false,
  horse_id = false
) => {
  console.log("checking race and horse indexes");
  try {
    var theMeeting = await centaur.processed_meetings
      .findOne({ _id: meeting_id })
      .lean();
    var checkedData = {
      inputHorseMatch: {},
    };
    // console.log(theMeeting)
    if (theMeeting.processedMeetingData.meeting.races.race && race_id) {
      var raceArray = theMeeting.processedMeetingData.meeting.races.race;
      checkedData.race =
        theMeeting.processedMeetingData.meeting.races.race.findIndex(
          (element) => element["@_id"] === race_id
        );
      checkedData.inputRace = theMeeting.inputMeetingData.races.race.findIndex(
        (element) => element["@_id"] === race_id
      );
      if (raceArray[checkedData.race].horses.horse && horse_id) {
        if (horse_id)
          checkedData.horse = raceArray[
            checkedData.race
          ].horses.horse.findIndex((element) => element["@_id"] === horse_id);
        for (const horse of raceArray[checkedData.race].horses.horse) {
          checkedData.inputHorseMatch[horse["@_id"].toString()] =
            theMeeting.inputMeetingData.races.race[
              checkedData.inputRace
            ].horses.horse.findIndex(
              (element) => element["@_id"] === horse["@_id"]
            );
        }
      }
    }
    console.log(checkedData);
    return checkedData;
  } catch (err) {
    console.log("Error finding race and horse indexes", err);
    return false;
  }
};

const processedMeetingUpdate = async (payload) => {
  const parsedPayload = JSON.parse(payload.body);
  console.log("parsedPayload:", parsedPayload);
  if (parsedPayload.level === "approve_results") {
    const { meetingId } = parsedPayload;
    const theResult = await results.genResultsByMeetingId(meetingId);
    console.log(`Race Results for ${meetingId} Updated: `, theResult);
    return { msg: "Results internally approved" };
  }

  const { user, meetingId, level, validated } = parsedPayload;
  console.log(parsedPayload);
  if (!validated) {
    if (!user) {
      return { data: "No user details found, aborting changes" };
    }
    if (!meetingId) {
      return { data: "No meeting ID found, aborting changes" };
    }

    const lockCheck = await centaur.processed_meetings
      .findOne({ _id: meetingId })
      .select("meetingLocked")
      .lean();

    if (lockCheck.meetingLocked !== user) {
      return { data: "User does not match claiming user, aborting changes" };
    }
    console.log(`User ${user} making changes`);
  }

  const updateMeeting = async (dataToUpdate) => {
    const status = await centaur.processed_meetings
      .updateOne({ _id: meetingId }, { $set: dataToUpdate })
      .lean();
    console.log(`ProcessedMeeting ${meetingId} Updated: `, status);
    return status;
  };

  const updateRace = async (dataToUpdate, raceIndex) => {
    const status = await updateMeeting(dataToUpdate);
    console.log(`Race ${raceIndex} - ${meetingId} Updated: `, status);

    if (parsedPayload.results_approved) {
      const theResult = await results.genResultsByRace(meetingId, raceIndex);
      console.log(
        `Race Results ${raceIndex} - ${meetingId} Updated: `,
        theResult
      );
    }

    return status;
  };

  const updateHorse = async (dataToUpdate, raceIndex, horseIndex) => {
    const status = await updateMeeting(dataToUpdate);
    console.log(
      `Race ${raceIndex} - Horse ${horseIndex} ${meetingId} Updated: `,
      status
    );
    return status;
  };

  const getIndexes = async (raceId, horseId) => {
    const indexes = await checkRaceandHorseId(meetingId, raceId, horseId);
    if (indexes.race === -1) {
      console.log("Error updating race and horse, cannot find race index");
      return false;
    }
    if (indexes.horse === -1) {
      console.log("Error updating race and horse, cannot find horse index");
      return false;
    }
    return indexes;
  };

  const updateFields = (payload, fieldsToUpdate, dataToUpdate, prefix) => {
    fieldsToUpdate.forEach((field) => {
      if (payload[field] !== undefined) {
          dataToUpdate[`${prefix}.${field}`] = payload[field];
      }
    });
  };

  let status = false;
  const meetingFieldsToUpdate = [
    "track",
    "rail_position",
    "tab_indicator",
    "dual_track",
    "stage",
    "errors",
    "silksReady",
  ];
  const inputFieldsToUpdate = ["silksReady"];
  const dataToUpdate = {
    validated: !!parsedPayload.validated,
    meetingStage: parsedPayload.stage,
    meetingErrorCount: parsedPayload.clear_errors ? 0 : undefined,
  };
  switch (level) {
    case "processed_meeting":
      updateFields(
        parsedPayload,
        meetingFieldsToUpdate,
        dataToUpdate,
        "processedMeetingData.meeting"
      );
      updateFields(
        parsedPayload,
        inputFieldsToUpdate,
        dataToUpdate,
        "inputMeetingData"
      );
      status = await updateMeeting(dataToUpdate);
      break;

    case "processed_race": {
      const { raceId, delete_race, locked } = parsedPayload;
      if (delete_race) {
        // Fetch the current meeting data
        const meeting = await centaur.processed_meetings
          .findOne({ _id: meetingId })
          .lean();

        // Filter out the race to be deleted
        const updatedRaces =
          meeting.processedMeetingData.meeting.races.race.filter(
            (race) => race["@_number"] !== delete_race
          );

        // Update the meeting with the filtered races
        const raceDataToUpdate = {
          "processedMeetingData.meeting.races.race": updatedRaces,
        };

        status = await updateMeeting(raceDataToUpdate);
        console.log(`Race ${delete_race} deleted from meeting ${meetingId}`);
        break;
      }
      const {
        race: raceIndex,
        inputRace: inputRace,
        inputHorseMatch: inputHorseMatch,
      } = await getIndexes(raceId);
      if (raceIndex === false) break;

      const raceToUpdate = `processedMeetingData.meeting.races.race.${raceIndex}`;
      const inputToUpdate = `inputMeetingData.races.race.${inputRace}`;
      const raceFieldsToUpdate = [
        "distance",
        "restrictions",
        "weight_type",
        "min_hcp_weight",
        "classes",
        "start_time",
        "race_type",
        "results_approved",
        "official_margin_1",
        "official_margin_2",
        "sectional",
        "duration",
        "apprentice_claim",
        "@_name",
        "@_shortname",
        "errors",
        "prizes",
        "race_stage",
        "track_condition",
        "group",
        "starters",
        "confidence",
        "single_horse",
        "race_record_indicator",
      ];
      const inputRaceFieldsToUpdate = [
        "raceName",
        "raceNameShort",
        "errors",
        "confidence",
        "single_horse",
      ];
      const raceDataToUpdate = {};
      if (typeof locked === "boolean") {
        raceDataToUpdate[`${inputToUpdate}.locked`] = locked;
        raceDataToUpdate[`${raceToUpdate}.locked`] = locked;
      }
      const dbTrack = await centaur.tracks.findOne({
        TRK_TRACK_NAME: parsedPayload.track,
      });
      if (dbTrack) {
        raceFieldsToUpdate.push("track");
        inputRaceFieldsToUpdate.push("track_id");
        parsedPayload.track = {
          "@_name": dbTrack.TRK_TRACK_NAME,
          "@_id": dbTrack.TRK_TRACK_DB_ID,
          "@_track_surface": dbTrack.TRK_TRACK_SURFACE_TYPE,
          "@_country": dbTrack.TRK_COUNTRY_OF_TRACK,
          "@_track_3char_abbrev": dbTrack.TRK_TRACK_3CHAR_ABBREV,
          "@_expected_condition": parsedPayload.track_condition,
        };
        parsedPayload.track_id = dbTrack.TRK_TRACK_DB_ID;
      }

      // if (parsedPayload.updateMarket){
      //   for (let i=0;i<parsedPayload.horses.horse.length;i++){
      //     const inputHorseIndex = inputHorseMatch[parsedPayload.horses.horse[i]["@_id"].toString()];
      //     raceDataToUpdate[`${raceToUpdate}.horses.horse.${i}.tip`] = parsedPayload.horses.horse[i].tip;
      //     raceDataToUpdate[`${inputToUpdate}.horses.horse.${inputHorseIndex}.tip`] = parsedPayload.horses.horse[i].tip;
      //     raceDataToUpdate[`${raceToUpdate}.horses.horse.${i}.betting`] = parsedPayload.horses.horse[i].betting;
      //     raceDataToUpdate[`${inputToUpdate}.horses.horse.${inputHorseIndex}.betting`] = parsedPayload.horses.horse[i].betting;
      //   }
      // }
      if (parsedPayload.updateMarket) {
        for (let i = 0; i < parsedPayload.horses.horse.length; i++) {
          const inputHorseIndex =
            inputHorseMatch[parsedPayload.horses.horse[i]["@_id"].toString()];
          raceDataToUpdate[`${raceToUpdate}.horses.horse.${i}.tip`] =
            parsedPayload.horses.horse[i].tip;
          raceDataToUpdate[`${raceToUpdate}.horses.horse.${i}.betting`] =
            parsedPayload.horses.horse[i].betting;

          if (inputHorseIndex !== undefined) {
            raceDataToUpdate[
              `${inputToUpdate}.horses.horse.${inputHorseIndex}.tip`
            ] = parsedPayload.horses.horse[i].tip;
            raceDataToUpdate[
              `${inputToUpdate}.horses.horse.${inputHorseIndex}.betting`
            ] = parsedPayload.horses.horse[i].betting;
          } else {
            console.warn(
              `No matching input horse found for processed horse ID: ${parsedPayload.horses.horse[i]["@_id"]}`
            );
          }
        }
      }
      updateFields(
        parsedPayload,
        raceFieldsToUpdate,
        raceDataToUpdate,
        raceToUpdate
      );
      updateFields(
        parsedPayload,
        inputRaceFieldsToUpdate,
        raceDataToUpdate,
        inputToUpdate
      );
      try {
        status = await updateRace(raceDataToUpdate, raceIndex);
        console.log(`Race ${raceIndex} - ${meetingId} Updated: `, status);
      } catch (error) {
        console.error(
          `Error updating race ${raceIndex} - ${meetingId}:`,
          error
        );
        console.error(
          "Update data:",
          JSON.stringify(raceDataToUpdate, null, 2)
        );
      }
      break;
    }

    case "processed_horse": {
      const { horseId, raceId } = parsedPayload;
      const {
        race: horseRaceIndex,
        horse: horseIndex,
        inputRace: inputRace,
        inputHorseMatch: inputHorseMatch,
      } = await getIndexes(raceId, horseId);
      if (horseRaceIndex === false || horseIndex === false) break;

      const horseToUpdate = `processedMeetingData.meeting.races.race.${horseRaceIndex}.horses.horse.${horseIndex}`;
      const inputHorseToUpdate = `inputMeetingData.races.race.${inputRace}.horses.horse.${
        inputHorseMatch[horseId.toString()]
      }`;
      const horseDataToUpdate = {};

      if (parsedPayload.delete_horse) {
        const theMeeting = await centaur.processed_meetings
          .findOne({ _id: meetingId })
          .lean();
        const theRace =
          theMeeting.processedMeetingData.meeting.races.race[horseRaceIndex];
        const inputRace =
          theMeeting.inputMeetingData.races.race[horseRaceIndex];
        delete theRace[horseIndex];
        delete inputRace[horseIndex];
        horseDataToUpdate[
          `processedMeetingData.meeting.races.race.${horseRaceIndex}.horses.horse`
        ] = theRace;
        horseDataToUpdate[
          `inputMeetingData.races.race.${horseRaceIndex}.horses.horse`
        ] = inputRace;
      } else {
        if (
          (typeof parsedPayload.jockeyName === "string" &&
            parsedPayload.jockeyName.trim() === "") ||
          (typeof parsedPayload.jockeyName === "object" &&
            !parsedPayload.jockeyName.value)
        ) {
          horseDataToUpdate[`${horseToUpdate}.jockey`] = {
            "@_name": "",
            "@_firstname": "",
            "@_surname": "",
            "@_apprentice_indicator": "",
            "@_id": "",
          };
        } else if (
          parsedPayload.jockeyName &&
          typeof parsedPayload.jockeyName === "object" &&
          parsedPayload.jockeyName.value != null
        ) {
          const { value: jockeyId } = parsedPayload.jockeyName;
          const jockey_data = await centaur.jockeys
            .findOne({ JOC_JOCKEY_ID: jockeyId })
            .lean();
          if (jockey_data) {
            const jockey_record = {
              "@_name": jockey_data.JOC_JOCKEY_DISPLAYNAME,
              "@_firstname": jockey_data.JOC_JOCKEY_FIRSTNAME,
              "@_surname": jockey_data.JOC_JOCKEY_SURNAME,
              "@_apprentice_indicator": parsedPayload.jockeyWeight
                ? parsedPayload.jockeyWeight.slice(0, 1) === "a"
                  ? "Y"
                  : ""
                : "",
              "@_allowance_weight":
                parsedPayload.jockeyWeight && parsedPayload.jockeyWeight !== 0
                  ? parsedPayload.jockeyWeight.replace("a", "")
                  : "",
              "@_id": jockey_data.JOC_JOCKEY_ID,
              "@_riding_weight":
                jockey_data.JOC_JOCKEY_ID === jockeyId
                  ? parsedPayload.jockeyRidingWeight
                  : "",
              statistics: await getJockeyStats(jockeyId),
            };
            horseDataToUpdate[`${horseToUpdate}.jockey`] = jockey_record;
          } else {
            return "Jockey not found";
          }
        }

        if (
          typeof parsedPayload.trainerName === "string" &&
          parsedPayload.trainerName.trim() === ""
        ) {
          horseDataToUpdate[`${horseToUpdate}.trainer`] = {
            "@_name": "",
            "@_firstname": "",
            "@_surname": "",
            "@_id": "",
          };
        } else if (
          parsedPayload.trainerName &&
          typeof parsedPayload.trainerName === "object" &&
          parsedPayload.trainerName.value != null
        ) {
          const { value: trainerId } = parsedPayload.trainerName;
          const trainer_data = await centaur.trainers
            .findOne({ TRN_TRAINER_ID: trainerId })
            .lean();
          if (trainer_data) {
            const trainer_record = {
              "@_name": trainer_data.TRN_TRAINER_DISPLAYNAME,
              "@_firstname": trainer_data.TRN_TRAINER_FIRSTNAME,
              "@_surname": trainer_data.TRN_TRAINER_SURNAME,
              "@_id": trainerId,
              statistics: await getTrainerStats(trainerId),
            };
            horseDataToUpdate[`${horseToUpdate}.trainer`] = trainer_record;
          } else {
            return "Trainer not found";
          }
        }

        if (parsedPayload.totalWeight || parsedPayload.allocatedWeight) {
          const total_weight = {
            "@_total":
              parsedPayload.totalWeight || parsedPayload.allocatedWeight,
            "@_allocated":
              parsedPayload.allocatedWeight || parsedPayload.totalWeight,
          };
          horseDataToUpdate[`${horseToUpdate}.weight`] = total_weight;
        }
        if (parsedPayload.dead_heat_indicator) {
          horseDataToUpdate[`${horseToUpdate}.dead_heat_indicator`] =
            parsedPayload.dead_heat_indicator;
        }
        if (
          parsedPayload.gear_changes &&
          !parsedPayload.gear_changes.gear_change
        ) {
          const gearChanges = parsedPayload.gear_changes
            .split(";")
            .map((gear) => {
              const [name, option] = gear.split(":");
              return {
                "@_name": name,
                "@_option": option || "",
                "@_id": helper.getGearID(name),
              };
            });
          horseDataToUpdate[`${inputHorseToUpdate}.gear_changes.gear_change`] =
            gearChanges;
          horseDataToUpdate[`${horseToUpdate}.gear_changes.gear_change`] =
            gearChanges;
        }

        const inputFieldsToUpdate = [
          "tip",
          "colours",
          "betting",
          "comment",
          "colours_link",
          "scratched",
          "errors",
          "stewards_report",
          "running_gear",
          "rating",
          "barrier",
        ];
        const fieldsToUpdate = [
          "tab_number",
          "owners",
          "colours",
          "weight_carried",
          "margin",
          "rating",
          "barrier",
          "positions",
          "finish_position",
          "decimalprices",
          "prizemoney_race",
          "prizemoney_bonus",
          ...inputFieldsToUpdate,
          "emergency_indicator",
          "dead_heat_indicator",
        ];

        updateFields(
          parsedPayload,
          fieldsToUpdate,
          horseDataToUpdate,
          horseToUpdate
        );
        updateFields(
          parsedPayload,
          inputFieldsToUpdate,
          horseDataToUpdate,
          inputHorseToUpdate
        );

        console.log(horseDataToUpdate);
      }

      status = await updateHorse(horseDataToUpdate, horseRaceIndex, horseIndex);
      break;
    }
    default:
      break;
  }

  return !!status;
};

const getTrainerStats = async (trainerId = 0) => {
  try {
    var st = 0;
    var s1 = 0;
    var s2 = 0;
    var s3 = 0;

    const pastDate = new Date(helper.getPastDateByDay(365)); //Calculate past year date from today
    console.log(trainerId, pastDate);
    const statsDataTotal = await centaur.form_index
      .find()
      .where("trainer_id")
      .equals(trainerId)
      .where("meeting_date")
      .gt(pastDate)
      .lean();
    // const statsDataTotal = await formIndex.find({trainer_id:trainerId}).lean();
    // const statsDataTotal = await centaur.form_index.find({trainer_id:trainerId}).lean();
    // const statsDataTotal = await centaur.form_index.find({trainer_id:trainerId,meeting_date:{ $gt: pastDate}}).lean();

    console.log("trainerstats: ", statsDataTotal);

    // var cleansedTrainerData = statsDataTotal
    var cleansedTrainerData = helper.cleanseBarrierTrials(statsDataTotal);

    for (race of cleansedTrainerData) {
      st++;
      if (race.finish_pos == 1) {
        s1++;
      } else if (race.finish_pos == 2) {
        s2++;
      } else if (race.finish_pos == 3) {
        s3++;
      }
    }

    console.log("Stats: " + st + "," + s1 + "," + s2 + "," + s3);

    const stats = {
      statistic: {
        "@_type": "one_year", //Fix value of 365 days
        "@_total": st.toString(),
        "@_firsts": s1.toString(),
        "@_seconds": s2.toString(),
        "@_thirds": s3.toString(),
      },
    };
    return stats;
  } catch (err) {
    console.log("Trainer Stats Error - " + err);
  }
};

const getJockeyStats = async (jockeyId = "") => {
  try {
    var st = 0;
    var s1 = 0;
    var s2 = 0;
    var s3 = 0;

    const pastDate = new Date(helper.getPastDateByDay(365)); //Calculate past year date from today
    const statsDataTotal = await centaur.form_index
      .find()
      .where("jockey_id")
      .equals(jockeyId)
      .where("meeting_date")
      .gt(pastDate)
      .lean();

    var cleansedJockeyData = statsDataTotal;
    //var cleansedJockeyData = helper.cleanseBarrierTrials(statsDataTotal)

    for (race of cleansedJockeyData) {
      st++;
      if (race.finish_pos == 1) {
        s1++;
      } else if (race.finish_pos == 2) {
        s2++;
      } else if (race.finish_pos == 3) {
        s3++;
      }
    }

    console.log("Stats: " + st + "," + s1 + "," + s2 + "," + s3);

    const stats = {
      statistic: {
        "@_type": "one_year", //Fix value of 365 days
        "@_total": st.toString(),
        "@_firsts": s1.toString(),
        "@_seconds": s2.toString(),
        "@_thirds": s3.toString(),
      },
    };
    return stats;
  } catch (err) {
    console.log("Jockey Stats Error - " + err);
  }
};

const processedMeetingDelete = async (payload) => {
  var payload = JSON.parse(payload.body);
  var meetingId = payload.meetingId;
  var dataToUpdate = {};
  if (payload.processedMeetingData) {
    dataToUpdate = {
      "processedMeetingData.meeting.races": {},
      "processedMeetingData.meeting.stage": "DELETED",
      meetingStage: "DELETED",
    };
  }
  if (payload.inputMeetingData) {
    dataToUpdate["inputMeetingData.races"] = {};
  }
  console.log(payload);
  if (!payload.user) return false;
  if (meetingId) {
    let checkChangeLog = await centaur.changelog
      .findOne({ meeting_id: meetingId })
      .lean();
    if (checkChangeLog) {
      var logs = checkChangeLog.changelog;
      logs.unshift({
        time: moment.utc().format(),
        bucket: "",
        file_path: payload.user,
        trigger: "MEETING " + (payload.fullDelete ? "OBLITERATED" : "DELETED"),
      });
      var changelogToUpdate = {
        changelog: logs,
      };

      let updateChangeLog = await centaur.changelog
        .updateOne(
          { _id: checkChangeLog._id },
          {
            $set: changelogToUpdate,
          }
        )
        .lean();
      console.log(updateChangeLog);
    }
    let record_delete = "";
    if (payload.fullDelete) {
      temp_delete = await centaur.temp_meetings.deleteOne({ _id: meetingId });
      record_delete = await centaur.processed_meetings.deleteOne({
        _id: meetingId,
      });
    } else {
      record_delete = await centaur.processed_meetings.updateOne(
        { _id: meetingId },
        {
          $set: dataToUpdate,
        }
      );
    }
    if (record_delete) {
      return true;
    } else {
      return false;
    }
  }
};

const processedMeetingReload = async (payload) => {
  // var record = {
  //     eventVersion: '2.1',
  //     eventSource: 'aws:s3',
  //     awsRegion: 'ap-southeast-2',
  //     eventTime: '2023-06-07T03:35:24.934Z',
  //     eventName: 'ObjectCreated:Put',
  //     userIdentity: { principalId: 'AWS:AIDA2F6JZA3B54IAOZIR3' },
  //     requestParameters: { sourceIPAddress: '************' },
  //     responseElements: {
  //       'x-amz-request-id': 'ZE83AGNVBXPN3YRH',
  //       'x-amz-id-2': '/oUK/0OHnyGt/yn4mtZtJTaKEEIzzgwadDe7UrABnjF93D7KBlZgjfgTaTajHywBxTB6S17CXG/RVrsdV/n5ITzTXjZ5RldL'
  //     },
  //     s3: {
  //       s3SchemaVersion: '1.0',
  //       configurationId: '8711c771-4f73-4ea3-8e4d-fd69716192ab',
  //       bucket: {
  //         name: 'mr-cen-file-storage-stgblue',
  //         ownerIdentity: { principalId: 'A2VS8IMAKDPYIV' },
  //         arn: 'arn:aws:s3:::mr-cen-file-storage-stgblue'
  //       },
  //       object: {
  //         key: 'unprocessed/20230609_CAV_FORM_XML_A.xml',
  //         size: 4629387,
  //         eTag: '27bd0ec9a7c255db380275dde48cae07',
  //         versionId: 'MsGy8kXHqkWkFuuhDvR0i2K7PCe1qxAv',
  //         sequencer: '00647FFAFCA3831924'
  //       },
  //       fileType: 'a'
  //     },
  //     messageId: '4e9a892c-38ca-4cc1-94a7-6be379c52db2'
  //   }
  payload = JSON.parse(payload.body);
  var messageId = uuid.v4();
  var record = {
    eventVersion: "2.1",
    eventSource: "aws:lambda",
    awsRegion: "ap-southeast-2",
    eventTime: moment().toISOString(),
    eventName: "UserTrigger",
    s3: {
      s3SchemaVersion: "1.0",
      bucket: {
        name: payload.bucket, //'mr-cen-file-storage-stgblue',
        arn: "arn:aws:s3:::" + payload.bucket,
      },
      object: {
        key: payload.file_path, //'unprocessed/20230609_CAV_FORM_XML_A.xml'
      },
      fileType: payload.file_path
        .split(".")[0]
        .split("_")
        .slice(-1)[0]
        .toLowerCase(),
    },
    messageId: messageId,
  };

  var lambda = new AWS.Lambda();
  var options = {
    record: record,
    userTrigger: true,
  };

  var params = {
    FunctionName: "MrCenLambdaStepFunctionExecutor-" + process.env.ENV,
    InvocationType: "Event",
    Payload: JSON.stringify(options),
  };
  var res_3 = await lambda.invoke(params).promise();
  console.log(res_3);
  console.log(`meeting load triggered for `);

  return false;
};

module.exports = {
  processedMeetingAPI,
  processedMeetingRead,
  processedMeetingUpdate,
  processedMeetingReload,
};
