const moment = require('moment')
const helper = require("./helper")
const uuid = require('uuid');
const results = require('./results')
// Import Models
const centaur = require('@mediality/centaur')

const compareMeetingAPI = async (payload) => {
    if ((payload.path == "/compare-meeting") && (payload.params) && (payload.params.id)) {
        var meetingId = payload.params.id
        var errors = []
        if (payload.params.checkstats == true){
            var checkstats = await checkStatsAndValidity(meetingId)
            if (!checkstats) return "Meeting compare failed, deleting temp"
        }
        var diffObject = {}
        var dir = payload.params.dir ?? 1
        const pm = await centaur.processed_meetings.findOne({_id:meetingId}).select('processedMeetingData.meeting').lean()
        const tm = await centaur.temp_meetings.findOne({_id:meetingId}).select('processedMeetingData.meeting meetingLoadHistory').lean()
        
        if (!tm) return "ERROR: temp meeting not found"
        if (pm){
            meet1 = pm.processedMeetingData.meeting
            meet2 = tm.processedMeetingData.meeting
            if (helper.meetStageChange(meet1.stage,meet2.stage)){
                diffObject.notes = "Meeting stage change"
            } else {
                diffObject = compareTheMeeting(meet1,meet2,dir,diffObject) 
                dir++
                diffObject = compareTheMeeting(meet2,meet1,dir,diffObject)
                diffObject = genDiffSummary(diffObject)
                diffObject.summary = genMeetingCompleteness(meet2)
            
                if (diffObject.races)
                for (racekey in diffObject.races){
                    if (diffObject.races[racekey].error){
                        for (error of diffObject.races[racekey].error){
                            errors.push(`${racekey} error: ${error}`)
                        } 
                    }
                }
            }
        } else {
            diffObject.notes = "New meeting load"
        }
        
        let checkChangeLog = await centaur.changelog.findOne({meeting_id:meetingId}).lean()
        const upm = await centaur.processed_meetings.findOne({_id:meetingId}).select('meetingLoadHistory').lean()
        var i = 0
        var skipFilesNoChanges = ['Gear_Changes','_FORM_','_Scratchings','_Nominations','_Weights','_Acceptances','_FinalFields','RATINGS']
        var writeChanges = true
        if (diffObject.notes && diffObject.notes == "No changes." ){
            // writeChanges = false
            console.log('not adding log or delivering because NO CHANGES')
            for (var type of skipFilesNoChanges) {
                if (tm.meetingLoadHistory[0].file_path.includes(type)) writeChanges = false
            }
        }
        
        if (writeChanges && checkChangeLog){
            var logs = checkChangeLog.changelog
            logs.unshift({
                time: tm.meetingLoadHistory[0].time,
                bucket: tm.meetingLoadHistory[0].bucket,
                file_path: tm.meetingLoadHistory[0].file_path,
                trigger: tm.meetingLoadHistory[0].trigger,
                changes: diffObject  
            })
            if (upm && upm.meetingLoadHistory && upm.meetingLoadHistory.length > tm.meetingLoadHistory.length){
                for (i;i<upm.meetingLoadHistory.length;i++){
                    if (upm.meetingLoadHistory[i].time === tm.meetingLoadHistory[0].time) break
                    logs.unshift(upm.meetingLoadHistory[i])
                    errors.push(`${upm.meetingLoadHistory[i].file_path} Failed to load`)
                }
            }
            var dataToUpdate = {
                "changelog": logs
            }
            
            let updateChangeLog = await centaur.changelog.updateOne({_id:checkChangeLog._id},{
                "$set": dataToUpdate
            }).lean()
            console.log(updateChangeLog)
        } else if (writeChanges) { 
            var logs = []
            if (tm.meetingLoadHistory.length > 0){
                logs = tm.meetingLoadHistory
                logs[0].changes = diffObject
                if (upm && upm.meetingLoadHistory && upm.meetingLoadHistory.length > tm.meetingLoadHistory.length){
                    for (i;i<upm.meetingLoadHistory.length;i++){
                        if (upm.meetingLoadHistory[i].time.toString() === tm.meetingLoadHistory[0].time.toString()) break
                        logs.unshift(upm.meetingLoadHistory[i])
                        errors.push(`${upm.meetingLoadHistory[i].file_path} Failed to load`)
                    }
                }
            } else {
                if (upm && upm.meetingLoadHistory && upm.meetingLoadHistory[0]) logs = upm.meetingLoadHistory
                console.log('Error: couldnt find the load item for these changes!!')
            }
            
            var id = uuid.v4()
            var dataToCreate = {
                _id: id,
                meeting_id: meetingId,
                changelog: logs
            }

            var newRecord = new centaur.changelog(dataToCreate)
            var d = await newRecord.save();
            console.log(d)
        } else if (checkChangeLog) {
            var logs = checkChangeLog.changelog
            if (upm && upm.meetingLoadHistory && upm.meetingLoadHistory.length > (tm.meetingLoadHistory.length - 1)){
                for (i;i<upm.meetingLoadHistory.length;i++){
                    if (upm.meetingLoadHistory[i].time === tm.meetingLoadHistory[1].time) break
                    if (upm.meetingLoadHistory[i].trigger == 'NOT LOADED'){
                        logs.unshift(upm.meetingLoadHistory[i])
                        errors.push(`${upm.meetingLoadHistory[i].file_path} Failed to load`)
                    }
                }
            }
            var dataToUpdate = {
                "changelog": logs
            }
            let updateChangeLog = await centaur.changelog.updateOne({_id:checkChangeLog._id},{
                "$set": dataToUpdate
            }).lean()
        } else {
            var logs = []
            if (upm && upm.meetingLoadHistory && upm.meetingLoadHistory.length > (tm.meetingLoadHistory.length - 1)){
                for (i;i<upm.meetingLoadHistory.length;i++){
                    if (upm.meetingLoadHistory[i].time === tm.meetingLoadHistory[1].time) break
                    if (upm.meetingLoadHistory[i].trigger == 'NOT LOADED'){
                        logs.unshift(upm.meetingLoadHistory[i])
                        errors.push(`${upm.meetingLoadHistory[i].file_path} Failed to load`)
                    }
                }
            }
            var id = uuid.v4()
            var dataToCreate = {
                _id: id,
                meeting_id: meetingId,
                changelog: logs
            }

            var newRecord = new centaur.changelog(dataToCreate)
            var d = await newRecord.save();
            console.log(d)

        }

        // console.log(diffObject)
        // const dtm = await centaur.temp_meetings.deleteOne({_id:meetingId}).lean()
        var files = '' 
        var raceNo = 0
        var distribute = false
        if (payload.params.files) files = payload.params.files
        if (!writeChanges) files = ''
        if (payload.params.raceNo) raceNo = payload.params.raceNo
        if (payload.params.distribute && writeChanges) distribute = payload.params.distribute
        let snd = await saveAndDistribute(meetingId,errors,distribute,files,raceNo)
        // console.log(snd)
        console.log('meeting compared (and deleted)')
        return 'meeting compared and finished'
    }
    return 'Invalid parameters'
}

const compareTheMeeting = (meet1,meet2,dir,diffObject) =>{
    try{
        
        console.log('compare meeting level')
        for (const item in meet1){
            if (['track','races','errors','product'].includes(item)) continue
            try{
                if (meet1[item] !== meet2[item]){
                    appendDiff(meet1[item],meet2[item],dir,diffObject,'meeting.'+item)
                }
            } catch(err){
                appendDiff(meet1[item],'ERROR DIFFING: '+err,dir,diffObject,'meeting.'+item)
            }
        }
        console.log('compare meeting track')
        for (const item in meet1.track){
            if (['weather'].includes(item)) continue
            try{
                if (meet1.track[item] !== meet2.track[item]){
                    appendDiff(meet1.track[item],meet2.track[item],dir,diffObject,'meeting.track.'+item)
                }
            } catch(err){
                appendDiff(meet1.track[item],'ERROR DIFFING: '+err,dir,diffObject,'meeting.track.'+item)
            }
        }
        for (const item in meet1.errors){
            try{
                if (meet1.errors[item] !== meet2.errors[item]){
                    appendDiff(meet1.errors[item],meet2.errors[item],dir,diffObject,'meeting.track.'+item)
                }
            } catch(err){
                appendDiff(meet1.errors[item],'ERROR DIFFING: '+err,dir,diffObject,'meeting.track.'+item)
            }
        }
        var i = 0
        for (var i = 0;i<meet1.races.race.length;i++){
            race_from = meet1.races.race[i]
            race_to = meet2.races.race[i]
            var raceno = 'Race '+race_from['@_number']
            console.log('compare '+raceno)
            for (const item in race_from){
                if (['errors','error_log','records'].includes(item)) continue
                if (item == 'horses'){
                    if (race_from[item]['horse'][0]){
                        for (var j=0;j<race_from[item]['horse'].length;j++){
                            var horse = race_from[item]['horse'][j]
                            var h_cname = horse['@_name'].replace(/[^A-z ]/g,"")
                            if (!race_to[item]['horse'][j]){
                                if (['Acceptances','FinalFields','Final Fields'].includes(meet1.stage) && !(helper.meetStageChange(meet1.stage,meet2.stage))){
                                    if (!diffObject.races) diffObject.races = {}
                                    if (!diffObject.races[raceno]) diffObject.races[raceno] = {}
                                    if (!diffObject.races[raceno].error) diffObject.races[raceno].error = []
                                    diffObject.races[raceno].error.push(`horse ${h_cname} ${race_from[item]['horse'][j]['@_id']} appears to have been ${dir == 1 ? "removed" : "added"}`)
                                }
                                break
                            }
                            if (horse['@_id'] != race_to[item]['horse'][j]['@_id']){
                                if (['Acceptances','FinalFields','Final Fields'].includes(meet1.stage) && !(helper.meetStageChange(meet1.stage,meet2.stage))){
                                    if (!diffObject.races) diffObject.races = {}
                                    if (!diffObject.races[raceno]) diffObject.races[raceno] = {}
                                    if (!diffObject.races[raceno].error) diffObject.races[raceno].error = []
                                    diffObject.races[raceno].error.push(`horse ${race_from[item]['horse'][j]['@_name']} ${(dir == 1 ? race_from : race_to)[item]['horse'][j]['@_id']} has completely changed to ${(dir == 1 ? race_to : race_from)[item]['horse'][j]['@_name']} ${race_to[item]['horse'][j]['@_id']}, aborting the compare of this and remaining horses`)
                                }
                                break
                            }
                            for (const horse_in in horse){
                                if (typeof horse[horse_in] === 'object'){
                                    if (['statistics'].includes(horse_in)) {
                                        // compareStats(race_from[item]['horse'][j][horse_in]['statistic'],race_to[item]['horse'][j][horse_in]['statistic'],dir,diffObject,'races.'+raceno+'.horses.'+h_cname+'.'+horse_in)
                                        continue
                                    }
                                    for (const horse_in_sub in horse[horse_in]){
                                        if (Array.isArray(horse[horse_in][horse_in_sub])){
                                            for (var k=0;k<horse[horse_in][horse_in_sub].length;k++){
                                                for (const horse_in_sub_bit in horse[horse_in][horse_in_sub][k]){
                                                    try{
                                                        if (race_from[item]['horse'][j][horse_in][horse_in_sub][k][horse_in_sub_bit] !== race_to[item]['horse'][j][horse_in][horse_in_sub][k][horse_in_sub_bit]){
                                                            appendDiff(race_from[item]['horse'][j][horse_in][horse_in_sub],race_to[item]['horse'][j][horse_in][horse_in_sub],dir,diffObject,'races.'+raceno+'.horses.'+h_cname+'.'+horse_in+'.'+horse_in_sub)
                                                        }
                                                    } catch(err){
                                                        appendDiff(race_from[item]['horse'][j][horse_in][horse_in_sub][k],'ERROR DIFFING: '+err,dir,diffObject,'races.'+raceno+'.horses.'+h_cname+'.'+horse_in+'.'+horse_in_sub+'.'+k.toString())
                                                    }
                                                }
                                            }
                                        } else if (typeof horse[horse_in][horse_in_sub] === 'object'){ 
                                            if (['trainer','jockey'].includes(horse_in) && horse_in_sub === 'statistics') {
                                                // compareStats(race_from[item]['horse'][j][horse_in][horse_in_sub]['statistic'],race_to[item]['horse'][j][horse_in][horse_in_sub]['statistic'],dir,diffObject,'races.'+raceno+'.horses.'+h_cname+'.'+horse_in+'.'+horse_in_sub)
                                                continue
                                            }
                                            for (const horse_in_sub_bit in horse[horse_in][horse_in_sub]){
                                                if (Array.isArray(horse[horse_in][horse_in_sub][horse_in_sub_bit])){
                                                    for (var k=0;k<horse[horse_in][horse_in_sub][horse_in_sub_bit].length;k++){
                                                        for (const horse_in_sub_bit_pec in horse[horse_in][horse_in_sub][horse_in_sub_bit][k]){
                                                            try{
                                                                if (race_from[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit][k][horse_in_sub_bit_pec].includes('progeny')) break
                                                            } catch (err){
                                                                console.log(race_from[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit][k][horse_in_sub_bit_pec])
                                                            }
                                                            try{
                                                                if (race_from[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit][k][horse_in_sub_bit_pec] != race_to[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit][k][horse_in_sub_bit_pec]){
                                                                    appendDiff(race_from[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit][k],race_to[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit][k],dir,diffObject,'races.'+raceno+'.horses.'+h_cname+'.'+horse_in+'.'+horse_in_sub+'.'+horse_in_sub_bit+'.'+k.toString())
                                                                }
                                                            } catch(err){
                                                                appendDiff(race_from[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit][k],'ERROR DIFFING: '+err,dir,diffObject,'races.'+raceno+'.horses.'+h_cname+'.'+horse_in+'.'+horse_in_sub+'.'+horse_in_sub_bit+'.'+k.toString())
                                                            }
                                                        }
                                                    }
                                                } else if (typeof horse[horse_in][horse_in_sub] === 'object') {
                                                    for (const horse_in_sub_bit_pec in horse[horse_in][horse_in_sub][horse_in_sub_bit]){
                                                        try{
                                                            if (race_from[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit][horse_in_sub_bit_pec] != race_to[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit][horse_in_sub_bit_pec]){
                                                                appendDiff(race_from[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit][horse_in_sub_bit_pec],race_to[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit][horse_in_sub_bit_pec],dir,diffObject,'races.'+raceno+'.horses.'+h_cname+'.'+horse_in+'.'+horse_in_sub+'.'+horse_in_sub_bit+'.'+horse_in_sub_bit_pec)
                                                            }
                                                        } catch(err){
                                                            appendDiff(race_from[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit][horse_in_sub_bit_pec],'ERROR DIFFING: '+err,dir,diffObject,'races.'+raceno+'.horses.'+h_cname+'.'+horse_in+'.'+horse_in_sub+'.'+horse_in_sub_bit+'.'+horse_in_sub_bit_pec)
                                                        }
                                                    }
                                                } else {
                                                    try{
                                                        if (race_from[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit] != race_to[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit]){
                                                            appendDiff(race_from[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit],race_to[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit],dir,diffObject,'races.'+raceno+'.horses.'+h_cname+'.'+horse_in+'.'+horse_in_sub+'.'+horse_in_sub_bit)
                                                        } 
                                                    } catch(err){
                                                        appendDiff(race_from[item]['horse'][j][horse_in][horse_in_sub][horse_in_sub_bit],'ERROR DIFFING: '+err,dir,diffObject,'races.'+raceno+'.horses.'+h_cname+'.'+horse_in+'.'+horse_in_sub+'.'+horse_in_sub_bit)
                                                    }
                                                }
                                            }
                                        } else {
                                            try{
                                                if (race_from[item]['horse'][j][horse_in][horse_in_sub] != race_to[item]['horse'][j][horse_in][horse_in_sub]){
                                                    appendDiff(race_from[item]['horse'][j][horse_in][horse_in_sub],race_to[item]['horse'][j][horse_in][horse_in_sub],dir,diffObject,'races.'+raceno+'.horses.'+h_cname+'.'+horse_in+'.'+horse_in_sub)
                                                }
                                            } catch(err){
                                                appendDiff(race_from[item]['horse'][j][horse_in][horse_in_sub],'ERROR DIFFING: '+err,dir,diffObject,'races.'+raceno+'.horses.'+h_cname+'.'+horse_in+'.'+horse_in_sub)
                                            }
                                        }
                                    }
                                } else {
                                    try{
                                        if (race_from[item]['horse'][j][horse_in] != race_to[item]['horse'][j][horse_in]){
                                            appendDiff(race_from[item]['horse'][j][horse_in],race_to[item]['horse'][j][horse_in],dir,diffObject,'races.'+raceno+'.horses.'+h_cname+'.'+horse_in)
                                        }
                                    } catch(err){
                                        appendDiff(race_from[item]['horse'][j][horse_in],'ERROR DIFFING: '+err,dir,diffObject,'races.'+raceno+'.horses.'+h_cname+'.'+horse_in)
                                    }
                                }
                            }
                        }
                    }
                }
                else if (item == 'prizes'){
                    if (race_from[item]['prize']){
                        if (race_from[item]['prize'][0]){
                            for (var j=0;j<race_from[item]['prize'].length;j++){
                                var prize = race_from[item]['prize'][j]
                                for (const prize_in in prize){
                                    try{
                                        if (race_from[item]['prize'][j][prize_in] != race_to[item]['prize'][j][prize_in]){
                                            appendDiff(race_from[item]['prize'][j],race_to[item]['prize'][j],dir,diffObject,'races.'+raceno+'.'+item+'.'+j.toString())
                                        }
                                    } catch(err){
                                        appendDiff(race_from[item]['prize'][j],'ERROR DIFFING: '+err,dir,diffObject,'races.'+raceno+'.'+item+'.'+j.toString())
                                    }
                                }
                            }
                        } else {
                            try{
                                if (race_from[item]['prize'] != race_to[item]['prize']){
                                    appendDiff(race_from[item]['prize'],race_to[item]['prize'],dir,diffObject,'races.'+raceno+'.'+item)
                                }
                            } catch(err){
                                appendDiff(race_from[item]['prize'],'ERROR DIFFING: '+err,dir,diffObject,'races.'+raceno+'.'+item+'.'+j.toString())
                            }
                        }
                    }else{
                        if (race_to[item]['prize']){
                            appendDiff('',race_to[item]['prize'],dir,diffObject,'races.'+raceno+'.'+item)
                        }
                    }
                }
                else if (['track','distance','restrictions','classes','prizes','sectional'].includes(item)){
                    for (const initem in race_from[item]){
                        if (['errors','records'].includes(item)) continue
                        try{
                            if (race_from[item][initem] != race_to[item][initem]){
                                appendDiff(race_from[item][initem],race_to[item][initem],dir,diffObject,'races.'+raceno+'.'+item+'.'+initem)
                            }
                        } catch(err){
                            appendDiff(race_from[item][initem],'ERROR DIFFING: '+err,dir,diffObject,'races.'+raceno+'.'+item+'.'+initem)
                        }
                    }
                } else  {
                    try{
                        if (race_from[item] != race_to[item]){
                            appendDiff(race_from[item],race_to[item],dir,diffObject,'races.'+raceno+'.'+item)
                        }
                    } catch(err){
                        appendDiff(race_from[item],'ERROR DIFFING: '+err,dir,diffObject,'races.'+raceno+'.'+item)
                    }
                }
            }
            var race_sort = {}
            if (diffObject.races){
                Object.keys(diffObject.races)
                    .sort()
                    .forEach(function(v, i) {
                        race_sort[v] = diffObject.races[v]
                        // console.log(v, data[v]);
                    });
                delete diffObject.races
                diffObject.races = race_sort
            }

        }
    } catch(err){
        console.log('ERROR IN COMAPRE: '+err)
    }
    return diffObject
}

const compareStats = (stats1,stats2,dir,diffObject,pathfull) => {
    if (!Array.isArray(stats1)) stats1 = [stats1]
    if (!Array.isArray(stats2)) stats2 = [stats2]
    for (var i=0;i<stats1.length;i++){
        if (stats1[i]['@_type'].includes('progeny')) continue
        for (const sub in stats1[i]){
            try{
                if (stats1[i][sub] !== stats2[i][sub]) {
                    console.log(`${pathfull}.${stats1[i]['@_type']}.${sub}`,stats1[i][sub],stats2[i][sub])
                    appendDiff(stats1[i][sub],stats2[i][sub],dir,diffObject,`${pathfull}.${stats1[i]['@_type']}.${sub}`)
                }
            } catch(err) {
                console.log('error with statscompare:'+err)
                console.log(stats1[i][sub],stats2[i][sub])
                appendDiff(stats1[i][sub],'ERROR DIFFING: '+err,dir,diffObject,`${pathfull}.${stats1[i]['@_type']}.${sub}`)
            }
        }
    }
}

const appendDiff = (item1,item2,dir,diffObj,pathfull) => {
    if (item1 && item1.toString().includes("ERROR DIFFING: TypeError: Cannot read property")) item1 = ''
    if (item2 && item2.toString().includes("ERROR DIFFING: TypeError: Cannot read property")) item2 = ''
    if (!item1 && !item2) return diffObj
    var path = pathfull.split('.')
    if (dir == 1){
        i1 = item1
        i2 = item2
    } else {
        i1 = item2
        i2 = item1
    }
    if (path[0]){
        if (!diffObj[path[0]]){
            diffObj[path[0]] = {}
        }
        if (path[1]){
            if (!diffObj[path[0]][path[1]]){
                diffObj[path[0]][path[1]] = {}
            }
            if (path[2]){
                if (!diffObj[path[0]][path[1]][path[2]]){
                    diffObj[path[0]][path[1]][path[2]] = {}
                }
                if (path[3]){
                    if (!diffObj[path[0]][path[1]][path[2]][path[3]]){
                        diffObj[path[0]][path[1]][path[2]][path[3]] = {}
                    }
                    if (path[4]){
                        if (!diffObj[path[0]][path[1]][path[2]][path[3]][path[4]]){
                            diffObj[path[0]][path[1]][path[2]][path[3]][path[4]] = {}
                        }
                        if (path[5]){
                            if (!diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]]){
                                diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]] = {}
                            }
                            if (path[6]){
                                if (!diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]][path[6]]){
                                    diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]][path[6]] = {}
                                }
                                if (path[7]){
                                    if (!diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]][path[6]][path[7]]){
                                        diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]][path[6]][path[7]] = {}
                                    }
                                    if (path[8]){
                                        if (!diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]][path[6]][path[7]][path[8]]){
                                            diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]][path[6]][path[7]][path[8]] = {}
                                        }
                                        diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]][path[6]][path[7]][path[8]] = [i1,i2]
                                    } else{
                                        diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]][path[6]][path[7]] = [i1,i2]
                                    }
                                } else{
                                    diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]][path[6]] = [i1,i2]
                                }
                            } else{
                                diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]] = [i1,i2]
                            }
                        } else{
                            diffObj[path[0]][path[1]][path[2]][path[3]][path[4]] = [i1,i2]
                        }
                    } else{
                        diffObj[path[0]][path[1]][path[2]][path[3]] = [i1,i2]
                    }
                } else{
                    diffObj[path[0]][path[1]][path[2]] = [i1,i2]
                }
            } else{
                diffObj[path[0]][path[1]] = [i1,i2]
            }
        } else{
            diffObj[path[0]] = [i1,i2]
        }
            
    }
    return diffObj
}

const genDiffSummary = (diffObj) => {
    diffObj.notes = ""
    if (!diffObj.meeting && !diffObj.races){
        diffObj.notes = "No changes."
        return diffObj
    }
    if (diffObj.meeting) diffObj.notes += "MEETING "
    if (diffObj.races){
        var racenames = 0
        var racetimes = 0
        var jockeys = 0
        var scratchings = 0
        var markets = 0
        var comment = 0
        for (const the_race in diffObj.races){
            race = diffObj.races[the_race]
            if (race['@_ra_name']) racenames++
            if (race.start_time) racetimes++
            if (race.horses){
                for (const the_horse in race.horses){
                    horse = race.horses[the_horse]
                    if (horse.jockey && !horse.scratched) jockeys++
                    if (horse.scratched) scratchings++
                    if (horse.betting) markets++
                    if (horse.comment) comment++
                }
            }
        }
        diffObj.count = {}
        if (racenames > 0) {diffObj.notes += "RACENAMES "; diffObj.count.racenames = racenames}
        if (racetimes > 0) {diffObj.notes += "RACETIMES "; diffObj.count.racetimes = racetimes}
        if (jockeys > 1) {diffObj.notes += "JOCKEYS "; diffObj.count.jockeys = jockeys}
        if (scratchings > 1) {diffObj.notes += "SCRATCHINGS "; diffObj.count.scratchings = scratchings}
        if (markets > 0) {diffObj.notes += "MARKETS "; diffObj.count.markets = markets}
        if (comment > 0) {diffObj.notes += "COMMENT "; diffObj.count.comment = comment}
    }
    return diffObj
}

const genMeetingCompleteness = (meeting) => {
    var races = meeting.races.race

    var horsetotal = 0, jockeyfill = 0, markets = 0, tips = 0, scratched = 0, comment = 0
    var tipstotal = races.length * 4

    for (var race of races) {
        for (var horse of race.horses.horse){
            if (horse.scratched){
                scratched++
            } else {
                horsetotal++
                if (horse.jockey && horse.jockey['@_id']){
                    jockeyfill++
                }
                if (horse.comment){
                    comment++
                }
                if (horse.tip){
                    tips++ 
                }
                if (horse.betting){
                    markets++ 
                }
            }
        }
    }

    return completionsummary = {
        "J" : [jockeyfill,horsetotal],
        "M" : [markets,horsetotal],
        "C" : [comment,horsetotal],
        "T" : [tips,tipstotal],
        "S" : scratched
    }
}

const checkStatsAndValidity = async (meetingId) => {
    sleep(3000)
    var statsready = false
    var count = 0
    while (!statsready){
        count++
        if (count > 30){ 
            statsready = false; 
            await errorMeeting(meetingId, 'Timed out while generating statistics, load was aborted. Try a reload if meeting exists.'); 
            return statsready 
        }
        console.log(count)
        sleep(2000)
        statsready = true
        var eventidArray = []
        var i = 0
        var meetingData = await centaur.temp_meetings.findOne({ _id: meetingId }).lean()
        
        if (meetingData.processedMeetingData.meeting.races.race.length != meetingData.inputMeetingData.races.race.length){
            statsready = false; 
            await errorMeeting(meetingId, 'ERROR: The number of races in the inputData doesnt match this new file, load was aborted. Please investigate the received file.')
            return statsready 
        }
       
        for (raceitem of meetingData.processedMeetingData.meeting.races.race){
            if (eventidArray.includes(raceitem['@_id'])){
                statsready = false; 
                await errorMeeting(meetingId, 'ERROR: Major issues with nominations numbers, load was aborted. Please investigate the received file.')
                return statsready
            }
            eventidArray.push(raceitem['@_id'])
            
            

            if (raceitem.horses && raceitem.horses.horse && raceitem.horses.horse[raceitem.horses.horse.length - 1]){
                var lasthorse = raceitem.horses.horse[raceitem.horses.horse.length - 1]
                if (!lasthorse.statistics || lasthorse.statistics == "" || !lasthorse.statistics.statistic || !lasthorse.statistics.statistic[0]) {
                    statsready = false; 
                    break 
                }
            } else {
                statsready = false; 
                await errorMeeting(meetingId, 'something went very wrong, looking for horses that dont exist in the race, load was aborted.')
                return statsready
            }
            
            i++
        }
    }
    return statsready
}

const errorMeeting = async (meetingId, error = 'postprocess couldnt finish, perhaps stats never completed.') => {
    console.log(`error and delete: ${meetingId}`)
    let errorarraypm = await centaur.temp_meetings.findOne({ _id: meetingId }).lean()
    errorarraypm.processedMeetingData.meeting.errors.unshift(error)
    errorarraypm.meetingLocked = "unlocked"
    errorarraypm.meetingErrorCount = errorarraypm.meetingErrorCount++
    var lastfile = {
        time : moment.utc().format(),
        bucket: errorarraypm.meetingLoadHistory[0].bucket,
        file_path: errorarraypm.meetingLoadHistory[0].file_path,
        trigger:"POSTLOAD FAIL"
    }
    let checkChangeLog = await centaur.changelog.findOne({meeting_id:meetingId}).lean()
    if (checkChangeLog){
        const upm = await centaur.processed_meetings.findOne({_id:meetingId}).select('meetingLoadHistory').lean()
        var logs = checkChangeLog.changelog
        logs.unshift(lastfile)
        if (upm && upm.meetingLoadHistory && upm.meetingLoadHistory.length > errorarraypm.meetingLoadHistory.length){
            for (var i=0;i<upm.meetingLoadHistory.length;i++){
                if (upm.meetingLoadHistory[i].time === errorarraypm.meetingLoadHistory[0].time) break
                logs.unshift(upm.meetingLoadHistory[i])
                errors.push(`${upm.meetingLoadHistory[i].file_path} Failed to load`)
            }
        }
        var dataToUpdate = {
            "changelog": logs
        }
        let updateChangeLog = await centaur.changelog.updateOne({_id:checkChangeLog._id},{
            "$set": dataToUpdate
        }).lean()
        console.log(updateChangeLog)
    }

    var checkmeet = await centaur.processed_meetings.findOne({ _id: meetingId }).lean()
    if (checkmeet && checkmeet.meetingStage != 'DELETED'){
        let errorArray = errorarraypm.processedMeetingData.meeting.errors
        let me = await centaur.processed_meetings.updateOne({ _id: meetingId }, {
            "$set": {'processedMeetingData.meeting.errors' : errorArray, meetingLocked: "unlocked", meetingErrorCount: errorarraypm.meetingErrorCount}
        }).lean()
        console.log
    } else if (!checkmeet) {
        // var sendMail = await helper.mailAlert('Initial file not loaded!',`File ${errorarraypm.meetingLoadHistory[0].file_path} wasnt able to load, and failed in postprocess with error: ${error}`,'alert')
        // console.log(sendMail)
        var newRecord = new centaur.processed_meetings(errorarraypm)
        var d = await newRecord.save();
        console.log(d)
    } else {
        if (checkmeet && checkmeet.meetingStage == 'DELETED') var dpm = await centaur.processed_meetings.deleteOne({ _id: meetingId }).lean()
        var newRecord = new centaur.processed_meetings(errorarraypm)
        var d = await newRecord.save();
        console.log(d)
    }

    let deletetemp = await centaur.temp_meetings.deleteOne({ _id: meetingId }).lean()
    console.log(deletetemp)
}

const sleep = async (miliseconds) => {
    var currentTime = new Date().getTime();
 
    while (currentTime + miliseconds >= new Date().getTime()) {
    }
 }

const saveAndDistribute = async (meetingId, errors, distribute = false, files = 'FIELDS,FORM', raceNo = 0) => {
    var tm = await centaur.temp_meetings.findOne({_id:meetingId}).lean()
    const dpm = await centaur.processed_meetings.deleteOne({_id:meetingId}).lean()
    tm.meetingLocked = "unlocked"
    var i = 1
    for (race of tm.processedMeetingData.meeting.races.race){
        if (!race['@_id']) {
            errors.push(`Race ${i} is missing`)
        }
        i++
    }
    if (errors.length > 0){
        tm.meetingErrorCount = tm.meetingErrorCount + errors.length
        tm.processedMeetingData.meeting.errors = errors.concat(tm.processedMeetingData.meeting.errors)
        tm.validated = false
       
    }
    if (tm.meetingErrorCount > 0){
        distribute = false
    }
    // console.log('saving and distributing mid')
    // TURNING OFF ALL AUTO DISTRIBUTION
    if (!distribute && files != '' || (tm.AcceptanceFileCounter == 1 && files == 'FIELDS,FORM,SCRATCHINGS')) {
    // if (!distribute && files != '') {
        tm.validated = false
        distribute = false
    }
    tm.updatedAt = moment.utc().format()
    tm.createdAt = moment.utc().format()
    console.log(tm)
    var newRecord = new centaur.processed_meetings(tm)
    var d = await newRecord.save();
    console.log(d)
    const dtm = await centaur.temp_meetings.deleteOne({_id:meetingId}).lean()
    if (files === 'RESULTS'){
        var theResult = await results.genResultsByMeetingId(meetingId)
        console.log(theResult)
        console.log(`form approved for ${meetingId} race ${raceNo}`)
    }
                    
    if (distribute){
        const AWS = require('aws-sdk');

        var lambda = new AWS.Lambda();
        var payload = {
            queryStringParameters: {
                id: meetingId,
                race_no: raceNo,
                if_validated: true,
                files: files
            }
        }

        var params = {
            FunctionName: "MrCenLambdaDeliveryFunction-" + process.env.ENV,
            InvocationType: 'Event',
            Payload:  JSON.stringify(payload) ,
        };
        var res_3 = await lambda.invoke(params).promise();
        console.log(res_3)
        console.log(`results distributed with raceNo ${raceNo}`)
        
    }
    return "success"
}

const saveOnly = async (meetingId, user, deleteTemp = true) => {
    console.log('saveOnly function')
    const pm = await centaur.processed_meetings.findOne({_id:meetingId}).select('processedMeetingData.meeting').lean()
    if (!pm) return "ERROR: processed meeting not found"
    const tm = await centaur.temp_meetings.findOne({_id:meetingId}).select('processedMeetingData.meeting meetingLoadHistory').lean()
    if (!tm) return "ERROR: temp meeting not found"
    var diffObject = {}
    var dir = deleteTemp ? 0 : 1
    meet1 = pm.processedMeetingData.meeting
    meet2 = tm.processedMeetingData.meeting
    var errors = []        
    diffObject = compareTheMeeting(meet1,meet2,dir,diffObject) 
    dir++
    diffObject = compareTheMeeting(meet2,meet1,dir,diffObject)
    diffObject = genDiffSummary(diffObject)
    diffObject.summary = genMeetingCompleteness(meet1)

    let checkChangeLog = await centaur.changelog.findOne({meeting_id:meetingId}).lean()
    var i = 0
    const upm = await centaur.processed_meetings.findOne({_id:meetingId}).select('meetingLoadHistory').lean()
    if (checkChangeLog){
        var logs = checkChangeLog.changelog
        logs.unshift({
            time: moment.utc().format(),
            bucket: "USER EDITS",
            file_path: user,
            trigger: "USER EDITS",
            changes: diffObject  
        })
        if (upm.meetingLoadHistory && upm.meetingLoadHistory.length > tm.meetingLoadHistory.length){
            for (i;i<upm.meetingLoadHistory.length;i++){
                if (upm.meetingLoadHistory[i].time === tm.meetingLoadHistory[0].time) break
                logs.unshift(upm.meetingLoadHistory[i])
                errors.push(`${upm.meetingLoadHistory[i].file_path} Failed to load`)
            }
        }
        var dataToUpdate = {
            "changelog": logs
        }
        let updateChangeLog = await centaur.changelog.updateOne({_id:checkChangeLog._id},{
            "$set": dataToUpdate
        }).lean()
        console.log(updateChangeLog)
    } else { 
        var logs = []
        if (tm.meetingLoadHistory && tm.meetingLoadHistory.length > 0){
            logs = tm.meetingLoadHistory
            
        } else {
            console.log('Error: couldnt find the load item for these changes!!')
        }
        logs.unshift({
            time: moment.utc().format(),
            bucket: "USER EDITS",
            file_path: user,
            trigger: "USER EDITS",
            changes: diffObject  
        })
        if (upm.meetingLoadHistory && upm.meetingLoadHistory.length > tm.meetingLoadHistory.length){
            for (i;i<upm.meetingLoadHistory.length;i++){
                if (upm.meetingLoadHistory[i].time.toString() === tm.meetingLoadHistory[0].time.toString()) break
                logs.unshift(upm.meetingLoadHistory[i])
                errors.push(`${upm.meetingLoadHistory[i].file_path} Failed to load`)
            }
        }
        var id = uuid.v4()
        var dataToCreate = {
            _id: id,
            meeting_id: meetingId,
            changelog: logs
        }

        var newRecord = new centaur.changelog(dataToCreate)
        var d = await newRecord.save();
        console.log(d)
    }
    console.log('moving to delete temp meeting and update meeting') 
    const dtm = await centaur.temp_meetings.deleteOne({_id:meetingId}).lean()
    
    var dataToUpdate = {}
    dataToUpdate['meetingLocked'] = "unlocked"
    dataToUpdate['validated'] = false
    dataToUpdate['updatedAt'] = moment.utc().format()
    dataToUpdate['createdAt'] = moment.utc().format()
    
    if (errors.length > 0){
        var npm = await centaur.processed_meetings.findOne({_id:meetingId}).select('processedMeetingData.meeting meetingErrorCount').lean()
        dataToUpdate['meetingErrorCount'] = npm.meetingErrorCount + errors.length
        dataToUpdate['processedMeetingData.meeting.errors'] = errors.concat(npm.processedMeetingData.meeting.errors)
        
    }
    var fpm = await centaur.processed_meetings.updateOne({_id:meetingId},{
        "$set": dataToUpdate
    }).lean()

    return {"data":"meeting changes saved reverted"}
}

const cancelEdit =  async (meetingId) => {
    let checkChangeLog = await centaur.changelog.findOne({meeting_id:meetingId}).lean()
    var i = 0
    const upm = await centaur.processed_meetings.findOne({_id:meetingId}).select('meetingLoadHistory').lean()
    var tm = await centaur.temp_meetings.findOne({_id:meetingId}).lean()
    var logs = []
    var errors = []
    if (checkChangeLog){
        logs = checkChangeLog.changelog
        if (upm.meetingLoadHistory && upm.meetingLoadHistory.length > tm.meetingLoadHistory.length){
            for (i;i<upm.meetingLoadHistory.length;i++){
                if (upm.meetingLoadHistory[i].time === tm.meetingLoadHistory[0].time) break
                logs.unshift(upm.meetingLoadHistory[i])
                errors.push(`${upm.meetingLoadHistory[i].file_path} Failed to load`)
            }
        }
        var dataToUpdate = {
            "changelog": logs
        }
        let updateChangeLog = await centaur.changelog.updateOne({_id:checkChangeLog._id},{
            "$set": dataToUpdate
        }).lean()
        console.log(updateChangeLog)
    } else { 
        logs = []
        if (upm.meetingLoadHistory && upm.meetingLoadHistory.length > 0){
            logs = upm.meetingLoadHistory
        } else {
            console.log('Error: couldnt find the load item for these changes!!')
        }
        if (upm.meetingLoadHistory && upm.meetingLoadHistory.length > tm.meetingLoadHistory.length){
            for (i;i<upm.meetingLoadHistory.length;i++){
                if (upm.meetingLoadHistory[i].time === tm.meetingLoadHistory[0].time) break
                logs.unshift(upm.meetingLoadHistory[i])
                errors.push(`${upm.meetingLoadHistory[i].file_path} Failed to load`)
            }
        }
        var id = uuid.v4()
        var dataToCreate = {
            _id: id,
            meeting_id: meetingId,
            changelog: logs
        }

        var newRecord = new centaur.changelog(dataToCreate)
        var d = await newRecord.save();
        console.log(d)
    }

    const dpm = await centaur.processed_meetings.deleteOne({_id:meetingId}).lean()
    
    tm.meetingLocked = "unlocked"
    if (errors.length > 0){
        tm.meetingErrorCount = tm.meetingErrorCount + errors.length
        tm.processedMeetingData.meeting.errors = errors.concat(tm.processedMeetingData.meeting.errors)
        tm.validated = false
    }
    tm.updatedAt = moment.utc().format()
    tm.createdAt = moment.utc().format()
    console.log(tm)
    var newRecord = new centaur.processed_meetings(tm)
    var d = await newRecord.save();
    console.log(d)
    const dtm = await centaur.temp_meetings.deleteOne({_id:meetingId}).lean()

    return {"data":"meeting reverted"}
}


module.exports = {
    compareMeetingAPI,
    saveAndDistribute,
    saveOnly,
    compareTheMeeting,
    cancelEdit
};
