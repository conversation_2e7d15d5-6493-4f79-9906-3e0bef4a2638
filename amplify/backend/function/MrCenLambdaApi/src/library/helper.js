const util = require('util')
const AWS = require('aws-sdk');
const moment = require('moment')
const centaur = require("@mediality/centaur");
const mailAlert = async (subject,body,mail_list = "error") =>{
    // console.log('trying to send mail')
    var ses = new AWS.SES({region: 'ap-southeast-2'});

    var level = {
        "task" : [
            "<EMAIL>",
            "<EMAIL>"
        ],
        "error" : [
            "<EMAIL>",
            "<EMAIL>"
        ],
        "alert" : [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ],
        "alarm" : [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
    }
    if (process.env != 'prd'){
        level = {
            "task" : [
                "<EMAIL>",
                "<EMAIL>"
            ],
            "error" : [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
            ],
            "alert" : [
                "<EMAIL>",
                "<EMAIL>"
            ],
            "alarm" : [
                "<EMAIL>",
                "<EMAIL>"
            ]
        }
    }
     
    const emailParams = {
        Destination: {
            ToAddresses: level[mail_list],
        },
        Message: {
            Body: {
                Text: { Data: body },
            },
            Subject: { Data: "Pegasus DB "+mail_list.toUpperCase()+" "+subject },
        },
        Source: "<EMAIL>",
    };
        
    try {
            let key = await ses.sendEmail(emailParams).promise();
            // console.log('mail sent');      
    } catch (e) {
            console.log('mail failed', e);
    }  
    return 'mail process complete';
    
}

const inspectObject = async (jsonObject) => {
    console.log(
        util.inspect(
            jsonObject,
            { showHidden: false, depth: null, colors: true }
        )
    )
}



const readS3File = async (fileName, bucketName) => {
    const AWS = require('aws-sdk');
    const s3 = new AWS.S3({ apiVersion: '2006-03-01' })
    var response = ""
    var params_s3 = {
        Bucket: bucketName,
        Key: fileName,
    };
    try {
        let data = await s3.getObject(params_s3).promise()
        response = data.Body.toString('utf-8')
    }
    catch (err) {
        console.log("S3 Error: " + err)
    }

    return response
}


const uploadFileS3 = async (bucketName, key, bodyData) => {

    const AWS = require('aws-sdk');
    const s3 = new AWS.S3({});

    const s3Bucket = bucketName;
    const objectName = `processed/${key}.json`;
    const objectData = JSON.stringify(bodyData, null, 2);
    const objectType = 'application/json';

    try {
        const params = {
            Bucket: s3Bucket,
            Key: objectName,
            Body: objectData,
            ContentType: objectType,
        };
        const result = await s3.putObject(params).promise();
        console.log(`File uploaded successfully`);
    } catch (error) {
        console.log(error);
        console.log('error');
    }
}


const getSecrets = async (secretName) => {
    const AWS = require('aws-sdk');
    var secretsmanager = new AWS.SecretsManager();
    var params = {
        SecretId: secretName,
    };
    const fetchSecretString = await secretsmanager.getSecretValue(params).promise();
    aws_secrets = JSON.parse(fetchSecretString.SecretString)
    return aws_secrets
}

const xmlToJs = async (xmlData) => {
    const { XMLParser, XMLBuilder, XMLValidator } = require('fast-xml-parser');
    const options = {
        ignoreAttributes: false,
        attributeNamePrefix: "@_"
    };
    const parser = new XMLParser(options);
    try {
        let jsonData = parser.parse(xmlData);
        return jsonData
    }
    catch (err) {
        console.log(err)
    }

}

const jsonToXml = async (jsonData) => {
    const { XMLParser, XMLBuilder, XMLValidator } = require('fast-xml-parser');
    const options = {
        ignoreAttributes: false,
        suppressEmptyNode: true
    };
    const builder = new XMLBuilder(options);
    const xmlContent = builder.build(jsonData);
    return xmlContent
}

const convertDate = (dt) => {
    //  dd/mm/yyyy
    //console.log(typeof(dt))
    return moment(dt).format('DD/MM/YYYY');
}

const sDate = (dt) => {
    return new Date(dt)
    //return moment(dt).format().toString();
}

const convertStringToDDMMYYYY = (dt = 'YYYY-MM-DD') => {
    if (isValidYYYYMMYYDate(dt)) {
        let temp = dt.split('-')
        let result = temp[2] + "/" + temp[1] + "/" + temp[0]
        return result
    } else {
        return false
    }

}

function isValidYYYYMMYYDate(dateString) {
    var regEx = /^\d{4}-\d{2}-\d{2}$/; // yyyy-mm-dd
    if(!dateString.match(regEx)) return false;  // Invalid format
    var d = new Date(dateString);
    var dNum = d.getTime();
    if(!dNum && dNum !== 0) return false; // NaN value, Invalid date
    return d.toISOString().slice(0,10) === dateString;
  }

const getDayMonth = (dt) => {
    //  ddmm
    return moment(dt).format('DDMM').toString();
}

const openDBConnection = async (env) => {
    const local = require("../local");
    const mongoose = require('mongoose');

    if (env == "local") {
        //Local environment

        var cert_path = './rds-cert/global-bundle.pem'
        console.log('Running Locally')
        DB_USERNAME = local.DB_USERNAME
        DB_PASSWORD = local.DB_PASSWORD
        DB_URL = local.DB_URL
        DB_NAME = local.DB_NAME
        CONNECTION_STRING = "mongodb://" + DB_USERNAME + ":" + DB_PASSWORD + "@" + DB_URL + "/" + DB_NAME
        CONNECTION_STRING = "mongodb://root:Test.123!@127.0.0.1:27000/dev-centaur?directConnection=true&ssl=true&retrywrites=false&tls=true"
        await mongoose.connect(CONNECTION_STRING, {
            connectTimeoutMS: 1000,
            tlsCAFile: cert_path,
            directConnection: true,
            ssl: true,
            sslValidate: false,
            maxPoolSize: 10

        }).then(
            () => {
                console.log('Connected to Local')
                response = "Connect to Local"
            },
            err => {
                console.log('Not connected:' + err)
                response = "Not Connected to Local"
            });

    } else {
        console.log('Running Online')
        var aws_secrets = await getSecrets(process.env.centaurSecrets)
        DB_USERNAME = aws_secrets.DB_USERNAME
        DB_PASSWORD = aws_secrets.DB_PASSWORD
        DB_URL = aws_secrets.DB_URL
        DB_NAME = aws_secrets.DB_NAME
        CONNECTION_STRING = "mongodb://" + DB_USERNAME + ":" + DB_PASSWORD + "@" + DB_URL + "/" + DB_NAME + "?tls=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
        await mongoose.connect(CONNECTION_STRING, {
            connectTimeoutMS: 1000,
            tlsCAFile: cert_path,
            directConnection: true,
            ssl: true,
            sslValidate: false,
            maxPoolSize: 10
        }).then(
            () => {
                console.log('Connected to Online')
                response = "Connect Online"
            },
            err => {
                console.log('Not connected:' + err)
                response = "Not Connected Online"
            });
    }
    return mongoose
}

const closeDBConnection = async (con) => {

    con.connection.close()
    console.log("Connection Closed")

}

const getCapitalFirstLetterOnly = (stage) => {
    return stage.toString().charAt(0)
}
const getTrackCondition = (trackCondition = "") => {
    return trackCondition.charAt(0);
}

const getTrackGrading = (trackCondition = "1") => {
    //return last character
    return trackCondition.charAt(trackCondition.length - 1);
}
const getS3SignedUrl = async (fileName) => {
    const AWS = require('aws-sdk')
    const s3 = new AWS.S3()
    const params = {
        Bucket: getBucketName(fileName),
        Key: getS3FileName(fileName)
        // Expires: 60 * 5
    }
    try {
        var result = "Not Found"

        //check if file exists
        // var fileExists = await s3.headObject(params, async function(err, data) {
        //     if (err) console.log(err); // an error occurred
        //     else {
        //         console.log('File Found')
        url = await s3.getSignedUrl('getObject', params)
        if (url) result = url
        // }
        return result

    }
    catch (err) {
        console.log(err)
        return "S3 Signed URL generation error"
    }
}

const isString = (x) => {
    return Object.prototype.toString.call(x) === '[object String]';
}

const getBucketName = (s3Url) => {
    if (s3Url) {
        const arr = s3Url.split("/")
        var bucketName = arr[2]
        return bucketName
    }

}

const getS3FileName = (s3Url) => {
    if (s3Url) {
        var temp = split(s3Url, '/', 3)
        return temp[3]
    }
}

const split = (str, separator, limit) => {
    str = str.split(separator);
    if (str.length > limit) {
        var ret = str.splice(0, limit);
        ret.push(str.join(separator));
        return ret;
    }
    return str;
}


const getAgeRestriction = (Id) => {
    var map = new Map([
        ["2", "2yo"],
        ["2U", "2yo+"],
        ["24", "2,3,4yo"],
        ["3", "3yo"],
        ["3U", "3yo+"],
        ["4", "4yo"],
        ["4U", "4yo+"],
        ["5", "5yo"],
        ["5U", "5yo+"],
        ["6", "6yo"],
        ["A", "Aged"],
        ["23", "2,3yo"],
        ["34", "3,4yo"],
        ["35", "3,4,5yo"],
        ["45", "4,5yo"],
        ["46", "4,5,6yo"],
        ["36", "3,4,5,6"],
    ])
    let result = map.get(Id)
    if (result == undefined) result = "Not Found"
    return result
}

const meetStageChange = (meet1stage,meet2stage) => {
    var map = {
        "Nominations": 0,
        "Weights": 1,
        "Acceptances": 2,
        "FinalFields": 2,
        "Final Fields": 2,
        "Results": 3
    }
    if (map[meet1stage] == map[meet2stage]) return false
    return true
}


const getGroup = (groupCode) => {
    //EVT_GROUP_NO - 1-1,2-2,3-3,4-LR
    var result = ""
    switch (groupCode) {
        case "0":
            result = "0"
            break;
        case "1":
            result = "1"
            break;
        case "2":
            result = "2"
            break;
        case "3":
            result = "3"
            break;
        case "4":
            result = "LR"
            break;
        default:
            result = "Not Found"
    }
    return result
}


const getOffMargin = (Id) => {
    var map = new Map([
        ["DH", "DH"],
        ["NS", "Ns"],
        ["SHH", "Sh1/2Hd"],
        ["HH", "Hf Hd"],
        ["SH", "Sh Hd"],
        ["H", "Hd"],
        ["LH", "Lg Hd"],
        ["HN", "Hf Nk"],
        ["SN", "Sh Nk"],
        ["NK", "Nk"],
        ["LN", "Lg Nk"],
        ["003", "1/4"],
        ["005", "1/2"],
        ["008", "3/4"],
        ["DS", "Dist"]
    ])
    let result = map.get(Id)
    if (result == undefined) result = "Not Found"
    return result
}


const getWeightType = (Id) => {
    var map = new Map([
        ["H", "Handicap"],
        ["W", "Weight For Age"],
        ["X", "Weight For Age With Penalties"],
        ["S", "Set Weight"],
        ["T", "Set Weight With Penalties"],
        ["U", "Set Weight With Penalties and Allowances"],
        ["P", "Special Weight"],
        ["C", "Catch Weight"],
        ["F", "Fly Weight"],
        ["A", "Set Weight With Allowances"],
        ["Q", "Quality"],

    ])
    let result = map.get(Id)
    if (result == undefined) result = "Not Found"
    return result
}
const getDurationMinutes = (duration) => {
    var minutes = duration / 3600
    return minutes.toFixed(2)
}




const getPastDateByDay = (days = 0) => {
    var date = moment();
    var result = date.subtract(days, 'day').format('YYYY-MM-DD') //+ "T00:00:00.000+00.00";
    return result
}

const capFstLtr = (str = "") => {
    if (!str) return "";
  
    const delimiters = [' ', '-', '\'', '.'];
    const chars = str.split('');
    let capitalizedStr = '';
    let capitalizeNext = true;
  
    for (const char of chars) {
      if (delimiters.includes(char)) {
        capitalizedStr += char;
        capitalizeNext = true;
      } else {
        capitalizedStr += capitalizeNext ? char.toUpperCase() : char.toLowerCase();
        capitalizeNext = false;
      }
    }
  
    return capitalizedStr;
  }


const getHorseColor = (colorCode) => {
    //CHESTNUT = ch / BAY = b / BROWN = br / BLACK = bl / GREY = gr / GREY-BAY = gr/b
    var result = 0
    switch (colorCode) {
        case "CHESTNUT":
            result = 1
            break;
        case "BAY":
            result = 2
            break;
        case "BROWN":
            result = 3
            break;
        case "BLACK":
            result = 4
            break;
        case "GREY":
            result = 5
            break;
        case "WHITE":
            result = 9
            break;
        case "BAY OR BROWN":
            result = 14
            break;
        case "BROWN OR BLACK":
            result = 15
            break;
        case "GREY-CHESTNUT":
            result = 16
            break;
        case "GREY-BAY":
            result = 17
            break;
        case "GREY-BROWN":
            result = 18
            break;
        case "GREY-BLACK":
            result = 19
            break;
        case "GREY-ROAN":
            result = 20
            break;
        case "ROAN":
            result = 21
            break;
        default:
            result = 0
    }
    return result
}

const cleanseBarrierTrials = (data) => {
    var cleanseddata = []
    for (raceEvent of data) {
        // if (raceEvent.weight_carried > 0){
        //     cleanseddata.push(raceEvent)
        // }
        if ((raceEvent.classes && raceEvent.classes.class_id && raceEvent.classes.class_id != 90 && (!raceEvent.classes.second_class_id || raceEvent.classes.second_class_id != 90)) || (raceEvent.classes && raceEvent.classes.class && raceEvent.classes.class == "Open" && (!raceEvent.classes.second_class_id || raceEvent.classes.second_class_id != 90) )){
          cleanseddata.push(raceEvent)
        }
    }
    return cleanseddata
  }

const getStateByNumber = async (stateNo) => {
    if (stateNo == 1){
        return 'ACT'
    } else if (stateNo == 2){
        return 'NSW'
    } else if (stateNo == 3){
        return 'VIC'
    } else if (stateNo == 4){
        return 'QLD'
    } else if (stateNo == 5){
        return 'SA'
    } else if (stateNo == 6){
        return 'WA'
    } else if (stateNo == 7){
        return 'TAS'
    } else if (stateNo == 8){
        return 'NT'
    } else if (stateNo == 9){
        return 'NZ'
    } else if (stateNo == 10){
        return 'HK'
    } else if (stateNo == 11){
        return 'SGP'
    } else if (stateNo == 99){
        return 'AUS'
    } else {
        return ''
    }
}

const getGearID = (Id) => {
    var map = new Map([
        ["Approved Race Plates (front and Hind)","416"],
        ["Approved Race Plates Front","3"],
        ["Approved Race Plates Hind","2"],
        ["Approved Race Plates","106"],
        ["Bandages (all round)","205"],
        ["Bandages (All Round)","205"],
        ["Bandages (Bumper)","133"],
        ["Bandages (fetlocks)","251"],
        ["Bandages (front and both hocks)","138"],
        ["Bandages (front and hind)","189"],
        ["Bandages (Front bumpers)","287"],
        ["Bandages (Front Fetlocks)","202"],
        ["Bandages (Front, Hind bumpers)","159"],
        ["Bandages (Front)","71"],
        ["Bandages (hind bumpers)","177"],
        ["Bandages (hind fetlocks)","174"],
        ["Bandages (hind hocks)","315"],
        ["Bandages (Hind)","87"],
        ["Bandages (Hocks)","219"],
        ["Bandages (Near Fore)","143"],
        ["Bandages (near hind hock)","386"],
        ["Bandages (Off Fore Fetlock)","308"],
        ["Bandages (Off Hind Fetlock)","361"],
        ["Bandages (off hock)","191"],
        ["Bandages Near Hind","110"],
        ["Bandages (Near Hind)","110"],
        ["Bandages Off Fore","5"],
        ["Bandages Off Hind","111"],
        ["Bandages (Off Fore)","5"],
        ["Bandages (Off Hind)","111"],
        ["Bandages","4"],
        ["Bar Plate (off side Front & Hind)","418"],
        ["Bar Plates (Aluminium bonded off fore)","314"],
        ["Bar Plates (Egg Bar near fore)","224"],
        ["Bar Plates (Egg Bars - front)","299"],
        ["Bar Plates (Egg Bars)","259"],
        ["Bar Plates (Front Heart Bars)","296"],
        ["Bar Plates (Front)","7"],
        ["Bar plates (Heart bars)","223"],
        ["Bar Plates (Hind (Egg Bar))","301"],
        ["Bar Plates (Hind)","6"],
        ["Bar Plates (Near Fore)","141"],
        ["Bar Plates (Near Side Front)","281"],
        ["Bar Plates (Near Side Hind)","294"],
        ["Bar Plates (Off Fore)","300"],
        ["Bar plates (Off hind)","346"],
        ["Bar Plates (Off Side Hind)","293"],
        ["Bar Plates (Offside Front)","212"],
        ["Bar Plates (Straight Bar (fronts))","175"],
        ["Bar Plates (Straight Bar)","154"],
        ["Bar Plates (Wedge Plates Front)","369"],
        ["Bar Plates (Wedges - hind)","360"],
        ["Bar Plates with Pads (Front)","311"],
        ["Bar Plates with Pads (Fronts and Hinds)","371"],
        ["bar plates with pads (hind)","370"],
        ["Bar plates with pads (near fore)","389"],
        ["Bar Plates with Pads (Near Side Front)","401"],
        ["Bar Plates with Pads (Offside Front)","318"],
        ["Bar plates with Syntheti (Fronts)","349"],
        ["Bar Plates with Syntheti (Near Side Hind)","406"],
        ["Bar Plates with Syntheti (Off-Side Front)","372"],
        ["Bar Plates: Egg bar/ Heart (off side front)","278"],
        ["Bar Plates: Egg bar/Heart (Fronts) Heart (Hinds)","277"],
        ["Bar Plates: Egg bar/Heart (Fronts)","307"],
        ["Bar Plates: Egg bar/Heart (Hinds)","295"],
        ["Bar Plates: Egg bar/Heart (near-side front)","276"],
        ["Bar Plates: Synthetic hoof repair & pad (NSF)","429"],
        ["Bar Plates","67"],
        ["Bar Shoe (Front)","139"],
        ["Bar shoe (left fore)","167"],
        ["Bar shoe (near fore)","192"],
        ["Bar Shoe (Near Front)","206"],
        ["Bar Shoe (Near Hind)","115"],
        ["Bar Shoe (Near Side Fore)","97"],
        ["Bar Shoe (Near Side Front)","242"],
        ["Bar Shoe (Near side)","243"],
        ["Bar Shoe (Off Hind)","117"],
        ["Bar Shoe (Off Side Fore)","100"],
        ["Bar Shoe (off side front)","183"],
        ["Bar Shoe (Off/Front)","184"],
        ["Bar Shoe","208"],
        ["Bar Shoes - Rolled Toe","233"],
        ["Bar Shoes (all round)","156"],
        ["Bar Shoes (both front)","83"],
        ["Bar Shoes (Front)","182"],
        ["Bar shoes (Hind)","94"],
        ["Bar Shoes","136"],
        ["Bare - Near Side Hind","425"],
        ["Bare-Front","282"],
        ["Bare-Hind","280"],
        ["Barrier Blanket","8"],
        ["Barrier Extension","108"],
        ["Bars Plates (Egg Bars - Front)","158"],
        ["Bell Boots (Front)","343"],
        ["Bell Boots","187"],
        ["Bit Burr (both sides)","262"],
        ["Bit Burr (Near side)","130"],
        ["Bit Burr (Off Side)","197"],
        ["Bit Burr","230"],
        ["Bit Lifter","12"],
        ["Bits (D-Bit)","366"],
        ["Bits (Egg Butt)","235"],
        ["Bits (Happy Mouth)","395"],
        ["Bits (JR-Lugging)","210"],
        ["Bits (kyneton)","238"],
        ["Bits (Lugging - Straight Bar)","147"],
        ["Bits (Lugging)","377"],
        ["Bits (Norton)","196"],
        ["Bits (ring)","195"],
        ["Bits (Running Rings)","368"],
        ["Bits (Victor)","227"],
        ["Bits (W)","169"],
        ["Bits Half Spoon","10"],
        ["Bits Rubber","11"],
        ["Bits","9"],
        ["Blindfold","79"],
        ["Blinker With One Cowl","21"],
        ["Blinkers (cup)","124"],
        ["Blinkers (Near Cup)","114"],
        ["Blinkers (Near Side)","20"],
        ["Blinkers (Off Cup)","170"],
        ["Blinkers (one eyed) (Near Side)","161"],
        ["Blinkers (one eyed) (Off Side)","144"],
        ["Blinkers (one eyed)","228"],
        ["Blinkers (Pre-Race Only)","422"],
        ["Blinkers Near Side","82"],
        ["Blinkers Off Side","116"],
        ["Blinkers (Near Side)","82"],
        ["Blinkers (Off Side)","116"],
        ["Blinkers","356"],
        ["Bonded Shoes (Front)","236"],
        ["Bonded shoes","260"],
        ["Boots (Bell boot off hind)","231"],
        ["Boots (Bumper)","160"],
        ["Boots (Coronet Ring)","331"],
        ["Boots (Fetlock) Front","186"],
        ["Boots (Front)","107"],
        ["Boots (Hind)","113"],
        ["Boots (Near Hind)","381"],
        ["Boots (near side hind hock)","367"],
        ["Boots (Off Hind)","297"],
        ["Boots (Rubber Ring - Near Fore)","332"],
        ["Boots (Rubber Ring Off Hind)","365"],
        ["Boots (Rubber rings) Hind","302"],
        ["Boots (Rubber Rings)","309"],
        ["Boots (Scalping rings - hind)","211"],
        ["Boots","15"],
        ["Brow Band","22"],
        ["Brushing Boots (front)","198"],
        ["Brushing Boots (Front)","198"],
        ["Brushing Boots (Hind)","70"],
        ["Brushing Boots","14"],
        ["Bubble Cheeker (Burr Bit-Near Side)","341"],
        ["Bubble Cheeker (Near and Offside)","298"],
        ["Bubble Cheeker Near Side","72"],
        ["Bubble Cheeker (Near Side)","72"],
        ["Bubble Cheeker Off Side","23"],
        ["Bubble Cheeker (Off Side)","23"],
        ["Bubble Cheeker","91"],
        ["Bubble Cheekers","445"],
        ["Bumper BA - Fronts","400"],
        ["Bumper BA (Hinds)","397"],
        ["bumper boots (front)","391"],
        ["Bumper Boots (hind)","188"],
        ["bumper boots (Front)","391"],
        ["Bumper Boots (Hind)","188"],
        ["Bumper Boots","16"],
        ["Butterfly bit with W tongue clip","249"],
        ["Butterfly Bit","24"],
        ["Cheekers (bubble off side only)","221"],
        ["Cheekers (near side)","207"],
        ["Cheekers (off side)","180"],
        ["Cheekers","66"],
        ["Compression Hood with Ear Muffs","446"],
        ["Compression Hood With Ear Muffs","446"],
        ["Concussion Plates (Easy Walkers-Front)","217"],
        ["Concussion Plates (Front and Hind)","417"],
        ["Concussion Plates (Front)","104"],
        ["Concussion Plates (Gliderz-Front)","334"],
        ["Concussion Plates (gliderz)","222"],
        ["Concussion Plates (Hind)","176"],
        ["Concussion Plates (Wedge off fore)","317"],
        ["Concussion Plates Lamina (Fronts)","316"],
        ["concussion plates shock near side front","431"],
        ["Concussion Plates-Shock (Front and Hind)","337"],
        ["Concussion Plates: Lamina (Hinds)","436"],
        ["concussion plates: lamina (offside front)","375"],
        ["Concussion Plates: Shock (Front)","303"],
        ["Concussion Plates: Shock (Hind)","304"],
        ["Concussion plates: Shock (Off Front)","347"],
        ["Concussion Plates","26"],
        ["Cornell Collar","25"],
        ["Cornell Head Collar","290"],
        ["Coronet Boots (Front)","330"],
        ["Coronet Boots (Hind)","329"],
        ["Cross Over Noseband","27"],
        ["Cross-over Nose Band","27"],
        ["Cross-over Nose Band (Cavasham)","190"],
        ["Cross-over Nose Band (Hanoverian)","313"],
        ["Dually Headcollar","412"],
        ["Ear Muffs (Pre-Race Only)","413"],
        ["Ear Muffs","29"],
        ["Ear Plugs (Pre-race only)","440"],
        ["Ear Plugs","28"],
        ["Egg Bar Plates (Front)","216"],
        ["Egg Bar Shoes","166"],
        ["Egg Bars (Hind)","148"],
        ["Equicast","272"],
        ["Equiloc Racing Pads (Front)","239"],
        ["Equilock Shoe Repair (Front & N/S Hind)","132"],
        ["Equilox (Fore)","81"],
        ["Equilox Hoof Repair (off front)","246"],
        ["Equilox Off Fore","84"],
        ["Equilox Off Hind On","85"],
        ["Equithane Hoof Repair","226"],
        ["Equithane Repair (off-side front)","273"],
        ["Gelded","30"],
        ["Glider Shoes","172"],
        ["Gliderz shoes (front)","264"],
        ["Gliderz Shoes","271"],
        ["Glu-Shoes","155"],
        ["Glue on Plates (front)","253"],
        ["Glue On Shoe (Near Fore)","336"],
        ["Glue On Shoes (Equipac) Front","362"],
        ["Glue On Shoes (Front & Hind)","420"],
        ["Glue on Shoes (Front)","105"],
        ["Glue On Shoes (Hind)","164"],
        ["Glue on Shoes (Hind)","164"],
        ["Glue On Shoes (Off-side Hind)","352"],
        ["Glue On Shoes","78"],
        ["Half Spoon Bit","424"],
        ["Hanging Bit","32"],
        ["Hanoverian Nose Band","399"],
        ["Heart Bar (Near Front)","213"],
        ["Heart Bar (off fore)","263"],
        ["Heart Bar Shoes","89"],
        ["Heart Bars (Front)","76"],
        ["Heart Bars (hind)","265"],
        ["Hock Boot (Near Hind)","444"],
        ["Hock Boot (Off Hind)","419"],
        ["Hock Boots","33"],
        ["Hood","34"],
        ["Hoof Pads (All Round)","291"],
        ["Hoof Pads (Mac pads - Front)","323"],
        ["Hoof Pads (Mac Pads)","327"],
        ["Hoof Pads (Moulded front)","240"],
        ["Hoof Pads (Near Fore)","256"],
        ["Hoof Pads (Off Fore)","306"],
        ["Hoof Pads (Off Hind)","335"],
        ["Hoof Pads (Wedges - Front)","237"],
        ["Hoof Pads (Wedges all round)","218"],
        ["Hoof Pads Front","36"],
        ["Hoof Pads","35"],
        ["Kyneton Nose Band (Hanoverian)","342"],
        ["Kyneton Nose Band","37"],
        ["Lugging Bit (JR)","376"],
        ["Lugging Bit (Rubber)","350"],
        ["Lugging Bit","38"],
        ["Nasal Strip","269"],
        ["Natural Balance Shoes (Front)","250"],
        ["Natural Balance Shoes","254"],
        ["Non Standard Shoes (Bare - Fronts)","385"],
        ["Non Standard Shoes (Bare - Hinds)","383"],
        ["Non Standard Shoes (Gliderz-Front)","122"],
        ["Non Standard Shoes (Gliderz)","152"],
        ["Non Standard Shoes (Near Side Front)","379"],
        ["Non Standard Shoes","39"],
        ["Norton Bit","40"],
        ["Nose Band","41"],
        ["Nose Roll","42"],
        ["One Eyed Blinker (Near Side)","92"],
        ["One Eyed Blinker (Off Side)","93"],
        ["One Eyed Blinker (Right)","126"],
        ["One Eyed Blinker","17"],
        ["One Eyed Blinkers (Left)","125"],
        ["One Eyed Winker (Left)","268"],
        ["One Eyed Winker (Right)","241"],
        ["One Eyed Winkers","64"],
        ["Pacifier with cowls","44"],
        ["Pacifier with One Cowl","410"],
        ["Pacifiers (Pre-Race Only)","415"],
        ["Pacifiers","43"],
        ["Pad (near fore)","261"],
        ["Pad (Off Fore)","98"],
        ["Pad Shoes","163"],
        ["Pads - plastic/leather/rub (Front & Hind)","328"],
        ["Pads - plastic/leather/rub (front)","283"],
        ["Pads - plastic/leather/rub (hind)","284"],
        ["Pads - plastic/leather/rub offside front","288"],
        ["Pads - plastic/leather/rub offside hind","310"],
        ["Pads (Front)","101"],
        ["Pads (Hind)","339"],
        ["Pads (Near Side Front)","123"],
        ["Pads (Near Side Hind)","405"],
        ["Pads (Off Side Front)","402"],
        ["Pads (Offside Hind)","403"],
        ["Pads: Plastic/leather/Rub Near Side front","321"],
        ["Pads: Plastic/leather/rub near side hind","340"],
        ["Pads","135"],
        ["Plates (3/4 hind)","157"],
        ["Pulling Bit","279"],
        ["Race Plates (3/4 shoe near fore)","200"],
        ["Race Plates (3/4) Front","178"],
        ["Race Plates (3/4) Hinds","373"],
        ["Race Plates (Bonded Fronts)","378"],
        ["Race Plates (Bonded)","382"],
        ["Race Plates (Easy walkers) front","173"],
        ["Race Plates (Ezy Walkers)","234"],
        ["Race Plates (Gliderz) Front","146"],
        ["Race Plates (Hoof Filler)","393"],
        ["Race Plates (Shock Tamers)","204"],
        ["Race Plates (Side Clips)","324"],
        ["Race Plates (Speedy Toe-Front)","46"],
        ["Race Plates (Speedy Toes)","292"],
        ["Race Plates (Square Toe - Fronts)","258"],
        ["Race Plates (Standard)","274"],
        ["Race Plates (Tips)","326"],
        ["Race Plates (Wedges - Off Fore)","333"],
        ["Race Plates (Wedges-All round)","338"],
        ["Race Plates (Wedges-Front)","193"],
        ["Race Plates (Wedges) Hind","179"],
        ["Race Plates (Wedges)","118"],
        ["Race Plates (World Plates - front)","199"],
        ["Race Plates (World)","285"],
        ["Race Plates Front","47"],
        ["Race Plates Square Toed","120"],
        ["Race Plates","45"],
        ["Racing Bare - Hind","380"],
        ["Racing Without Shoes Front","50"],
        ["Racing Without Shoes Hind And Front","51"],
        ["Racing Without Shoes Hind","49"],
        ["Racing Without Shoes","48"],
        ["Raised Heel","185"],
        ["Reverse Egg Bar Shoes","203"],
        ["Ring Bit","112"],
        ["Rolled Toe (egg-bar) shoes","247"],
        ["Rolled Toe Shoes","214"],
        ["Rubber Ring","52"],
        ["Scalping Boots","432"],
        ["Shadow Roll","55"],
        ["Sheepskin Browband","56"],
        ["Sheepskin Cheek Pieces (One Side)","409"],
        ["Sheepskin Cheek Pieces","408"],
        ["Shock Pads","127"],
        ["Shock Shod Shoes (Easy Walkers) Front","153"],
        ["Shock Shod Shoes (Front)","137"],
        ["Shock Shod Shoes (Glider shoes)","248"],
        ["Shock Shod Shoes (Gliderz-Front)","201"],
        ["Shock Shod Shoes (Shock Tamers)","257"],
        ["Shock Shod Shoes","69"],
        ["Shock Shoes (Gliderz)","142"],
        ["Shock Tamer (Off-Fore)","414"],
        ["Shoes - Shock Tamers (Front)","215"],
        ["Side Winker One Side","54"],
        ["Square Toe Egg Bar (Front)","140"],
        ["Square Toe Shoes","232"],
        ["Stallion Chain","80"],
        ["Standard Bit (D)","392"],
        ["Standard Bit (ring bit)","57"],
        ["Standard Plates (No pads)","427"],
        ["Standard Plates","53"],
        ["Standard Race Tips (Front)","129"],
        ["Standard Race Tips (Hind)","165"],
        ["Standard Race Tips","131"],
        ["Standard Shoe (O/S Front)","435"],
        ["Standard Shoes (both Hinds)","430"],
        ["Standard Shoes all-round","437"],
        ["synthetic hoof filler (both front)","387"],
        ["Synthetic Hoof Filler (Front)","320"],
        ["Synthetic Hoof Filler (near fore)","319"],
        ["Synthetic Hoof Filler (Near Hind)","428"],
        ["Synthetic Hoof Filler (Off Fore)","407"],
        ["Synthetic Hoof Filler (Off Hind)","345"],
        ["Synthetic Hoof Filler (Right Fore)","404"],
        ["Synthetic Hoof Filler","194"],
        ["Synthetic hoof repair - EQ (fronts)","289"],
        ["Synthetic hoof repair - Equithane - Fronts","348"],
        ["Synthetic Hoof Repair (Near Side Fore)","325"],
        ["Synthetic Hoof Repair (Near Side Hind)","312"],
        ["Synthetic hoof repair: EQ (fronts and hinds)","359"],
        ["Synthetic hoof repair: EQ (near side front)","363"],
        ["Synthetic Hoof Repair: EQ (Off Side Front)","374"],
        ["Synthetic hoof repair: EQ (off side hind)","364"],
        ["Tail Chain","58"],
        ["Tendon Boots (Behind)","441"],
        ["Tendon Boots (Near Fore)","433"],
        ["Tendon Boots (Off Fore)","434"],
        ["Tendon Boots Front","162"],
        ["Tendon Boots (Front)","162"],
        ["Tips (all round)","344"],
        ["Tips (Fronts)","286"],
        ["Tips (Hind)","322"],
        ["Tips (Near Fore)","225"],
        ["Tips (Off Side Front)","396"],
        ["Tips","90"],
        ["Toe Clips","423"],
        ["Tongue Bit","73"],
        ["Tongue Clip","442"],
        ["Tongue Control Bit (Lugging)","151"],
        ["Tongue Control and Lugging Bit","151"],
        ["Tongue Control Bit (Straight bar rubber)","275"],
        ["Tongue Control Bit (Victor)","209"],
        ["Tongue Control Bit (w)","220"],
        ["Tongue Control Bit","68"],
        ["Tongue Control","109"],
        ["Tongue Tie","60"],
        ["Tongue Tie (variant)","358"],
        ["Unshod (behind)","426"],
        ["Unshod (front)","438"],
        ["Unshod (Behind)","426"],
        ["Unshod (Front)","438"],
        ["Visor (Near Side)","305"],
        ["Visor (Off Side)","384"],
        ["Visor with one cowl","443"],
        ["Visor","61"],
        ["Visors","267"],
        ["Wedge Pads (Front)","229"],
        ["Wedge Plates (Front)","119"],
        ["Wedge Plates (Hind)","150"],
        ["Wedge Shoe (near hind)","181"],
        ["Wedge Shoe (Off Side Front)","270"],
        ["Wedge Shoes (Fore)","96"],
        ["Wedge Shoes (Hind)","95"],
        ["Wedge Shoes","121"],
        ["Wedges (Hind)","245"],
        ["Weighted shoes (hind)","252"],
        ["Winker (Near Side)","63"],
        ["Winker Near Side","63"],
        ["Winker Off Side","75"],
        ["Winker (Off Side)","75"],
        ["Winkers","62"],
        ["World Racing Plates (Front)","255"],
        ["World Racing Plates (off fore)","266"],
        ["World Racing Plates","168"],
        ["WWR Plates (Fronts)","244"],
        ["Standard Bit","1"]
    ])
    let result = map.get(Id)
    if (result == undefined) {
        console.log(`Unable to map gear type ${Id}`)
        result = 0
    }
    return result
}
const generateUniqueRaceId = (meetingDate, trackId, raceNumber, trialResult) => {
    var yy = moment(meetingDate).format('YY').toString()
    var dd = moment(meetingDate).format('DDD').toString()
    if (dd < 10) {dd = '00' + dd.toString()} else if (dd < 100) {dd = '0' + dd.toString()} else dd = dd.toString()
    var raceNumberString = raceNumber.toString()
    if (raceNumber < 10){
        raceNumberString = "0" + raceNumberString
    }
    if (trialResult){
        trackId = (parseInt(trackId) + 5000).toString()
    }else{
        if (parseInt(trackId) < 1000) {
            if (parseInt(trackId) < 100){
                if (parseInt(trackId) < 10) {
                    trackId = "000"+trackId.toString()
                } else {
                    trackId = "00"+trackId.toString()
                }
            } else {
                trackId = "0"+trackId.toString()
            } 
        }
    }
    var id = yy + dd + raceNumberString + trackId
    return id
}
module.exports = {
    inspectObject,
    readS3File,
    uploadFileS3,
    getSecrets,
    jsonToXml,
    xmlToJs,
    convertDate,
    sDate,
    convertStringToDDMMYYYY,
    getDayMonth,
    openDBConnection,
    closeDBConnection,
    getCapitalFirstLetterOnly,
    getS3SignedUrl,
    isString,
    getBucketName,
    getS3FileName,
    getTrackCondition,
    getTrackGrading,
    getAgeRestriction,
    getGroup,
    getOffMargin,
    getWeightType,
    getDurationMinutes,
    getPastDateByDay,
    capFstLtr,
    getHorseColor,
    cleanseBarrierTrials,
    getStateByNumber,
    getGearID,
    mailAlert,
    generateUniqueRaceId,
    meetStageChange
}
