const mongoose = require('mongoose');
const moment = require('moment')
const helper = require("./helper")
const uuid = require('uuid');
const centaur = require('@mediality/centaur')

const trainerAPI = async (req) => {
    switch (req.method) {
        case "GET":
            console.log("Inside Trainer GET Handler")
            data = await trainerRead(req)
            return data
            break;
        case "POST":
            console.log("Inside Trainer POST Handler")
            data = await trainerCreate(req)
            return data
            break;
        case "PUT":
            console.log("Inside Trainer PUT Handler")
            data = await trainerUpdate(req)
            return data
            break;
        case "DELETE":
            console.log("Inside Trainer DELETE Handler")
            data = await trainerDelete(req)
            return data
            break;
        default:
            return { "msg": "No API Handler" }
    }
}

const trainerRead = async (payload) => {
    var response = ''

    if (payload.params) {
        if (('id' in payload.params) && (payload.params.id)) {
            var data = await centaur.trainers.findOne({ _id: payload.params.id }).lean()
            if (data) {
                response = data
            }
        }

        if (('s' in payload.params) && (payload.params.s)) {
            s = payload.params.s.trim()
            s = s.toUpperCase()
            if (!helper.isString(s)) {
                return "Invalid String"
            }
            var data = false
            if (s.charAt(0) == '"' && s.charAt(s.length - 1) == '"'){
                s = s.slice(1,-1)
                data = await centaur.trainers.find({ TRN_TRAINER_DISPLAYNAME: s }).limit(50).lean()
            }else {
                data = await centaur.trainers.find({ TRN_TRAINER_DISPLAYNAME: { $regex: s } }).select().limit(50).lean()
            }
            if (data) {
                response = data
            }
        }
    }
    return response
}

const trainerCreate = async (payload) => {
    var payload = JSON.parse(payload.body)
    let latest_trainer = await centaur.trainers.findOne().sort({ TRN_TRAINER_ID: -1 }).limit(1)
    var trainerId = parseInt(latest_trainer.TRN_TRAINER_ID) + 1
    var id = uuid.v4()
    var dataToCreate = {
        _id: id,
        RISATrainerID: payload.RISATrainerID ? payload.RISATrainerID : 0,
        RISAPartnerID: payload.RISAPartnerID ? payload.RISAPartnerID : 0,
        Name: payload.Name ? payload.Name : '',
        NZ_Name: payload.NZ_Name ? payload.NZ_Name : '',
        Location: payload.Location ? payload.Location : '',
        State: parseInt(payload.State ? payload.State : 0),
        TRN_TRAINER_ID: trainerId,
        TRN_TRAINER_SURNAME: payload.TRN_TRAINER_SURNAME ? payload.TRN_TRAINER_SURNAME : '',
        TRN_TRAINER_FIRSTNAME: payload.TRN_TRAINER_FIRSTNAME ? payload.TRN_TRAINER_FIRSTNAME : '',
        TRN_TRAINER_DISPLAYNAME: payload.TRN_TRAINER_DISPLAYNAME ? payload.TRN_TRAINER_DISPLAYNAME : '',
        TRN_PARTNERSHIP_DISPLAYNAME: payload.TRN_PARTNERSHIP_DISPLAYNAME ? payload.TRN_PARTNERSHIP_DISPLAYNAME : '',
        TRN_ROW_STATUS: payload.TRN_ROW_STATUS ? payload.TRN_ROW_STATUS : 'A'
    }
    let newRecord = new centaur.trainers(dataToCreate)
    var status = await newRecord.save();
    console.log("New Trainer Created: " + status)
    if (status) {
        return true
    } else {
        return false
    }
}

const trainerUpdate = async (payload) => {
    var payload = JSON.parse(payload.body)
    var dataToUpdate = {
        TRN_TRAINER_ID: parseInt(payload.TRN_TRAINER_ID ? payload.TRN_TRAINER_ID : 0),
        RISATrainerID: payload.RISATrainerID ? payload.RISATrainerID : 0,
        RISAPartnerID: payload.RISAPartnerID ? payload.RISAPartnerID : 0,
        Name: payload.Name ? payload.Name : '',
        NZ_Name: payload.NZ_Name ? payload.NZ_Name : '',
        Location: payload.Location ? payload.Location : '',
        State: parseInt(payload.State ? payload.State : 0),
        TRN_TRAINER_SURNAME: payload.TRN_TRAINER_SURNAME ? payload.TRN_TRAINER_SURNAME : '',
        TRN_TRAINER_FIRSTNAME: payload.TRN_TRAINER_FIRSTNAME ? payload.TRN_TRAINER_FIRSTNAME : '',
        TRN_TRAINER_DISPLAYNAME: payload.TRN_TRAINER_DISPLAYNAME ? payload.TRN_TRAINER_DISPLAYNAME : '',
        TRN_PARTNERSHIP_DISPLAYNAME: payload.TRN_PARTNERSHIP_DISPLAYNAME ? payload.TRN_PARTNERSHIP_DISPLAYNAME : '',
        TRN_ROW_STATUS: payload.TRN_ROW_STATUS ? payload.TRN_ROW_STATUS : ''
    }
    let status = await centaur.trainers.updateOne({ _id: payload._id }, {
        "$set": dataToUpdate
    }).lean()
    console.log(`Trainer ${payload.TRN_TRAINER_ID} Updated: `)
    console.log(status)
    if (status) {
        return true
    } else {
        return false
    }
}

const trainerDelete = async (payload) => {
    var payload = JSON.parse(payload.body)
    var trainerId = payload._id
    if (trainerId) {
        var record_delete = await centaur.trainers.deleteOne({ _id: trainerId })
        if (record_delete) {
            return true
        } else {
            return false
        }
    }
}

module.exports = {
    trainerAPI,
    trainerCreate,
    trainerRead,
    trainerUpdate,
    trainerDelete
}
