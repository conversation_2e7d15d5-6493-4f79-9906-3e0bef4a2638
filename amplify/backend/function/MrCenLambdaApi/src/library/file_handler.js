const AWS = require("aws-sdk");
const REGION = process.env.region || "ap-southeast-2";
const BUCKET = `mr-cen-file-storage-${process.env.ENV}`;
const prefix = "unprocessed/";

// Configure AWS SDK
AWS.config.update({ region: REGION });

const s3 = new AWS.S3({ apiVersion: "2006-03-01" });

const fileAPI = async (req) => {
  console.log(req);
  let data;
  switch (req.method) {
    case "GET":
      console.log("Inside FileAPI GET Handler");
      data = await getS3SignedUrl(req.params.fileName, req.params.fileType, 'putObject');
      return { statusCode: 200, body: JSON.stringify(data) };
    case "POST":
      console.log("Inside FileAPI POST Handler");
      data = await getS3SignedUrl(req.params.fileName, 'putObject');
      return { statusCode: 200, body: JSON.stringify(data) };
    default:
      return { statusCode: 400, body: JSON.stringify({ msg: "No API Handler" }) };
  }
};

const getS3SignedUrl = async (fileName,fileType, operation) => {
  const params = {
    Bucket: BUCKET,
    Key: `${prefix}${fileName}`,
    Expires: 60,
    ContentType: fileType,
  };

  try {
    const url = await s3.getSignedUrlPromise(operation, params);
    return { url };
  } catch (err) {
    console.error(err);
    return { error: "S3 Signed URL generation error" };
  }
};

module.exports = {
  fileAPI,
};
