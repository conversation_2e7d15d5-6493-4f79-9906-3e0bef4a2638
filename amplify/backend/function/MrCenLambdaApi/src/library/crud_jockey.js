const helper = require("./helper");
const uuid = require("uuid");
const centaur = require("@mediality/centaur");
const AWS = require("aws-sdk");
const lambda = new AWS.Lambda();
const jockeyAPI = async (req) => {
  switch (req.method) {
    case "GET":
      console.log("Inside Jockey GET Handler");
      data = await jockeyRead(req);
      return data;
    case "POST":
      console.log("Inside Jockey POST Handler");
      data = await jockeyCreate(req);
      return data;
    case "PUT":
      console.log("Inside Jockey PUT Handler");
      data = await jockeyUpdate(req);
      return data;
    case "DELETE":
      console.log("Inside Jockey DELETE Handler");
      data = await jockeyDelete(req);
      return data;
    default:
      return { msg: "No API Handler" };
  }
};

const jockeyRead = async (payload) => {
  var response = "";

  if (payload.params) {
    if ("id" in payload.params && payload.params.id) {
      var data = await centaur.jockeys
        .findOne({ _id: payload.params.id })
        .lean();
      if (data) {
        response = data;
      }
    }

    if ("s" in payload.params && payload.params.s) {
      s = payload.params.s.trim();
      s = s.toUpperCase();
      if (!helper.isString(s)) {
        return "Invalid String";
      }
      var data = false;
      if (s.charAt(0) == '"' && s.charAt(s.length - 1) == '"') {
        s = s.slice(1, -1);
        data = await centaur.jockeys
          .find({ JOC_JOCKEY_DISPLAYNAME: s })
          .limit(50)
          .lean();
      } else {
        data = await centaur.jockeys
          .find({ JOC_JOCKEY_DISPLAYNAME: { $regex: s } })
          .select()
          .limit(50)
          .lean();
      }
      if (data) {
        response = data;
      }
    }
  }
  return response;
};

const jockeyCreate = async (payload) => {
  var payload = JSON.parse(payload.body);
  let latest_jockey = await centaur.jockeys
    .findOne()
    .sort({ JOC_JOCKEY_ID: -1 })
    .limit(1);
  var jockeyId = parseInt(latest_jockey.JOC_JOCKEY_ID) + 1;
  var id = uuid.v4();
  var dataToCreate = {
    _id: id,
    JOC_JOCKEY_ID: jockeyId,
    JOC_JOCKEY_SURNAME: payload.JOC_JOCKEY_SURNAME
      ? payload.JOC_JOCKEY_SURNAME
      : "",
    JOC_JOCKEY_FIRSTNAME: payload.JOC_JOCKEY_FIRSTNAME
      ? payload.JOC_JOCKEY_FIRSTNAME
      : "",
    JOC_JOCKEY_DISPLAYNAME: payload.JOC_JOCKEY_DISPLAYNAME
      ? payload.JOC_JOCKEY_DISPLAYNAME
      : "",
    JOC_STATE_REGISTERED: payload.JOC_STATE_REGISTERED
      ? payload.JOC_STATE_REGISTERED
      : 0,
    JOC_JOCKEY_DISTRICT: payload.JOC_JOCKEY_DISTRICT
      ? payload.JOC_JOCKEY_DISTRICT
      : "",
    JOC_JOCKEY_WEIGHT: payload.JOC_JOCKEY_WEIGHT
      ? payload.JOC_JOCKEY_WEIGHT
      : "",
    JOC_JOCKEY_APPRENTICE_IND: payload.JOC_JOCKEY_APPRENTICE_IND
      ? payload.JOC_JOCKEY_APPRENTICE_IND
      : "",
    JOC_JOCKEY_AMATEUR_IND: payload.JOC_JOCKEY_AMATEUR_IND
      ? payload.JOC_JOCKEY_AMATEUR_IND
      : "",
    JOC_COUNTRY_CODE: payload.JOC_COUNTRY_CODE ? payload.JOC_COUNTRY_CODE : "",
    JOC_JOCKEY_IN_USE_IND: payload.JOC_JOCKEY_IN_USE_IND
      ? payload.JOC_JOCKEY_IN_USE_IND
      : "",
    JOC_JOCKEY_RANAME: payload.JOC_JOCKEY_RANAME
      ? payload.JOC_JOCKEY_RANAME
      : "",
    JOC_JOCKEY_NZNAME: payload.JOC_JOCKEY_NZNAME
      ? payload.JOC_JOCKEY_NZNAME
      : ""
  };
  let newRecord = new centaur.jockeys(dataToCreate);
  var status = await newRecord.save();
  console.log("New Jockey Created: " + status);
  if (status) {
    return true;
  } else {
    return false;
  }
};

const jockeyUpdate = async (payload) => {
  console.log(payload);
  var payload = JSON.parse(payload.body);
  var jockeyId = payload._id;
  const jockeyInDB = await centaur.jockeys.findOne({ _id: jockeyId }).lean();
  var dataToUpdate = {
    JOC_JOCKEY_ID: payload.JOC_JOCKEY_ID ? payload.JOC_JOCKEY_ID : 0,
    JOC_JOCKEY_SURNAME: payload.JOC_JOCKEY_SURNAME
      ? payload.JOC_JOCKEY_SURNAME
      : "",
    JOC_JOCKEY_FIRSTNAME: payload.JOC_JOCKEY_FIRSTNAME
      ? payload.JOC_JOCKEY_FIRSTNAME
      : "",
    JOC_JOCKEY_DISPLAYNAME: payload.JOC_JOCKEY_DISPLAYNAME
      ? payload.JOC_JOCKEY_DISPLAYNAME
      : "",
    JOC_STATE_REGISTERED: payload.JOC_STATE_REGISTERED
      ? payload.JOC_STATE_REGISTERED
      : 0,
    JOC_JOCKEY_DISTRICT: payload.JOC_JOCKEY_DISTRICT
      ? payload.JOC_JOCKEY_DISTRICT
      : "",
    JOC_JOCKEY_WEIGHT: payload.JOC_JOCKEY_WEIGHT
      ? payload.JOC_JOCKEY_WEIGHT
      : "",
    JOC_JOCKEY_APPRENTICE_IND: payload.JOC_JOCKEY_APPRENTICE_IND
      ? payload.JOC_JOCKEY_APPRENTICE_IND
      : "",
    JOC_JOCKEY_AMATEUR_IND: payload.JOC_JOCKEY_AMATEUR_IND
      ? payload.JOC_JOCKEY_AMATEUR_IND
      : "",
    JOC_COUNTRY_CODE: payload.JOC_COUNTRY_CODE ? payload.JOC_COUNTRY_CODE : "",
    JOC_JOCKEY_IN_USE_IND: payload.JOC_JOCKEY_IN_USE_IND
      ? payload.JOC_JOCKEY_IN_USE_IND
      : "",
    JOC_JOCKEY_RANAME: payload.JOC_JOCKEY_RANAME
      ? payload.JOC_JOCKEY_RANAME
      : "",
    JOC_JOCKEY_NZNAME: payload.JOC_JOCKEY_NZNAME
      ? payload.JOC_JOCKEY_NZNAME
      : ""
  };
  let status = await centaur.jockeys
    .updateOne(
      { _id: jockeyId },
      {
        $set: dataToUpdate,
      }
    )
    .lean();
  if (
    payload.JOC_JOCKEY_FIRSTNAME !== jockeyInDB.JOC_JOCKEY_FIRSTNAME ||
    payload.JOC_JOCKEY_DISPLAYNAME !== jockeyInDB.JOC_JOCKEY_DISPLAYNAME ||
    payload.JOC_JOCKEY_SURNAME !== jockeyInDB.JOC_JOCKEY_SURNAME
  ) {
    const jockeyToBePassed = await centaur.jockeys.findOne({ _id: jockeyId }).lean();
    console.log("Jockey updates for name ");
    const horseUpdateLambda = `MRCenLambdaHorseNameUpdate-${process.env.ENV}`;
    console.log(horseUpdateLambda);
    const params = {
      FunctionName: horseUpdateLambda,
      InvocationType: "Event", // Changed to "Event" for async invocation
      Payload: JSON.stringify({ jockey: jockeyToBePassed, jockeyUpdate: true }),
    };
    const result = await lambdaInvokeAsync(params); // This now waits for the async operation
    console.log('Lambda invoke result:', result);

  }

  console.log(`Jockey ${jockeyId} Updated: `);
  console.log(status);
  if (status) {
    return true;
  } else {
    return false;
  }
};

const jockeyDelete = async (payload) => {
  var payload = JSON.parse(payload.body);
  var jockeyId = payload._id;
  if (jockeyId) {
    var record_delete = await centaur.jockeys.deleteOne({ _id: jockeyId });
    if (record_delete) {
      return true;
    } else {
      return false;
    }
  }
};
const lambdaInvokeAsync = async (params) => {
  return new Promise((resolve, reject) => {
    lambda.invoke(params, function (err, data) {
      if (err) {
        console.error("Error while calling MRCenLambdaHorseNameUpdate", err);
        reject(err);
      } else {
        console.log("Successfully called MRCenLambdaHorseNameUpdate asynchronously", data);
        resolve(data);
      }
    });
  });
};


module.exports = {
  jockeyAPI,
  jockeyCreate,
  jockeyRead,
  jockeyUpdate,
  jockeyDelete,
};
