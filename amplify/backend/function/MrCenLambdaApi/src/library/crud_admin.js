const centaur = require('centaur')
const mongoose = require('mongoose')

exports.read = async (event) => {
    try {
        var secretDetails = await centaur.getSecrets(process.env.centaurSecrets)
        var connDetails = await centaur.generateConnectionString(secretDetails)
        await mongoose.connect(connDetails.CONNECTION_STRING, connDetails.PARAMETERS)
    } catch (err) {
        console.log(err)
        return err
    } finally {
        await conn.close()
        console.log(`Mongoose Connection State: ${conn.readyState}`);
        return results
    }
};
