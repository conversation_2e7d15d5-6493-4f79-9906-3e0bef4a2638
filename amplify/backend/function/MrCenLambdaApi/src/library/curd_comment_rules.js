const centaur = require("@mediality/centaur")
const helper = require("./helper"); // if you have helper methods similar to the horse file
const uuid = require("uuid");

/**
 * Main entry point for comments API based on HTTP method.
 * @param {Object} req - The request object.
 * @returns {Object|Boolean} Response or status object.
 */
const commentsAPI = async (req) => {
  let data;
  switch (req.method) {
    case "GET":
      console.log("Inside Comments GET Handler");
      data = await commentsRead(req);
      return data;
    case "POST":
      console.log("Inside Comments POST Handler");
      data = await commentsCreate(req);
      return data;
    case "PUT":
      console.log("Inside Comments PUT Handler");
      data = await commentsUpdate(req);
      return data;
    case "DELETE":
      console.log("Inside Comments DELETE Handler");
      data = await commentsDelete(req);
      return data;
    default:
      return { msg: "No API Handler for that method." };
  }
};

/**
 * Fetches comments records based on query parameters.
 * - If req.params.id is provided, fetch a single comment by its Id.
 * - If req.params.s is provided, perform a name-based search (case-insensitive).
 * @param {Object} payload
 * @returns {Array|Object|String} The retrieved document(s) or a message.
 */
const commentsRead = async (payload) => {
  let response = "";
  if (payload.params) {
    // 1) Reading by exact numeric Id
    if ("id" in payload.params && payload.params.id) {
      const doc = await centaur.comments.findOne({ Id: parseInt(payload.params.id) }).lean();
      if (doc) {
        response = doc;
      }
    }

    // 2) Searching by string (Name or name)
    if ("s" in payload.params && payload.params.s) {
      let s = payload.params.s.trim();

      // Optional: check if s is a valid string using a helper
      if (helper && typeof helper.isString === "function") {
        if (!helper.isString(s)) {
          return "Invalid search string";
        }
      }

      // If user encloses the search term in quotes, we do an exact match
      if (s.charAt(0) === '"' && s.charAt(s.length - 1) === '"') {
        s = s.slice(1, -1); // remove surrounding quotes
        response = await centaur.comments
          .find({ $or: [{ Name: s }, { name: s }] })
          .limit(50)
          .lean();
      } else {
        // partial match
        // note: You can add `$options: 'i'` to make it case-insensitive if desired
        response = await centaur.comments
          .find({
            $or: [
              { Name: { $regex: s } },
              { name: { $regex: s } },
            ],
          })
          .limit(50)
          .lean();
      }
    }
    // return all comments that are active
    if ("all" in payload.params && payload.params.all) {
      response = await centaur.comments.find({ active: true}).limit(50).lean();
    }

  }
  return response;
};

/**
 * Creates a new comment record.
 * - Auto-increments `Id` by finding the highest existing Id in the collection.
 * @param {Object} req
 * @returns {Boolean}
 */
const commentsCreate = async (req) => {
  // parse the incoming JSON body
  const body = JSON.parse(req.body);
  console.log(body);

  // {
  //   groupName: 'Rest',
  //   name: 'Spell',
  //   order: 1,
  //   description: 'szdsgv',
  //   active: true,
  //   direction: 0,
  //   sentences: [ { sentence: 'REPLACE', order: 1, usecount: 0 } ],
  //   ruleParameters: [ { type: 'count', compare1: [Object], minvalue: 1 } ],
  //   groupExists: false,
  //   ruleExists: false
  // }
  // get the latest record to find the max Id
  let latestComment = await centaur.comments.findOne().sort({ order: -1 }).limit(1).lean();
  let nextId = latestComment && latestComment.order ? latestComment.order + 1 : 1;

  const newrule = {
      name: body.name,
      order: body.order,
      description: body.description,
      active: body.active,
      direction: body.direction,
      sentences: body.sentences,
      ruleParameters: body.ruleParameters,
  }

  const dataToCreate = {
    _id: uuid.v4(),
    name: body.groupName || "",
    active: typeof body.active === "boolean" ? body.active : false,
    order: body.nextId,
    ranking: body.ranking || 0,
    usecount: body.usecount || 0,
    rules: [newrule],
  };

  try {
    let newRecord = new centaur.comments(dataToCreate);
    let status = await newRecord.save();
    console.log("New Comment Created:", status);
    return !!status;
  } catch (err) {
    console.error("Error creating comment:", err);
    return false;
  }
};

/**
 * Updates an existing comment record.
 * - Expects `_id` in the body to locate the document.
 * @param {Object} req
 * @returns {Boolean}
 */
const commentsUpdate = async (req) => {
  const body = JSON.parse(req.body);
  console.log(body);
  // find the existing record by groupName, making sure it exists
  let existing = await centaur.comments.findOne({ name: body.groupName }).lean();
  if (!existing) {
    console.log(`No rule group found with _id ${body.groupName}`);
    return false;
  }
  let dataToUpdate = {};
  // update a group vs update a rule
  if (body.updateGroup) {
    // update the group 
    dataToUpdate = {
      "active": existing.active,
      "order": existing.order,
      "ranking": existing.ranking,
      "testing": body.testing,
      "groupParameters": existing.groupParameters ?? []
    };
    console.log("Data to update:", dataToUpdate);
  } else {
    // update a rule
    // remove extra bits from the rule itself
    delete body.groupExists;
    delete body.groupName;
    delete body.ruleExists;
      
    let rules = existing.rules;
    let ruleIndex = -1;
    ruleIndex = rules.findIndex((f) => f.name === body.name);

    
    if (ruleIndex > -1) {
      // if rule already exists, find it in the array and remove it so it can be replaced
      rules.splice(ruleIndex,1,body);
    } else {
      // else add
      rules.push(body);
    }
    
    // Prepare updated data (only including fields you want to allow updating)
    dataToUpdate.rules = rules;
    console.log("Data to update:", dataToUpdate);
  }
  console.log(existing)
  console.log(existing._id)
  try {
    let status = await centaur.comments
      .updateOne({ _id: existing._id }, { $set: dataToUpdate })
      .lean();
    console.log(`Comment ${existing._id} updated: `, status);
    return !!status;
  } catch (err) {
    console.error("Error updating comment:", err);
    return false;
  }
};

/**
 * Deletes an existing comment record.
 * - Expects `_id` in the body to locate the document.
 * @param {Object} req
 * @returns {Boolean}
 */
const commentsDelete = async (req) => {
  const body = JSON.parse(req.body);

  if (!body._id) {
    console.log("No _id provided for delete");
    return false;
  }

  try {
    const recordDelete = await centaur.comments.deleteOne({ _id: body._id });
    console.log("Delete result:", recordDelete);
    // The deleteOne operation returns an object like { acknowledged: true, deletedCount: 1 }
    return recordDelete.acknowledged && recordDelete.deletedCount > 0;
  } catch (err) {
    console.error("Error deleting comment:", err);
    return false;
  }
};

module.exports = {
  commentsAPI,
  commentsCreate,
  commentsRead,
  commentsUpdate,
  commentsDelete,
};
