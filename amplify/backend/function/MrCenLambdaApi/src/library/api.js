const mongoose = require("mongoose");
const helper = require("./helper");
// Import Models
const centaur = require("@mediality/centaur");
const load_end = require("./load_end");
const getUuid = require("uuid-by-string");
// mongoose.set('debug', true);

const inputMeetingsAPI = async (payload) => {
  var response = "Trainer not found";
  if (payload.path == "/inputmeetings") {
    var data = await centaur.files_received_sftps
      .find()
      .sort({ createdAt: -1 })
      .limit(50)
      .lean();
    if (data) {
      response = data;
    } else {
      response = "No Input Meetings found";
    }
  }
  return response;
};

const getEstimatedMessages = async (queueUrl) => {
  var REGION = process.env.region || "ap-southeast-2";
  const AWS = require("aws-sdk");
  AWS.config.update({ region: REGION });
  var sqs = new AWS.SQS();
  var params = { QueueUrl: queueUrl, AttributeNames: ["All"] };
  const fetchSqsAttributes = await sqs.getQueueAttributes(params).promise();
  if (fetchSqsAttributes) {
    return fetchSqsAttributes;
  } else {
    console.log("Error counting messages");
    return "Error";
  }
};

const getQueueURL = async (queueName) => {
  var REGION = process.env.region || "ap-southeast-2";
  const AWS = require("aws-sdk");
  AWS.config.update({ region: REGION });
  var sqs = new AWS.SQS();
  var params = { QueueName: queueName };
  const fetchSqsUrl = await sqs.getQueueUrl(params).promise();
  if (fetchSqsUrl) {
    return fetchSqsUrl;
  } else {
    console.log("Error getting Queue URL");
    return "";
  }
};

const getStepFunctionStatus = async (stepFunctionARN, status) => {
  //status values -  RUNNING | SUCCEEDED | FAILED | TIMED_OUT | ABORTED
  const AWS = require("aws-sdk");
  var stepfunctions = new AWS.StepFunctions();
  var params = {
    stateMachineArn: stepFunctionARN /* required */,
    maxResults: 50,
    statusFilter: status,
  };
  const result = stepfunctions.listExecutions(params).promise();
  if (result) {
    return result;
  } else {
    console.log("Error getting Step function executions");
    return "";
  }
};

const queuesAPI = async (payload) => {
  var response = "",
    processing_count = "",
    failed_count = "",
    processing_complete = "";

  if (payload.path == "/queues") {
    var processingQueueName = `MrCenProcessingQueue-${process.env.ENV}`;
    var deadLetterQueueName = `MrCenDeadLetterQueue-${process.env.ENV}`;
    var processingQueueUrl = await getQueueURL(processingQueueName);
    var deadLetterQueueUrl = await getQueueURL(deadLetterQueueName);
    var processingQueue = await getEstimatedMessages(
      processingQueueUrl["QueueUrl"]
    );
    var deadLetterQueue = await getEstimatedMessages(
      deadLetterQueueUrl["QueueUrl"]
    );

    processing_count =
      deadLetterQueue["Attributes"]["ApproximateNumberOfMessages"];
    failed_count = processingQueue["Attributes"]["ApproximateNumberOfMessages"];

    //Fetch Processed Files - query processedmeeting count
    processing_complete = await centaur.processed_meetings
      .estimatedDocumentCount()
      .lean();

    const count = {
      processing: parseInt(processing_count),
      failed: parseInt(failed_count),
      completed: parseInt(processing_complete),
    };
    return count;
  }
};

const getS3Url = async (payload) => {
  var response = "Filename not received";
  if (payload.path == "/s3download" && payload.params) {
    if ("filename" in payload.params && payload.params.filename) {
      var data = await helper.getS3SignedUrl(payload.params.filename);
      if (data) {
        response = data;
      }
    }
  }
  return response;
};
const horseAPI = async (payload) => {
  var response = "Horse Not Found";

  if (payload.path == "/horse" && payload.params) {
    if ("id" in payload.params && payload.params.id) {
      var data = await centaur.horses
        .findOne({ HRN_HORSE_ID: payload.params.id })
        .lean();
      if (data) {
        response = data;
      }
    }

    if ("s" in payload.params && payload.params.s) {
      s = payload.params.s.trim();
      s = s.toUpperCase();
      if (!helper.isString(s)) {
        return "Invalid String";
      }
      var data = await centaur.horses
        .find({ HRN_HORSE_NAME: { $regex: "^" + s } })
        .select("HRN_HORSE_ID HRN_HORSE_NAME")
        .limit(50)
        .lean();
      if (data) {
        response = data;
      }
    }
  }
  return response;
};

const meetingAPI = async (payload) => {
  var response = "Meeting Not Found";

  if (payload.path == "/meeting" && payload.params) {
    if ("id" in payload.params && payload.params.id) {
      var data = await centaur.processed_meetings
        .findOne({ _id: payload.params.id })
        .lean();
      if (data) {
        response = data;
        if ("getform" in payload.params && payload.params.getform) {
          var race_index = 0;
          for (race of response.processedMeetingData.meeting.races.race) {
            var horse_index = 0;
            for (horse of race.horses.horse) {
              var formdata = await centaur.form.findOne({
                horse_id: horse["@_id"],
              });
              if (formdata.form && formdata.form.length > 0) {
                horse.form = formdata.form;
              } else {
                horse.form = [];
              }
              race.horses.horse[horse_index] = horse;
              horse_index++;
            }
            response.processedMeetingData.meeting.races.race[race_index] = race;
            race_index++;
          }
          try {
            // console.log('lets try send to s3')
            var AWS = require("aws-sdk");
            var filename = payload.params.id;
            let S3 = new AWS.S3({ region: process.env.AWS_REGION });
            var params = {
              Bucket: "centaur-supplier-storage-" + process.env.ENV,
              Key: filename + ".json",
              Body: JSON.stringify(response),
              ContentType: "application/json",
            };

            let s3Response = await S3.upload(params).promise();

            let res = {
              statusCode: 200,
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                id: filename,
                s3Path: s3Response.Location,
              }),
            };

            console.log(res);
          } catch (err) {
            console.log(`error sending to s3 ${err}`);
          }
        }
      }
    }
  }

  return response;
};

const horseFormsAPI = async (payload) => {
  if (payload.path == "/forms" && payload.params) {
    if ("id" in payload.params && payload.params.id) {
      var data = await centaur.form
        .find({ horse_id: payload.params.id })
        .lean();
      if (data) {
        response = data;
      }
    }
  }
  return response;
};

const registrationsAPI = async (payload) => {
  console.log("registrations ", payload);
  if (payload.path == "/registrations" && payload.params) {
    if ("date" in payload.params && payload.params.date) {
      var date = new Date(payload.params.date);
      var dateminusone = new Date(payload.params.date);
      dateminusone.setDate(dateminusone.getDate() - 1);
      console.log(date);
      var data = await centaur.registration_files
        .findOne({ regFileDate: { $gte: dateminusone, $lte: date } })
        .lean();
      if (data) {
        response = data;
      }
    }
  }
  return response;
};

const getInputMeetingArray = async (meetingId,body) => {
  var data = await centaur.temp_meetings.findOne({ _id: meetingId }).lean();
  if (data) {
    response = data;
    var raceArray = [];
    for (val of data.processedMeetingData.meeting.races.race) {
      var horseArray = [];
      var jockeyPairing = {};
      var trainerPairing = {};
      var ratingPairing = {};
      for (const horse of val.horses.horse) {
        var horseId = horse["@_id"];
        if (horseId) {
          horseArray.push(horseId);
          jockeyPairing[horseId] = horse.jockey ? horse.jockey["@_id"] : "";
          trainerPairing[horseId] = horse.trainer ? horse.trainer["@_id"] : "";
          ratingPairing[horseId] = {
            weight_carried: horse.weight_carried,
            rating: horse.rating,
            age: horse["@_age"],
            sex: horse["@_sex"],
            scratched: horse.scratched ?? false,
            tip: horse.tip ?? "",
            jockey_stats: horse.jockey ? horse.jockey["statistics"] : "",
            trainer_stats: horse.trainer ? horse.trainer["statistics"] : "",
          };
        }
      }
      var temp = {
        meetingId: meetingId,
        meetingDate: data.meetingDate,
        race: val["@_number"],
        distance: val.distance["@_metres"],
        track_id: val.track["@_id"],
        class_id: val.classes.class_id,
        confidence: val.confidence ?? 5,
        entrants: horseArray,
        pair_jockey: jockeyPairing,
        pair_trainer: trainerPairing,
        pair_rating: ratingPairing,
        meeting_location: data.processedMeetingData.meeting.track["@_location"] ?? ""
      };
      raceArray.push(temp);
      params = {
        body: body,
        raceArray: raceArray
      }
      result = temp;
      //break
    }
    return JSON.stringify(params);
  } else {
    console.log("Error reading Meeting: " + meetingId);
    return "";
  }
};

const generateStatsAPI = async (payload) => {
  console.log('statsgen payload')
  console.log(payload)
  if (payload.body !== undefined) {
    params = JSON.parse(payload.body)
    payload.params = params.params
  }
  if (payload.path == "/generatestats" && payload.params) {
    console.log('statsgen api')
    if ("id" in payload.params && payload.params.id) {
      //Generate Race Array to pass to Stats Step Function
      console.log(payload)
      if (payload.params.body && payload.params.body.compareType === 'update'){

        let temporary_meeting = await centaur.temp_meetings
          .findOne({ _id: payload.params.id })
          .lean();
        if (temporary_meeting) {
          return {
            statusCode: 401,
            body: JSON.stringify("Temporary meeting already exists!"),
          };
        } else {
            //step 3: create a new record in temp_meetings with the processed meeting data
            let processed_meeting = await centaur.processed_meetings
              .findOne({ _id: payload.params.id })
              .lean();
            var newRecordTempMeeting = await new centaur.temp_meetings(processed_meeting);
            var savedMeeting = await newRecordTempMeeting.save();
            console.log("temp_meeting saved successfully:", savedMeeting);
            // step 4: locks the meeting with user so that no other user can update the meeting
            let lockMeeting = await centaur.processed_meetings
            .updateOne(
              { _id: payload.params.id },
              {
                $set: { meetingLocked: "update" },
              }
            )
            .lean();
            console.log(lockMeeting)
        }
      }
      var inputParams = await getInputMeetingArray(payload.params.id,payload.params.body);
      if (inputParams) {
        //Execute Step function
        const stateMachineARN = `arn:aws:states:${process.env.REGION}:${process.env.AWS_ACCOUNT_ID}:stateMachine:MrCenProcessStatsWorkflow-${process.env.ENV}`;
        const aws = require("aws-sdk");
        var stepfunctions = new aws.StepFunctions();
        const params = {
          stateMachineArn: stateMachineARN,
          input: inputParams
        };
        var response = await stepfunctions.startExecution(params).promise();
        if (response) {
          console.log(response);
          data = `Step Function Execution started for MeetingId- ${payload.params.id}`;
        } else {
          console.log("Step Function execution error");
          data = `Step Function Execution Error for MeetingId- ${payload.params.id}`;
        }
        return data;
      } else {
        console.log(
          "Error generating Stats for meetingId: " + payload.params.id
        );
      }
    }
  }
};

const generateMeetingCount = async (meetingId) => {
  var data = await centaur.temp_meetings.findOne({ _id: meetingId }).lean();
  var count = 0;
  if (data) {
    if (data.processedMeetingData.meeting.errors.length) {
      if (Array.isArray(data.processedMeetingData.meeting.errors)) {
        count = count + data.processedMeetingData.meeting.errors.length;
      }
    }
    for (race of data.processedMeetingData.meeting.races.race) {
      //Count Race Level Errors
      if (race.errors.length) {
        if (Array.isArray(race.errors)) {
          count = count + race.errors.length;
        }
      }
      //Count Horse Level Errors
      for (horse of race.horses.horse) {
        if (horse.errors.length) {
          if (Array.isArray(horse.errors)) {
            count = count + horse.errors.length;
          }
        }
      }
    }
    //Update meeting
    var validated = false;
    if (count == 0) {
      validated = true;
    }
    let update = await centaur.temp_meetings
      .updateOne(
        { _id: meetingId },
        {
          $set: { meetingErrorCount: count, validated: validated },
        }
      )
      .lean();
    if (update) {
      console.log("Meeting Error Count Updated");
      return count;
    } else {
      return "";
    }
  } else {
    return "";
  }
};

const generateMeetingErrorAPI = async (payload) => {
  if (payload.path == "/gen-meeting-error-count" && payload.params) {
    if ("id" in payload.params && payload.params.id) {
      var data = await generateMeetingCount(payload.params.id);
      return data;
    }
  }
};

const allAPI = async (payload) => {
  var response = "";
  if (payload.path == "/all" && payload.params) {
    if ("table" in payload.params && payload.params.table) {
      var tableName = payload.params.table.trim();
      switch (tableName) {
        case "tracks":
          var data = await centaur.tracks.find().lean();
          if (data) {
            response = data;
          }
          break;
        case "clubs":
          var data = await centaur.clubs.find().lean();
          if (data) {
            response = data;
          }
          break;
        case "countries":
          var data = await centaur.countries.find().lean();
          if (data) {
            response = data;
          }
          break;
        case "jockeys":
          var data = await centaur.jockeys
            .find()
            .select("JOC_JOCKEY_ID JOC_JOCKEY_DISPLAYNAME")
            .lean();
          if (data) {
            response = data;
          }
          break;
        case "trainers":
          var data = await centaur.trainers
            .find()
            .select("TRN_TRAINER_DISPLAYNAME TRN_TRAINER_ID State Location")
            .lean();
          if (data) {
            response = data;
          }
          break;
        case "race_classes":
          var data = await centaur.race_classes.find().lean();
          if (data) {
            response = data;
          }
          break;
        default:
          return "";
      }
    }
  }
  return response;
};

const transferMeetingAPI = async (payload) => {
  console.log(JSON.stringify(payload));
  const session = await mongoose.startSession();
  session.startTransaction();
  let response;
  try {
    console.log(JSON.stringify(payload));
    if (payload.path == "/transfer-meeting") {
      if (payload.body) {
        console.log("found source and destination");
        const bodyObject = JSON.parse(payload.body);
        const meetingId = bodyObject.meeting_id;
        const selectedTrack = bodyObject.selected_track;
        const transferMeetingDate = bodyObject.transfer_meeting_date;
        var queryDate = new Date(transferMeetingDate).toISOString();
        var destinationMeeting = await centaur.processed_meetings
          .findOne({
            "processedMeetingData.meeting.track['@_id']": selectedTrack,
            meetingDate: queryDate,
          })
          .lean();
        if (destinationMeeting) {
          await centaur.processed_meetings.deleteOne({
            _id: destinationMeeting._id,
          },{session});
        }
        var sourceMeeting = await centaur.processed_meetings
          .findOne({ _id: meetingId })
          .lean();
        let trackInDb = await centaur.tracks
          .findOne({ TRK_TRACK_DB_ID: selectedTrack })
          .lean();
        let venueAbbr = trackInDb.TRK_RSB_TRACK_ABBREV;

        let theUUID = `${transferMeetingDate}-${venueAbbr}`;
        let newMeetingId = await getUuid(theUUID);
        sourceMeeting._id = newMeetingId;
        sourceMeeting.meetingDate = queryDate;
        if(sourceMeeting.processedMeetingData.meeting){
          sourceMeeting.processedMeetingData.meeting["date"] = new Date(queryDate).toLocaleDateString('en-GB')
        }
        await centaur.processed_meetings.deleteOne({ _id: meetingId },{session});
        // This loop is for processedMeetingData
        if (
          sourceMeeting.processedMeetingData.meeting &&
          sourceMeeting.processedMeetingData.meeting.races.race
        ) {
          sourceMeeting.processedMeetingData.meeting.races.race.forEach(
            async (race) => {
              race["@_id"] = await helper.generateUniqueRaceId(
                queryDate,
                trackInDb.TRK_TRACK_DB_ID,
                race["@_nominations_number"],
                0
              );
              if (race.horses.horse) {
                race.horses.horse.forEach((horse) => {
                  if( horse.scratched){
                    delete horse.scratched;
                  }  
                });
              }
              race.track = {
                "@_name": trackInDb.TRK_TRACK_NAME,
                "@_id": trackInDb.TRK_TRACK_DB_ID,
                "@_track_surface": trackInDb.TRK_TRACK_SURFACE_TYPE,
                "@_country": trackInDb.TRK_COUNTRY_OF_TRACK,
                "@_track_3char_abbrev": trackInDb.TRK_TRACK_3CHAR_ABBREV
              }
            }
          );
        }

        // This loop is for inputMeetingData
        if (
          sourceMeeting.inputMeetingData &&
          sourceMeeting.inputMeetingData.races.race
        ) {
          sourceMeeting.inputMeetingData.races.race.forEach(async (race) => {
            race["@_id"] = await helper.generateUniqueRaceId(
              queryDate,
              trackInDb.TRK_TRACK_DB_ID,
              race["@_nominations_number"],
              0
            );
            if (race.horses.horse) {
              race.horses.horse.forEach((horse) => {
                horse.scratched = false;
              });
            }
          });
        }
        if (sourceMeeting.processedMeetingData.meeting.track) {
          sourceMeeting.processedMeetingData.meeting.track["@_name"] =
          trackInDb["TRK_TRACK_NAME"];
          sourceMeeting.processedMeetingData.meeting.track["@_id"] =
            trackInDb["TRK_TRACK_DB_ID"];
          sourceMeeting.processedMeetingData.meeting.track["@_track_surface"] =
            trackInDb["TRK_TRACK_SURFACE_TYPE"];
          sourceMeeting.processedMeetingData.meeting.track["@_location"] =
            trackInDb["TRK_LOCATION"];
          sourceMeeting.processedMeetingData.meeting.track["@_country"] =
            trackInDb["TRK_COUNTRY_OF_TRACK"];
          sourceMeeting.processedMeetingData.meeting.track[
            "@_track_3char_abbrev"
          ] = trackInDb["TRK_TRACK_3CHAR_ABBREV"];
          sourceMeeting.processedMeetingData.meeting.track[
            "@_track_4char_abbrev"
          ] = trackInDb["TRK_RSB_TRACK_ABBREV"];
          sourceMeeting.processedMeetingData.meeting.track[
            "@_track_6char_abbrev"
          ] = trackInDb["TRK_TRACK_6CHAR_ABBREV"];
          
        }
        if(sourceMeeting.processedMeetingData.meeting.product){
          sourceMeeting.processedMeetingData.meeting.product["@_date"] = transferMeetingDate.match(/\d+/g).join('');
          sourceMeeting.processedMeetingData.meeting.product["@_track"] = trackInDb["TRK_TRACK_3CHAR_ABBREV"];
          sourceMeeting.processedMeetingData.meeting.product["@_file"] = `${trackInDb["TRK_TRACK_3CHAR_ABBREV"]}${transferMeetingDate.match(/\d+/g).join('')}.xml`;
          sourceMeeting.processedMeetingData.meeting.product["@_directory"] = `XML_${sourceMeeting.meetingStage.charAt(0).toUpperCase()}`;
        }
        sourceMeeting.meetingLocked = "unlocked";
        const insertResult = await centaur.processed_meetings.create([sourceMeeting],{session});
        console.log('Insert Result is: ', insertResult);
        await session.commitTransaction();
        session.endSession();
        response = {
          status: "success",
          message: `Meeting transferred successfully to ${transferMeetingDate}`,
          data: {
            insertResult: insertResult,
          }
        };
        return response;
      }
    }
  } catch (err) {
    await session.abortTransaction(); 
    await session.endSession(); 
    response = {
      status: "error",
      message: "Failed to transfer meeting.",
      error: err.message,
    };
    console.log("error with meeting transfer", err);
    return response;
   
  }
  finally{
    await session.endSession();
  
  }
};


// DO NOT USE
// const meetingStatusAPI = async (payload) => {
//   if (payload.path == "/meeting-status" && payload.params) {
//     if (payload.params.status && payload.params.id) {
//       var meetingStatus = String(payload.params.status);
//       var meetingId = payload.params.id;
//       if (
//         meetingStatus == "Active" ||
//         meetingStatus == "Abandoned" ||
//         meetingStatus == "Transferred"
//       ) {
//         var dataToUpdate = {
//           meetingStatus: meetingStatus,
//         };
//         let status = await centaur.processed_meetings
//           .updateOne(
//             { _id: meetingId },
//             {
//               $set: dataToUpdate,
//             }
//           )
//           .lean();
//         if (status) {
//           console.log(`Meeting Status Successfully updated - ${meetingId}`);
//           return true;
//         } else {
//           console.log(`Failed to Update Meeting Status - ${meetingId}`);
//           return false;
//         }
//       } else {
//         console.log("Unknown meeting status received");
//         return false;
//       }
//     }
//   }
//   return response;
// };

const getChangelog = async (payload) => {
  if (payload.path == "/changelog" && payload.params) {
    if (payload.params.id) {
      if (payload.params.last_only) {
        var changelog = await centaur.changelog
          .findOne({ meeting_id: payload.params.id })
          .lean();
        for (log of changelog.changelog) {
          if (log.file_path != "DELIVERY EVENT") return log;
        }

        return await centaur.changelog
          .findOne({ meeting_id: payload.params.id })
          .lean().changelog[0];
      }
      return await centaur.changelog
        .findOne({ meeting_id: payload.params.id })
        .lean();
    }
  }
};
const checkMeetingLocks = async (payload) => {
  if (payload.path == "/locks" && payload.params) {
    if (payload.params.date) {
      var date = new Date(payload.params.date);
      // return centaur.processed_meeting.find({meeting_id:payload.params.id}).lean()

      if (date) {
        var data = await centaur.processed_meetings
          .find({ meetingDate: date })
          .select("_id meetingLocked meetingErrorCount validated")
          .lean();
        if (data) {
          for (meeting of data) {
            if (meeting.meetingErrorCount > 0) {
              meeting.errors = (
                await centaur.processed_meetings
                  .findOne({ _id: meeting._id })
                  .select("processedMeetingData.meeting.errors")
                  .lean()
              ).processedMeetingData.meeting.errors;
            }
          }
          return data;
        } else {
          return { data: "not found" };
        }
      }
    } else if (payload.params.from && payload.params.to) {
      var from = new Date(payload.params.from);
      var to = new Date(payload.params.to);

      var data = await centaur.processed_meetings
        .find({
          $and: [
            { meetingDate: { $gte: from, $lte: to } },
            { meetingErrorCount: { $gt: 0 } },
          ],
        })
        .sort({ meetingDate: 1 })
        .select("_id meetingDate")
        .lean();
      if (data) {
        response = data;
      } else {
        response = "";
      }
    }
  }
};

const claimMeeting = async (payload) => {
  console.log(payload);
  const AWS = require("aws-sdk");
  if (payload.path == "/claim" && payload.params) {
    if (
      payload.params.id &&
      payload.params.user &&
      payload.params.claim == "true"
    ) {
      var meeting = await centaur.processed_meetings
        .findOne({ _id: payload.params.id })
        .lean();
      if (meeting) {
        if (meeting.meetingLocked && meeting.meetingLocked !== "unlocked")
          return {
            data: `meeting ${payload.params.id} already claimed! refresh browser.`,
          };
        let ctm = await centaur.temp_meetings
          .findOne({ _id: payload.params.id })
          .lean();
        if (ctm) {
          return {
            data: `temp meeting ${payload.params.id} already exists, somethings gone terribly wrong.`,
          };
        }
        let utm = await centaur.processed_meetings
          .updateOne(
            { _id: payload.params.id },
            {
              $set: { meetingLocked: payload.params.user, validated: false },
            }
          )
          .lean();
        console.log(
          `locked ${payload.params.id} for user ${payload.params.user}`
        );
        var newRecord = new centaur.temp_meetings(meeting);
        var d = await newRecord.save();
        console.log(d);
        return {
          data: `meeting ${payload.params.id} claimed by ${payload.params.user}`,
        };
      } else {
        return { data: `meeting ${payload.params.id} not found` };
      }
    } else if (
      payload.params.id &&
      payload.params.user &&
      payload.params.save == "true"
    ) {
      var lambda = new AWS.Lambda();
      if (payload.params.id) {
        var lambdaPayload = {
          meetingId: payload.params.id,
          compareType: "saveOnly",
          user: payload.params.user,
        };
        var params = {
          FunctionName: "MRCentCompareMeeting-" + process.env.ENV,
          InvocationType: "Event",
          Payload: JSON.stringify(lambdaPayload),
        };
        var res_2 = await lambda.invoke(params).promise();
        if (res_2) {
          return {
            data: `meeting ${payload.params.id} updated by ${payload.params.user}`,
          };
        } else {
          return {
            data: `meeting ${payload.params.id} failed to compare and save`,
          };
        }
      } else {
        console.log("Meeting Id not received");
        return { data: `meeting ${payload.params.id} not found` };
      }
    } else if (
      payload.params.id &&
      payload.params.user &&
      payload.params.cancel == "true"
    ) {
      var lambda = new AWS.Lambda();
      if (payload.params.id) {
        var lambdaPayload = {
          meetingId: payload.params.id,
          compareType: "cancelEdit",
          user: payload.params.user,
        };
        var params = {
          FunctionName: "MRCentCompareMeeting-" + process.env.ENV,
          InvocationType: "Event",
          Payload: JSON.stringify(lambdaPayload),
        };
        var res_2 = await lambda.invoke(params).promise();
        if (res_2) {
          return {
            data: `meeting ${payload.params.id} edit cancelled by ${payload.params.user}`,
          };
        } else {
          return {
            data: `meeting ${payload.params.id} failed to cancel edit.`,
          };
        }
      } else {
        console.log("Meeting Id not received");
        return { data: `meeting ${payload.params.id} not found` };
      }
    }
  }
  return { data: "failed to claim meeting" };
};

module.exports = {
  inputMeetingsAPI,
  getS3Url,
  horseAPI,
  meetingAPI,
  horseFormsAPI,
  registrationsAPI,
  queuesAPI,
  generateStatsAPI,
  getInputMeetingArray,
  generateMeetingCount,
  generateMeetingErrorAPI,
  allAPI,
  transferMeetingAPI,
  // meetingStatusAPI,
  getChangelog,
  checkMeetingLocks,
  claimMeeting,
};
