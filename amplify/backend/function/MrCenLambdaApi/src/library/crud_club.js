const mongoose = require('mongoose');
const moment = require('moment')
const helper = require("./helper")
const uuid = require('uuid');
const centaur = require('@mediality/centaur')


const clubAPI = async (req) => {
    switch (req.method) {
        case "GET":
            console.log("Inside Club GET Handler")
            data = await clubRead(req)
            return data
            break;
        case "POST":
            console.log("Inside Club POST Handler")
            data = await clubCreate(req)
            return data
            break;
        case "PUT":
            console.log("Inside Club PUT Handler")
            data = await clubUpdate(req)
            return data
            break;
        case "DELETE":
            console.log("Inside Club DELETE Handler")
            data = await clubDelete(req)
            return data
            break;
        default:
            return { "msg": "No API Handler" }
    }
}

const clubRead = async (payload) => {
    var response = ''

    if (payload.params) {
        if (('id' in payload.params) && (payload.params.id)) {
            var data = await centaur.clubs.findOne({ _id: payload.params.id }).lean()
            if (data) {
                response = data
                // console.log(`club data return data: ${data}`)
            }
        }

        if (('s' in payload.params) && (payload.params.s)) {
            
            s = payload.params.s.trim()
            s = s.toUpperCase()
            if (!helper.isString(s)) {
                return "Invalid String"
            }
            var data = false
            if (s.charAt(0) == '"' && s.charAt(s.length - 1) == '"'){
                s = s.slice(1,-1)
                data = await centaur.clubs.find({ CLB_CLUB_NAME: s }).limit(50).lean()
            }else {
                data = await centaur.clubs.find({ CLB_CLUB_NAME: { $regex: s } }).select().limit(50).lean()
            }
            
            if (data) {
                response = data
            }
        }
    }
    return response
}

const clubCreate = async (payload) => {

    var payload = JSON.parse(payload.body)

    var id = uuid.v4()
    var clubId = payload.CLB_CLUB_ID
    if (clubId){
        let check_exists = await centaur.clubs.findOne({ CLB_CLUB_ID: clubId }).lean()
        if (check_exists) {
            return "ClubID Already Exists"
        }
    } else {
        return "Error: CLB_CLUB_ID missing from payload: " + payload
    }

    var dataToCreate = {
        _id: id,
        CLB_CLUB_ID: payload.CLB_CLUB_ID,
        CLB_CLUB_NAME: payload.CLB_CLUB_NAME ? payload.CLB_CLUB_NAME : '',
        CLB_CLUB_ABBREV: payload.CLB_CLUB_ABBREV ? payload.CLB_CLUB_ABBREV : '',
        CLB_STATE: payload.CLB_STATE ? parseInt(payload.CLB_STATE) : 0,
        CLB_COUNTRY_CODE: payload.CLB_COUNTRY_CODE ? payload.CLB_COUNTRY_CODE : '',
        CLB_ALT_NAME: payload.CLB_ALT_NAME ? payload.CLB_ALT_NAME : ''
    }

    let newRecord = new centaur.clubs(dataToCreate)
    var status = await newRecord.save();
    console.log("New Club Created: " + status)
    if (status) {
        return true
    } else {
        return false
    }
}

const clubUpdate = async (payload) => {

    var payload = JSON.parse(payload.body)
    
    _id = payload._id

    var clubOptsToUpdate = ['CLB_CLUB_ID','CLB_CLUB_NAME','CLB_CLUB_ABBREV','CLB_STATE','CLB_COUNTRY_CODE','CLB_ALT_NAME']
    var dataToUpdate = {}

    for (const s in payload){
        if (clubOptsToUpdate.includes(s)){
            if (s == 'CLB_STATE'){
                dataToUpdate[s] = parseInt(payload[s])
            } else {
                dataToUpdate[s] = payload[s]
            }
            
        }
    }
    console.log(dataToUpdate)
    let status = await centaur.clubs.updateOne({ _id: _id }, {
        "$set": dataToUpdate
    }).lean()
    console.log(`Club ${_id} Updated: `)
    console.log(status)
    if (status) {
        return true
    } else {
        return false
    }
}

const clubDelete = async (payload) => {
    var payload = JSON.parse(payload.body)
    var _id = payload._id
    if (_id) {
        var record_delete = await centaur.clubs.deleteOne({ _id: _id })
        console.log(`Club ${_id} Deleted`)
        if (record_delete) {
            return true
        } else {
            return false
        }
    }
}

module.exports = {
    clubAPI,
    clubCreate,
    clubRead,
    clubUpdate,
    clubDelete
}
