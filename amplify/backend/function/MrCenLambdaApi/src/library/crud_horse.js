const helper = require("./helper");
const uuid = require("uuid");
const AWS = require("aws-sdk");
const lambda = new AWS.Lambda();
const centaur = require("@mediality/centaur");

const horseAPI = async (req) => {
  let data;
  switch (req.method) {
    case "GET":
      console.log("Inside Horse GET Handler");
      data = await horseRead(req);
      return data;
    case "POST":
      console.log("Inside Horse POST Handler");
      data = await horseCreate(req);
      return data;
    case "PUT":
      console.log("Inside Horse PUT Handler");
      data = await horseUpdate(req);
      return data;
    case "DELETE":
      console.log("Inside Horse DELETE Handler");
      data = await horseDelete(req);
      return data;
    default:
      return { msg: "No API Handler" };
  }
};

const horseRead = async (payload) => {
  var response = "";
  if (payload.params) {
    if ("id" in payload.params && payload.params.id) {
      var data = await centaur.horses
        .findOne({ HRN_HORSE_ID: payload.params.id })
        .lean();
      if (data) {
        response = data;
      }
    }
    if ("s" in payload.params && payload.params.s) {
      var s = payload.params.s.trim();
      s = s.toUpperCase();
      if (!helper.isString(s)) {
        return "Invalid String";
      }
      var searchData = false;
      if (s.charAt(0) == '"' && s.charAt(s.length - 1) == '"') {
        s = s.slice(1, -1);
        searchData = await centaur.horses
          .find({ HRN_HORSE_NAME: s })
          .limit(50)
          .lean();
      } else {
        searchData = await centaur.horses
          .find({ HRN_HORSE_NAME: { $regex: s } })
          .select("HRN_HORSE_ID HRN_HORSE_NAME")
          .limit(50)
          .lean();
      }
      if (searchData) {
        response = searchData;
      }
    }
  }
  return response;
};

const horseCreate = async (payload) => {
  var payload = JSON.parse(payload.body);
  let latest_horse = await centaur.horses
    .findOne()
    .sort({ HRN_HORSE_ID: -1 })
    .limit(1);
  var horseId = parseInt(latest_horse.HRN_HORSE_ID) + 1;
  var id = uuid.v4();
  var dataToCreate = {
    _id: id,
    HRN_HORSE_ID: horseId,
    HRN_COUNTRY_OF_NAME: payload.HRN_COUNTRY_OF_NAME
      ? payload.HRN_COUNTRY_OF_NAME
      : "",
    HRN_HORSE_NAME: payload.HRN_HORSE_NAME
      ? payload.HRN_HORSE_NAME.toUpperCase()
      : "",
    HRN_HORSE_NAME: payload.HRN_HORSE_NAME
      ? payload.HRN_HORSE_NAME.toUpperCase()
      : "",
    HOR_HORSE_DB_ID: horseId,
    HRN_DISPLAY_NAME: payload.HRN_DISPLAY_NAME ?? payload.HRN_HORSE_NAME,
    HOR_FOALING_DATE: payload.HOR_FOALING_DATE ? payload.HOR_FOALING_DATE : "",
    HOR_COUNTRY_OF_ORIGIN: payload.HRN_COUNTRY_OF_NAME
      ? payload.HRN_COUNTRY_OF_NAME
      : "",
    HOR_COUNTRY_OF_ORIGIN: payload.HOR_COUNTRY_OF_ORIGIN
      ? payload.HOR_COUNTRY_OF_ORIGIN
      : "",
    HOR_SEX: payload.HOR_SEX ? payload.HOR_SEX : "",
    HOR_SIRE_ID: parseInt(payload.HOR_SIRE_NAME ? payload.HOR_SIRE_NAME : 0),
    HOR_DAM_ID: parseInt(payload.HOR_DAM_NAME ? payload.HOR_DAM_NAME : 0),
    HOR_TOTAL_PRIZEMONEY: parseInt(
      payload.HOR_TOTAL_PRIZEMONEY ? payload.HOR_TOTAL_PRIZEMONEY : 0
    ),
    HOR_TRAINER_ID: parseInt(
      payload.HOR_TRAINER_ID ? payload.HOR_TRAINER_ID : 0
    ),
    HOR_TRAINING_LOCATION: payload.HOR_TRAINING_LOCATION ?? "",
    HOR_OWNER_NAMES: payload.HOR_OWNER_NAMES ? payload.HOR_OWNER_NAMES : "",
    HOR_RACING_COLOURS: payload.HOR_RACING_COLOURS
      ? payload.HOR_RACING_COLOURS
      : "",
    HOR_COLOUR: payload.HOR_COLOUR ? payload.HOR_COLOUR : "",
    HOR_ROW_STATUS: payload.HOR_ROW_STATUS ? payload.HOR_ROW_STATUS : "",
    HRN_PREV_NAMES: payload.HRN_PREV_NAMES ? payload.HRN_PREV_NAMES : [],
    HOR_RATINGS: payload.HOR_RATINGS ? payload.HOR_RATINGS : {}
  };
  let newRecord = new centaur.horses(dataToCreate);
  var status = await newRecord.save();
  console.log("New Horse Created: " + status);
  if (status) {
    var formId = uuid.v4();
    var formToCreate = {
      _id: formId,
      horse_id: horseId,
      horse_name: payload.HRN_HORSE_NAME ? payload.HRN_HORSE_NAME : "",
      form: [],
    };
    let newForm = new centaur.form(formToCreate);
    var formStatus = await newForm.save();
    console.log(`form created for horse ${horseId}: ${formStatus}`);
    return true;
  } else {
    return false;
  }
};

const horseUpdate = async (payload) => {
  var payload = JSON.parse(payload.body);
  var horseId = payload._id;
  var checkID = await centaur.horses.findOne({ _id: horseId }).lean();
  if (checkID.HRN_HORSE_ID != payload.HRN_HORSE_ID) {
    console.log(
      `updating horse ${payload.HRN_HORSE_NAME} ID from ${checkID.HRN_HORSE_ID} to ${payload.HRN_HORSE_ID}`
    );
    var checkDup = await centaur.horses
      .findOne({ HRN_HORSE_ID: payload.HRN_HORSE_ID })
      .lean();
    if (checkDup && checkDup.HRN_HORSE_NAME != payload.HRN_HORSE_NAME) {
      console.log(
        `there is now a duplicate at ${checkDup._id} with ID ${payload.HRN_HORSE_ID}`
      );
      var sendMail = await helper.mailAlert(
        "DB Update error",
        `MULTIPLE HORSES WITH ID: ${payload.HRN_HORSE_ID} ${payload.HRN_HORSE_NAME} & ${checkDup.HRN_HORSE_NAME}`,
        "alert"
      );
      console.log(sendMail);
    }
    var horseIDForm = await centaur.form
      .updateOne(
        { horse_id: checkID.HRN_HORSE_ID },
        {
          $set: { horse_id: payload.HRN_HORSE_ID },
        }
      )
      .lean();
    console.log(`horse ID in form updated ${horseIDForm}`);
  }
  var dataToUpdate = {
    HRN_HORSE_ID: parseInt(payload.HRN_HORSE_ID ? payload.HRN_HORSE_ID : ""),
    HRN_COUNTRY_OF_NAME: payload.HRN_COUNTRY_OF_NAME
      ? payload.HRN_COUNTRY_OF_NAME
      : "",
    HRN_HORSE_NAME: payload.HRN_HORSE_NAME
      ? payload.HRN_HORSE_NAME.toUpperCase()
      : "",
    HRN_DISPLAY_NAME: payload.HRN_DISPLAY_NAME ?? payload.HRN_HORSE_NAME,
    HOR_HORSE_DB_ID: parseInt(payload.HRN_HORSE_ID ? payload.HRN_HORSE_ID : ""),
    HOR_FOALING_DATE: payload.HOR_FOALING_DATE ? payload.HOR_FOALING_DATE : "",
    HOR_COUNTRY_OF_ORIGIN: payload.HOR_COUNTRY_OF_ORIGIN
      ? payload.HOR_COUNTRY_OF_ORIGIN
      : "",
    HOR_SEX: payload.HOR_SEX ? payload.HOR_SEX : "",
    HOR_SIRE_ID: parseInt(payload.HOR_SIRE_NAME ? payload.HOR_SIRE_NAME : 0),
    HOR_DAM_ID: parseInt(payload.HOR_DAM_NAME ? payload.HOR_DAM_NAME : 0),
    HOR_TRAINER_ID: parseInt(
      payload.HOR_TRAINER_ID ? payload.HOR_TRAINER_ID : 0
    ),
    HOR_TOTAL_PRIZEMONEY: parseInt(
      payload.HOR_TOTAL_PRIZEMONEY ? payload.HOR_TOTAL_PRIZEMONEY : 0
    ),
    HOR_TRAINING_LOCATION: payload.HOR_TRAINING_LOCATION ?? "",
    HOR_OWNER_NAMES: payload.HOR_OWNER_NAMES ? payload.HOR_OWNER_NAMES : "",
    HOR_RACING_COLOURS: payload.HOR_RACING_COLOURS
      ? payload.HOR_RACING_COLOURS
      : "",
    HOR_COLOUR: payload.HOR_COLOUR ? payload.HOR_COLOUR : "",
    HOR_ROW_STATUS: payload.HOR_ROW_STATUS ? payload.HOR_ROW_STATUS : "",
    HRN_PREV_NAMES: payload.HRN_PREV_NAMES ? payload.HRN_PREV_NAMES : [],
    HOR_CURRENT_BLINKER_IND: payload.HOR_CURRENT_BLINKER_IND
      ? payload.HOR_CURRENT_BLINKER_IND
      : "",
    HOR_RATINGS: payload.HOR_RATINGS ? payload.HOR_RATINGS : {}
  };
  //triggers the db update for change in horse display name
  if (payload.HRN_DISPLAY_NAME && checkID.HRN_DISPLAY_NAME !== payload.HRN_DISPLAY_NAME) {
    const horseUpdateLambda = `MRCenLambdaHorseNameUpdate-${process.env.ENV}`;
    const params = {
      FunctionName: horseUpdateLambda, 
      InvocationType: "RequestResponse", 
      Payload: JSON.stringify({ horseId: payload.HRN_HORSE_ID, horsePreviousNames: payload.HRN_PREV_NAMES,
      horseDisplayName: payload.HRN_DISPLAY_NAME }), 
    };

    lambda.invoke(params, function (err, data) {
      if (err) {
        console.log(err, err.stack); 
      } else {
        console.log(data); 
      }
    });
  }
  console.log(dataToUpdate);
  let status = await centaur.horses
    .updateOne(
      { _id: horseId },
      {
        $set: dataToUpdate,
      }
    )
    .lean();
  if (checkID.HRN_PREV_NAMES != payload.HRN_PREV_NAMES) {
    console.log(`horse name in form updated ${payload.HRN_PREV_NAMES}`);
    var horseForm = await centaur.form
      .updateOne(
        { horse_id: payload.HRN_HORSE_ID },
        {
          $set: { horse_name: payload.HRN_HORSE_NAME.toUpperCase() },
        }
      )
      .lean();
    console.log(`horse name in form updated ${horseForm}`);
  }
  console.log(`Horse ${horseId} Updated: `);
  console.log(status);
  if (status) {
    return true;
  } else {
    return false;
  }
};

const horseDelete = async (payload) => {
  payload = JSON.parse(payload.body);
  const horseId = payload._id;
  if (horseId) {
    const horseRecord = await centaur.horses.findOne({ _id: horseId }).lean();
    const record_delete = await centaur.horses.deleteOne({ _id: horseId });
    const dupRecords = await centaur.horses.find({ HRN_HORSE_ID: horseRecord.HRN_HORSE_ID }).lean();
    let delFormEntry = true;
    if (dupRecords.length > 0){
      for (const dupRec of dupRecords){
        if (dupRec.HRN_HORSE_NAME.toLowerCase() === horseRecord.HRN_HORSE_NAME.toLowerCase()){
          delFormEntry = false;
        }
      }
    }
    if (delFormEntry){
      const formEntry = await centaur.form.find({horse_id:horseRecord.HRN_HORSE_ID}).lean()
      for (const entry of formEntry){
        if (entry.horse_name.toLowerCase() === horseRecord.HRN_HORSE_NAME.toLowerCase()){
          const form_delete = await centaur.form.deleteOne({ _id: entry._id });
          console.log(form_delete)
          break
        }
      }
    }
    if (record_delete) {
      return true;
    } else {
      return false;
    }
  }
};

module.exports = {
  horseAPI,
  horseCreate,
  horseRead,
  horseUpdate,
  horseDelete,
};
