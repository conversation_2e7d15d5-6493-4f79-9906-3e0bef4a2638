const mongoose = require("mongoose");
const moment = require("moment");
const helper = require("./helper");
const uuid = require("uuid");
const centaur = require("@mediality/centaur");

const horseFormAPI = async (req) => {
  if (!req.method && req.body && req.body.method) req.method = req.body.method;
  switch (req.method) {
    case "GET":
      console.log("Inside HorseForm GET Handler");
      data = await horseFormRead(req);
      return data;
      break;
    case "POST":
      console.log("Inside HorseForm POST Handler");
      data = await horseFormCreate(req);
      return data;
      break;
    case "PUT":
      console.log("Inside HorseForm PUT Handler");
      data = await horseFormUpdate(req);
      return data;
      break;
    case "DELETE":
      console.log("Inside HorseForm DELETE Handler");
      data = await horseFormDelete(req);
      return data;
      break;
    default:
      return { msg: "No API Handler" };
  }
};

const horseFormRead = async (payload) => {
  var response = "";

  if (payload.params) {
    // if (('id' in payload.params) && (payload.params.id)) {
    //     var data = await centaur.form.findOne({ _id: payload.params.id }).lean()
    //     if (data) {
    //         response = data
    //     }
    // }

    if ("horseid" in payload.params && payload.params.horseid) {
      var data = await centaur.form
        .find({ horse_id: payload.params.horseid })
        .lean();
      if (data) {
        response = data;
      }
    }

    if ("s" in payload.params && payload.params.s) {
      s = payload.params.s.trim();
      s = s.toUpperCase();
      if (!helper.isString(s)) {
        return "Invalid String";
      }
      var data = false;
      if (s.charAt(0) == '"' && s.charAt(s.length - 1) == '"') {
        s = s.slice(1, -1);
        data = await centaur.form.find({ horse_name: s }).limit(50).lean();
      } else {
        data = await centaur.form
          .find({ horse_name: { $regex: s } })
          .limit(50)
          .lean();
      }
      if (data) {
        response = data;
      }
    }
  }
  return response;
};

const horseFormCreate = async (payload) => {
  var parsedPayload = {};
  try {
    parsedPayload = JSON.parse(payload.body);
  } catch (err) {
    parsedPayload = payload.body;
  }
  var payload = parsedPayload;
  console.log(payload);
  var track = "";
  var jockey = "";
  // set track data based on selection from database
  if (payload.track["@_id"]) {
    track = await centaur.tracks
      .find({ TRK_TRACK_DB_ID: payload.track["@_id"] })
      .lean();
    track = track[0];
    if (payload.track["@_location"])
      track.TRK_LOCATION = payload.track["@_location"];
  }
  // set jockey  data based on selection from database
  if (payload.jockey["@_id"]) {
    jockey = await centaur.jockeys
      .find({ JOC_JOCKEY_ID: payload.jockey["@_id"] })
      .lean();
    jockey = jockey[0];
  }

  if (payload.event_id) {
    event_id = payload.event_id;
  } else {
    event_id = 0;
  }

  console.log("d1", payload.meeting_date);
  var meeting_date = moment(payload.meeting_date);
  console.log("d2", meeting_date);
  console.log("d3", new Date(meeting_date.toISOString()));
  var gearChanges = [];
  if (payload.gear) {
    var gearSplit = payload.gear.split(";");
    for (gear of gearSplit) {
      var theGearChange = gear.split(":");
      gearChanges.push({
        "@_name": theGearChange[0],
        "@_option": theGearChange[1] ?? "",
        "@_id": helper.getGearID(theGearChange[0]),
      });
    }
  } else if (
    payload.gear_changes &&
    payload.gear_changes.gear_change &&
    payload.gear_changes.gear_change[0]
  ) {
    gearChanges = payload.gear_changes.gear_change;
  }

  var runningGear = [];
  if (payload.ran_gear) {
    runningGear = payload.ran_gear.split(";");
  } else if (
    payload.running_gear &&
    payload.running_gear.gear_item &&
    payload.running_gear.gear_item[0]
  ) {
    runningGear = payload.running_gear.gear_item;
  }

  var other_runner = [];
  var r = 0;
  for (runner of payload.other_runners.other_runner) {
    other_runner.push({
      "@_position": payload.other_runners.other_runner[r]["@_position"]
        ? payload.other_runners.other_runner[r]["@_position"]
        : r + 1,
      "@_horse": payload.other_runners.other_runner[r]["@_horse"]
        ? payload.other_runners.other_runner[r]["@_horse"]
        : "",
      "@_jockey": payload.other_runners.other_runner[r]["@_jockey"]
        ? payload.other_runners.other_runner[r]["@_jockey"]
        : "",
      "@_country": payload.other_runners.other_runner[r]["@_country"]
        ? payload.other_runners.other_runner[r]["@_country"]
        : "",
      "@_weight": payload.other_runners.other_runner[r]["@_weight"]
        ? payload.other_runners.other_runner[r]["@_weight"]
        : 0,
      "@_margin": payload.other_runners.other_runner[r]["@_margin"]
        ? payload.other_runners.other_runner[r]["@_margin"]
        : "",
      "@_barrier": payload.other_runners.other_runner[r]["@_barrier"]
        ? payload.other_runners.other_runner[r]["@_barrier"]
        : 0,
    });
    r++;
  }
  var sectional = {};
  if (payload.sectional) {
    sectional = {
      "@_distance": payload.sectional["@_distance"]
        ? payload.sectional["@_distance"]
        : 0,
      "@_time": payload.sectional["@_time"] ? payload.sectional["@_time"] : 0,
      "@_location": payload.sectional["@_location"]
        ? payload.sectional["@_location"]
        : "",
    };
  } else {
    sectional = {
      "@_distance": 0,
      "@_time": "0:00.00",
      "@_location": "",
    };
  }

  horse_id = payload.horse_id;

  //build entry payload from form and track/jockey data
  var formData = {
    horse_id: horse_id ? horse_id : 0,
    meeting_date: meeting_date ? new Date(meeting_date.toISOString()) : "",
    meeting_id: payload.meeting_id ? payload.meeting_id : uuid.v4(),
    event_id: event_id ? event_id : 0,
    trainer_id: payload.trainer_id ? payload.trainer_id : 0,
    rail_position: payload.rail_position ? payload.rail_position : 0,
    track: {
      "@_name": track.TRK_TRACK_NAME ? track.TRK_TRACK_NAME : "",
      "@_id": track.TRK_TRACK_DB_ID ? track.TRK_TRACK_DB_ID : "",
      "@_location": track.TRK_LOCATION ? track.TRK_LOCATION : "",
      "@_condition": payload.track["@_condition"]
        ? payload.track["@_condition"].charAt(0)
        : "",
      "@_grading": payload.track["@_grading"] ?? "0",
      "@_track_surface": track.TRK_TRACK_SURFACE_TYPE
        ? track.TRK_TRACK_SURFACE_TYPE
        : "",
      "@_country": track.TRK_COUNTRY_OF_TRACK ? track.TRK_COUNTRY_OF_TRACK : "",
      "@_track_3char_abbrev": track.TRK_TRACK_3CHAR_ABBREV
        ? track.TRK_TRACK_3CHAR_ABBREV
        : "",
      "@_track_4char_abbrev": track.TRK_RSB_TRACK_ABBREV
        ? track.TRK_RSB_TRACK_ABBREV
        : "",
      "@_track_6char_abbrev": track.TRK_TRACK_6CHAR_ABBREV
        ? track.TRK_TRACK_6CHAR_ABBREV
        : "",
    },
    race: {
      "@_number": payload.race["@_number"] ? payload.race["@_number"] : 0,
      "@_name": payload.race["@_name"] ? payload.race["@_name"] : "",
    },
    distance: {
      "@_metres": payload.distance["@_metres"]
        ? payload.distance["@_metres"]
        : 0,
    },
    restrictions: {
      "@_age": payload.restrictions["@_age"]
        ? payload.restrictions["@_age"]
        : "",
      "@_sex": payload.restrictions["@_sex"]
        ? payload.restrictions["@_sex"]
        : 0,
      "@_jockey": payload.restrictions["@_jockey"]
        ? payload.restrictions["@_jockey"]
        : "",
    },
    group: payload.group ? payload.group : "",
    official_margin_1: payload.official_margin_1
      ? payload.official_margin_1
      : "",
    official_margin_2: payload.official_margin_2
      ? payload.official_margin_2
      : "",
    beaten_margin: payload.beaten_margin ? payload.beaten_margin : "",
    classes: payload.classes ? payload.classes : 0,
    event_prizemoney: payload.event_prizemoney ? payload.event_prizemoney : 0,
    weight_type: payload.weight_type ? payload.weight_type : "",
    starters: payload.starters ? payload.starters : 0,
    event_duration: payload.event_duration ? payload.event_duration : "",
    sectional: sectional,
    jockey: {
      "@_name": jockey.JOC_JOCKEY_DISPLAYNAME
        ? jockey.JOC_JOCKEY_DISPLAYNAME
        : "",
      "@_firstname": jockey.JOC_JOCKEY_FIRSTNAME
        ? jockey.JOC_JOCKEY_FIRSTNAME
        : "",
      "@_surname": jockey.JOC_JOCKEY_SURNAME ? jockey.JOC_JOCKEY_SURNAME : "",
      "@_id": jockey.JOC_JOCKEY_ID ? jockey.JOC_JOCKEY_ID : 0,
    },
    weight_carried: payload.weight_carried ? payload.weight_carried : 0,
    barrier: payload.barrier ? payload.barrier : 0,
    prices: {
      "@_opening": payload.prices["@_opening"]
        ? payload.prices["@_opening"]
        : "",
      "@_mid": payload.prices["@_mid"] ?? "",
      "@_starting": payload.prices["@_starting"]
        ? payload.prices["@_starting"]
        : "",
    },
    decimalprices: {
      "@_opening": payload.decimalprices["@_opening"]
        ? payload.decimalprices["@_opening"]
        : 0,
      "@_mid": payload.decimalprices["@_mid"] ?? 0,
      "@_starting": payload.decimalprices["@_starting"]
        ? payload.decimalprices["@_starting"]
        : 0,
    },
    positions: {
      "@_settling_down": payload.positions["@_settling_down"]
        ? payload.positions["@_settling_down"]
        : 0,
      "@_m1200": payload.positions["@_m1200"]
        ? payload.positions["@_m1200"]
        : 0,
      "@_m800": payload.positions["@_m800"] ? payload.positions["@_m800"] : 0,
      "@_m400": payload.positions["@_m400"] ? payload.positions["@_m400"] : 0,
      "@_finish": payload.finish_position ? payload.finish_position : 0,
    },
    finish_position: payload.finish_position ? payload.finish_position : 0,
    margin: payload.margin ? payload.margin : "",
    favourite_indicator: payload.favourite_indicator ?? "",
    horse_prizemoney: payload.horse_prizemoney ?? 0,
    horse_prizemoney_bonus: payload.horse_prizemoney_bonus ?? 0,
    gear_changes: { gear_change: gearChanges },
    running_gear: { gear_item: runningGear },
    stewards_report: payload.stewards_report
      ? payload.stewards_report
      : ["", "", ""],
    other_runners: {
      other_runner: other_runner,
    },
    days_since_last_run: payload.days_since_last_run
      ? payload.days_since_last_run
      : 0,
    rating: payload.rating ? payload.rating : "",
  };
  //check to see if form exists for horse by ID
  var data = await centaur.form.find({ horse_id: horse_id }).lean();

  var status = false;
  // if entry in form table exists
  var formIndex = 0;
  if (data[0]) {
    // add a new form item to the horses form entry
    if (!data[0].form) {
      data[0].form = [];
    }
    var horseForm = data[0].form;

    horseForm.push(formData);

    horseForm.sort((a, b) => a.meeting_date - b.meeting_date);
    var checkdate = "";

    for (var horseFormItem of horseForm) {
      if (checkdate) {
        var prevDate = moment(checkdate);
        var meetDate = moment(horseFormItem.meeting_date);
        horseFormItem.days_since_last_run = meetDate.diff(prevDate, "days");
      }
      if (
        horseFormItem.classes &&
        horseFormItem.classes.class_id &&
        horseFormItem.classes.class_id != 90
      ) {
        checkdate = horseFormItem.meeting_date;
      }
    }
    horseForm.sort((a, b) => b.meeting_date - a.meeting_date);

    let addForm = await centaur.form
      .updateOne({ _id: data[0]._id }, { form: horseForm })
      .lean();

    status = addForm;
    console.log(
      "Horse Form for " +
        payload.FRM_HORSE_NAME +
        " Updated at position " +
        formIndex
    );
  } else {
    // if no form entry exists
    var id = uuid.v4();
    var dataToCreate = {
      _id: id,
      form: [formData],
      horse_id: payload.FRM_HORSE_ID,
      horse_name: payload.FRM_HORSE_NAME.toUpperCase(),
    };
    // create new document in form table with horse data and this run of form
    let newHorse = new centaur.form(dataToCreate);
    var status = await newHorse.save();
    console.log("New Horse Form Created: " + status);
  }

  var formIndexData = {
    track_id: track.TRK_TRACK_DB_ID ?? "",
    meeting_id: payload.meeting_id ?? "",
    meeting_date: payload.meeting_date ? new Date(payload.meeting_date) : "",
    event_id: event_id,
    trainer_id: payload.trainer_id ?? 0,
    weight_carried: payload.weight_carried ?? 0,
    race_no: payload.race["@_number"] ?? 0,
    horse_id: horse_id,
    jockey_id: jockey.JOC_JOCKEY_ID ?? 0,
    finish_pos: payload.finish_position ?? 0,
  };

  if (status) {
    var updateResults = await updateHorseResults(horse_id);
    var updatePrize = await updatePrizeMoney(
      horse_id,
      event_id,
      (Number(payload.horse_prizemoney) || 0) +
        (Number(payload.horse_prizemoney_bonus) || 0)
    );
    var the_form_index = await createIndex(formIndexData);
    console.log(updateResults, updatePrize, the_form_index);
  }

  if (status) {
    return true;
  } else {
    return false;
  }
};

const horseFormUpdate = async (payload) => {
  console.log('horseFormUpdate');
  console.log(payload);
  
  var parsedPayload = {};
  try {
    parsedPayload = JSON.parse(payload.body);
    console.log(parsedPayload);
  } catch (err) {
    parsedPayload = payload.body;
  }
  // Fields to exclude from the merge
  const excludeFields = [
    'form_count',
    'distance_metres',
    'track_name', 
    'class_name',
    'ran_gear',
    'horse_name'
  ];

  // Create a clean version of payload without excluded fields
  // const cleanPayload = Object.keys(parsedPayload)
  //   .filter(key => !excludeFields.includes(key))
  //   .reduce((obj, key) => {
  //     obj[key] = parsedPayload[key];
  //     return obj;
  //   }, {});
  // console.log(parsedPayload.meeting_date)
  // let cleanPayload = {};
  // for (const key in parsedPayload){
  //   if (excludeFields.includes(key)) continue;
  //   cleanPayload[key] = parsedPayload[key];
  // }

  // find existing form entry for horse by id
  var data = await centaur.form
    .find({ horse_id: parsedPayload.horse_id })
    .lean();
    
  var status = false;

  if (data[0]) {
    var horseForm = data[0].form;
    
    for (var i = 0; i < horseForm.length; i++) {
      if (horseForm[i].event_id == parsedPayload.event_id) {
        // Merge existing entry with cleaned payload data
        // const mergedEntry = { ...horseForm[i], ...cleanPayload };
        
        // horseForm[i] = cleanPayload;
       
        horseForm[i] = {};
        for (const key in parsedPayload){
          if (excludeFields.includes(key)) continue;
          horseForm[i][key] = parsedPayload[key];
        }
        break;
      }
    }

    const convertISOString = (val) => {
      const d = new Date(val);
      return d.toISOString();
    };
    
    horseForm.sort(
      (a, b) => 
      parseInt(convertISOString(a.meeting_date).replace("T00:00:00.000Z","").replace(/-/g,"") ) - 
      parseInt(convertISOString(b.meeting_date).replace("T00:00:00.000Z","").replace(/-/g,"") )
    );
    var checkdate = "";
    for (var horseFormItem of horseForm) {
      if (checkdate) {
        var prevDate = moment(checkdate);
        var meetDate = moment(horseFormItem.meeting_date);
        horseFormItem.days_since_last_run = meetDate.diff(prevDate, "days");
      }
      if (
        horseFormItem.classes &&
        horseFormItem.classes.class_id &&
        horseFormItem.classes.class_id != 90
      ) {
        checkdate = horseFormItem.meeting_date;
      }
    }
    horseForm.sort(
      (a, b) => 
      parseInt(convertISOString(b.meeting_date).replace("T00:00:00.000Z","").replace(/-/g,"") ) - 
      parseInt(convertISOString(a.meeting_date).replace("T00:00:00.000Z","").replace(/-/g,"") )
    );

    status = await centaur.form.updateOne(
      { _id: data[0]._id },
      { form: horseForm }
    ).lean();
    
    console.log('updated form', data[0]._id);
     if (status) {

      var formIndexData = {
        track_id: parsedPayload.track["@_id"] ?? "",
        meeting_id: parsedPayload.meeting_id ?? "",
        meeting_date: parsedPayload.meeting_date ? new Date(payload.meeting_date) : "",
        event_id: parsedPayload.event_id,
        trainer_id: parsedPayload.trainer_id ?? 0,
        weight_carried: parsedPayload.weight_carried ?? 0,
        race_no: parsedPayload.race["@_number"] ?? 0,
        horse_id: parsedPayload.horse_id,
        jockey_id: parsedPayload.jockey["@_id"] ?? 0,
        finish_pos: parsedPayload.finish_position ?? 0,
      };

      var updateResults = await updateHorseResults(parsedPayload.horse_id);
      var updatePrize = await updatePrizeMoney(
        parsedPayload.horse_id,
        parsedPayload.event_id,
        (Number(parsedPayload.horse_prizemoney) || 0) +
          (Number(parsedPayload.horse_prizemoney_bonus) || 0)
      );
      var the_form_index = await createIndex(formIndexData);
      console.log(updateResults, updatePrize, the_form_index);
    }
  }

  if (!status) {
    console.log("no form item found to update");
    await horseFormCreate(payload);
  }

  return status;
};
const horseFormDelete = async (payload) => {
  console.log(payload);
  var parsedPayload = JSON.parse(payload.body);
  // find existing form entry for horse by id
  var horse_id = parsedPayload.horse_id;
  var data = await centaur.form.find({ horse_id: horse_id }).lean();
  if (data[0]) {
    // create form array
    var horseForm = data[0].form;
    // check each item in form array if it matches correct event ID
    for (var i = 0; i < horseForm.length; i++) {
      if (horseForm[i].event_id == parsedPayload.event_id) {
        // remove matching form entry
        var event_id = horseForm[i].event_id;
        let removeFormItem = await centaur.form
          .updateOne({ _id: data[0]._id }, { $pull: { form: horseForm[i] } })
          .lean();
        console.log(removeFormItem);
        if (removeFormItem) {
          var fidelete = await centaur.form_index
            .findOne()
            .where("horse_id")
            .equals(horse_id)
            .where("event_id")
            .equals(event_id)
            .lean();
          console.log("fidelete: ");
          console.log(fidelete);
          if (fidelete) {
            let removeFormIndex = await centaur.form_index
              .deleteOne({ _id: fidelete._id })
              .lean();
            console.log(removeFormIndex);
          }
          var horsePrizeUpdate = await centaur.horses
            .findOne({ HRN_HORSE_ID: parsedPayload.horse_id })
            .lean();
          if (horsePrizeUpdate) {
            var updateResults = await updateHorseResults(horse_id);
            var updatePrize = await updatePrizeMoney(horse_id, event_id, 0);
            console.log(updateResults, updatePrize);
          }
          return true;
        }
      }
    }
    // delete form index
  }

  return false;
};

const updateHorseResults = async (horseId) => {
  //return
  try {
    var data = await centaur.form.find({ horse_id: horseId }).lean();
    if (data[0]) {
      var horse_id = data[0].horse_id;
      var cleansedResults = helper.cleanseBarrierTrials(data[0].form);

      console.log(
        "Results for : " + data[0].horse_name + " - " + data[0].horse_id
      );

      var ts = 0,
        s1 = 0,
        s2 = 0,
        s3 = 0,
        s4 = 0,
        s5 = 0;
      var dt = 0,
        d1 = 0,
        d2 = 0,
        d3 = 0;
      var wt = 0,
        w1 = 0,
        w2 = 0,
        w3 = 0;

      for (run of cleansedResults) {
        var wet = false;
        ts++;
        if (parseInt(run.track["@grading"]) > 4) {
          wt++;
          wet = true;
        } else {
          dt++;
        }
        if (run.finish_position == 1) {
          s1++;
          if (wet) {
            w1++;
          } else {
            d1++;
          }
        } else if (run.finish_position == 2) {
          s2++;
          if (wet) {
            w2++;
          } else {
            d2++;
          }
        } else if (run.finish_position == 3) {
          s3++;
          if (wet) {
            w3++;
          } else {
            d3++;
          }
        } else if (run.finish_position == 4) {
          s4++;
        } else if (run.finish_position == 5) {
          s5++;
        }
      }

      var dataToUpdate = {
        HOR_START_TOTAL: ts,
        HOR_H_1STS_TOTAL: s1,
        HOR_H_2NDS_TOTAL: s2,
        HOR_H_3RDS_TOTAL: s3,
        HOR_H_4THS_TOTAL: s4,
        HOR_H_5THS_TOTAL: s5,
        HOR_DRY_START_TOTAL: dt,
        HOR_DRY_1ST_TOTAL: d1,
        HOR_DRY_2ND_TOTAL: d2,
        HOR_DRY_3RD_TOTAL: d3,
        HOR_WET_START_TOTAL: wt,
        HOR_WET_1ST_TOTAL: w1,
        HOR_WET_2ND_TOTAL: w2,
        HOR_WET_3RD_TOTAL: w3,
      };
    }

    let status = await centaur.horses
      .updateOne(
        { HRN_HORSE_ID: horse_id },
        {
          $set: dataToUpdate,
        }
      )
      .lean();
    console.log(`Horse ${horse_id} runs Updated: `);
    console.log(status);
    if (status) {
      return true;
    } else {
      return false;
    }
  } catch (err) {
    console.log("Error updating horse runs: ", err);
    return false;
  }
};

const updatePrizeMoney = async (horse_id, event_id, newPrizemoney) => {
  // Check if newPrizemoney is a valid number
  if (typeof newPrizemoney !== "number" || isNaN(newPrizemoney)) {
    throw new Error("Invalid newPrizemoney value. It must be a valid number.");
  }
  const horse = await centaur.horses.findOne({ HRN_HORSE_ID: horse_id }).lean();
  console.log(horse_id, event_id, newPrizemoney);
  console.log(horse);

  const horsePrizeArray = horse.HOR_RACE_PRIZEMONEY || [];
  let prizeToRemove = 0;

  for (let i = 0; i < horsePrizeArray.length; i++) {
    if (horsePrizeArray[i].event_id === event_id) {
      prizeToRemove = horsePrizeArray[i].prize || 0;
      horsePrizeArray.splice(i, 1);
      break;
    }
  }

  horsePrizeArray.push({ event_id: event_id, prize: newPrizemoney });
  console.log("horseprizearray:");
  console.log(horsePrizeArray);

  const totalPrizeMoney =
    (horse.HOR_TOTAL_PRIZEMONEY || 0) + (newPrizemoney - prizeToRemove);
  console.log(
    horse.HOR_TOTAL_PRIZEMONEY,
    newPrizemoney,
    prizeToRemove,
    totalPrizeMoney
  );

  const dataToUpdate = {
    HOR_TOTAL_PRIZEMONEY: totalPrizeMoney,
    HOR_RACE_PRIZEMONEY: horsePrizeArray,
  };

  const status = await centaur.horses
    .updateOne({ HRN_HORSE_ID: horse_id }, { $set: dataToUpdate })
    .lean();

  console.log("Prize money updated for horse!");
  return status;
};
const createIndex = async (indexData) => {
  try {
    var checkifexists = await centaur.form_index
      .find()
      .where("event_id")
      .equals(indexData.event_id)
      .where("horse_id")
      .equals(indexData.horse_id);

    if (checkifexists[0]) {
      var updateIndex = await centaur.form_index
        .updateOne(
          { _id: checkifexists[0]._id },
          {
            $set: indexData,
          }
        )
        .lean();
      console.log(updateIndex);
    } else {
      console.log("making new index");
      var id = uuid.v4();
      var dataToCreate = { _id: id };
      let newIndex = new centaur.form_index(dataToCreate);
      var status = await newIndex.save();
      status = await centaur.form_index
        .updateOne(
          { _id: id },
          {
            $set: indexData,
          }
        )
        .lean();
      console.log(status);
    }
    return true;
  } catch (err) {
    console.log("Error updating form_index: ", err);
    return false;
  }
};

module.exports = {
  horseFormAPI,
  horseFormCreate,
  horseFormRead,
  horseFormUpdate,
  horseFormDelete,
};
