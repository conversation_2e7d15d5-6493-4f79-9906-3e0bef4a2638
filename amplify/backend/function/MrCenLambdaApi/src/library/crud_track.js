const mongoose = require('mongoose');
const moment = require('moment')
const helper = require("./helper")
const uuid = require('uuid');
const centaur = require('@mediality/centaur')

const trackAPI = async (req) => {
    switch (req.method) {
        case "GET":
            console.log("Inside Track GET Handler")
            data = await trackRead(req)
            return data
            break;
        case "POST":
            console.log("Inside Track POST Handler")
            data = await trackCreate(req)
            return data
            break;
        case "PUT":
            console.log("Inside Track PUT Handler")
            data = await trackUpdate(req)
            return data
            break;
        case "DELETE":
            console.log("Inside Track DELETE Handler")
            data = await trackDelete(req)
            return data
            break;
        default:
            return { "msg": "No API Handler" }
    }
}

const trackRead = async (payload) => {
    let response = ''
    if (payload.params) {
        if (('id' in payload.params) && (payload.params.id)) {
            data = await centaur.tracks.findOne({ _id: payload.params.id }).lean()
            if (data) {
                response = data
            }
        }
        if(('track_id' in payload.params) && (payload.params.track_id)){
            data = await centaur.tracks.findOne({ TRK_TRACK_DB_ID: payload.params.track_id }).lean()
            if (data) {
                response = data
            }
        }
        if (('s' in payload.params) && (payload.params.s)) {
            s = payload.params.s.trim()
            s = s.toUpperCase()
            if (!helper.isString(s)) {
                return "Invalid String"
            }
            var data = false
            if (s.charAt(0) == '"' && s.charAt(s.length - 1) == '"'){
                s = s.slice(1,-1)
                data = await centaur.tracks.find({ TRK_TRACK_NAME: s }).limit(50).lean()
            }else {
                data = await centaur.tracks.find({ TRK_TRACK_NAME: { $regex: s } }).select().limit(50).lean()
            }
            if (data) {
                response = data
            }
        }
    }
    return response
}

const trackCreate = async (payload) => {
    var payload = JSON.parse(payload.body)
    let check_exists = await centaur.tracks.findOne({ TRK_TRACK_NAME: payload.TRK_TRACK_NAME, TRK_TRACK_3CHAR_ABBREV: payload.TRK_TRACK_3CHAR_ABBREV, TRK_TRACK_6CHAR_ABBREVBB: payload.TRK_TRACK_6CHAR_ABBREVBB }).limit(1)
    if (check_exists) {
        return "Already Exists"
    }
    console.log("track=create payload:", payload)
    let latest_track = await centaur.tracks.findOne().sort({ TRK_TRACK_DB_ID: -1 }).limit(1)
    var trackId = parseInt(latest_track.TRK_TRACK_DB_ID) + 1
    var id = uuid.v4()
    var dataToCreate = {
        _id: id,
        TRK_TRACK_DB_ID: trackId,
        TRK_TRACK_NAME: payload.TRK_TRACK_NAME ? payload.TRK_TRACK_NAME : '',
        TRK_TRACK_3CHAR_ABBREV: payload.TRK_TRACK_3CHAR_ABBREV ? payload.TRK_TRACK_3CHAR_ABBREV : '',
        TRK_TRACK_6CHAR_ABBREV: payload.TRK_TRACK_6CHAR_ABBREV ? payload.TRK_TRACK_6CHAR_ABBREV : '',
        TRK_RSB_TRACK_ABBREV: payload.TRK_RSB_TRACK_ABBREV ? payload.TRK_RSB_TRACK_ABBREV : '',
        TRK_TRACK_SURFACE_TYPE: payload.TRK_TRACK_SURFACE_TYPE ? payload.TRK_TRACK_SURFACE_TYPE : '',
        TRK_LENGTH_OF_STRAIGHT: payload.TRK_LENGTH_OF_STRAIGHT ? payload.TRK_LENGTH_OF_STRAIGHT : '',
        TRK_LENGTH_OF_CIRCUIT: payload.TRK_LENGTH_OF_CIRCUIT ? payload.TRK_LENGTH_OF_CIRCUIT : '',
        TRK_DIRECTION_OF_RUNNING: payload.TRK_DIRECTION_OF_RUNNING ? payload.TRK_DIRECTION_OF_RUNNING : '',
        TRK_LOCATION: payload.TRK_LOCATION ? payload.TRK_LOCATION : '',
        TRK_COUNTRY_OF_TRACK: payload.TRK_COUNTRY_OF_TRACK ? payload.TRK_COUNTRY_OF_TRACK : '',
        TRK_STATE_OF_TRACK: payload.TRK_STATE_OF_TRACK ? payload.TRK_STATE_OF_TRACK : '',
        TRK_IN_BETWEEN_MARGINS: payload.TRK_IN_BETWEEN_MARGINS ? payload.TRK_IN_BETWEEN_MARGINS : '',
        TRK_ALT_NAME: payload.TRK_ALT_NAME ? payload.TRK_ALT_NAME : ''
    }
    let newRecord = new centaur.tracks(dataToCreate)
    var status = await newRecord.save();
    console.log("New Track Created: " + status)
    if (status) {
        return true
    } else {
        return false
    }
}

const trackUpdate = async (payload) => {
    var payload = JSON.parse(payload.body)
    var trackId = payload._id
    var dataToUpdate = {
        TRK_TRACK_ID: payload.TRK_TRACK_ID ? payload.TRK_TRACK_ID : '',
        TRK_TRACK_NAME: payload.TRK_TRACK_NAME ? payload.TRK_TRACK_NAME : '',
        TRK_TRACK_3CHAR_ABBREV: payload.TRK_TRACK_3CHAR_ABBREV ? payload.TRK_TRACK_3CHAR_ABBREV : '',
        TRK_TRACK_6CHAR_ABBREV: payload.TRK_TRACK_6CHAR_ABBREV ? payload.TRK_TRACK_6CHAR_ABBREV : '',
        TRK_RSB_TRACK_ABBREV: payload.TRK_RSB_TRACK_ABBREV ? payload.TRK_RSB_TRACK_ABBREV : '',
        TRK_TRACK_SURFACE_TYPE: payload.TRK_TRACK_SURFACE_TYPE ? payload.TRK_TRACK_SURFACE_TYPE : '',
        TRK_LENGTH_OF_STRAIGHT: payload.TRK_LENGTH_OF_STRAIGHT ? payload.TRK_LENGTH_OF_STRAIGHT : '',
        TRK_LENGTH_OF_CIRCUIT: payload.TRK_LENGTH_OF_CIRCUIT ? payload.TRK_LENGTH_OF_CIRCUIT : '',
        TRK_DIRECTION_OF_RUNNING: payload.TRK_DIRECTION_OF_RUNNING ? payload.TRK_DIRECTION_OF_RUNNING : '',
        TRK_LOCATION: payload.TRK_LOCATION ? payload.TRK_LOCATION : '',
        TRK_COUNTRY_OF_TRACK: payload.TRK_COUNTRY_OF_TRACK ? payload.TRK_COUNTRY_OF_TRACK : '',
        TRK_STATE_OF_TRACK: payload.TRK_STATE_OF_TRACK ? payload.TRK_STATE_OF_TRACK : '',
        TRK_IN_BETWEEN_MARGINS: payload.TRK_IN_BETWEEN_MARGINS ? payload.TRK_IN_BETWEEN_MARGINS : '',
        TRK_ALT_NAME: payload.TRK_ALT_NAME ? payload.TRK_ALT_NAME : ''
    }
    let status = await centaur.tracks.updateOne({ _id: trackId }, {
        "$set": dataToUpdate
    }).lean()
    console.log(`Track ${payload.TRK_TRACK_NAME} Updated: ${status}`)
    
    if (status) {
        return true
    } else {
        return false
    }
}

const trackDelete = async (payload) => {
    var payload = JSON.parse(payload.body)
    var trackId = payload._id
    if (trackId) {
        var record_delete = await centaur.tracks.deleteOne({ _id: trackId })
        if (record_delete) {
            return true
        } else {
            return false
        }
    }
}

module.exports = {
    trackAPI,
    trackCreate,
    trackRead,
    trackUpdate,
    trackDelete
}
