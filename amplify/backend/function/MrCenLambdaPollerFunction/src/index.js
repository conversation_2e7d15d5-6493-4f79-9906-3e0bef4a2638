const fs = require('fs')
const fsExtra = require('fs-extra')
const mongoose = require('mongoose')
const getUuid = require('uuid-by-string')
const helper = require("./library/helper")
const AWSXRay = require("aws-xray-sdk-core")
const wrap = require('@dazn/lambda-powertools-pattern-basic')
const filesRxSftpModel = require("./models/files_received_sftps")

AWSXRay.enableAutomaticMode();



// timeDiff should be in milleseconds
// 5184000000 - 60 Days in milleseconds
// 1209600000 - 14 Days in milleseconds
var secondsSinceEpoch = Math.round(Date.now())
var timeDiff = process.env.TIME_DIFF || 1209600000
var fileProcessLimit = parseInt(process.env.FILE_PROCESS_LIMIT) || 15
var cert_path = __dirname + '/rds-cert/global-bundle.pem'
var conn = mongoose.connection;

conn.on('connected', function () {console.log('database connection  connected successfully');});
conn.on('disconnected', function () {console.log('database connection close successfully');})
conn.on('error', console.error.bind(console, 'connection error:'));

var MOCK_SFTP_DATA = process.env.MOCK_SFTP_DATA || "false"
var AWS_LOCAL = process.env.AWS_LOCAL || "false"


exports.getAllFiles = async (event, context) => {
    try {
        var segment = new AWSXRay.Segment('getAllFiles')
        var awsSecrets = await helper.getSecrets(process.env.centaurSecrets)
        var connDetails = await helper.generateConnectionString(awsSecrets)

        await mongoose.connect(
            connDetails.CONNECTION_STRING,
            connDetails.PARAMETERS)

        var SFTP_HOST = awsSecrets.SFTP_HOST
        var SFTP_USER = awsSecrets.SFTP_USER
        var SFTP_PASSWORD = awsSecrets.SFTP_PASSWORD
        var SFTP_PORT = awsSecrets.SFTP_PORT

        if (process.env.ENV) { FILES_BUCKET_NAME = `mr-cen-file-storage-${process.env.ENV}`
        } else { var FILES_BUCKET_NAME = awsSecrets.FILES_BUCKET_NAME }

        var sftp_details = {
            SFTP_HOST: SFTP_HOST,
            SFTP_USER: SFTP_USER,
            SFTP_PASSWORD: SFTP_PASSWORD,
            SFTP_PORT: SFTP_PORT
        }


        if (MOCK_SFTP_DATA == "true") {
            var { filesData } = require("./sample/sample")
        } else {
            var filesData = {};
            filesData.home = await helper.getSFTPFiles(sftp_details, "/",'')
            filesData.gears = await helper.getSFTPFiles(sftp_details, "/GearChanges/")
            filesData.scratchings = await helper.getSFTPFiles(sftp_details, "/Scratchings/")
            filesData.registrations = await helper.getSFTPFiles(sftp_details, "/Registrations/")
            // filesData.riders = await helper.getSFTPFiles(sftp_details, "/RiderUpdate/")

        }

        fsExtra.emptyDir('/tmp/', err => {
            if (err) return console.error(err)
            console.log('directory emptied successfully!')})

        var keyMatch = {
            "home":"/",
            "gears":"/GearChanges/",
            "scratchings":"/Scratchings/",
            "registrations":"/Registrations/",
            "riders":"/RiderUpdate/"
        }

        // console.log(`Current Epoch Time: ${secondsSinceEpoch}`)
        // console.log(`File Consideration Time: ${fileConsiderationTime}`)

        var processedFiles = 0
        var skippedFiles = 0
        var procArray = []

        for (const key in filesData){
            var length = filesData[key].length
            for (var index = 0; index < length; ) {
                var input = filesData[key][index]
                console.log(input)
                var fileConsiderationTime = secondsSinceEpoch - timeDiff
                const recordExists = await filesRxSftpModel.exists({_id:getUuid(input['name'],input['modifyTime'].toString())})

                if (!(filesData[key][index]['modifyTime'] < fileConsiderationTime)) {
                    console.log(input['name'], getUuid(input['name'],input['modifyTime'].toString()))
                }

                if (filesData[key][index]['modifyTime'] < fileConsiderationTime) {
                    //console.log("Skipping File is too old for consideration!")
                    skippedFiles++
                } else if (recordExists) {
                    console.log("Record Exists, Skipping!")
                } else {
                    var meetname = input['name'].split(",")[0]
                    console.log(meetname)
                    if (!procArray.includes(meetname)){
                        console.log("Processing File: ", input['name'])
                        input['normalisedFile'] = await helper.normaliseFileNameS3(input['name'])
                        await helper.downloadFileFromSFTP(sftp_details, input['name'], input['normalisedFile'],keyMatch[key])
                        var env = process.env.ENV
                        var xmlData = await fs.readFileSync(`/tmp/${input['normalisedFile']}`, 'utf8');
                        var jsData = await helper.xmlToJs(xmlData)
                        if (jsData['Meeting'] && jsData['Meeting']['Track']){
                            console.log('file is meeting')
                            var venueAbbr = await jsData['Meeting']['Track']['@_VenueAbbr']
                            var trackName = await jsData['Meeting']['Track']['@_TrackName']
                        } else if (jsData['Meeting'] && jsData['Meeting']['@_VenueAbbr']){
                            console.log('file is gear changes')
                            var venueAbbr = await jsData['Meeting']['@_VenueAbbr']
                            var trackName = await jsData['Meeting']['@_TrackName']

                        } else if (jsData['RiderUpdate']){
                            console.log('file is rider updates')
                            var venueAbbr = await jsData['RiderUpdate']['Track']['@_VenueAbbr']
                            var trackName = await jsData['RiderUpdate']['Track']['@_TrackName']

                        } else if (jsData['MeetingScratchings']){
                            console.log('file is scratching updates')
                            var venueAbbr = await jsData['MeetingScratchings']['Track']['@_VenueAbbr']
                            var trackName = await jsData['MeetingScratchings']['Track']['@_TrackName']

                        } else if (jsData['Rego']){
                            console.log('file is registration updates')
                            input['meetingDate'] = jsData['Rego']['@_GenerationDate'].substring(0,10)
                            console.log(input['meetingDate'])
                            var venueAbbr = 'REGO'
                            var trackName = 'REGO'
                        }
                        input['meetingUuid'] = await getUuid(`${input['meetingDate']}-${venueAbbr}-${trackName}`)
                        input['meetingId'] = `${input['meetingDate']}-${venueAbbr}-${trackName}`
                        input['fileReceiptTime'] = new Date();
                        if (!jsData['Rego']){
                            input['meetingDate'] = await helper.getMeetingDate(input['normalisedFile'].split('_')[0])
                        }
                        input['fileType'] = input['name'].split(".")[0].split(" ").slice(-1)[0];

                        console.log("Uploading File!")
                        console.log(input)
                        var uploadResults = await helper.uploadFileS3(FILES_BUCKET_NAME, input, "/tmp/")

                        console.log("Creating DB Record")

                        var newRecord = new filesRxSftpModel({
                            _id: getUuid(input['name'],input['modifyTime'].toString()),
                            type: input['type'],
                            name: input['name'],
                            fileType: input['fileType'],
                            meetingDate: input['meetingDate'],
                            meetingUuid: input['meetingUuid'],
                            meetingId: input['meetingId'],
                            s3file: uploadResults[0]['s3Location'],
                            modifyTime: input['modifyTime'],
                            accessTime: input['accessTime'],
                            rights: input['rights'],
                            owner: input['owner'],
                            group: input['group']
                        });

                        var d = await newRecord.save();

                        if (d) {
                            procArray.push(meetname)
                            console.log(procArray)
                            console.log("Record Created!")
                            processedFiles++;
                            console.log(`Files Processed this execution: ${processedFiles}`)
                            console.log(`Files Skipped this execution due to consideration time: ${skippedFiles}`)
                        } else {
                            console.log("Record Creation Failed!")
                        }
                    } else {
                        console.log(`Wont process multiple files of the same meeting at a single runthrough: ${input['name']}`)
                    }
                }

                if (processedFiles == fileProcessLimit){
                    console.log(`Processed ${processedFiles}, exiting loop`)
                    break
                }

                index++;
            }
        }
        // Purge tmp directory once finished
        fsExtra.emptyDir('/tmp/', err => {
            if (err) return console.error(err)
                console.log('directory emptied successfully!')
            })
    } catch (err) {
        console.log(err)
        segment.addError(err)
    } finally {
        console.log(`Mongoose Connection State: ${conn.readyState}`);
        conn.close()
        segment.close()
    }
    return processedFiles;
}

exports.getAllFilesLocal = async (event, context) => {
    const { filesData } = require("./sample/sample")

    var awsSecrets = await helper.getSecrets(process.env.centaurSecrets)
    var connDetails = await helper.generateConnectionString(awsSecrets)

    await mongoose.connect(
        connDetails.CONNECTION_STRING,
        connDetails.PARAMETERS)

    if (process.env.ENV) { var FILES_BUCKET_NAME = `mr-cen-file-storage-${process.env.ENV}`
    } else { FILES_BUCKET_NAME = awsSecrets.FILES_BUCKET_NAME }

    fsExtra.emptyDir('./sample/temp', err => {
        if (err) return console.error(err)
        console.log('directory emptied successfully!')
        })

    var length = filesData.length

    var processedFiles = 0
    for (var index = 0; index < length;) {
        var input = filesData[index]
        var fileConsiderationTime = secondsSinceEpoch - timeDiff
        const recordExists = await filesRxSftpModel.exists({_id:getUuid(input['name'],input['modifyTime'].toString())})

        console.log(`Current Epoch Time: ${secondsSinceEpoch}`)
        console.log(`File Modified Time: ${filesData[index]['modifyTime']}`)
        console.log(`File Consideration Time: ${fileConsiderationTime}`)

        if (filesData[index]['modifyTime'] < fileConsiderationTime) {
            //console.log("Skipping, file is too old for consideration!")
        } else if (recordExists) {
            console.log("Record Exists, Skipping!")
        } else {
            input['normalisedFile'] = await helper.createDummyFile(input['name'])
            input['meetingDate'] = await helper.getMeetingDate(input['normalisedFile'].split('_')[0])

            var uploadResults = await helper.uploadFileS3(FILES_BUCKET_NAME, input)
            console.log(uploadResults)
            console.log("Creating DB Record")
            var newRecord = new filesRxSftpModel({
                _id: getUuid(input['name'],input['modifyTime'].toString()),
                type: input['type'],
                name: input['name'],
                meetingDate: input['meetingDate'],
                s3file: uploadResults[0]['s3Location'],
                modifyTime: input['modifyTime'],
                accessTime: input['accessTime'],
                rights: input['rights'],
                owner: input['owner'],
                group: input['group']
            });

            var d = await newRecord.save();

            process.exit(0);
            if (d) {
                console.log("Record Created!")
                processedFiles++;
                console.log(`Files Processed this execution: ${processedFiles}`)
            } else {
                console.log("Record Creation Failed!")
            }
        }

        if (processedFiles > fileProcessLimit){
            console.log(`Processed ${processedFiles}, exiting loop`)
            break
        }
        index++;
    }

    fsExtra.emptyDir('./sample/temp', err => {
        if (err) return console.error(err)
        console.log('directory emptied successfully!');
        })

    console.log(`Mongoose Connection State: ${conn.readyState}`);
    conn.close();

    return processedFiles;
}

exports.handler = wrap(async(event, context) => {
    if (AWS_LOCAL == "false") {
        if (MOCK_SFTP_DATA == "true") { console.log("Mocking SFTP data") }
        var fileCount = 0;
        var fileCount = await this.getAllFiles(event, context)
    }
    else if (AWS_LOCAL == "true") {
        console.log("Running Locally")
        var fileCount = 0;
        var fileCount = await this.getAllFilesLocal(event, context)
    }
    var response = {
        statusCode: 200,
        body: { 'message': `File Count Processed: ${fileCount}` }
    }
    return response
});
