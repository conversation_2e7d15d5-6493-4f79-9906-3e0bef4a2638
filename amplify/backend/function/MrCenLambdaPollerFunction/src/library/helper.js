const fs = require('fs');
const path = require('path');
const AWS = require('aws-sdk');
const fsExtra = require('fs-extra');
const Client = require('ssh2-sftp-client');


const getMeetingDate = async (stringDate) => {
    var Year  = stringDate.substring(0,4);
    var Month = stringDate.substring(4,6);
    var Day   = stringDate.substring(6,8);
    return new Date(`${Year}-${Month}-${Day}`)
}

const getSFTPFiles = async (sftp_details, folder, filter = '*') => {
    const sftp = new Client();
    var returnData;
    var theData = {}
    try {
        await sftp.connect({
            host: sftp_details.SFTP_HOST,
            port: sftp_details.SFTP_PORT,
            username: sftp_details.SFTP_USER,
            password: sftp_details.SFTP_PASSWORD
        }).then(() => {
            return sftp.list(folder, filter + '.xml');
        }).then(data => {
            returnData = data
            sftp.end()
            return data
        }).catch(err => {
            console.log("There was an error retrieving the files list, check SFTP connection, quitting poller.");
            console.log('Error Details: \n\n', err);
            console.log('\n');
            process.exit(1)
        });
        console.log("Finish Testing")
    } catch (err) {
        console.log(err)
    } finally {
        return returnData
    }
}


const generateConnectionString = async (aws_secrets) => {
    var DB_USERNAME = aws_secrets.DB_USERNAME
    var DB_PASSWORD = aws_secrets.DB_PASSWORD

    var DB_NAME = aws_secrets.DB_NAME
    var cert_path = __dirname + '/../rds-cert/global-bundle.pem'

    if (process.env.AWS_LOCAL == "true") {
        var DB_URL = "127.0.0.1:27000"
        var CONNECTION_STRING = "mongodb://" + DB_USERNAME + ":" + DB_PASSWORD + "@" + DB_URL + "/" + DB_NAME + "?tls=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
    } else {
        var DB_URL = aws_secrets.DB_URL
        var CONNECTION_STRING = "mongodb://" + DB_USERNAME + ":" + DB_PASSWORD + "@" + DB_URL + "/" + DB_NAME + "?tls=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
    }

    var connDetails = {
        CONNECTION_STRING: CONNECTION_STRING,
        PARAMETERS: {
            directConnection: true,
            ssl: true,
            tlsCAFile: cert_path,
            sslValidate: false
        }
    }

    return connDetails
}

const downloadFileFromSFTP = async (sftp_details, remoteFileName, localFileName, remoteFilePath = '') => {
    const sftp = new Client();

    let remotePath = remoteFilePath + remoteFileName
    let dst = path.join("/tmp/", localFileName)
    console.log(`Downloading File to: ${dst}`)

    await sftp.connect({
        host: sftp_details.SFTP_HOST,
        port: sftp_details.SFTP_PORT,
        username: sftp_details.SFTP_USER,
        password: sftp_details.SFTP_PASSWORD
    })
        .then(() => {
            return sftp.get(remotePath, dst);
        })
        .then(() => {
            sftp.end();
        })
        .catch(err => {
            console.error(err.message);
        }
        )
}

const getSecrets = async (secretName) => {
    var secretsmanager = new AWS.SecretsManager();
    var params = {
        SecretId: secretName,
    };
    const fetchSecretString = await secretsmanager.getSecretValue(params).promise();
    var aws_secrets = JSON.parse(fetchSecretString.SecretString)
    return aws_secrets
}

const createDummyFile = async (filename, fileDir = "/tmp/") => {
    // normalise file for s3
    var s3filename = filename.replace(/\)\s-\s|\s\(|\s{1}-\s{1}|\s{1}|,\s|1\.0|@/g, '_')
    var s3filename = s3filename.replace(/_+/g, '_')
    const srcFilePath = path.join(__dirname, "..", "sample", "sample.xml")
    const dstFilePath = path.join(fileDir, s3filename)
    fs.copyFileSync(srcFilePath, dstFilePath)
    return s3filename
}

const normaliseFileNameS3 = async (filename, path = "/tmp/") => {
    // normalise file for s3
    filename = filename.replace(/\)\s-\s|\s\(|\s{1}-\s{1}|\s{1}|,\s|\@\s|1\.0|@/g, '_')
    filename = filename.replace(/_+/g, '_')
    filename = filename.replace(/(_Professional_|_Night_|_bet365_Park_|_Sportsbet_Park_|_TAB_Park_|_bet365_|_Ladbrokes_|_TAB_|_Sportsbet_|_Sportsbet-)/g, '_')
    filename = filename.replace(/Scratchings_Update/g, 'Scratchings')
    filename = filename.replace(/Gear_Changes/g, 'Gear')

    // rename file to s3 normalise file
    // To be implemented

    return filename
}

const uploadFileS3 = async (bucketName, fileDetails, dirPath = "/tmp/") => {
    var s3 = new AWS.S3({ apiVersion: '2006-03-01' });
    var uploadKey, fileContent, s3prefix, returnData

    s3prefix = "unprocessed/"
    returnData = Array()
    uploadKey = s3prefix + fileDetails['normalisedFile']

    fileContent = await fs.readFileSync(
        path.join(dirPath, fileDetails['normalisedFile']),
        // {encoding:'utf8', flag:'r'} // uncomment for human readable
    );

    // Parameters for Upload
    var uParams = { Bucket: bucketName, Key: uploadKey, Body: fileContent };
    var temp = await s3.upload(uParams).promise();
    await sleep(10);

    console.log('Verifying: ' + fileDetails['normalisedFile']);

    // Parameters for Verification
    var vParams = { Bucket: bucketName, Key: uploadKey };

    let file = await s3.headObject(vParams).promise()
        .then(function (data) {
            console.log("File Found")
            return 'found';
        }).catch(function (err) {
            console.log(`File Not Found: ${err}`)
            return 'notfound';
        });

    console.log(fileDetails)

    try {
        // Purge tmp directory once finished
        fsExtra.emptyDir('/tmp/', err => {
            if (err) return console.error(err)
                console.log('directory emptied successifully!')
            })
    } catch (err) {
        console.error(err)
    }

    returnData.push({ "fileName": fileDetails['normalisedFile'], "s3Location": "s3://" + bucketName + "/" + uploadKey, "rawModifiedAt": fileDetails['modifyTime'] })
    return returnData
}

function sleep(ms) {
    return new Promise((resolve) => {
        setTimeout(resolve, ms);
    });
}

const xmlToJs = async (xmlData) => {
    const { XMLParser, XMLBuilder, XMLValidator } = require('fast-xml-parser');
    const options = {
        ignoreAttributes: false,
        attributeNamePrefix: "@_"
    };
    const parser = new XMLParser(options);
    try {
        let jsonData = parser.parse(xmlData);
        return jsonData
    }
    catch (err) {
        console.log(err)
    }
}

module.exports = {
    getMeetingDate,
    generateConnectionString,
    getSFTPFiles,
    downloadFileFromSFTP,
    getSecrets,
    createDummyFile,
    normaliseFileNameS3,
    uploadFileS3,
    xmlToJs,
    sleep
};
