{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.10.0\",\"stackType\":\"function-Lambda\",\"metadata\":{}}", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "externalLayerVersion": {"Type": "String", "Default": "4"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}, "centaurSecrets": {"Type": "String"}, "functioncentaurappCentaurAppCommonLayerArn": {"Type": "String", "Default": "centaurappCentaurAppCommonLayerArn"}, "timeDiff": {"Type": "String"}, "awsLocal": {"Type": "String"}, "mockSftpData": {"Type": "String"}, "fileProcessLimit": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "TracingConfig": {"Mode": "Active"}, "Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "MrCenLambdaPollerFunction", {"Fn::Join": ["", ["MrCenLambdaPollerFunction", "-", {"Ref": "env"}]]}]}, "MemorySize": "2048", "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "centaurSecrets": {"Ref": "centaurSecrets"}, "TIME_DIFF": {"Ref": "timeDiff"}, "AWS_LOCAL": {"Ref": "awsLocal"}, "MOCK_SFTP_DATA": {"Ref": "mockSftpData"}, "FILE_PROCESS_LIMIT": {"Ref": "fileProcessLimit"}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Timeout": 300, "VpcConfig": {"SecurityGroupIds": ["{{resolve:ssm:centaurLambdaDbSecurityGroup}}"], "SubnetIds": ["{{resolve:ssm:centaurVpcAppSubnet0}}", "{{resolve:ssm:centaurVpcAppSubnet1}}", "{{resolve:ssm:centaurVpcAppSubnet2}}"]}, "Layers": [{"Ref": "functioncentaurappCentaurAppCommonLayerArn"}, {"Fn::Sub": ["arn:aws:lambda:ap-southeast-2:454764279330:layer:LambdaPollerDependencies:${version}", {"version": {"Ref": "externalLayerVersion"}}]}]}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "centaurappLambdaRolefac70397", {"Fn::Join": ["", ["centaurappLambdaRolefac70397", "-", {"Ref": "env"}]]}]}, "Path": "/", "ManagedPolicyArns": ["arn:aws:iam::aws:policy/AmazonS3FullAccess", "arn:aws:iam::aws:policy/AmazonDocDBFullAccess", "arn:aws:iam::aws:policy/SecretsManagerReadWrite", "arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess", "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"], "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}, "EventRule": {"Type": "AWS::Events::Rule", "Properties": {"Description": "A Scheduled Trigger for Event Bridge", "Name": {"Fn::Join": ["-", ["centaur-poller-trigger", {"Ref": "env"}]]}, "EventBusName": "default", "ScheduleExpression": "cron(0,5,10,15,20,25,30,35,40,45,50,55 * * * ? *)", "Targets": [{"Id": {"Fn::Join": ["-", ["TargetLambdaPoller", {"Ref": "env"}]]}, "Arn": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}], "State": "DISABLED"}}, "PermissionForEventsToInvokeLambda": {"Type": "AWS::Lambda::Permission", "Properties": {"FunctionName": {"Ref": "LambdaFunction"}, "Action": "lambda:InvokeFunction", "Principal": "events.amazonaws.com", "SourceArn": {"Fn::GetAtt": ["EventRule", "<PERSON><PERSON>"]}}}, "InvocationErrorAlarm": {"Type": "AWS::CloudWatch::Alarm", "DependsOn": ["LambdaFunction"], "Properties": {"AlarmActions": [{"Fn::Sub": ["arn:aws:sns:${region}:${account}:MrCenAlarms-${environment}", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "environment": {"Ref": "env"}}]}], "AlarmName": {"Fn::Join": ["", ["LambdaError-", {"Ref": "LambdaFunction"}]]}, "ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "FunctionName", "Value": {"Ref": "LambdaFunction"}}], "EvaluationPeriods": 1, "MetricName": "Errors", "Namespace": "AWS/Lambda", "Period": 60, "Statistic": "Maximum", "Threshold": 1, "TreatMissingData": "notBreaching"}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}}}