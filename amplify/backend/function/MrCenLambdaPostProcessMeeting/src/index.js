const AWSXRay = require("aws-xray-sdk-core")
const wrap = require('@dazn/lambda-powertools-pattern-basic')
const AWS = require('aws-sdk');

AWSXRay.enableAutomaticMode();



exports.handler = wrap(async (event) => {

    console.log('Entering PostProcess Meeting Function!')

    try {
        var segment = new AWSXRay.Segment('Post ProcessMeeting Function ')
        var lambda = new AWS.Lambda();
        const meetingId = event.meetingData.meetingId

        if (meetingId) {

            // Pass meeting id and trigger LambdaAPI function /generatestats /gen-meeting-error-count

            // var payload = {
            //     path: '/gen-meeting-error-count',
            //     queryStringParameters: {
            //         id: meetingId
            //     }
            // }

            // var params = {
            //     FunctionName: "MrCenLambdaApi-" + process.env.ENV,
            //     InvocationType: 'Event',
            //     Payload:  JSON.stringify(payload) ,
            // };
            // var res_1 = await lambda.invoke(params).promise();
            // console.log(res_1)


            var payload = {
                path: '/generatestats',
                queryStringParameters: {
                    id: meetingId,
                    body: {
                        id: meetingId,
                        checkstats: true,
                        // AUTOMATED DISTRIBUTION FLAG
                        distribute: true,
                        files: 'FIELDS,FORM,SCRATCHINGS',
                        user: 'PEGASUS',
                        compareType: 'load'
                    }
                }
            }

            // user = body.user;
            // compareType = body.compareType ?? "update";
            // files = body.files ?? "";
            // checkstats = body.checkstats ?? true;
            // distribute = body.distribute ?? false;

            var params = {
                FunctionName: "MrCenLambdaApi-" + process.env.ENV,
                InvocationType: 'Event',
                Payload:  JSON.stringify(payload) ,
            };
            var res_2 = await lambda.invoke(params).promise();
            console.log(res_2)

            // var payload = {
            //     path: '/compare-meeting',
            //     queryStringParameters: {
            //         id: meetingId,
            //         checkstats: true,
            //         // AUTOMATED DISTRIBUTION FLAG
            //         distribute: true,
            //         files: 'FIELDS,FORM,SCRATCHINGS'    
            //     }
            // }

            // var params = {
            //     FunctionName: "MrCenLambdaApi-" + process.env.ENV,
            //     InvocationType: 'Event',
            //     Payload:  JSON.stringify(payload) ,
            // };
            // var res_3 = await lambda.invoke(params).promise();
            // console.log(res_3)

            return "API Invoked for Stats Generation and Error Count"


        } else {
            console.log('Meeting Id not received')
        }

        return 'Stats and ErrorCount Process Completed'
    } catch (err) {
        console.log(err)
        segment.addError(err)
    } finally {
        
        segment.close()
    }
});

exports.localTesting = async (event) => {

};


// const fs = require('fs');
// var sf_input = JSON.parse(fs.readFileSync('sample/event_getHorseData.json'));
//this.localTesting();
