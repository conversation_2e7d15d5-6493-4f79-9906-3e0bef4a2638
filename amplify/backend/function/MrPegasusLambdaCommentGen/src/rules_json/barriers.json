{"_id": "d89e6aa1-caab-4224-9db1-6f3ddd1a49c7", "name": "Barriers", "active": true, "order": 22, "ranking": 20, "usecount": 0, "groupParameters": [], "rules": [{"name": "InsideBarrier", "active": true, "description": "Barriers 1-4. <PERSON><PERSON><PERSON> for races over 1601m and Flemington races under 1201m. {odds-restr}", "order": 2, "direction": 1, "usecount": 0, "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentRace", "field": "starters"}, "compare2": {"phase": "staticValue", "value": 9}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "barrier"}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "lessThan"}], "sentences": [{"sentence": "Drawn well.", "order": 1, "usecount": 0}, {"sentence": "Good draw.", "order": 2, "usecount": 0}, {"sentence": "Nice gate.", "order": 3, "usecount": 0}, {"sentence": "Decent alley.", "order": 4, "usecount": 0}, {"sentence": "Good gate.", "order": 5, "usecount": 0}, {"sentence": "Drawn favourably.", "order": 6, "usecount": 0}, {"sentence": "Drawn to receive every chance.", "order": 7, "usecount": 0}, {"sentence": "Drawn to effect.", "order": 8, "usecount": 0}]}, {"name": "OuterBarrier", "active": true, "description": "Outside 20% barrier with 10 or more starters. Ignore for races over 1601m and Flemington races under 1201m. {odds-restr}", "order": 4, "direction": -1, "usecount": 0, "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 7}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentRace", "field": "starters"}, "compare2": {"phase": "staticValue", "value": 9}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "barrier"}, "compare2": {"phase": "currentRace", "field": "starters", "modifyOperator": "*", "modifyValue": 0.8}, "compareFunc": "greaterThan"}], "sentences": [{"sentence": "Drawn poorly.", "order": 1, "usecount": 0}, {"sentence": "Badly drawn.", "order": 2, "usecount": 0}, {"sentence": "Awkwardly drawn.", "order": 3, "usecount": 0}, {"sentence": "Drawn off the track.", "order": 4, "usecount": 0}, {"sentence": "Tough alley.", "order": 5, "usecount": 0}, {"sentence": "Needs luck from out there.", "order": 6, "usecount": 0}, {"sentence": "Wide alley is a concern.", "order": 7, "usecount": 0}, {"sentence": "Has come up with a horror draw.", "order": 8, "usecount": 0}]}, {"name": "InsideBarrierGoodSettling", "active": true, "description": "Inside 33% barrier with 8 or more starters. Only apply to horses which had 4 or less in their settling down at each of its last two runs. Ignore for races over 1601m and Flemington races under 1201m. {odds-restr}", "order": 1, "direction": 1, "usecount": 0, "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 7.01}, "compareFunc": "lessThan"}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm"}}, {"type": "compare", "compare1": {"phase": "dbForm", "number": 1, "field": "positions.@_settling_down"}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "dbForm", "number": 2, "field": "positions.@_settling_down"}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentRace", "field": "starters"}, "compare2": {"phase": "staticValue", "value": 9}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "barrier"}, "compare2": {"phase": "currentRace", "field": "starters", "modifyOperator": "*", "modifyValue": 0.34}, "compareFunc": "lessThan"}], "sentences": [{"sentence": "Should land in a good spot from the decent draw.", "order": 8, "usecount": 0}, {"sentence": "Drawn to get a nice run.", "order": 4, "usecount": 0}, {"sentence": "Favourably drawn.", "order": 5, "usecount": 0}, {"sentence": "Drawn to effect.", "order": 6, "usecount": 0}, {"sentence": "Ideally drawn.", "order": 3, "usecount": 0}, {"sentence": "Well placed from the perfect draw.", "order": 1, "usecount": 0}, {"sentence": "Expect will be prominent from the decent draw.", "order": 2, "usecount": 0}, {"sentence": "Should get a nice run.", "order": 7, "usecount": 0}]}, {"name": "OuterBarrierGoodSettling", "active": true, "description": "Outside 20% barrier with 10 or more starters. Only apply to horses which had 4 or less in their settling down at each of its last two runs. Ignore for races over 1601m and Flemington races under 1201m. {odds-restr}", "order": 3, "direction": 1, "usecount": 0, "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 2, "consecutive": true, "compare1": {"phase": "dbForm", "field": "positions.@_settling_down"}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentRace", "field": "starters"}, "compare2": {"phase": "staticValue", "value": 9}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "barrier"}, "compare2": {"phase": "currentRace", "field": "starters", "modifyOperator": "*", "modifyValue": 0.8}, "compareFunc": "greaterThan"}], "sentences": [{"sentence": "Will need some luck from a tricky gate.", "order": 1, "usecount": 0}, {"sentence": "Awkwardly drawn.", "order": 2, "usecount": 0}, {"sentence": "Has a wide gate to contend with.", "order": 3, "usecount": 0}, {"sentence": "Drawn deep.", "order": 4, "usecount": 0}, {"sentence": "Needs the breaks from a wide gate.", "order": 5, "usecount": 0}, {"sentence": "Wide barrier a concern.", "order": 6, "usecount": 0}]}]}