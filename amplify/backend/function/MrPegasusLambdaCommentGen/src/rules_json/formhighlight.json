{"name": "FormHighlight", "active": true, "order": 16, "ranking": 9, "usecount": 0, "groupParameters": [], "rules": [{"name": "FeatureMultipleCountryWins", "active": true, "description": "Won 2 or more country level races valued at $70,000 or greater this campaign. Did not win at provincial or metro level this campaign.", "order": 2, "direction": -1, "usecount": 0, "ruleParameters": [{"type": "count", "minvalue": 2, "campaign": "current", "compare1": {"phase": "dbForm"}}, {"type": "count", "maxvalue": 0, "campaign": "current", "compare1": {"phase": "dbForm", "field": "track.@_location"}, "compare2": {"phase": "staticValue", "value": "C"}, "compareFunc": "notEqualTo", "compare3": {"phase": "dbForm", "field": "finish_position"}, "compare4": {"phase": "staticValue", "value": 1}, "compareFunc3": "equalTo"}, {"type": "count", "minvalue": 2, "campaign": "current", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "event_prizemoney"}, "compare4": {"phase": "staticValue", "value": 69999}, "compareFunc3": "greaterThan"}, {"type": "count", "maxvalue": 0, "campaign": "current", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "event_prizemoney"}, "compare4": {"phase": "staticValue", "value": 70000}, "compareFunc3": "lessThan"}], "sentences": [{"sentence": "Won {last-win:1:race.@_name} and {last-win:2:race.@_name} this campaign.", "order": 1, "usecount": 0}]}, {"name": "WeakerOpposition", "active": true, "description": "Last form run was in the same state. Last form run was 2 to 3 class brackets higher. $7 or lower odds.", "order": 3, "direction": 1, "usecount": 0, "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "track.@_state"}, "compare2": {"phase": "dbForm", "number": 1, "field": "track.@_state"}, "compareFunc": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentRace", "field": "classes.bracket"}, "compare2": {"phase": "staticValue", "value": 0}, "compareFunc": "notEqualTo"}, {"type": "compare", "compare1": {"phase": "dbForm", "number": 1, "field": "classes.bracket"}, "compare2": {"phase": "staticValue", "value": 0}, "compareFunc": "notEqualTo"}, {"type": "compare", "compare1": {"phase": "currentRace", "field": "classes.bracket", "modifyOperator": "+", "modifyValue": 1}, "compare2": {"phase": "dbForm", "number": 1, "field": "classes.bracket"}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentRace", "field": "classes.bracket", "modifyOperator": "+", "modifyValue": 4}, "compare2": {"phase": "dbForm", "number": 1, "field": "classes.bracket"}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 19.99}, "compareFunc": "greaterThan"}], "sentences": [{"sentence": "Meeting easier opposition here.", "order": 2, "usecount": 0}, {"sentence": "Down in class.", "order": 3, "usecount": 0}, {"sentence": "Drops in grade.", "order": 5, "usecount": 0}, {"sentence": "Easier here.", "order": 6, "usecount": 0}, {"sentence": "Should find this a bit easier.", "order": 1, "usecount": 0}, {"sentence": "Back in easier class.", "order": 7, "usecount": 0}, {"sentence": "Class drop helps.", "order": 4, "usecount": 0}, {"sentence": "Back to weaker company.", "order": 8, "usecount": 0}]}]}