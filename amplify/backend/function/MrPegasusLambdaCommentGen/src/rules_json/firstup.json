{"name": "FirstUp", "active": true, "order": 13, "ranking": 11, "usecount": 3421365, "groupParameters": [{"type": "compare", "compare1": {"phase": "currentMeeting", "field": "meetingDate", "date": true}, "compare2": {"phase": "dbForm", "field": "meeting_date", "date": true, "number": 1, "modifyOperator": "+", "modifyValue": 82}, "compareFunc": "greaterThan"}], "rules": [{"name": "NoFirstUpWins", "order": 5, "description": "For horses in a first-up preparation who are yet to win from 2 first-up runs or more and have a less than 20% place rate first-up. {odds-restr}", "active": true, "direction": -1, "usecount": 0, "sentences": [{"sentence": "Not a noted first-up performer.", "order": 2, "usecount": 0}, {"sentence": "Hasn't offered much fresh in the past.", "order": 1, "usecount": 0}, {"sentence": "Expected to need this outing.", "order": 4, "usecount": 0}, {"sentence": "Unplaced first-up.", "order": 6, "usecount": 0}, {"sentence": "Likely to need this run.", "order": 5, "usecount": 0}, {"sentence": "Fresh stats don't flatter.", "order": 3, "usecount": 0}], "ruleParameters": [{"type": "count", "campaign": "each", "campaignrun": 1, "minvalue": 2, "compare1": {"phase": "dbForm"}}, {"type": "count", "campaign": "each", "campaignrun": 1, "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "percent", "campaign": "each", "campaignrun": 1, "percentTarget": 0.2, "percentCompare": "lessThan", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 19.99}, "compareFunc": "greaterThan"}]}, {"name": "GoodFirstUp", "order": 2, "description": "For horses in a first-up preparation who have won/placed 20 per cent and less than 70 per cent after at least three first-ups. Must include at least one win. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Has a good first up record.", "order": 3, "usecount": 0}, {"sentence": "Generally competitive fresh.", "order": 1, "usecount": 0}, {"sentence": "Goes well fresh.", "order": 5, "usecount": 0}, {"sentence": "Comes to hand quickly.", "order": 2, "usecount": 0}, {"sentence": "Has run well fresh.", "order": 4, "usecount": 0}, {"sentence": "Proven fresh.", "order": 6, "usecount": 0}, {"sentence": "Capable fresh.", "order": 7, "usecount": 0}, {"sentence": "Can pull out a run fresh.", "order": 8, "usecount": 0}], "ruleParameters": [{"type": "count", "campaign": "each", "campaignrun": 1, "minvalue": 3, "compare1": {"phase": "dbForm"}}, {"type": "count", "campaign": "each", "campaignrun": 1, "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "percent", "campaign": "each", "campaignrun": 1, "percentTarget": 0.2, "percentCompare": "greaterThan", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan"}, {"type": "percent", "campaign": "each", "campaignrun": 1, "percentTarget": 0.7, "percentCompare": "lessThan", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}]}, {"name": "GreatFirstUp", "order": 1, "description": "For horses in a first-up preparation who have won/placed 70 per cent and more after at least two first-ups. Must include at least two wins. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Performs well fresh.", "order": 5, "usecount": 0}, {"sentence": "Has excellent first up record.", "order": 1, "usecount": 0}, {"sentence": "Boasts an impressive fresh record.", "order": 2, "usecount": 0}, {"sentence": "Strikes form quickly.", "order": 3, "usecount": 0}, {"sentence": "Comes to hand quickly.", "order": 4, "usecount": 0}, {"sentence": "Hits straps quickly.", "order": 6, "usecount": 0}], "ruleParameters": [{"type": "count", "campaign": "each", "campaignrun": 1, "minvalue": 2, "compare1": {"phase": "dbForm"}}, {"type": "count", "campaign": "each", "campaignrun": 1, "minvalue": 2, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "percent", "campaign": "each", "campaignrun": 1, "percentTarget": 0.7, "percentCompare": "greaterThan", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}]}, {"name": "WonLastFirstUp", "order": 3, "description": "For horses in a first-up preparation who won their first-up last preparation and has had at least 3 starts. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Won in {last-first-up-class} company at {last-first-up-track} first-up last campaign.", "order": 1, "usecount": 0}, {"sentence": "Saluted at {last-first-up-track} first-up last time in.", "order": 2, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 3, "compare1": {"phase": "dbForm"}}, {"type": "count", "campaign": "each", "campaignrun": 1, "consecutive": true, "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}]}, {"name": "PlacedLastFirstUp", "order": 4, "description": "For horses in a first-up preparation who placed in their first-up last preparation and has had at least 3 starts. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Placed in {last-first-up-class} company at {last-first-up-track} first-up last campaign.", "order": 1, "usecount": 0}, {"sentence": "Wasn't far away in {last-first-up-class} company at {last-first-up-track} first-up last campaign.", "order": 2, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm"}}, {"type": "count", "campaign": "each", "campaignrun": 1, "consecutive": true, "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}]}, {"name": "WinnerFirst-UpWonPlacedTrial", "order": 6, "description": "For horses in first-up peparation who have won 1+ times this distance and s<PERSON> finished 2nd or 3rd in 70% of first-up runs. Had 1 recent trial (inside 30 days) which it won.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Trialled well for return.", "order": 1, "usecount": 0}, {"sentence": "Recent trial winner.", "order": 2, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "dbTrialForm", "number": 1, "field": "classes.class_id"}, "compare2": {"phase": "staticValue", "value": 90}, "compareFunc": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentMeeting", "field": "meetingDate", "date": true}, "compare2": {"phase": "dbTrialForm", "field": "meeting_date", "date": true, "number": 1, "modifyOperator": "+", "modifyValue": 31}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "dbTrialForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "percent", "campaign": "each", "campaignrun": 1, "percentTarget": 0.69, "percentCompare": "greaterThan", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan"}, {"type": "count", "campaign": "each", "campaignrun": 1, "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "currentRace", "field": "distance.@_metres", "distance": true}, "compare4": {"phase": "dbForm", "field": "distance.@_metres", "distance": true}, "compareFunc3": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 9.01}, "compareFunc": "lessThan"}], "jsonParams": {"odds_or_below": 9}}, {"name": "WinnerFirst-UpMultipleTrials", "order": 6, "description": "For horses that have won first-up and had 2+ barrier trials since last race start.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Winner fresh and {trials-since-last-start} trials will have {sex} ready for this.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "count", "consecutive": true, "minvalue": 2, "compare1": {"phase": "dbTrialForm", "number": 1, "field": "classes.class_id"}, "compare2": {"phase": "staticValue", "value": 90}, "compareFunc": "equalTo"}, {"type": "count", "campaign": "each", "campaignrun": 1, "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}]}, {"name": "PlacedFirst-UpWonPlacedTrial", "order": 6, "description": "For horses that have not won first-up but have finished 2nd or 3rd first-up, and won or placed in latest barrier trials since last race start.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Trialled well for return.", "order": 1, "usecount": 0}, {"sentence": "Recent trial winner.", "order": 2, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "dbTrialForm", "number": 1, "field": "classes.class_id"}, "compare2": {"phase": "staticValue", "value": 90}, "compareFunc": "equalTo"}, {"type": "count", "campaign": "each", "campaignrun": 1, "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "dbTrialForm", "number": 1, "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 19.01}, "compareFunc": "lessThan"}]}, {"name": "WinnerFirst-up", "order": 6, "description": "For a horse that has one win first-up but no trials before returning here.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Capable fresh.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "count", "campaign": "each", "campaignrun": 1, "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 19.01}, "compareFunc": "lessThan"}]}, {"name": "MultipleWinnerFirst-UpWonTrials1", "order": 6, "description": "For horses in first-up peparation who have won 2+ times first-up and also finished 2nd or 3rd in 66% of first-up runs. Won 2+ trials since.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Tough first-up assignment but usually strong on the fresh side and fared well in recent trials.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 4, "compare1": {"phase": "dbForm"}}, {"type": "count", "consecutive": true, "minvalue": 2, "compare1": {"phase": "dbTrialForm", "field": "classes.class_id"}, "compare2": {"phase": "staticValue", "value": 90}, "compareFunc": "equalTo"}, {"type": "count", "consecutive": true, "minvalue": 2, "compare1": {"phase": "dbTrialForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "count", "campaign": "each", "campaignrun": 1, "minvalue": 2, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "percent", "campaign": "each", "campaignrun": 1, "percentTarget": 0.65, "percentCompare": "greaterThan", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 19.01}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 9.99}, "compareFunc": "greaterThan"}]}, {"name": "MultipleWinnerFirst-UpWonTrials2", "order": 6, "description": "For horses in first-up peparation who have won 2+ times and finished 2nd or 3rd in 66% of first-up runs. Won 2+ trials since.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Highly effective fresh and {trials-since-last-start} solid trials will have {sex} well primed for this.", "order": 1, "usecount": 0}, {"sentence": "Dangerous fresh and indicated was forward with {trials-since-last-start} trials.", "order": 2, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 4, "compare1": {"phase": "dbForm"}}, {"type": "count", "consecutive": true, "minvalue": 2, "compare1": {"phase": "dbTrialForm", "field": "classes.class_id"}, "compare2": {"phase": "staticValue", "value": 90}, "compareFunc": "equalTo"}, {"type": "count", "consecutive": true, "minvalue": 2, "compare1": {"phase": "dbTrialForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "count", "campaign": "each", "campaignrun": 1, "minvalue": 2, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "percent", "campaign": "each", "campaignrun": 1, "percentTarget": 0.65, "percentCompare": "greaterThan", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}]}, {"name": "UnbeatenFirstUp", "order": 1, "description": "For horses in a first-up preparation who have won all previous starts first-up. Must include at least two wins.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Unbeaten first-up.", "order": 5, "usecount": 0}], "ruleParameters": [{"type": "count", "campaign": "each", "campaignrun": 1, "minvalue": 2, "compare1": {"phase": "dbForm"}}, {"type": "count", "campaign": "each", "campaignrun": 1, "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "notEqualTo"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}]}, {"name": "NoWinsFirst-UpShortDist", "order": 1, "description": "For horses in a first-up preparation who have never finished 1st,2nd,3rd, or 4th first-up and this distance is at least 401m under shortest winning distance.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Returns over an unsuitable trip and has never fired fresh.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "count", "campaign": "each", "campaignrun": 1, "minvalue": 2, "compare1": {"phase": "dbForm"}}, {"type": "count", "campaign": "each", "campaignrun": 1, "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "lessThan"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "distance.@_metres", "distance": true}, "compare2": {"phase": "upcomingRace", "field": "distance.@_metres", "modifyOperator": "+", "modifyValue": 399}, "compareFunc": "greaterThan", "compare3": {"phase": "dbForm", "field": "finish_position"}, "compare4": {"phase": "staticValue", "value": 1}, "compareFunc3": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 14.99}, "compareFunc": "greaterThan"}]}], "Exclusive": [13, 14]}