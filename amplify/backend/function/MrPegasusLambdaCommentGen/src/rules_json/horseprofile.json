{"_id": "b7bb81df-5ef9-4fca-9159-85b4ba200e98", "name": "HorseProfile", "active": true, "order": 7, "ranking": 10, "usecount": 0, "groupParameters": [], "rules": [{"name": "ShootingFor", "order": 12, "description": "For horses that have won 3 or more races in a row.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Chasing {wins-in-row} successive win.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "count", "consecutive": true, "minvalue": 3, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}]}, {"name": "PromisingMaiden", "order": 19, "description": "For maiden horses with between 2 and 5 starts that has yet to win but finished 2nd last start and placed in 66% of all other starts. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Showing potential.", "order": 4, "usecount": 0}, {"sentence": "Shown some promise.", "order": 2, "usecount": 0}, {"sentence": "Capable type.", "order": 3, "usecount": 0}, {"sentence": "Promising {age}.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm"}}, {"type": "count", "maxvalue": 5, "compare1": {"phase": "dbForm"}}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 5.01}, "compareFunc": "lessThan"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}]}, {"name": "SprinterS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "order": 18, "description": "For horse with 4+ wins and all wins are under 1300m. Was positioned 1 or 2 when settling down at last three starts.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "High speed {sex}.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 4, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 4, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "distance.@_metres"}, "compare2": {"phase": "staticValue", "value": 1300}, "compareFunc": "greaterThan", "compare3": {"phase": "dbForm", "field": "finish_position"}, "compare4": {"phase": "staticValue", "value": 1}, "compareFunc3": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}, {"type": "count", "minvalue": 3, "consecutive": true, "compare1": {"phase": "dbForm", "field": "positions.@_settling_down"}, "compare2": {"phase": "staticValue", "value": 3}, "compareFunc": "lessThan"}]}, {"name": "ShortCourseSpecialist", "order": 18, "description": "For horse with 4+ wins and all wins are under 1151m.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Short course specialist.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 4, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 4, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "distance.@_metres"}, "compare2": {"phase": "staticValue", "value": 1150}, "compareFunc": "greaterThan", "compare3": {"phase": "dbForm", "field": "finish_position"}, "compare4": {"phase": "staticValue", "value": 1}, "compareFunc3": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}]}, {"name": "DistanceSpecialist", "order": 18, "description": "For horse with 4+ wins and 100% of all wins are at this distance. Didn't win last start.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "{distance} specialist.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 4, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 4, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "distance.@_metres"}, "compare2": {"phase": "currentRace", "field": "distance.@_metres"}, "compareFunc": "notEqualTo", "compare3": {"phase": "dbForm", "field": "finish_position"}, "compare4": {"phase": "staticValue", "value": 1}, "compareFunc3": "equalTo"}, {"type": "compare", "compare1": {"phase": "dbForm", "number": 1, "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}]}, {"name": "TrackSpecialist", "order": 18, "description": "For horses with 10+ starts and 4+ wins. Raced different track last start. All wins on this track. Ignore Fannie Bay, Pioneer Park.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Track specialist.", "order": 1, "usecount": 0}, {"sentence": "Does best work here.", "order": 2, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 10, "compare1": {"phase": "dbForm"}}, {"type": "compare", "compare1": {"phase": "currentRace", "field": "track.@_name"}, "compare2": {"phase": "dbForm", "number": 1, "field": "track.@_name"}, "compareFunc": "notEqualTo"}, {"type": "count", "minvalue": 4, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_name"}, "compare4": {"phase": "currentRace", "field": "track.@_name"}, "compareFunc3": "equalTo"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_name"}, "compare4": {"phase": "currentRace", "field": "track.@_name"}, "compareFunc3": "notEqualTo"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 7.01}, "compareFunc": "lessThan"}]}, {"name": "PromisingMaidenShortOdds", "order": 18, "description": "For maiden horses with 1- 3 starts that has yet to win but finished 2nd at last start and 2nd or 3rd at any runs before that.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Showing potential and looks well poised.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm"}}, {"type": "count", "maxvalue": 3, "compare1": {"phase": "dbForm"}}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 3.01}, "compareFunc": "lessThan"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 3}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "dbForm", "number": 1, "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 2}, "compareFunc": "equalTo"}]}, {"name": "UnbeatenHorse", "order": 10, "description": "For unbeaten horses who have had at least {min-starts} starts. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Undefeated galloper.", "order": 1, "usecount": 0}, {"sentence": "Unbeaten {sex}.", "order": 2, "usecount": 0}, {"sentence": "Boasts perfect record.", "order": 3, "usecount": 0}, {"sentence": "Record superb.", "order": 4, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10}, "compareFunc": "lessThan"}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm"}}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "notEqualTo"}]}, {"name": "TalentedHorse", "order": 16, "description": "For horses that won in their last start and have won {min-wins} or more races and a 1st/2nd/3rd percentage of {min-place-percent}% or more after at least {min-starts} starts. Ignore for horses with odds over ${odds-ignore}, or FF5 ratings ranking over {ff5-rank-ignore}% for NZ tracks.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Talented {sex}.", "order": 1, "usecount": 0}, {"sentence": "Strong performer.", "order": 2, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 5, "compare1": {"phase": "dbForm"}}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 4.01}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "place_percentage"}, "compare2": {"phase": "staticValue", "value": 74}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 4, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}]}, {"name": "100%win/place", "order": 16, "description": "For horses with 6+ starts and won between 40% and 75% of races and placed all other starts.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "100% win-place strike rate.", "order": 1, "usecount": 0}, {"sentence": "Consummate professional.", "order": 2, "usecount": 0}, {"sentence": "Super consistent {sex}.", "order": 3, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 6, "compare1": {"phase": "dbForm"}}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "place_percentage"}, "compare2": {"phase": "staticValue", "value": 99}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "win_percentage"}, "compare2": {"phase": "staticValue", "value": 39}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "win_percentage"}, "compare2": {"phase": "staticValue", "value": 76}, "compareFunc": "lessThan"}]}, {"name": "Pro<PERSON><PERSON><PERSON><PERSON><PERSON>", "order": 16, "description": "For horses with 9 or more starts and won 75% of all races.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Prolific winner.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 9, "compare1": {"phase": "dbForm"}}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 15.01}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "win_percentage"}, "compare2": {"phase": "staticValue", "value": 74}, "compareFunc": "greaterThan"}]}, {"name": "ConsistentHorse", "order": 17, "description": "For horses that won or placed in their last 3 starts and have a win percentage of {min-win-percent}% or more and a 1st/2nd/3rd/4th percentage of {min-place-percent}% or more after at least {min-starts} starts. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Consistent {sex}.", "order": 1, "usecount": 0}, {"sentence": "Honest competitor.", "order": 2, "usecount": 0}, {"sentence": "Solid performer.", "order": 3, "usecount": 0}, {"sentence": "Capable galloper.", "order": 4, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 5, "compare1": {"phase": "dbForm"}}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 7.01}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "place_percentage"}, "compare2": {"phase": "staticValue", "value": 65}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "win_percentage"}, "compare2": {"phase": "staticValue", "value": 32}, "compareFunc": "greaterThan"}, {"type": "count", "consecutive": true, "minvalue": 3, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "lessThan"}]}, {"name": "HandyHorse", "order": 18, "description": "For horses that won or placed in their last 2 starts and have a win percentage of {min-win-percent}% or more and a 1st/2nd/3rd/4th percentage of {min-place-percent}% or more after at least {min-starts} starts. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Honest customer.", "order": 1, "usecount": 0}, {"sentence": "Handy galloper.", "order": 2, "usecount": 0}, {"sentence": "Useful galloper.", "order": 3, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 5, "compare1": {"phase": "dbForm"}}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 7.01}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "place_percentage"}, "compare2": {"phase": "staticValue", "value": 49}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "win_percentage"}, "compare2": {"phase": "staticValue", "value": 32}, "compareFunc": "greaterThan"}, {"type": "count", "consecutive": true, "minvalue": 2, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "lessThan"}]}, {"name": "VeteranBadOdds", "order": 20, "description": "For horses {min-age}yo or older but less than or equal to {max-age}yo that have had {min-starts} starts or more and {min-wins} wins or more. Ignore for jumps races. {odds-restr}", "active": true, "direction": 0, "usecount": 0, "sentences": [{"sentence": "Veteran galloper.", "order": 1, "usecount": 0}, {"sentence": "Seasoned campaigner.", "order": 2, "usecount": 0}, {"sentence": "Veteran.", "order": 3, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "race_type"}, "compare2": {"phase": "staticValue", "value": "Flat"}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 50, "compare1": {"phase": "dbForm"}}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 14.99}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "@_age"}, "compare2": {"phase": "staticValue", "value": 7}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "@_age"}, "compare2": {"phase": "staticValue", "value": 10}, "compareFunc": "lessThan"}, {"type": "count", "minvalue": 5, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}]}, {"name": "VeteranGoodOdds", "order": 21, "description": "For horses {min-age}yo or older but less than or equal to {max-age}yo that have had {min-starts} starts or more and {min-wins} or more wins this preparation. Ignore for jumps races. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Veteran who retains zest for racing.", "order": 1, "usecount": 0}, {"sentence": "Evergreen galloper.", "order": 2, "usecount": 0}, {"sentence": "Tough veteran.", "order": 3, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "race_type"}, "compare2": {"phase": "staticValue", "value": "Flat"}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 50, "compare1": {"phase": "dbForm"}}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "@_age"}, "compare2": {"phase": "staticValue", "value": 7}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "@_age"}, "compare2": {"phase": "staticValue", "value": 10}, "compareFunc": "lessThan"}, {"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}]}, {"name": "OldTimerBadOdds", "order": 23, "description": "For horses {min-age}yo or older. Ignore for jumps races. {odds-restr}", "active": true, "direction": -1, "usecount": 0, "sentences": [{"sentence": "Old timer.", "order": 1, "usecount": 0}, {"sentence": "Veteran.", "order": 2, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "race_type"}, "compare2": {"phase": "staticValue", "value": "Flat"}, "compareFunc": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 14.99}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "@_age"}, "compare2": {"phase": "staticValue", "value": 9}, "compareFunc": "greaterThan"}]}, {"name": "OldTimer<PERSON>ood<PERSON><PERSON><PERSON>", "order": 22, "description": "For horses {min-age}yo or older that have had {min-starts} starts or more and a win percentage of {min-win-percent} or more. Ignore for jumps races. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Great old stager.", "order": 1, "usecount": 0}, {"sentence": "Iron horse.", "order": 2, "usecount": 0}, {"sentence": "Marvellous old galloper.", "order": 3, "usecount": 0}, {"sentence": "Tough old competitor.", "order": 4, "usecount": 0}, {"sentence": "Durable old {sex}.", "order": 5, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "race_type"}, "compare2": {"phase": "staticValue", "value": "Flat"}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 50, "compare1": {"phase": "dbForm"}}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 7.01}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "@_age"}, "compare2": {"phase": "staticValue", "value": 9}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "win_percentage"}, "compare2": {"phase": "staticValue", "value": 9}, "compareFunc": "greaterThan"}]}, {"name": "ReadyToWin", "order": 11, "description": "For horses that haven’t won this campaign, but have come second or third in their last 2 recent runs. If it has a third or fourth recent run then it must have come sixth or better in them as well. Recent runs must have a gap between dates of 28 days or less. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Knocking on the door.", "order": 5, "usecount": 0}, {"sentence": "Ready to win.", "order": 1, "usecount": 0}, {"sentence": "Primed to strike.", "order": 2, "usecount": 0}, {"sentence": "Due for a win.", "order": 3, "usecount": 0}, {"sentence": "Close-up of late.", "order": 4, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 2, "campaign": "current", "compare1": {"phase": "dbForm"}}, {"type": "count", "maxvalue": 0, "campaign": "current", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "count", "maxvalue": 0, "campaign": "current", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 6}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentMeeting", "field": "meetingDate", "date": true}, "compare2": {"phase": "dbForm", "field": "meeting_date", "date": true, "number": 1, "modifyOperator": "+", "modifyValue": 29}, "compareFunc": "lessThan"}, {"type": "count", "maxvalue": 0, "campaign": "current", "campaignrunexclude": [1], "compare1": {"phase": "dbForm", "field": "days_since_last_run"}, "compare2": {"phase": "staticValue", "value": 28}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "dbForm", "number": 1, "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "dbForm", "number": 2, "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 5.01}, "compareFunc": "lessThan"}]}, {"name": "StrongSPProfile", "order": 11, "description": "For a horse who hasn't won last three starts this campaign but started under $4 all starts.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Strong SP profile.", "order": 5, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 3, "campaign": "current", "compare1": {"phase": "dbForm"}}, {"type": "count", "maxvalue": 0, "campaign": "current", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "count", "maxvalue": 0, "campaign": "current", "compare1": {"phase": "dbForm", "field": "decimalprices.@_starting"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 7.01}, "compareFunc": "lessThan"}]}, {"name": "StrongSPProfileMaiden", "order": 11, "description": "For a horse who is still a maiden and had 2+ starts. Started under $4 all starts.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Strong SP profile.", "order": 5, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm"}}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "decimalprices.@_starting"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 7.01}, "compareFunc": "lessThan"}]}, {"name": "ChasingHatTrick", "order": 13, "description": "For horses that have come first in their last 2 recent runs and either not had a third recent run or did not win it. Recent runs must have a gap between dates of 28 days or less. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Chasing hat-trick.", "order": 1, "usecount": 0}, {"sentence": "Shooting for hat-trick.", "order": 2, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 2, "consecutive": true, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "compare", "ifExists": true, "compare1": {"phase": "dbForm", "number": 3, "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "notEqualTo"}, {"type": "compare", "compare1": {"phase": "currentMeeting", "field": "meetingDate", "date": true}, "compare2": {"phase": "dbForm", "field": "meeting_date", "date": true, "number": 1, "modifyOperator": "+", "modifyValue": 29}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "dbForm", "field": "days_since_last_run", "number": 1, "isNotEmpty": true}, "compare2": {"phase": "staticValue", "value": 29}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "dbForm", "field": "days_since_last_run", "number": 2, "isNotEmpty": false}, "compare2": {"phase": "staticValue", "value": 29}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 5.01}, "compareFunc": "lessThan"}]}, {"name": "ChasingSameTrackHatTrick", "order": 12, "description": "For horses that have come first in their last 2 recent runs at same track and either not had a third recent run or did not win it. Recent runs must have a gap between dates of 28 days or less. Again on same track. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Chasing {last-track} hat-trick.", "order": 1, "usecount": 0}, {"sentence": "Shooting for {last-track} hat-trick.", "order": 2, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 2, "consecutive": true, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 2, "consecutive": true, "compare1": {"phase": "dbForm", "field": "track.@_name"}, "compare2": {"phase": "currentRace", "field": "track.@_name"}, "compareFunc": "equalTo"}, {"type": "compare", "ifExists": true, "compare1": {"phase": "dbForm", "number": 3, "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "notEqualTo"}, {"type": "compare", "compare1": {"phase": "currentMeeting", "field": "meetingDate", "date": true}, "compare2": {"phase": "dbForm", "field": "meeting_date", "date": true, "number": 1, "modifyOperator": "+", "modifyValue": 29}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "dbForm", "field": "days_since_last_run", "number": 1, "isNotEmpty": true}, "compare2": {"phase": "staticValue", "value": 29}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "dbForm", "field": "days_since_last_run", "number": 2, "isNotEmpty": false}, "compare2": {"phase": "staticValue", "value": 29}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}]}, {"name": "InForm", "order": 14, "description": "For horses that have won their last recent run, come 2nd OR 3rd at the recent run 2 starts back, and won at the recent run 3 starts back. They must have at least 7 total runs and 3 recent runs. Recent runs must have a gap between dates of 28 days or less. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Racing in top order.", "order": 1, "usecount": 0}, {"sentence": "Holding form.", "order": 2, "usecount": 0}, {"sentence": "Form solid.", "order": 6, "usecount": 0}, {"sentence": "Going really well.", "order": 3, "usecount": 0}, {"sentence": "Racing in great heart.", "order": 4, "usecount": 0}, {"sentence": "Having a good preparation.", "order": 5, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 7, "compare1": {"phase": "dbForm"}}, {"type": "compare", "ifExists": true, "compare1": {"phase": "dbForm", "number": 1, "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "compare", "ifExists": true, "compare1": {"phase": "dbForm", "number": 2, "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "notEqualTo"}, {"type": "compare", "ifExists": true, "compare1": {"phase": "dbForm", "number": 2, "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "lessThan"}, {"type": "compare", "ifExists": true, "compare1": {"phase": "dbForm", "number": 3, "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentMeeting", "field": "meetingDate", "date": true}, "compare2": {"phase": "dbForm", "field": "meeting_date", "date": true, "number": 1, "modifyOperator": "+", "modifyValue": 29}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "dbForm", "field": "days_since_last_run", "number": 1, "isNotEmpty": true}, "compare2": {"phase": "staticValue", "value": 29}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "dbForm", "field": "days_since_last_run", "number": 2, "isNotEmpty": false}, "compare2": {"phase": "staticValue", "value": 29}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}]}, {"name": "RacingWell", "order": 15, "description": "For horses that have come second, third or fourth in their last 3 runs and those races have had 8 or more runners and been within the last 2 months. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Racing well.", "order": 1, "usecount": 0}, {"sentence": "Form solid.", "order": 2, "usecount": 0}, {"sentence": "Form sound.", "order": 3, "usecount": 0}, {"sentence": "Going well.", "order": 4, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 3, "consecutive": true, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "notEqualTo"}, {"type": "count", "minvalue": 3, "consecutive": true, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "lessThan"}, {"type": "count", "minvalue": 3, "consecutive": true, "compare1": {"phase": "dbForm", "field": "starters"}, "compare2": {"phase": "staticValue", "value": 8}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 3, "consecutive": true, "compare1": {"phase": "dbForm", "field": "meeting_date", "date": true}, "compare2": {"phase": "currentMeeting", "field": "meetingDate", "date": true, "modifyOperator": "+", "modifyValue": 62}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}]}, {"name": "JumpsDebut", "order": 1, "description": "For horses making their jumps debut. {odds-restr}", "active": true, "direction": 0, "usecount": 0, "sentences": [{"sentence": "Making jumps debut.", "order": 1, "usecount": 0}, {"sentence": "Having first start over the jumps.", "order": 2, "usecount": 0}, {"sentence": "Kicks off jumping career here.", "order": 3, "usecount": 0}, {"sentence": "First time over obstacles.", "order": 4, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "race_type"}, "compare2": {"phase": "staticValue", "value": "Jumps"}, "compareFunc": "equalTo"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "isJumps": true}}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 15.01}, "compareFunc": "lessThan"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "order": 2, "description": "For horses in a jumps race that have previously had a single jumps race and won it. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Won at jumps debut.", "order": 1, "usecount": 0}, {"sentence": "Won only other jumps start.", "order": 2, "usecount": 0}, {"sentence": "Good win at jumps debut.", "order": 3, "usecount": 0}, {"sentence": "<PERSON><PERSON> win at jumps debut.", "order": 4, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "race_type"}, "compare2": {"phase": "staticValue", "value": "Jumps"}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm", "isJumps": true}}, {"type": "count", "maxvalue": 1, "compare1": {"phase": "dbForm", "isJumps": true}}, {"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position", "isJumps": true}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 7.01}, "compareFunc": "lessThan"}]}, {"name": "PlacedOnlyJumps", "order": 3, "description": "For horses in a jumps race that have previously had a single jumps race and came second or third. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Handy placing at only other jumps assignment.", "order": 1, "usecount": 0}, {"sentence": "Made promising start to jumps career.", "order": 2, "usecount": 0}, {"sentence": "Indicated potential at jumps debut.", "order": 3, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "race_type"}, "compare2": {"phase": "staticValue", "value": "Jumps"}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm", "isJumps": true}}, {"type": "count", "maxvalue": 1, "compare1": {"phase": "dbForm", "isJumps": true}}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position", "isJumps": true}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position", "isJumps": true}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 15.01}, "compareFunc": "lessThan"}]}, {"name": "WonLatestJumps", "order": 4, "description": "For horses in a jumps race that have had at least 2 jumps starts and won their last jumps race. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Performed impressively at latest jumps appearance.", "order": 1, "usecount": 0}, {"sentence": "Determined win at latest jumps assignment.", "order": 2, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "race_type"}, "compare2": {"phase": "staticValue", "value": "Jumps"}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm", "isJumps": true}}, {"type": "count", "minvalue": 1, "consecutive": true, "compare1": {"phase": "dbForm", "field": "finish_position", "isJumps": true}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 15.01}, "compareFunc": "lessThan"}]}, {"name": "PlacedLatestJumps", "order": 5, "description": "For horses in a jumps race that have had at least 2 jumps starts and came second or third their last jumps race. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Good effort at latest jumps outing.", "order": 1, "usecount": 0}, {"sentence": "Solid performance at latest jumps assignment.", "order": 2, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "race_type"}, "compare2": {"phase": "staticValue", "value": "Jumps"}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm", "isJumps": true}}, {"type": "count", "minvalue": 1, "consecutive": true, "compare1": {"phase": "dbForm", "field": "finish_position", "isJumps": true}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "notEqualTo"}, {"type": "count", "minvalue": 1, "consecutive": true, "compare1": {"phase": "dbForm", "field": "finish_position", "isJumps": true}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 15.01}, "compareFunc": "lessThan"}]}, {"name": "WasntDisgracedLatestJumps", "order": 6, "description": "For horses in a jumps race that have had at least 2 jumps starts and came more than third in their last jumps race, but finished in the top 50% of the field. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Wasn't disgraced at latest jumps assignment.", "order": 1, "usecount": 0}, {"sentence": "Fair showing at latest jumps outing.", "order": 2, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "race_type"}, "compare2": {"phase": "staticValue", "value": "Jumps"}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm", "isJumps": true}}, {"type": "count", "minvalue": 1, "consecutive": true, "compare1": {"phase": "dbForm", "field": "finish_position", "isJumps": true}, "compare2": {"phase": "staticValue", "value": 3}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 1, "consecutive": true, "compare1": {"phase": "dbForm", "field": "finish_position", "isJumps": true}, "compare2": {"phase": "dbForm", "field": "starters", "isJumps": true, "modifyOperator": "*", "modifyValue": 0.5}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 9.99}, "compareFunc": "greaterThan"}]}, {"name": "BattledHomeLatestJumps", "order": 7, "description": "For horses in a jumps race that have had at least 2 jumps starts and finished behind 50% of the field but in the top 80%. {odds-restr}", "active": true, "direction": 0, "usecount": 0, "sentences": [{"sentence": "Only battled home at latest jumps appearance.", "order": 1, "usecount": 0}, {"sentence": "Just fair last time over obstacles.", "order": 2, "usecount": 0}, {"sentence": "Well accounted for at latest jumps assignment.", "order": 3, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "race_type"}, "compare2": {"phase": "staticValue", "value": "Jumps"}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm", "isJumps": true}}, {"type": "count", "minvalue": 1, "consecutive": true, "compare1": {"phase": "dbForm", "field": "finish_position", "isJumps": true}, "compare2": {"phase": "staticValue", "value": 3}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 1, "consecutive": true, "compare1": {"phase": "dbForm", "field": "finish_position", "isJumps": true}, "compare2": {"phase": "dbForm", "field": "starters", "isJumps": true, "modifyOperator": "*", "modifyValue": 0.5}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 1, "consecutive": true, "compare1": {"phase": "dbForm", "field": "finish_position", "isJumps": true}, "compare2": {"phase": "dbForm", "field": "starters", "isJumps": true, "modifyOperator": "*", "modifyValue": 0.8}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 9.99}, "compareFunc": "greaterThan"}]}, {"name": "DidLittleLatestJumps", "order": 8, "description": "For horses in a jumps race that have had at least 2 jumps starts and finished behind 80% of the field. {odds-restr}", "active": true, "direction": -1, "usecount": 0, "sentences": [{"sentence": "Did little at latest jumps assignment.", "order": 1, "usecount": 0}, {"sentence": "Poor showing last time over the jumps.", "order": 2, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "race_type"}, "compare2": {"phase": "staticValue", "value": "Jumps"}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm", "isJumps": true}}, {"type": "count", "minvalue": 1, "consecutive": true, "compare1": {"phase": "dbForm", "field": "finish_position", "isJumps": true}, "compare2": {"phase": "dbForm", "field": "starters", "isJumps": true, "modifyOperator": "*", "modifyValue": 0.8}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 19.99}, "compareFunc": "greaterThan"}]}, {"name": "FellLatestJumps", "order": 9, "description": "For horses in a jumps race that fell in their last jumps race. {odds-restr}", "active": true, "direction": 0, "usecount": 0, "sentences": [{"sentence": "Fell at latest jumps assignment.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "race_type"}, "compare2": {"phase": "staticValue", "value": "Jumps"}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 1, "consecutive": true, "compare1": {"phase": "dbForm", "field": "finish_position", "isJumps": true}, "compare2": {"phase": "staticValue", "value": "FL"}, "compareFunc": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 19.99}, "compareFunc": "greaterThan"}]}, {"name": "LightlyRacedHorse", "order": 24, "description": "For horse that is a 3yo or older and has had between 3 and 7 starts with at least two spells identified bewteen runs.", "active": true, "direction": 0, "usecount": 0, "sentences": [{"sentence": "Lightly raced {age}yo.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "count", "minvalue": 3, "compare1": {"phase": "dbForm"}}, {"type": "count", "maxvalue": 7, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm", "field": "days_since_last_run"}, "compare2": {"phase": "staticValue", "value": 83}, "compareFunc": "greaterThan"}]}]}