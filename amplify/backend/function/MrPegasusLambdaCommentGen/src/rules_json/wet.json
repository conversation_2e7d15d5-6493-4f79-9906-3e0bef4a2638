{"name": "Wet", "active": true, "order": 19, "ranking": 16, "usecount": 0, "groupParameters": [], "rules": [{"name": "NoWetStarts", "order": 1, "description": "For Soft6+ races. For horses who haven't run in wet after 3 or more starts. Ignore for horses with odds ${odds-ignore} or under.", "active": true, "direction": 0, "usecount": 0, "sentences": [{"sentence": "Query wet.", "order": 4, "usecount": 0}, {"sentence": "Untested in wet.", "order": 2, "usecount": 0}, {"sentence": "Untried in the wet.", "order": 3, "usecount": 0}, {"sentence": "Unknown in wet.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentMeeting", "field": "processedMeetingData.meeting.track.@_expected_condition", "condition": true}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 3, "compare1": {"phase": "dbForm"}}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 11.99}, "compareFunc": "greaterThan"}]}, {"name": "NoWetWinsOrPlaces", "order": 2, "description": "For Soft6+ races. Only for horses who haven't won or placed in the wet after 3 or more starts on wet tracks (Soft6+) and their win/place percentage is better on dry tracks. Ignore comment if it's a maiden race. Ignore for horses with odds ${odds-ignore} or under.", "active": true, "direction": -1, "usecount": 0, "sentences": [{"sentence": "Unplaced in wet.", "order": 4, "usecount": 0}, {"sentence": "Prefer on top of the ground.", "order": 1, "usecount": 0}, {"sentence": "Has struggled on wet tracks.", "order": 2, "usecount": 0}, {"sentence": "Seems to struggle on wet tracks.", "order": 3, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentMeeting", "field": "processedMeetingData.meeting.track.@_expected_condition", "condition": true}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentRace", "field": "class.class_id"}, "compare2": {"phase": "staticValue", "value": 30}, "compareFunc": "notEqualTo"}, {"type": "count", "minvalue": 3, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 3, "compare1": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "greaterThan"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 4}, "compareFunc3": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "place_percentage"}, "compare2": {"phase": "staticValue", "value": 0.01}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 11.99}, "compareFunc": "greaterThan"}]}, {"name": "WonOftenOnWet", "order": 5, "description": "For Soft6+ races. For horses who have won 1 or more times in the wet (Soft6+) and won/placed 50% or more in the wet from 3 or more starts. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Has form in wet.", "order": 1, "usecount": 0}, {"sentence": "Handles wet ground.", "order": 2, "usecount": 0}, {"sentence": "Has wet form.", "order": 3, "usecount": 0}, {"sentence": "Handles the sting out of the ground.", "order": 4, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentMeeting", "field": "processedMeetingData.meeting.track.@_expected_condition", "condition": true}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 3, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 3, "compare1": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 4}, "compareFunc3": "greaterThan"}, {"type": "percent", "percentTarget": 0.49, "percentCompare": "greaterThan", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 4}, "compareFunc3": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 15.01}, "compareFunc": "lessThan"}]}, {"name": "WonFrequentlyInWet", "order": 4, "description": "For Soft6+ races. For horses who have won 2 or more times in the wet (Soft6+) and won/placed 67% or more in the wet from 3 or more starts. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Excels on rain affected tracks.", "order": 1, "usecount": 0}, {"sentence": "Relishes the sting out of the ground.", "order": 2, "usecount": 0}, {"sentence": "Has won multiple times in wet.", "order": 4, "usecount": 0}, {"sentence": "Strong wet stats.", "order": 5, "usecount": 0}, {"sentence": "Loves the wet.", "order": 6, "usecount": 0}, {"sentence": "Wet tracks certainly don't pose a problem.", "order": 7, "usecount": 0}, {"sentence": "Has terrific record on wet tracks.", "order": 3, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentMeeting", "field": "processedMeetingData.meeting.track.@_expected_condition", "condition": true}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 3, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 3, "compare1": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 4}, "compareFunc3": "greaterThan"}, {"type": "percent", "percentTarget": 0.66, "percentCompare": "greaterThan", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 4}, "compareFunc3": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 15.01}, "compareFunc": "lessThan"}]}, {"name": "WonMoreThanOnceInWet", "order": 6, "description": "For Soft6+ races. For horses who have won 2 or more times in the wet (Soft6+) but won/placed 50% or less in the wet from 3 or more starts. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Effective wet.", "order": 1, "usecount": 0}, {"sentence": "Handles the wet.", "order": 2, "usecount": 0}, {"sentence": "Proven in wet.", "order": 3, "usecount": 0}, {"sentence": "Gets through rain affected ground.", "order": 4, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentMeeting", "field": "processedMeetingData.meeting.track.@_expected_condition", "condition": true}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 3, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 3, "compare1": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 4}, "compareFunc3": "greaterThan"}, {"type": "percent", "percentTarget": 0.51, "percentCompare": "lessThan", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 4}, "compareFunc3": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 15.01}, "compareFunc": "lessThan"}]}, {"name": "AllWinsWet", "order": 3, "description": "For Soft6+ races. For horses who have won 3 or more times and all wins were in the wet (Soft6+). Must have had 7 or more wet starts. Ignore for horses with odds over ${odds-ignore}.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Genuine wet tracker.", "order": 1, "usecount": 0}, {"sentence": "Noted wet tracker.", "order": 2, "usecount": 0}, {"sentence": "Superior wet tracker.", "order": 3, "usecount": 0}, {"sentence": "Wet track specialist.", "order": 4, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentMeeting", "field": "processedMeetingData.meeting.track.@_expected_condition", "condition": true}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 3, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 7, "compare1": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 3, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 4}, "compareFunc3": "greaterThan"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 5}, "compareFunc3": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 18.01}, "compareFunc": "lessThan"}]}, {"name": "AllWinsSoft", "order": 3, "description": "For Soft 5 to soft 7 races. Horses that have won two or more times and all wins are on soft rated tracks.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Likes the sting out.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentMeeting", "field": "processedMeetingData.meeting.track.@_expected_condition", "condition": true}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentMeeting", "field": "processedMeetingData.meeting.track.@_expected_condition", "condition": true}, "compare2": {"phase": "staticValue", "value": 8}, "compareFunc": "lessThan"}, {"type": "count", "minvalue": 3, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 4}, "compareFunc3": "greaterThan"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 5}, "compareFunc3": "lessThan"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 7}, "compareFunc3": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 18.01}, "compareFunc": "lessThan"}]}, {"name": "AllWinsHeavy", "order": 3, "description": "For Heavy 8 - Heavy 10 races. Horses that have won two or more times and all wins are on heavy rates tracks.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "All wins heavy.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentMeeting", "field": "processedMeetingData.meeting.track.@_expected_condition", "condition": true}, "compare2": {"phase": "staticValue", "value": 7}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare2": {"phase": "staticValue", "value": 7}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 7}, "compareFunc3": "greaterThan"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 8}, "compareFunc3": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 18.01}, "compareFunc": "lessThan"}]}, {"name": "NoHeavyStartsStatsSoft", "order": 3, "description": "For Heavy 8 - Heavy 10 races. For horses who haven't run on a heavy rated track after 1 or more starts. Had 1+ start on soft rated tracks. Won 1+ starts on soft rated tracks.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Wet form - {stats:condition:soft}; untested heavy.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentMeeting", "field": "processedMeetingData.meeting.track.@_expected_condition", "condition": true}, "compare2": {"phase": "staticValue", "value": 7}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm"}}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare2": {"phase": "staticValue", "value": 7}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 4}, "compareFunc3": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 18.01}, "compareFunc": "lessThan"}]}, {"name": "AllWinsDry", "order": 3, "description": "For Soft 5 to heavy 10. Horses that have had 5+ starts. Had 1+ starts on firm or good tracks. Had 1+ starts on soft tracks. Had 1+ starts on heavy tracks. All wins on firm and good tracks.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Best on good/firm going.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentMeeting", "field": "processedMeetingData.meeting.track.@_expected_condition", "condition": true}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 5, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare2": {"phase": "staticValue", "value": 5}, "compareFunc": "lessThan"}, {"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "greaterThan", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 8}, "compareFunc3": "lessThan"}, {"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare2": {"phase": "staticValue", "value": 7}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 5}, "compareFunc3": "lessThan"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_grading", "condition": true}, "compare4": {"phase": "staticValue", "value": 4}, "compareFunc3": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 18.01}, "compareFunc": "lessThan"}]}, {"name": "AllWinsSynthetic", "order": 3, "description": "For horses racing on a synthetic track. Had 5+ starts and 2+ wins. Won all previous races on synthetic tracks.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Strong synthetic form.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "track.@_track_surface"}, "compare2": {"phase": "staticValue", "value": "Y"}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 5, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 5, "compare1": {"phase": "dbForm", "field": "track.@_track_surface"}, "compare2": {"phase": "staticValue", "value": "Y"}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_track_surface"}, "compare4": {"phase": "staticValue", "value": "Y"}, "compareFunc3": "equalTo"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_track_surface"}, "compare4": {"phase": "staticValue", "value": "Y"}, "compareFunc3": "notEqualTo"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 18.01}, "compareFunc": "lessThan"}]}, {"name": "Won/PlacedAllSynthetic", "order": 3, "description": "For horses racing on a synthetic track. Had 5+ starts and 1+ wins. Won or placed all previous races on synthetic tracks.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Strong synthetic form.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "track.@_track_surface"}, "compare2": {"phase": "staticValue", "value": "Y"}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 5, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 5, "compare1": {"phase": "dbForm", "field": "track.@_track_surface"}, "compare2": {"phase": "staticValue", "value": "Y"}, "compareFunc": "equalTo"}, {"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_track_surface"}, "compare4": {"phase": "staticValue", "value": "Y"}, "compareFunc3": "equalTo"}, {"type": "count", "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 3}, "compareFunc": "greaterThan", "compare3": {"phase": "dbForm", "field": "track.@_track_surface"}, "compare4": {"phase": "staticValue", "value": "Y"}, "compareFunc3": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 18.01}, "compareFunc": "lessThan"}]}, {"name": "WinsDryWet", "order": 3, "description": "For Soft 5 to heavy 10. Horses that have won on Firm or good tracks. Also won on soft and heavy tracks.", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Strong synthetic form.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentMeeting", "field": "processedMeetingData.meeting.track.@_expected_condition", "condition": true}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "greaterThan"}, {"type": "count", "minvalue": 2, "compare1": {"phase": "dbForm"}}, {"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_grading"}, "compare4": {"phase": "staticValue", "value": 7}, "compareFunc3": "greaterThan"}, {"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_grading"}, "compare4": {"phase": "staticValue", "value": 7}, "compareFunc3": "greaterThan"}, {"type": "count", "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo", "compare3": {"phase": "dbForm", "field": "track.@_grading"}, "compare4": {"phase": "staticRange", "value1": 4, "value2": 8}, "compareFunc3": "insideRange"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 18.01}, "compareFunc": "lessThan"}]}]}