{"name": "SecondUp", "active": true, "order": 15, "ranking": 12, "usecount": 0, "groupParameters": [{"type": "compare", "compare1": {"phase": "dbForm", "field": "meeting_date", "date": true, "number": 1}, "compare2": {"phase": "dbForm", "field": "meeting_date", "date": true, "number": 2, "modifyOperator": "+", "modifyValue": 82}, "compareFunc": "greaterThan"}], "rules": [{"name": "NoSecondUpWinsOrPlaces", "order": 1, "description": "For horses in a second-up who are yet to win or place from 3 second-up runs or more. Don't include if coming back from a let up or freshened. {odds-restr}", "active": true, "direction": -1, "usecount": 0, "sentences": [{"sentence": "Unplaced second-up.", "order": 1, "usecount": 0}, {"sentence": "Second-up record isn't flash.", "order": 2, "usecount": 0}, {"sentence": "Isn't a noted second-up performer.", "order": 3, "usecount": 0}, {"sentence": "Yet to place second-up.", "order": 4, "usecount": 0}], "ruleParameters": [{"type": "count", "campaign": "each", "campaignrun": 2, "minvalue": 3, "compare1": {"phase": "dbForm"}}, {"type": "count", "campaign": "each", "campaignrun": 2, "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentMeeting", "field": "meetingDate", "date": true}, "compare2": {"phase": "dbForm", "field": "meeting_date", "date": true, "number": 1, "modifyOperator": "+", "modifyValue": 29}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 19.99}, "compareFunc": "greaterThan"}]}, {"name": "UnbeatenSecondUp", "order": 2, "description": "For horses in a second-up who have won all previous starts second-up. Don't include if coming back from a let up or freshened. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Unbeaten second-up.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "count", "campaign": "each", "campaignrun": 2, "minvalue": 1, "compare1": {"phase": "dbForm"}}, {"type": "count", "campaign": "each", "campaignrun": 2, "maxvalue": 0, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "notEqualTo"}, {"type": "compare", "compare1": {"phase": "currentMeeting", "field": "meetingDate", "date": true}, "compare2": {"phase": "dbForm", "field": "meeting_date", "date": true, "number": 1, "modifyOperator": "+", "modifyValue": 29}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 19.01}, "compareFunc": "lessThan"}]}, {"name": "GoodSecondUp", "order": 2, "description": "For horses in a second-up who have won/placed 50 per cent and less than 70 per cent after three starts. Must include at least one win. Don't include if coming back from a let up or freshened. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Has had success second-up.", "order": 1, "usecount": 0}, {"sentence": "Strips trimmer and goes well second-up.", "order": 2, "usecount": 0}, {"sentence": "<PERSON>ven second-up.", "order": 3, "usecount": 0}], "ruleParameters": [{"type": "count", "campaign": "each", "campaignrun": 2, "minvalue": 3, "compare1": {"phase": "dbForm"}}, {"type": "count", "campaign": "each", "campaignrun": 2, "minvalue": 1, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "percent", "campaign": "each", "campaignrun": 2, "percentTarget": 0.49, "percentCompare": "greaterThan", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan"}, {"type": "percent", "campaign": "each", "campaignrun": 2, "percentTarget": 0.7, "percentCompare": "lessThan", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentMeeting", "field": "meetingDate", "date": true}, "compare2": {"phase": "dbForm", "field": "meeting_date", "date": true, "number": 1, "modifyOperator": "+", "modifyValue": 29}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}]}, {"name": "GreatSecondUp", "order": 3, "description": "For horses in a second-up who have won/placed 70 per cent and more after two starts. Must include at least two wins. Don't include if coming back from a let up or freshened. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Has an impressive second-up record.", "order": 1, "usecount": 0}, {"sentence": "Strips fitter and boasts impressive second-up stats.", "order": 2, "usecount": 0}, {"sentence": "Has an excellent second-up record.", "order": 3, "usecount": 0}], "ruleParameters": [{"type": "count", "campaign": "each", "campaignrun": 2, "minvalue": 2, "compare1": {"phase": "dbForm"}}, {"type": "count", "campaign": "each", "campaignrun": 2, "minvalue": 2, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "percent", "campaign": "each", "campaignrun": 2, "percentTarget": 0.69, "percentCompare": "greaterThan", "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentMeeting", "field": "meetingDate", "date": true}, "compare2": {"phase": "dbForm", "field": "meeting_date", "date": true, "number": 1, "modifyOperator": "+", "modifyValue": 29}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}]}, {"name": "SecondUpAndGoodOdds", "order": 4, "description": "For horses in a second-up. Don't include if coming back from a let up or freshened. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Strips fitter.", "order": 1, "usecount": 0}, {"sentence": "Fitter.", "order": 2, "usecount": 0}, {"sentence": "Will only be fitter.", "order": 3, "usecount": 0}], "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentMeeting", "field": "meetingDate", "date": true}, "compare2": {"phase": "dbForm", "field": "meeting_date", "date": true, "number": 1, "modifyOperator": "+", "modifyValue": 29}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 15.01}, "compareFunc": "lessThan"}]}, {"name": "SecondUpAndWonLastPrep", "order": 5, "description": "For horses in a second-up who won second-up in their last preparation. Their recent first-up must be no more than 21 days ago. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Won second-up last campaign.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "count", "campaign": "each", "campaignrun": 2, "minvalue": 1, "compare1": {"phase": "dbForm"}}, {"type": "compare", "campaign": "previous", "campaignrun": 2, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 1}, "compareFunc": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentMeeting", "field": "meetingDate", "date": true}, "compare2": {"phase": "dbForm", "field": "meeting_date", "date": true, "number": 1, "modifyOperator": "+", "modifyValue": 21}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}]}, {"name": "SecondUpAndPlacedLastPrep", "order": 6, "description": "For horses in a second-up who placed second-up in their last preparation. Their recent first-up must be no more than 21 days ago. {odds-restr}", "active": true, "direction": 1, "usecount": 0, "sentences": [{"sentence": "Placed in {last-second-up-class} company at {last-second-up-track} second-up last campaign.", "order": 1, "usecount": 0}], "ruleParameters": [{"type": "count", "campaign": "each", "campaignrun": 2, "minvalue": 1, "compare1": {"phase": "dbForm"}}, {"type": "compare", "campaign": "previous", "campaignrun": 2, "compare1": {"phase": "dbForm", "field": "finish_position"}, "compare2": {"phase": "staticValue", "value": 4}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentMeeting", "field": "meetingDate", "date": true}, "compare2": {"phase": "dbForm", "field": "meeting_date", "date": true, "number": 1, "modifyOperator": "+", "modifyValue": 21}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 10.01}, "compareFunc": "lessThan"}], "jsonParams": {"odds_or_below": 10}}]}