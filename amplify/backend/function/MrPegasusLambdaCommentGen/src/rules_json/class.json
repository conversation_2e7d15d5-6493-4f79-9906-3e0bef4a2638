{"name": "Class", "active": true, "order": 13, "ranking": 10, "usecount": 0, "groupParameters": [], "rules": [{"name": "StrongerOpposition", "active": true, "description": "Last form run was in the same state. Last form run was 2 to 3 class brackets lower. $20 or higher odds.", "order": 2, "direction": -1, "usecount": 0, "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "track.@_state"}, "compare2": {"phase": "dbForm", "number": 1, "field": "track.@_state"}, "compareFunc": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentRace", "field": "classes.bracket"}, "compare2": {"phase": "staticValue", "value": 0}, "compareFunc": "notEqualTo"}, {"type": "compare", "compare1": {"phase": "dbForm", "number": 1, "field": "classes.bracket", "class": true}, "compare2": {"phase": "staticValue", "value": 0}, "compareFunc": "notEqualTo"}, {"type": "compare", "compare1": {"phase": "currentRace", "field": "classes.bracket", "modifyOperator": "-", "modifyValue": 1}, "compare2": {"phase": "dbForm", "number": 1, "field": "classes.bracket"}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentRace", "field": "classes.bracket", "modifyOperator": "-", "modifyValue": 4}, "compare2": {"phase": "dbForm", "number": 1, "field": "classes.bracket"}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 19.99}, "compareFunc": "greaterThan"}], "sentences": [{"sentence": "Faces stronger opposition now.", "order": 3, "usecount": 0}, {"sentence": "Up in class.", "order": 5, "usecount": 0}, {"sentence": "Going up in grade.", "order": 6, "usecount": 0}, {"sentence": "Rises in class.", "order": 1, "usecount": 0}, {"sentence": "Meeting stronger company.", "order": 7, "usecount": 0}, {"sentence": "Tougher now.", "order": 4, "usecount": 0}, {"sentence": "Rising in grade.", "order": 8, "usecount": 0}, {"sentence": "Goes to another level.", "order": 9, "usecount": 0}, {"sentence": "<PERSON><PERSON><PERSON> challenge here.", "order": 2, "usecount": 0}]}, {"name": "WeakerOpposition", "active": true, "description": "Last form run was in the same state. Last form run was 2 to 3 class brackets higher. $7 or lower odds.", "order": 3, "direction": 1, "usecount": 0, "ruleParameters": [{"type": "compare", "compare1": {"phase": "currentRace", "field": "track.@_state"}, "compare2": {"phase": "dbForm", "number": 1, "field": "track.@_state"}, "compareFunc": "equalTo"}, {"type": "compare", "compare1": {"phase": "currentRace", "field": "classes.bracket"}, "compare2": {"phase": "staticValue", "value": 0}, "compareFunc": "notEqualTo"}, {"type": "compare", "compare1": {"phase": "dbForm", "number": 1, "field": "classes.bracket"}, "compare2": {"phase": "staticValue", "value": 0}, "compareFunc": "notEqualTo"}, {"type": "compare", "compare1": {"phase": "currentRace", "field": "classes.bracket", "modifyOperator": "+", "modifyValue": 1}, "compare2": {"phase": "dbForm", "number": 1, "field": "classes.bracket"}, "compareFunc": "lessThan"}, {"type": "compare", "compare1": {"phase": "currentRace", "field": "classes.bracket", "modifyOperator": "+", "modifyValue": 4}, "compare2": {"phase": "dbForm", "number": 1, "field": "classes.bracket"}, "compareFunc": "greaterThan"}, {"type": "compare", "compare1": {"phase": "currentHorse", "field": "betting"}, "compare2": {"phase": "staticValue", "value": 19.99}, "compareFunc": "greaterThan"}], "sentences": [{"sentence": "Meeting easier opposition here.", "order": 2, "usecount": 0}, {"sentence": "Down in class.", "order": 3, "usecount": 0}, {"sentence": "Drops in grade.", "order": 5, "usecount": 0}, {"sentence": "Easier here.", "order": 6, "usecount": 0}, {"sentence": "Should find this a bit easier.", "order": 1, "usecount": 0}, {"sentence": "Back in easier class.", "order": 7, "usecount": 0}, {"sentence": "Class drop helps.", "order": 4, "usecount": 0}, {"sentence": "Back to weaker company.", "order": 8, "usecount": 0}]}]}