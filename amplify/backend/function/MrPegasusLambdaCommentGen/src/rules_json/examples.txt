
sentence structure:

In {{compare1[phase]}}, {{campaign + " campaign,"}} {{campaignrun + " up,"}} {{"excluding " + campaignrunexclude + " up,"}} {{"where is " +isJumps+ " a jumps race," }} {{"run " + compare1[number]}} {{ "if exists "+ ifExists  }} {{"where " + compare3[field] + modifyOperator + modifyValue}} {{ "is " + compareFunc3 }} {{ compare4[value]/compare4[field]/compare4[value1] + modifyOperator + modifyValue}} {{"and "+ compare4[value2]}}, {{ type }}: 
{{ minvalue/maxvalue[key] }} {{ minvalue/maxvalue[value]}} {{ consecutive }} {{"where " + compare1[field] + modifyOperator + modifyValue}} is {{ compareFunc }}  {{ compare2[value]/compare2[field]/compare2[value1] + modifyOperator + modifyValue}}   {{"and "+ compare2[value2]}} {{"in " + compare2[phase]}} {{", is " + percentCompare}} {{percentValue * 100 + "%"}}

{
    "type": "count",
    "minvalue": 1,
    "compare1": {
        "phase": "dbForm",
        "field": "finish_position"
    },
    "compare2": {
        "phase": "staticValue",
        "value": 1
    },
    "compareFunc": "equalTo",
    "compare3": {
        "phase": "dbForm",
        "field": "track.@_grading"
    },
    "compare4": {
        "phase": "staticValue",
        "value": 7
    },
    "compareFunc3": "greaterThan"
}

In dbForm, where track.@_grading is greaterThan 7, count: minvalue 1 where finish_position is equalTo 1

{
    "type": "count",
    "minvalue": 2,
    "consecutive": true,
    "compare1": {
        "phase": "dbForm",
        "field": "track.@_name"
    },
    "compare2": {
        "phase": "currentRace",
        "field": "track.@_name"
    },
    "compareFunc": "equalTo"
}

In dbForm, count: minvalue 2 consecutive where track.@_name is equalTo track.@_name in currentRace

{
    "type": "compare",
    "ifExists": true,
    "compare1": {
        "phase": "dbForm",
        "number": 3,
        "field": "finish_position"
    },
    "compare2": {
        "phase": "staticValue",
        "value": 1
    },
    "compareFunc": "notEqualTo"
}

In dbForm, run 3 if exists true, compare: where finish_position is notEqualTo 1

{
    "type": "percent",
    "campaign": "each",
    "campaignrun": 2,
    "percentTarget": 0.49,
    "percentCompare": "greaterThan",
    "compare1": {
        "phase": "dbForm",
        "field": "finish_position"
    },
    "compare2": {
        "phase": "staticValue",
        "value": 4
    },
    "compareFunc": "lessThan"
}

In dbForm, each campaign, 2 up, percent: where finish_position is lessThan 4, is greaterThan 49%

type:

    compare // compare a field in a phase to another field or to a static value
    count  // count form items, either matching specific criteria or not
    percent // calculate a percentage from form items, that can match specific criteria

consecutive // used with count to denote a false if any items do not match before a min value is met
maxvalue // used with count to denote the max number of form items that can match before a false is returned, often used with 0 to define matches that disqualify a rule
minvalue // used with count to denote the number of form items that are required to match before a true can be returned
campaign // used with dbForm or dbTrialForm, a camapign is all runs between spells (84 days wthout racing) options are "current", "previous" and "each". Use to assess whole campaigns or to pick a run number from the campaign using "campaignrun" 
campaignrun // when using phase:dbForm, only look at selected run from selected campaign. Unlike "number", 1 is the earliest campaign run, 2 is the next etc. can be an array. Ones that do not exist will be ignored.
campaignrunexclude // like campaignrun, but this is telling the system to look at all the runs not listed. can be an array. Ones that do not exist will be ignored.
ifExists // if the form item this would be referencing doesnt exist, return true
percentTarget // used with percent, shows the target percentage
percentCompare // used with greaterThan or lessThan to determine how we treat the target percent





compare1,compare2,compareFunc: 

    // this is the primary data point we are considering
    // matching these values with the compareFunc 
    // will return the desired positive or negative for the param


compare3,compare4,comparefunc3:

    // this is the filter datapoints, 
    // the filter is used to determine whether or not to check a dataset.
    // if the match returns true, 
    // it will continue to check compare1,compare2,compareFunc set 
    // if it returns false, it moves to the next dataset
    // without returning false for the param


compareFunc,comparefunc3:

    greaterThan // x > y
    lessThan // x < y
    equalTo // x === y
    notEqualTo // x !== y
    insideRange // only with staticRange x < y, x > z   please note, the numbers defined on the range are NOT INCLUDED. inside a range of 4 to 8 will only match 5,6,7 
    outsideRange  // only with staticRange x > y, x < z


compare1,compare2,compare3,compare4:

    phase:

    currentHorse // horse entry in current processed meeting
    currentRace // race level data from current race of processed meeting
    currentMeeting // meeting level data from current processed meeting
    dbHorse // horse entry in horse db table
    dbForm // data relating to the 'form' array of the horse form db entry, with trials removed
    dbTrialForm // data relating to trial form entries from the above form array
    staticValue // a fixed value
    staticRange // a fixed range that can match either insideRange or outsideRange


    field // the key path in the phase object being referenced
    number // in relation to dbForm and dbTrialForm items, 1 is most recent, 2 is next most recent etc.
    modifyOperator // * / - + mathematical operators to apply to the field value for comparison
    modifyValue // the value to be used with the mathematical operator to apply to the field value for comparison
    isJumps // used with count/percent compare1 to return true if a form item is a jumps race
    distance // true when a distance field is being compared
    date // true when a date field is being compared
    condition // true when a track condition value is being compared, its used to seperate the grading numeric value from the string condition (H8 becomes 8)
    


                


"ruleParameters": [
    // for the below, we are checking that the dbForm count (form.length)
    // has am minimum value of 2

    {
        "type": "count",
        "minvalue": 2,
        "compare1": {
            "phase": "dbForm"
        }
    },


    // for the below, we are looking at the horse.betting
    // value of the horse in the processed_meeting (currentHorse)
    // will return true if horse.betting valus is 
    // less than the staticValue of 5

    {
        "type": "compare",
        "compare1": {
            "phase": "currentHorse",
            "field": "betting"
        },
        "compare2": {
            "phase": "staticValue",
            "value": 5.01
        },
        "compareFunc": "lessThan"
    },


    // for the below, we are checking that the horse.barrier
    // from the processed_meeting, is "lessThan"
    // the value of the "currentRace" starters from the
    // processed_meeting (race.starters) 
    // multiplied ("modifyOperator" : "*") by 0.34
    // ("modifyValue": 0.34)
    // for example, in a 20 horse race, 
    // barrier 1-6 would return true (20 * 0.34 = 6.8)

    {
        "type": "compare",
        "compare1": {
            "phase": "currentHorse",
            "field": "barrier"
        },
        "compare2": {
            "phase": "currentRace",
            "value": "starters",
            "modifyOperator": "*",
            "modifyValue": 0.34

        },
        "compareFunc": "lessThan"
    }


    // for the below, it checks the 1 form item only ("number" : 1)
    // it is comparing the "field" form[0].positions["@_settling_down"]
    // is "lessThan" the staticValue of 5
    // it will return true or false

    {
        "type": "compare",
        "compare1": {
            "phase": "dbForm",
            "number": 1,
            "field": "positions.@_settling_down"
        },
        "compare2": {
            "phase": "staticValue",
            "value": 5
        },
        "compare": "lessThan"
    },


    // for the below, it will cycle through the form items
    // in each, it will compare the "field" form.finish_position,
    // to see if it is "equalTo" the "staticValue" of 1
    // because consecutive is selected, if any do not match, false is returned
    // if the minvalue number of form items is checked, 
    // and false has not been returned, true is returned

    {
        "type": "count",
        "consecutive": true,
        "minvalue": 3,
        "compare1": {
            "phase": "dbForm",
            "field": "finish_position"
        },
        "compare2": {
            "phase": "staticValue",
            "value": 1
        },
        "compareFunc": "equalTo"
    }
]




