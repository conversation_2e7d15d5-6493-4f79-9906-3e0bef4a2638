const centaur = require("@mediality/centaur");
const helper = require("./library/helper");
const barriers = require('./rules_json/barriers.json');
const horseprofile = require('./rules_json/horseprofile.json');
const firstup = require('./rules_json/firstup.json');
/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
let increasedlogging;
let classBrackets = new Map();
let trackStates = new Map();

exports.handler = async (event) => {
    event = JSON.parse(event.body);
    console.log(`EVENT: ${JSON.stringify(event)}`);
    let meeting = {};
    let con;
    let dataToUpdate;
    if (event.increasedlogging.length > 0) increasedlogging = event.increasedlogging;
    console.log(`increased logging for: ${increasedlogging}`)
    try{
        con = await helper.openDBConnection();

        
        meeting = await centaur.processed_meetings.findOne({_id:event.meeting_id}).lean();
        dataToUpdate = await commentGenRace(meeting,event.event_id,event.test,event.localtest);

        // return dataToUpdate;
        const response = {
            statusCode: 200,
            headers: { "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Headers": "*" },
            body: JSON.stringify(dataToUpdate)
        }
        console.log(response);
        return response
    } catch(err){
        console.log("Error in Handler: " + err)
        await helper.closeDBConnection(con)
        return { statusCode: 500, body: JSON.stringify(err.message) };
    } finally {
        console.log("finally");
        await helper.closeDBConnection(con)
        const response = {
            statusCode: 200,
            headers: { "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Headers": "*" },
            body: JSON.stringify(dataToUpdate)
        }
        return response
    }
    
};


const commentGenRace = async (meeting,event_id,test,localtest) => {
    let race = {};
    for (const race_raw of meeting.processedMeetingData.meeting.races.race){
        console.log(race_raw["@_id"]);
        if (race_raw["@_id"] !== event_id) continue;
        console.log("matched!");
        race = JSON.parse(JSON.stringify(race_raw));
        break;
    }
    
    let ruleGroups = []
    if (test){
        ruleGroups = await centaur.comments.find({$and:[{active:true},{testing:true}]}).sort({order:1}).lean();
    } else {
        ruleGroups = await centaur.comments.find({active:true}).sort({order:1}).lean();
    }

    if (test && ruleGroups.length === 0) {
        ruleGroups = await centaur.comments.find({active:true}).sort({order:1}).lean();
        localtest = true;
    }

    
    const classes = await centaur.race_classes.find().select("CLA_CLASS_DB_ID CLA_BRACKET").lean();
    for (const obj of classes){
        classBrackets.set(obj.CLA_CLASS_DB_ID, obj.CLA_BRACKET);
    }
    const tracks = await centaur.tracks.find().select("TRK_TRACK_DB_ID TRK_STATE_OF_TRACK").lean();
    for (const trk of tracks){
        trackStates.set(trk.TRK_TRACK_DB_ID, trk.TRK_STATE_OF_TRACK);
    }
    // console.log(classBrackets);
    // console.log(trackStates);
    race.starters = helper.getRaceStarters(race.horses.horse);
    race.classes.bracket = classBrackets.get(race.classes.class_id);
    race.track["@_state"] = trackStates.get(race.track["@_id"]);
    race.market_array = helper.sortMarkets(race);
    race.race_index = meeting.processedMeetingData.meeting.races.race.findIndex((element) => element["@_id"] === race["@_id"]);
    race.raceInputIndex = meeting.inputMeetingData.races.race.findIndex((element) => element["@_id"] === race["@_id"]);
            
    // console.log('starters',race.starters);
    // console.log('markets',race.market_array);
    
    let genData = {};
    let testData = []
    for (const horse of race.horses.horse){
        if (localtest && !increasedlogging.includes(horse["@_id"])) continue;
        // console.log('horse');
        // console.log(horse);
        let dbForm = [], dbTrialForm = [];
        const raw_form = await centaur.form.find({horse_id:horse["@_id"]}).lean();
        const dbHorse = await centaur.horses.find({HRN_HORSE_ID:horse["@_id"]}).lean();
        // console.log(raw_form);
        if (raw_form.length > 1){
            let sendMail = await helper.mailAlert(
                `ALERT: Comment failed for horse, duplicate form entry ${meeting.meetingDate} ${meeting.processsedMeetingData.meeting.track["@_name"]}`,
                `${meeting.meetingDate} ${meeting.processsedMeetingData.meeting.track["@_name"]} Race ${race["@_number"]}: duplicate form entries, horse ID ${horse["@_id"]} ${horse["@_name"]}`,
                "alert"
                );
            console.log(sendMail);
            continue;
        } else if (raw_form[0].form) {
            dbTrialForm = raw_form[0].form;
            dbForm = helper.cleanseBarrierTrials(raw_form[0].form);
            dbForm = helper.addBracketsAndStates(dbForm,classBrackets,trackStates);
            console.log(dbForm[0]);
        }
        // console.log('retrieved form');
        // console.log(dbForm);
        let formcomment = "";
        let comment = generateSummaryComment(dbForm,dbTrialForm,dbHorse,horse,race,meeting,test,localtest,ruleGroups);
        console.log(comment);
        if (test || localtest){
            testData.push({"name":horse["@_name"], "id":horse["@_id"], "rules": comment});
        } else {
            const horseIndex = meeting.processedMeetingData.meeting.races.race[race.raceInputIndex].horses.horse.findIndex((element) => element["@_id"] === horse["@_id"]);
            const horseInputIndex = meeting.inputMeetingData.races.race[race.raceInputIndex].horses.horse.findIndex((element) => element["@_id"] === horse["@_id"]);
            const horseToUpdate = "processedMeetingData.meeting.races.race." + race.race_index + ".horses.horse." + horseIndex + ".comment"
            const inputToUpdate = "inputMeetingData.races.race." + race.raceInputIndex + ".horses.horse." + horseInputIndex + ".comment"
            const horseToUpdateForm = "processedMeetingData.meeting.races.race." + race.race_index + ".horses.horse." + horseIndex + ".form_comments"
            const inputToUpdateForm = "inputMeetingData.races.race." + race.raceInputIndex + ".horses.horse." + horseInputIndex + ".form_comment"
            genData[horseToUpdate] = comment;
            genData[inputToUpdate] = comment;
            genData[horseToUpdateForm] = formcomment;
            genData[inputToUpdateForm] = formcomment;
        }
    }
    if (test || localtest){
        return testData;
    } else {
        return genData;
    }
    

}

const generateSummaryComment = (dbForm,dbTrialForm,dbHorse,horse,race,meeting,test,localtest,groups) => {
    // console.log('generating summary content');
    optLog(horse,`increase logging for: ${horse["@_id"]} ${horse["@_name"]}`)
    let summarycontent = [];
    groupLoop: for (const group of groups){
        optLog(horse,`Rule group: '${group.name}' order: ${group.order}`);
        let groupBool = true;
        if (group.groupParameters) for (param of group.groupParameters){
            switch (param.type) {
                case "compare":
                    groupBool = calculateCompare(param,dbForm,dbTrialForm,dbHorse,horse,race,meeting);
                    optLog(horse,`compare: ${groupBool}`)
                    break;
                case "count":
                    groupBool = calculateCount(param,dbForm,dbTrialForm,dbHorse,horse,race,meeting);
                    optLog(horse,`count: ${groupBool}`)
                    break;
                case "percent":
                    groupBool = calculatePercent(param,dbForm,dbTrialForm,dbHorse,horse,race,meeting);
                    optLog(horse,`percent: ${groupBool}`)
                    break;
                default:
                    console.log(`Rule param type not found.`);
                    break;
            }
        }
        if (!groupBool) continue groupLoop;
        const rules = group.rules.sort((a,b) => a.order - b.order);
        ruleLoop: for (const rule of rules){
            // console.log(`Rule name: '${rule.name}' order: ${rule.order}`);
            optLog(horse,`Rule name: '${rule.name}' order: ${rule.order}`)
            if (!rule.active) {
                console.log("rule not active, skipping");
                continue;
            }
            let ruleBool = true;                
            for (const param of rule.ruleParameters){
                let paramBool = true;
                try{
                    optLog(horse,param);
                    switch (param.type) {
                        case "compare":
                            paramBool = calculateCompare(param,dbForm,dbTrialForm,dbHorse,horse,race,meeting);
                            optLog(horse,`compare: ${paramBool}`)
                            break;
                        case "count":
                            paramBool = calculateCount(param,dbForm,dbTrialForm,dbHorse,horse,race,meeting);
                            optLog(horse,`count: ${paramBool}`)
                            break;
                        case "percent":
                            paramBool = calculatePercent(param,dbForm,dbTrialForm,dbHorse,horse,race,meeting);
                            optLog(horse,`percent: ${paramBool}`)
                            break;
                        default:
                            console.log(`Rule param type not found.`);
                            break;
                    }
                } catch(err) {
                    console.log(`${horse["@_id"]} ${rule.name}: ${err}`);
                    console.log(param);
                    continue ruleLoop;
                }
                if (!paramBool) {
                    ruleBool = false;
                    break;
                }

            }
            // console.log({"group":group.name,"rule":rule.name,"matches":ruleBool});
            if ((test || localtest) && ruleBool) {
                // console.log({"horse":horse["@_name"],"group":group.name,"rule":rule.name,"matches":ruleBool});
                summarycontent.push({"horse":horse["@_name"],"group":group.name,"rule":rule.name,"matches":ruleBool});
            } else if (ruleBool){
                const sentence = pickSentence(rule.sentences);
                summarycontent.push({rank:group.ranking,direction:rule.direction,sentence:sentence})
                continue groupLoop;
            }        
        }
    }
    // console.log(summarycontent);
    return summarycontent;
}

const calculateCompare = (param,dbForm,dbTrialForm,dbHorse,horse,race,meeting) => {
    optLog(horse,`compare: ${param.compare1.phase} ${param.compare1.field} ${param.compareFunc} ${param.compare2.phase} ${param.compare2.field ?? param.compare2.value}`)
    // console.log(`compare: ${param.compare1.phase} ${param.compare1.field} ${param.compareFunc} ${param.compare2.phase} ${param.compare2.field ?? param.compare2.value}`);
    
    const entity_1 = helper.definePhaseEntity(param.compare1,dbForm,dbTrialForm,dbHorse,horse,race,meeting);
    const entity_2 = helper.definePhaseEntity(param.compare2,dbForm,dbTrialForm,dbHorse,horse,race,meeting);
    optLog(horse,entity_1);
    optLog(horse,entity_2);
    return helper.compareEntities(param.compareFunc,param.compare1,entity_1,param.compare2,entity_2);
}

const calculatePercent = (param,dbForm,dbTrialForm,dbHorse,horse,race,meeting) => {
    // console.log("calculate percent");
    optLog(horse,`percent min: ${param.percentCompare ?? '-'} target: ${param.percentTarget ?? '-'} ${param.compare1.field ?? ''} ${param.compare2 ? param.compare2.value : ''}`)
    // console.log(`percent min: ${param.percentCompare ?? '-'} target: ${param.percentTarget ?? '-'} ${param.compare1.field ?? ''} ${param.compare2 ? param.compare2.value : ''}`)
    let percentCompare = true;
    let totalCount = 0;
    let percentCount = 0;
    let campaign = false;
    let form = [];
    if (param.compare1.phase == "dbForm"){
        form = form.concat(dbForm);
    } else if (param.compare1.phase == "dbTrialForm"){
        form = form.concat(dbTrialForm);
    }
    if (form.length === 0) return false;
    if (param.campaign) {
        form = helper.filterCampaign(form,param.campaign);
        campaign = true;
    }
    let i = -1;
    let j = -1;
    aWhileLoop: while (i < form.length){

        if (campaign){
            if (i < 0) i = 0;
            j++;
            if (form[i][j] === undefined){
                j = 0;
                i++;
            }
            // skip or include campaign 'ups' 
            if (param.campaignrunexclude !== undefined && param.campaignrunexclude.includes(j+1)) continue aWhileLoop;
            if (param.campaignrun !== undefined && (j+1) !== param.campaignrun) continue aWhileLoop;

        } else {
            i++;
        }

        // break loop if we've run out of form
        if (form[i] === undefined) break;
        let thisForm;
        if (form[i] && Array.isArray(form[i])){
            thisForm = form[i][j];
        } else {
            thisForm = form[i];
        }
        // skip or include campaign 'ups' 
        if (param.compare1.isJumps !== undefined){
            if (helper.runIsJumps(thisForm) !== param.compare1.isJumps) continue aWhileLoop;
        }

        // do the compares, counts and checks
        if (param.compare1 && param.compare2){

            //with params 3 & 4, we are filtering which form that we are checking the values of.
            if (param.compare3 && param.compare4){
                let entity_4 = helper.definePhaseEntity(param.compare4,dbForm,dbTrialForm,dbHorse,horse,race,meeting);
                if (!(helper.compareEntities(param.compareFunc3,param.compare3,thisForm,param.compare4,entity_4))) {
                    // console.log('c3 nomatch continuing awhileloop')
                    continue aWhileLoop;
                }
            }
            
            //with params 1 & 2, we are creating a pass or fail boolean to check if the (sometimes filtered) form item meets specific criteria
            let entity_2 = helper.definePhaseEntity(param.compare2,dbForm,dbTrialForm,dbHorse,horse,race,meeting);
            percentCompare = helper.compareEntities(param.compareFunc,param.compare1,thisForm,param.compare2,entity_2);
            if (percentCompare) percentCount++;
            totalCount++;
        }
    }
    switch (param.percentCompare) {
        case "greaterThan":
            return (percentCount / totalCount) >  param.percentTarget;
        case "lessThan":
            return (percentCount / totalCount) <  param.percentTarget;
        default:
            return false;
    }
}


const calculateCount = (param,dbForm,dbTrialForm,dbHorse,horse,race,meeting) => {
    // console.log("calculate count");
    optLog(horse,`count min: ${param.minvalue ?? '-'} max: ${param.maxvalue ?? '-'} ${param.compare1.field ?? ''} ${param.compare2 ? param.compare2.value : ''}`)
    let countCompare = true;
    let countCount = 0;
    let campaign = false;
    let form = [];
    if (param.compare1.phase == "dbForm"){
        form = form.concat(dbForm);
    } else if (param.compare1.phase == "dbTrialForm"){
        form = form.concat(dbTrialForm);
    }
    if (form.length === 0) {
        if (param.maxvalue !== undefined && param.maxvalue === 0) return true;
        return false;
    }
    if (param.campaign) {
        form = helper.filterCampaign(form,param.campaign);
        campaign = true;
        optLog(horse,form);
    }
    let i = -1;
    let j = -1;
    aWhileLoop: while (i < form.length){
        // console.log(`countCount: ${countCount} i: ${i} j: ${j}`)
        if (campaign){
            if (i < 0) i = 0;
            j++;
            if (form[i][j] === undefined){
                j = 0;
                i++;
            }
            // skip or include campaign 'ups' 
            if (param.campaignrunexclude !== undefined && param.campaignrunexclude.includes(j+1)) continue aWhileLoop;
            if (param.campaignrun !== undefined && (j+1) !== param.campaignrun) continue aWhileLoop;

        } else {
            i++;
        }

        // break loop if we've run out of form
        if (form[i] === undefined) break;
        let thisForm;
        if (form[i] && Array.isArray(form[i])){
            thisForm = form[i][j];
        } else {
            thisForm = form[i];
        }

        // do the compares, counts and checks
        if (param.compare1.isJumps !== undefined){
            if (helper.runIsJumps(thisForm) !== param.compare1.isJumps) continue aWhileLoop;
        }
        // console.log(param);
        if (param.compare1.field === undefined){
            countCount++;
        } else if (param.compare1 && param.compare2){
            if (param.compare3 && param.compare4){
                let entity_4 = helper.definePhaseEntity(param.compare4,dbForm,dbTrialForm,dbHorse,horse,race,meeting);
                if (!(helper.compareEntities(param.compareFunc3,param.compare3,thisForm,param.compare4,entity_4))) {
                    continue aWhileLoop;
                }
            }
            
            let entity_2 = helper.definePhaseEntity(param.compare2,dbForm,dbTrialForm,dbHorse,horse,race,meeting);
            countCompare = helper.compareEntities(param.compareFunc,param.compare1,thisForm,param.compare2,entity_2);
            if (param.consecutive && !countCompare) return false;
            if (countCompare) countCount++;
        }
        
        if (param.minvalue !== undefined && countCount >= param.minvalue) return true;
        if (param.maxvalue !== undefined && countCount > param.maxvalue) return false;
        
    }
    if (param.minvalue !== undefined && countCount < param.minvalue) return false;
    return true;
}

const pickSentence = async (sentences) => {

}

const optLog = (horse,log) => {
    if (increasedlogging.includes(horse["@_id"])){
        console.log(log);
    }
}