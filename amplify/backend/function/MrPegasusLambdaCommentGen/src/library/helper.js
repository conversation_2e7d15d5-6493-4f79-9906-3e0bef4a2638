const moment = require('moment');

const mailAlert = async (subject,body,mail_list = "error") =>{
    // console.log('trying to send mail')
    const AWS = require('aws-sdk');
    var ses = new AWS.SES({region: 'ap-southeast-2'});

    var level = {
        "task" : [
            "<EMAIL>",
            "<EMAIL>"
        ],
        "error" : [
            "<EMAIL>",
            "<EMAIL>"
        ],
        "alert" : [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ],
        "alarm" : [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
    }
    if (process.env != 'prd'){
        level = {
            "task" : [
                "<EMAIL>",
                "<EMAIL>"
            ],
            "error" : [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
            ],
            "alert" : [
                "<EMAIL>",
                "<EMAIL>"
            ],
            "alarm" : [
                "<EMAIL>",
                "<EMAIL>"
            ]
        }
    }
     
    const emailParams = {
        Destination: {
            ToAddresses: level[mail_list],
        },
        Message: {
            Body: {
                Text: { Data: body },
            },
            Subject: { Data: "Pegasus DB "+mail_list.toUpperCase()+" "+subject },
        },
        Source: "<EMAIL>",
    };
        
    try {
            let key = await ses.sendEmail(emailParams).promise();
            // console.log('mail sent');      
    } catch (e) {
            console.log('mail failed', e);
    }  
    return 'mail process complete';
    
}

const compareEntities = (compareFunc,phase_1,entity_1,phase_2,entity_2) => {
    let val_3;
    if (phase_1.phase == "staticValue") {
        entity_1.staticValue = entity_1.value;
        phase_1.field = "staticValue";
    }
    if (phase_2.phase == "staticValue") {
        entity_2.staticValue = entity_2.value;
        phase_2.field = "staticValue";
    } else if (phase_2.phase == "staticRange") {
        entity_2.staticRange = entity_2.value1;
        entity_2.staticRange2 = entity_2.value2;
        phase_2.field = "staticRange";
        phase_2.field2 = "staticRange2";
        val_3 = applyModifiers(phase_2,disseminateObjPath(phase_2.field2,entity_2))
    }

    if (phase_1.date !== phase_2.date){
        console.log("cannot compare a date to a non date! returning");
        return false;
    }

    
    const val_1 = applyModifiers(phase_1,disseminateObjPath(phase_1.field,entity_1));
    const val_2 = applyModifiers(phase_2,disseminateObjPath(phase_2.field,entity_2));
    
   
    switch (compareFunc) {
        case "equalTo":
            if (phase_1.distance) {
                if (!isNaN(val_2) && !isNaN(parseFloat(val_2))){
                    return similarDistance(parseFloat(val_1),parseFloat(val_2));
                } else {
                    console.log(`error comparing distance ${val_1} to ${val_2}`);
                    return false;
                }
            } else if (!isNaN(val_2) && !isNaN(parseFloat(val_2))) {
                return (parseFloat(val_1) === parseFloat(val_2));
            } else {
                return (val_1 === val_2);
            }
        case "notEqualTo":
            if (phase_1.distance) {
                if (!isNaN(val_2) && !isNaN(parseFloat(val_2))){
                    return (similarDistance(parseFloat(val_1),parseFloat(val_2)) ? false : true);
                } else {
                    console.log(`error comparing distance ${val_1} to ${val_2}`);
                    return false;
                }
            } else if (!isNaN(val_2) && !isNaN(parseFloat(val_2))) {
                return (parseFloat(val_1) !== parseFloat(val_2));
            } else {
                return (val_1 !== val_2);
            }
        case "greaterThan":
            return (parseFloat(val_1) > parseFloat(val_2));
        case "lessThan":
            return (parseFloat(val_1) < parseFloat(val_2)); 
        case "insideRange":
            return ((parseFloat(val_1) > parseFloat(val_2)) && (parseFloat(val_1) < parseFloat(val_3)));
        case "outsideRange":
            return ((parseFloat(val_1) < parseFloat(val_2)) || (parseFloat(val_1) > parseFloat(val_3)));
        default:
            console.log("compare function invalid");
            console.log(compareFunc);
            return false;
    }
}

const disseminateObjPath = (path,object) => {
    // console.log(`disseminate ${path}`);
    const fieldsplit = path.split(".");
    let val;
    if (fieldsplit.length === 2) {
        // console.log("fieldsplit length 2");
        val = object[fieldsplit[0]][fieldsplit[1]];
        // console.log(object);
        // console.log(val);
    } else if (fieldsplit.length === 3) {
        // console.log("fieldsplit length 3");
        val = object[fieldsplit[0]][fieldsplit[1]][fieldsplit[2]];
        // console.log(object);
        // console.log(val);
    }  else if (fieldsplit.length === 4) {
        // console.log("fieldsplit length 4");
        val = object[fieldsplit[0]][fieldsplit[1]][fieldsplit[2]][fieldsplit[3]];
        // console.log(object);
        // console.log(val);
    } else {
        val =  object[path];
    }
    return val;
}

const applyModifiers = (compare,value) =>{
    if (compare.date){
        value = makeDateNumber(value);
    }
    if (compare.condition){
        value = value.replace(/([A-z]|\(|\))*/g,"");
    }
    if (compare.modifyOperator === undefined ||
        compare.modifyValue === undefined) return value;
    switch (compare.modifyOperator) {
        case "*":
            return value * compare.modifyValue;
        case "/":
            return value / compare.modifyValue;
        case "-":
            return value - compare.modifyValue;
        case "+":
            return value + compare.modifyValue;
        default:
            console.log("invalid modifyOperator");
            return value;
    }
}

const makeDateNumber = (date) => {
    var a = moment([2010, 1, 1]);
    var b = moment(date);
    return b.diff(a, 'days');
}


const getRaceStarters = (horses) =>{
    var starters = 0
    for (var horse of horses){
      if (!horse.scratched) starters++
    }
    return starters
  }

  const getSecrets = async (secretName) => {
    const AWS = require('aws-sdk');
    var secretsmanager = new AWS.SecretsManager();
    var params = {
      SecretId: secretName,
    };
    const fetchSecretString = await secretsmanager.getSecretValue(params).promise();
    var aws_secrets = JSON.parse(fetchSecretString.SecretString)
    return aws_secrets
  }

  const openDBConnection = async (env) => {
    const mongoose = require('mongoose');
    var cert_path = "./rds-cert/global-bundle.pem";
    
    console.log('Running Online')
    var aws_secrets = await getSecrets(process.env.centaurSecrets)
    DB_USERNAME = aws_secrets.DB_USERNAME
    DB_PASSWORD = aws_secrets.DB_PASSWORD
    DB_URL = aws_secrets.DB_URL
    DB_NAME = aws_secrets.DB_NAME
    CONNECTION_STRING = "mongodb://" + DB_USERNAME + ":" + DB_PASSWORD + "@" + DB_URL + "/" + DB_NAME + "?tls=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
    await mongoose.connect(CONNECTION_STRING, {
        connectTimeoutMS: 1000,
        tlsCAFile: cert_path,
        directConnection: true,
        ssl: true,
        sslValidate: false,
        maxPoolSize: 10
    }).then(
        () => {
            console.log('Connected to Online')
            response = "Connect Online"
        },
        err => {
            console.log('Not connected:' + err)
            response = "Not Connected Online"
        });
    
    return mongoose
  }
  
  const closeDBConnection = async (con) => {
  
    con.connection.close()
    console.log("Connection Closed")
  
  }

  const cleanseBarrierTrials = (data) => {
    var cleanseddata = []
    for (raceEvent of data) {
        // if (raceEvent.weight_carried > 0){
        //     cleanseddata.push(raceEvent)
        // }
        if ((raceEvent.classes && raceEvent.classes.class_id && raceEvent.classes.class_id != 90 && (!raceEvent.classes.second_class_id || raceEvent.classes.second_class_id != 90)) || (raceEvent.classes && raceEvent.classes.class && raceEvent.classes.class == "Open" && (!raceEvent.classes.second_class_id || raceEvent.classes.second_class_id != 90) )){
          cleanseddata.push(raceEvent)
        }
    }
    return cleanseddata
  }

  const similarDistance = (dist_1,dist_2) => {
    var max = dist_1 + 50
    var min = dist_1 - 50
  
    return (dist_2 > min && dist_2 < max)
  }

  const getSexName = (Id) => {
    var map = new Map([
      ["F", "Filly"],
      ["C", "Colt"],
      ["G", "Gelding"],
      ["M", "Mare"],
      ["R", "Rig"],
      ["H", "Horse"]
    ])
    let result = map.get(Id)
    if (result == undefined) result = Id
    return result
  }

  const getStateByNumber = (stateNo) => {
    if (stateNo == 1){
        return 'ACT'
    } else if (stateNo == 2){
        return 'NSW'
    } else if (stateNo == 3){
        return 'VIC'
    } else if (stateNo == 4){
        return 'QLD'
    } else if (stateNo == 5){
        return 'SA'
    } else if (stateNo == 6){
        return 'WA'
    } else if (stateNo == 7){
        return 'TAS'
    } else if (stateNo == 8){
        return 'NT'
    } else if (stateNo == 9){
        return 'NZ'
    } else if (stateNo == 10){
        return 'HK'
    } else if (stateNo == 11){
        return 'SGP'
    } else if (stateNo == 99){
        return 'AUS'
    } else {
        return ''
    }
  }

  const classDirectionSynonyms = (direction) => {
    const down = [
      "in weaker class",
      "in weaker company",
      "in weaker event",
      "in weaker grade",
      "in easier class",
      "in easier company",
      "in easier event",
      "in easier grade",
      "in lesser class",
      "in lesser company",
      "in lesser event",
      "in lesser grade"
    ]
    const up = [
      "in stronger class",
      "in stronger company",
      "in stronger event",
      "in stronger grade",
      "in harder class",
      "in harder company",
      "in harder event",
      "in harder grade",
      "in tougher class",
      "in tougher company",
      "in tougher event",
      "in tougher grade"
    ]
  
    const same = [
      "in similar class",
      "in similar company",
      "in similar grade",
      "this level"
    ]
  
    if (direction == 'U'){
      return up[(Math.floor(Math.random() * up.length))]
    } else if (direction == 'D'){
      return down[(Math.floor(Math.random() * down.length))]
    } else if (direction == 'S'){
      return same[(Math.floor(Math.random() * same.length))]
    }
  
    return ''
  
  }

  const formatStewards = (report) => {
    const stewards_options_1 = {
        "#" : "Tightened for room",
        "$" : "Drifted back",
        "(" : "Bucked",
        "/" : "Disappointed for a run",
        "1" : "Struck interference",
        "2" : "Blundered",
        "3" : "Checked",
        "4" : "Eased",
        "5" : "Shifted out",
        "6" : "Carried wide",
        "7" : "Shifted in",
        "8" : "Hit rail",
        "9" : "Squeezed out",
        "=" : "Crowded",
        "@" : "Awkwardly placed",
        "^" : "Restrained",
        "A" : "Shied",
        "B" : "Wide",
        "I" : "Blocked for run",
        "J" : "Eased",
        "K" : "Inconvenienced",
        "M" : "Laid in",
        "N" : "Laid out",
        "O" : "Hung in",
        "P" : "Hung out",
        "Q" : "Severely hampered",
        "R" : "Hampered",
        "X" : "Held up",
        "Y" : "Overraced",
        "Z" : "Weakened"
      }
    
      const stewards_options_2 = {
        "!" : "near 50m ",
        "#" : "near 250m ",
        "$" : "near 300m ",
        "%" : "several times ",
        "&" : "near 500m ",
        ")" : "Near 900m",
        "*" : "Near 700m",
        "0" : "during race",
        "1" : "near 100m ",
        "2" : "near 200m ",
        "3" : "near 400m ",
        "4" : "near 600m ",
        "5" : "near 800m ",
        "6" : "near 1000m ",
        "7" : "near 1200m ",
        "8" : "near 1400m ",
        "9" : "near 1600m ",
        "@" : "near 150m ",
        "A" : "near 2000m ",
        "B" : "early on ",
        "C" : "near turn ",
        "D" : "in straight",
        "E" : "near post ",
        "F" : "at start ",
        "G" : "at crossing", 
        "H" : "early stages ",
        "J" : "early, middle stages ",
        "K" : "concluding stages ",
        "L" : "on straightening",
        "M" : "middle stages ",
        "T" : "throughout "
      }
      const stewards_options_3 = {
        "1N" : "Bled from one nostril",
        "1P" : "One paced",
        "2P" : "Second - positive swab",
        "3P" : "Third - positive swab",
        "3W" : "Raced three wide",
        "4W" : "Raced four wide",
        "5W" : "Raced five wide",
        "B=" : "Began awkwardly and lost ground",
        "BO" : "Choked down",
        "BR" : "Bumped rival",
        "BS" : "Bounded on jumping",
        "BV" : "Brushed rail",
        "BX" : "Struck barrier",
        "C0" : " ",
        "C1" : " ",
        "C2" : " ",
        "C3" : " ",
        "C4" : " ",
        "C5" : "Lost Ground",
        "C6" : " ",
        "C7" : "Raced Greenly",
        "C8" : "Broke Down",
        "C9" : "Disappointed for a run",
        "CA" : "Blocked for run",
        "CB" : " ",
        "CC" : "Pulled hard",
        "CD" : "Ran wide at turn",
        "CE" : "Hung in during race",
        "CF" : "Hung out during race",
        "CG" : "Ran off on turn",
        "CH" : "Clipped heels",
        "CO" : "Coughing",
        "D0" : " ",
        "D1" : "Rider's whip entangled",
        "D2" : " ",
        "D3" : "Reared at start",
        "D4" : "Left at start",
        "D5" : "Became unbalanced",
        "D6" : "Bucked at start",
        "D7" : "Began awkwardly",
        "D8" : "Tongue over bit",
        "D9" : "Failed due to track condition",
        "DA" : "Caused interference",
        "DB" : "Fractious in stalls",
        "DC" : "Backed on bar as gates opened",
        "DD" : "Swung sideways at start",
        "DE" : "Raced erratically",
        "DF" : "Slowly away",
        "DI" : "Ducked in",
        "DO" : "Ducked out",
        "DP" : "Promoted due to disqualification",
        "DR" : "Rider dropped rein",
        "DV" : "Distressed",
        "E0" : "Rider lost iron",
        "E1" : "Rider lost whip",
        "E2" : "Saddle slipped",
        "E3" : "Vet examination",
        "E4" : "Shifted ground",
        "E5" : "Head in adjoining stall",
        "E6" : "Struck head on barrier",
        "E7" : "Galloped on",
        "E8" : "Gear broke",
        "E9" : "Struck with another's whip",
        "EA" : "Bled",
        "EB" : "Fell",
        "EC" : "Lame",
        "ED" : "Injured",
        "EE" : "Lost a plate",
        "EF" : "Twisted a plate",
        "EI" : "Suffered Exercise-Induced Pulmonary Haemorrhage ",
        "EJ" : "Eased down",
        "F0" : " ",
        "F1" : " ",
        "F2" : "Ordered to trial",
        "F3" : "Shied at crossing",
        "F4" : "Rider told to use more care",
        "F5" : "Tightened for room",
        "F6" : "Swab taken by order of stewards",
        "F7" : "Inquiry into performance",
        "F8" : "Rider told to make more effort to improve position",
        "F9" : "Rider told to use more vigour",
        "FA" : "Rider charged with careless riding",
        "FB" : "Protest lodged, upheld",
        "FC" : "Protest lodged, dismissed",
        "FD" : "Protested against, upheld",
        "FE" : "Protested against, dismissed",
        "FF" : "Read full report",
        "FG" : "Failed to handle going",
        "FI" : "Floating incident - passed fit",
        "FR" : "Raced flat",
        "FS" : "Broke through barriers, cleared to start",
        "GA" : "Proved difficult to ride out",
        "GB" : "Difficult to load",
        "GC" : "Passed fit at barrier",
        "GD" : "Stewards queried run",
        "GF" : "Impeded by a fallen horse",
        "GI" : "Gear issue",
        "HH" : "Contacted hurdle",
        "HS" : "Contacted steeple",
        "HU" : "Got its head up",
        "II" : "Eye injury",
        "KR" : "Keen",
        "L1" : "Lame 1/5",
        "L2" : "Lame 2/5",
        "L3" : "Lame 3/5",
        "L4" : "Lame 4/5",
        "L5" : "Lame 5/5",
        "L=" : "Loading incident - passed fit",
        "LI" : "Rider lost both irons",
        "LP" : "Lost plates",
        "MI" : "Minor injury",
        "MS" : "Muscle strain",
        "MY" : "Mounting yard incident - passed fit",
        "NI" : "Mouth injury",
        "NR" : "Failed to respond to riding",
        "NS" : "Replated at barriers",
        "NX" : "Not fully tested",
        "PP" : "Raced below expectations",
        "RP" : "Reluctant to proceed to barriers",
        "RR" : "Resented racing between runners",
        "RS" : "Sore",
        "RW" : "Rider concerned with action",
        "S1" : "Slowly away (1L)",
        "S2" : "Dwelt at start (2L)",
        "S3" : "Broke poorly (3L)",
        "S4" : "Commenced slowly (4L)",
        "S5" : "Blew start (5L)",
        "S6" : "Blew start (6L)",
        "SL" : "Second - rider weighed in light",
        "SS" : "Slipped at start",
        "SX" : "Bombed the start (7L+)",
        "T1" : "Fractious in barriers",
        "TA" : "Tempo against",
        "TB" : "Taken back from wide barrier",
        "TF" : "Too firm",
        "TI" : "Tendon injury",
        "TL" : "Third - rider weighed in light",
        "TO" : "Tailed off",
        "TP" : "Travelled poorly",
        "TQ" : "Tactics queried",
        "TS" : "Travelled well",
        "TW" : "Too wet",
        "U1" : "Pre race incident",
        "U2" : "Shin sore",
        "U3" : "Warning issued",
        "U4" : "Vet Certificate required",
        "UL" : "Unplaced - rider weighed in light",
        "UP" : "Under pressure turn",
        "V0" : "Bled second time - life time ban",
        "V1" : "Vetted - no abnormalities",
        "V2" : "Vetted - no abnormalities, ordered to trial",
        "V3" : "Cardiac arrhythmia",
        "V4" : "Elevated heart rate",
        "V5" : "Respiratory issues",
        "V6" : "Lacerations", 
        "V7" : "In season",
        "V8" : "Poor recovery",
        "V9" : "Bled first time - three month ban",
        "VA" : "Heat stress",
        "VB" : "Blood in trachea",
        "VC" : "Irregular heart rate",
        "VD" : "Thumps",
        "VE" : "Tied up",
        "W1" : "Warning - barrier manners",
        "W2" : "Warning - uncompetitive",
        "W3" : "Warning - racing manners",
        "WC" : "Whip breach - rider charged",
        "WL" : "Winner - rider weighed in light",
        "WN" : "Weakened noticeably",
        "WP" : "Winner - positive swab",
        "WR" : "Whip breach - relegated",
        "WS" : "Wayward under pressure in straight",
        "WT" : "Wide throughout",
        "WW" : "Raced wide with cover",
        "WY" : "Raced wide without cover",
        "XP" : "Unplaced - positive swab"
      }
  
      var textReport = ""
      
    if (stewards_options_3[report]){
        textReport = stewards_options_3[report]
    } else if (report.length == 2){
        var splitAgain = report.split('')
        textReport = ((stewards_options_1[splitAgain[0]] ?? '') + ' ' + (stewards_options_2[splitAgain[1]] ?? '')).trim()
    } else {
        return report
    }
    
    return textReport
  }
  
  const runIsJumps = (item) => {
    const jumpsclasses = [50,53,59,61,62,63,64,92,108,109,110,111,112,113,115,116,117,118,119,120]
    if (
      (
        item.restrictions && item.restrictions['@_jockey'] && 
        ["X", "E", "H"].includes(item.restrictions['@_jockey'])
      ) || (
        (jumpsclasses.includes(item.classes.class_id)) || 
        (item.classes.second_class_id && jumpsclasses.includes(item.classes.second_class_id)) || 
        (item.classes.third_class_id && jumpsclasses.includes(item.classes.third_class_id))
      ) 
    ){
      return true
    } else {
      return false
    }
  }

  const sortMarkets = (race) => {
    let marketArray = [];
    for (const horse of race.horses.horse){
        if (!horse.scratched && horse.betting)
          marketArray.push({ "horse_id" : horse['@_id'], "betting" : parseFloat(horse['betting']) });
      }
    marketArray.sort((a,b) => a.betting - b.betting);

    return marketArray;
  }

  const filterCampaign = (form,filterType) => {
    // console.log("filtering campaign");
    let spell = 84;
    let filteredForm = [];
    let i = 0;
    let j = 0;
    for (const run of form){
        if (!Array.isArray(filteredForm[i])) filteredForm[i] = [];
        filteredForm[i].push(run);
        if (run.days_since_last_run > spell || (j+1) == form.length){
            i++;
        } 
        j++;
    }
    let filteredCampaigns = [];
    for (let campaign of filteredForm){
        filteredCampaigns.push(campaign.reverse());
    }
    // console.log(filteredCampaigns);
    switch (filterType) {
        case "current":
            return filteredCampaigns[0];
        case "previous":
            return (filteredCampaigns[1] ? filteredCampaigns[1] : []);
        case "each":
            return filteredCampaigns;
        default:
            console.log(`campaign filtertype not found ${filterType}`);
            return []; 
    }
    
  }

  const definePhaseEntity = (compareParam,dbForm,dbTrialForm,dbHorse,currentHorse,currentRace,currentMeeting) => {
    switch (compareParam.phase) {
        case "dbForm":
            if (compareParam.number !== undefined) {
                if (!dbForm[compareParam.number - 1]) return false;
                return JSON.parse(JSON.stringify(dbForm[compareParam.number - 1]));
            } else {
                console.log("form item number not defined for compare");
                return false;
            }
        case "dbTrialForm":
            if (compareParam.number !== undefined) {
                if (!dbTrialForm[compareParam.number - 1]) return false;
                return JSON.parse(JSON.stringify(dbTrialForm[compareParam.number - 1]));
            } else {
                console.log("form item number not defined for compare");
                return false;
            }
        case "dbHorse":
            return JSON.parse(JSON.stringify(dbHorse));
        case "currentHorse":
            return JSON.parse(JSON.stringify(currentHorse));
        case "currentRace":
            return JSON.parse(JSON.stringify(currentRace));
        case "currentMeeting":
            return JSON.parse(JSON.stringify(currentMeeting));
        case "staticValue":
            return JSON.parse(JSON.stringify(compareParam));
        default:
            console.log(`Rule param compare1 phase not found.`);  
            return false;
    }
  }

  const addBracketsAndStates = (form,classes,states) => {
    for (let item of form){
        item.classes.bracket = classes.get(item.classes.class_id);
        item.track["@_state"] = states.get(item.track["@_id"]);
    }

    return form;
  }

  module.exports = {
    openDBConnection,
    closeDBConnection,
    getSecrets,
    getRaceStarters,
    getSexName,
    getStateByNumber,
    classDirectionSynonyms,
    runIsJumps,
    cleanseBarrierTrials,
    formatStewards,
    similarDistance,
    sortMarkets,
    mailAlert,
    filterCampaign,
    compareEntities,
    definePhaseEntity,
    addBracketsAndStates
  };