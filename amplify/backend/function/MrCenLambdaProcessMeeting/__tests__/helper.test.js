const mongoose = require('mongoose');
const moment = require('moment')
const helper = require("../src/library/helper.js")

var input
var output

// readS3File,
// uploadFileS3,
// getSecrets,
/*
input = ''
output = ''
describe('JSON to XML function', () => {
  it('it should return XML', () => {
    expect(helper.jsonToXml(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('XML to JSON function', () => {
  it('it should return JSON', () => {
    expect(helper.xmlToJs(input)).toEqual(output);
  });
})


input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.convertDate(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.sDate(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getDayMonth(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.convertToUTC(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.processTrackRating(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.processPrizes(input)).toEqual(output);
  });
})


input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.processPrizes_NZ(input)).toEqual(output);
  });
})


input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.processPrizes_HK(input)).toEqual(output);
  });
})


input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.extractDamName_NZ(input)).toEqual(output);
  });
})


input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.numberSuffix(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getPastDateByDay(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.capFstLtr(input)).toEqual(output);
  });
})
*/
input = 'hello'
output = 'HELLO'
describe('UpperCase function', () => {
  it('it should return string in Upper Case', () => {
    expect(helper.upperCase(input)).toEqual(output);
  });
})
/*
input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.processStarts(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getHorseColor(input)).toEqual(output);
  });
})


input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getTrackCondition(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getTrackGrading(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getGroup(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getAgeRestriction(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getDurationMinutes(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getHorseAge(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getOffMargin(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getWeightType(input)).toEqual(output);
  });
})



// openDBConnection,
// closeDBConnection,

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getCapitalFirstLetterOnly(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getDualTrack(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.fixDuration(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.checkResultsStage(input)).toEqual(output);
  });
})


input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.convertOdds(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.validateXml(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.checkFileTypeCountry(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.checkHongKongFile(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.fixHongKongFile(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.splitNameBySpace(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getMeetingDate_SGP(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.extractDamName_SGP(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.extractDamName_HK(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.extractHorseName_HK(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getStateName(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.generateUniqueRaceId(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getGroupType(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.getSexRestriction(input)).toEqual(output);
  });
})

input = ''
output = ''
describe('', () => {
  it('', () => {
    expect(helper.processGearElement(input)).toEqual(output);
  });
})

*/
