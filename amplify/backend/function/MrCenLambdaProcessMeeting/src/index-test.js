const lambda = require('./index.js')

const localTesting = async (event) => {
    console.log(event)

    console.log(lambda)
    // Test GenMeetingData
    var result = await lambda.handler(event)
    console.log(result)



//     process.env.ENV = 'local'
//     var con = helper.openDBConnection(process.env.ENV)
    // Test GenMeetingData
    // var event = await readFile('../events/events_genMeetingData.json')
    // var result = await genMeetingData(event)
    // console.log(result)

    // Test GenMeetingData
    // var event = await readFile('../events/events_genRaceData.json')
    // var result = await genRaceData(event)
    // console.log(result)

    //Test Results
    // var event = await helper.readFile('../bfbbd6b0-dfee-5160-a6ac-0314fe526425.json')
    // var result = await engine.processResults(event, meetingId)
    // console.log(result)

    // var con= helper.openDBConnection(process.env.ENV)
    // var data = await processedMeeting.findOne({ _id: '62457ea9-e627-5359-af01-5fdc4ca73686' }).lean()
    // if (data) {
    //     pmd = data
    // }
    // console.log(pmd.processedMeetingData.meeting.races.race[0])

    // var filename = "../20220212Morphettville_Results.xml"
    // var file = await helper.readFile(filename)
    // var xml = await helper.xmlToJs(file)
    // var writeFile = await helper.writeFile('r.json', JSON.stringify(xml))

    // var filename = "../test-files/hk.xml"
    // var filecontent = await helper.readFile(filename)
    // var result = await helper.validateXml(filecontent)
    // console.log(result)

    // var filename = "../test-files/hk.xml"
    // var fileContent = await helper.readFile(filename)
    // if (helper.checkHongKongFile(fileContent)) {
    //     var newHKContent = helper.fixHongKongFile(fileContent)
    //     var jscontent = await helper.xmlToJs(newHKContent)

    // }


    // var writeFile = await helper.writeFile('../test-files/hk.json', JSON.stringify(jscontent))
    //console.log(js['!ATTLIST']['!ATTLIST']) //.'!ATTLIST')

    // var event = await helper.readFile('../test-files/sg.json')
    // var result = await normalize.normalizeMeetingData_SGP(JSON.parse(event), "xx", "Acceptances", "SGP")
    // console.log(result)
    //event = JSON.parse(event)
    //var rawhorse = event.meeting.races.race[0].runners.runner[0]

    // for (rawHorse of event.meeting.races.race[0].runners.runner) {

    //     console.log(rawHorse['@_runnername'])
    // }

    //console.log(event.RaceCard.Race[0])


    // var filename = "../test-files/ra.xml"
    // // var ra = JSON.parse(await helper.readFile(filename))
    // var ra = await helper.readFile(filename)
    // var jsContent = await helper.xmlToJs(ra)
    // await engine.processRegistrationAU(jsContent)

    // process.exit(1)
}

/* Local Testing */
const fs = require('fs');
var input = JSON.parse(fs.readFileSync('./test-inputs/one-race.json'));
localTesting(input)
