// THIS IS FROM index.js, genmeetingdata
// case "TISDB":
//     // TIS files were a fun special case as they cover all countries, and have a few of thier own rules
//     if (event.meetingStage == "Results") {
//         // processResults = await engine.processResults_TISDB(rawData, meetingId, category)
//         processResults = await engine.processResults_TISDB(rawData, meetingId, category)
//         return "ResultsProcessed"
//     }else if (category == 'trial') {
//         normalizedData = await normalize.normalizeMeetingData_TISDB(rawData, meetingId, 'Acceptances', event.country, 'trial')
    
//     }else{
//         normalizedData = await normalize.normalizeMeetingData_TISDB(rawData, meetingId, meetingStage, event.country, category)
//     }
//     break


// THIS IS FROM index.js, genracedata 
// case "TISDB":
//     normalizedRaceData = await normalize.normalizeRaceData_TISDB(rawData, event.meetingStage, event.meetingId, event.raceId, event.fileraceno, event.category, event.silksReady)
//     break

// THIS IS FROM helper.js, checkFileTypeCountry
// if (jsData['meeting'] && jsData['meeting']['product']) {
//     // console.log('its a TISDB file!')
//     if (jsData['meeting']['product']['@_directory'].includes('FORM_XML') || jsData['meeting']['product']['@_directory'].includes('RESULTS_XML') || jsData['meeting']['product']['@_directory'].includes('RACE_BY_RACE')){
//         result.meetingStage = meetingStageSelect(jsData['meeting']['stage'])
//         result.meetingDate = moment(jsData['meeting']['date'], 'DD/MM/YYYY').format('YYYY-MM-DD')
//         result.venueAbbr = jsData['meeting']['track']['@_track_4char_abbrev']
//         result.trackName = jsData['meeting']['track']['@_name']
//         result.country = 'TISDB'
        
//         // console.log(result)
//         if ((jsData['meeting']['races'] && jsData['meeting']['races']['race'] && jsData['meeting']['races']['race'][0] && jsData['meeting']['races']['race'][0]['@_name'] == 'Barrier Trial') || 
//         (jsData['meeting']['events'] && jsData['meeting']['events']['event'] && (jsData['meeting']['events']['event'][0] && jsData['meeting']['events']['event'][0]['@_name'] == 'Barrier Trial' || jsData['meeting']['events']['event']['@_name'] == 'Barrier Trial'))){
//             result.category = 'trial'
//         } else {
//             result.category = 'nonTrial'
//         }
//     }
    
// }

// normalize.js
// const normalizeMeetingData_TISDB = async (
//     rawData,
//     meetingId,
//     meetingStage,
//     country,
//     category
//   ) => {
//     rawData = rawData.meeting;
//     var errors = [];
//     try {
//       var trial_file = category == "trial" ? "_T" : "";
//       const product_directory =
//         "XML_" + meetingStage.substring(0, 1) + trial_file; // Hard Coded
//       const product_track = rawData.track["@_id"];
//       const meetingDateUTC = helper.convertToUTC_TISDB(rawData.date);
//       var track_data = await centaur.tracks
//         .findOne({ TRK_TRACK_DB_ID: product_track })
//         .lean();
//       if (!track_data) {
//         console.log(
//           `((ERROR)): Meeting Level - Track Not Found - ${product_track}. aborting.`
//         );
//         // console.log('Stopping Execution - Track Not Found')
//         return { eventStatus: "notProcessing" };
//         // process.exit(1) //Stopping execution as Track Information is missing
//       } else {
//         var club_name = "",
//           club_id = "",
//           club_code = "",
//           club_state = "";
//         club_code = rawData.track["@_club"];
//         // changing vic amateur TC to Melbourne RC
//         if (club_code == "10054") {
//           club_code = "10100";
//         }
  
//         const club_data = await centaur.clubs
//           .findOne({ CLB_CLUB_NAME: club_code })
//           .lean();
//         if (!club_data) {
//           // console.log('((ALERT)): Meeting Level - Club Not Found')
//           errors.push(`Meeting Level - Club Not Found - ${club_code}`);
//         } else {
//           club_name = club_data.CLB_CLUB_NAME;
//           club_id = club_data.CLB_CLUB_ID;
//           club_state = club_data.CLB_STATE;
//         }
  
//         const product_date = helper.getDayMonth(meetingDateUTC);
//         const product_file =
//           track_data.TRK_TRACK_3CHAR_ABBREV + product_date + trial_file + ".XML";
  
//         var expected_condition =
//           rawData.track["@_expected_condition"] &&
//           rawData.track["@_expected_condition"] != "Y"
//             ? rawData.track["@_expected_condition"]
//             : rawData.track["@_track_surface"] == "Y"
//             ? "Y0"
//             : "G0";
//         var record_meeting = {
//           meeting: {
//             product: {
//               "@_directory": product_directory,
//               "@_track": track_data.TRK_TRACK_3CHAR_ABBREV,
//               "@_date": product_date,
//               "@_file": product_file,
//             },
//             stage: meetingStage,
//             date: rawData.date,
//             rail_position: rawData.rail_position,
//             tab_indicator: category == "trial" ? "Trial" : rawData.tab_indicator,
//             dual_track: rawData.dual_track,
//             track: {
//               "@_name": track_data.TRK_TRACK_NAME,
//               "@_id": track_data.TRK_TRACK_DB_ID,
//               "@_expected_condition": expected_condition,
//               "@_club": club_name,
//               "@_track_surface": track_data.TRK_TRACK_SURFACE_TYPE,
//               "@_location": ["M", "C", "P"].includes(rawData.track["@_location"])
//                 ? rawData.track["@_location"]
//                 : track_data.TRK_LOCATION,
//               "@_country": track_data.TRK_COUNTRY_OF_TRACK,
//               "@_state": rawData.track["@_state"],
//               // "@_state": 'TISDB',
//               "@_track_3char_abbrev": track_data.TRK_TRACK_3CHAR_ABBREV,
//               "@_track_4char_abbrev": track_data.TRK_RSB_TRACK_ABBREV,
//               "@_track_6char_abbrev": track_data.TRK_TRACK_6CHAR_ABBREV,
//               "@_night_meeting": rawData.track["@_night_meeting"] ? "N" : "",
//             },
//             errors: errors,
//             races: {
//               race: [{}],
//             },
//           },
//         };
//       }
//       /*---End Creating Meeting Level data---*/
//       const meetingExists = await centaur.processed_meetings
//         .findOne({ _id: meetingId })
//         .lean();
//       var existingRaceArray = [];
//       var silksReady = false;
//       if (meetingExists && meetingExists.meetingStage != "DELETED") {
//         // turn this back on, maybe?
//         // var checkForColors = checkForHorseSilks(meetingDateUTC,track_data.TRK_TRACK_NAME,meetingId)
//         if (meetingExists.inputMeetingData.silksReady) {
//           silksReady = true;
//         }
//         existingRaceArray = meetingExists.processedMeetingData.meeting.races.race;
//         if (category != "trial") {
//           if (rawData.races.race.length >= existingRaceArray.length) {
//             existingRaceArray = [];
//           }
//         } else {
//           if (rawData.events.event.length >= existingRaceArray.length) {
//             existingRaceArray = [];
//           }
//         }
//       }
//       record_meeting.meeting.silksReady = silksReady;
//       var races = [];
//       if (category != "trial") {
//         if (
//           rawData.races &&
//           rawData.races.race &&
//           typeof rawData.races.race[Symbol.iterator] === "function"
//         ) {
//           races = rawData.races.race;
//         } else if (rawData.races && rawData.races.race) {
//           races.push(rawData.races.race);
//         }
//       } else {
//         if (
//           rawData.events &&
//           rawData.events.event &&
//           typeof rawData.events.event[Symbol.iterator] === "function"
//         ) {
//           races = rawData.events.event;
//         } else if (rawData.events && rawData.events.event) {
//           races.push(rawData.events.event);
//         }
//       }
//       // if (races.length < existingRaceArray.length){
//       //     record_meeting.meeting.errors.push(`New file has fewer races, could be an early race accepting, or races being removed from the meet.`)
//       // }
  
//       // console.log('existingRaceArray',existingRaceArray)
//       // Generate Empty Races Array , it contains many races---*/
//       var raceArray = [];
//       var emptyRaceArray = [];
//       var tempRace = {};
//       if (existingRaceArray.length > 0) {
//         for (var j = 0; j < existingRaceArray.length; j++) {
//           var notInFile = true;
//           for (var i = 0; i < races.length; i++) {
//             var eventId = races[i]["@_id"];
  
//             if (existingRaceArray[j]["@_id"] == eventId) {
//               tempRace = {
//                 "@_number": j + 1,
//                 "@_fileraceno": i + 1,
//               };
//               raceArray.push(tempRace);
//               notInFile = false;
//             }
//           }
//           if (notInFile) {
//             raceArray.push(existingRaceArray[j]);
//             record_meeting.meeting.errors.push(
//               `Race not included in file - delete meeting and reload latest to distribute - ${existingRaceArray[j]["@_id"]}`
//             );
//           }
//           tempRace = {
//             "@_number": existingRaceArray[j]["@_number"],
//           };
//           emptyRaceArray.push(tempRace);
//         }
//       } else {
//         var adjust = 0;
//         for (var i = 0; i < races.length; i++) {
//           var rawRace = races[i];
//           if (races.length < rawRace["@_number"] && rawRace["@_number"] < 19) {
//             tempRace = {
//               "@_number": i + 1,
//             };
//           } else if (races.length < rawRace["@_number"]) {
//             tempRace = {
//               "@_number": i + 1,
//             };
//             adjust = adjust + 1;
//           } else {
//             tempRace = {
//               "@_number": parseInt(rawRace["@_number"]) + adjust,
//             };
//           }
  
//           emptyRaceArray.push(tempRace);
//         }
//       } /*---End Creating Races data---*/
  
//       // console.log('emptyRaceArray',emptyRaceArray)
  
//       raceArray = raceArray.length > 0 ? raceArray : emptyRaceArray;
//       record_meeting.meeting.races.race = raceArray;
  
//       // console.log('raceArray',raceArray)
//       //Generate RaceArray for InputMeetingData Section
//       var inputMeetingData = {
//         races: {
//           race: emptyRaceArray,
//         },
//         silksReady: silksReady,
//       };
  
//       //return record_meeting
  
//       /*---Create Array to pass in MAP STEP FUNCTION---*/
//       /* Add meetingId in Array */
//       raceDataArray = { races: [] };
//       raceArray.forEach((element) => {
//         temp = {
//           raceId: element["@_number"],
//           fileraceno: element["@_fileraceno"] ?? element["@_number"],
//           eventId: element["@_id"] ?? "",
//           meetingId: meetingId,
//           stage: "genRaceData",
//           meetingStage: meetingStage,
//           country: country,
//           category: category,
//         };
//         raceDataArray.races.push(temp);
//       });
  
//       var normalizedData = {
//         record_meeting: record_meeting,
//         meetingDate: meetingDateUTC,
//         meetingStage: meetingStage,
//         meetingState: helper.getStateName(track_data.TRK_STATE_OF_TRACK),
//         raceDataArray: raceDataArray,
//         inputMeetingData: inputMeetingData,
//       };
//       // console.log(raceDataArray)
//       return normalizedData;
//     } catch (error) {
//       // var sendMail = await helper.mailAlert('TIS Meeting Data error',`TIS Race Meeting couldnt generate ${meetingId}. ${error}`,'error')
//       // console.log(sendMail)
//       console.log(
//         "((ERROR)): Normalize Error in creating meeting level data: " + error
//       );
//     }
//   };
  
//   const normalizeRaceData_TISDB = async (
//     rawData,
//     meetingStage,
//     meetingId,
//     raceId,
//     fileraceno,
//     category,
//     silksReady = false
//   ) => {
//     // console.log("Creating TISDB Race Data - MeetingId:" + meetingId + ", RaceId:" + raceId + ", fileraceno:" + fileraceno)
//     rawData = rawData.meeting;
//     try {
//       var errors = [];
//       var error_log = [];
//       var acceptances = "";
//       // var results = ''
//       // var resultsNumericTrackRating = ''
//       var eventId = "";
  
//       var races = [];
//       if (category != "trial") {
//         if (
//           rawData.races &&
//           rawData.races.race &&
//           typeof rawData.races.race[Symbol.iterator] === "function"
//         ) {
//           races = rawData.races.race;
//         } else if (rawData.races && rawData.races.race) {
//           races.push(rawData.races.race);
//         }
//       } else {
//         if (
//           rawData.events &&
//           rawData.events.event &&
//           typeof rawData.events.event[Symbol.iterator] === "function"
//         ) {
//           races = rawData.events.event;
//         } else if (rawData.events && rawData.events.event) {
//           races.push(rawData.events.event);
//         }
//       }
//       // console.log(races)
//       var rawRace = races[fileraceno - 1]; // -1 added due to db array starting from 0
  
//       //Fetch Meeting Date from Processed Meeting Table
//       var meeting_record = await centaur.temp_meetings
//         .findById(meetingId)
//         .select("meetingDate")
//         .select("inputMeetingData")
//         .lean();
//       var meetingDate = meeting_record.meetingDate;
  
//       //Fetch Track data from Track table - Error is handled at meeting level
//       const product_track = rawRace.track["@_id"];
//       var track_data = await centaur.tracks
//         .findOne({ TRK_TRACK_DB_ID: product_track })
//         .lean();
  
//       var nomsNumber = rawRace["@_nomination_number"];
//       if (!nomsNumber && ["Nominations", "Weights"].includes(meetingStage)) {
//         nomsNumber = rawRace["@_number"];
//       }
//       var theTime = "0000";
  
//       //Acceptance data - to be updated at Acceptance stage
//       if (meetingStage == "Acceptances" || meetingStage == "FinalFields") {
//         if (rawRace.start_time) {
//           var raceHours = await rawRace.start_time.split(/(:|am|pm)/);
//           if (raceHours[3] == "pm") {
//             raceHours[0] = (parseInt(raceHours[0]) + 12).toString();
//             if (raceHours[0] == "24") {
//               raceHours[0] = "12";
//             }
//           }
//           if (raceHours[0].length == 1) {
//             theTime = "0" + raceHours[0] + raceHours[2];
//           } else {
//             theTime = raceHours[0] + raceHours[2];
//           }
//         }
//         acceptances = {
//           start_time: theTime,
//           temporary_weight_change: rawRace.temporary_weight_change,
//         };
//       }
  
//       var eventId = "";
//       var raceName = "";
//       var shortRaceName = "";
//       var trialStatus = 0;
//       if (rawRace["@_id"]) {
//         eventId = rawRace["@_id"];
//       } else {
//         if (category == "trial") {
//           trialStatus = 1;
//           var eventId = helper.generateUniqueRaceId(
//             meetingDate,
//             track_data.TRK_TRACK_DB_ID,
//             rawRace["@_number"],
//             trialStatus
//           );
//         } else {
//           var eventId = helper.generateUniqueRaceId(
//             meetingDate,
//             track_data.TRK_TRACK_DB_ID,
//             nomsNumber,
//             trialStatus
//           );
//         }
//       }
//       // console.log('eventid:',eventId)
//       var raceInput = {};
//       // console.log(meeting_record.inputMeetingData.races)
//       if (
//         meeting_record.inputMeetingData.races &&
//         meeting_record.inputMeetingData.races.race
//       ) {
//         for (var theRace of meeting_record.inputMeetingData.races.race) {
//           if (theRace["@_id"] == eventId) {
//             raceInput = theRace;
//             if (raceInput.error_log) {
//               error_log = raceInput.error_log;
//             } else if (raceInput.errors) {
//               error_log = raceInput.errors;
//             }
//             errors = raceInput.errors;
//             if (
//               raceInput.start_time &&
//               raceInput.start_time != acceptances.start_time
//             ) {
//               // errors = await helper.logError(error_log,errors,`start time has changed from ${raceInput.start_time} to ${acceptances.start_time}`)
//               // error_log = await helper.logError([],error_log,`start time has changed from ${raceInput.start_time} to ${acceptances.start_time}`)
//             }
//             if (
//               raceInput["@_ra_name"] &&
//               raceInput["@_ra_name"] != rawRace["@_name"]
//             ) {
//               // errors = await helper.logError(error_log,errors,`RA race name has changed from ${raceInput['@_ra_name']} to ${rawRace['@_name']}`)
//               // error_log = await helper.logError([],error_log,`RA race name has changed from ${raceInput['@_ra_name']} to ${rawRace['@_name']}`)
//             }
//           }
//         }
//       }
//       var expected_condition =
//         rawData.track["@_expected_condition"] ??
//         (rawData.track["@_condition"]
//           ? rawData.track["@_condition"] +
//             (rawData.track["@_grading"] ? rawData.track["@_grading"] : 0)
//           : "");
//       if (!expected_condition) {
//         // errors = await helper.logError(error_log,errors,`Track condition is missing from race`)
//         // error_log = await helper.logError([],error_log,`Track condition is missing from race`)
//       }
//       var classes_array = {};
//       if (Array.isArray(rawRace.classes.class)) {
//         classes_array.class = rawRace.classes.class[0];
//         classes_array.class_id = await getClassByName(rawRace.classes.class[0]);
//         if (rawRace.classes.class[1]) {
//           classes_array.second_class = rawRace.classes.class[1];
//           classes_array.second_class_id = await getClassByName(
//             rawRace.classes.class[1]
//           );
//         }
//         if (rawRace.classes.class[2]) {
//           classes_array.third_class = rawRace.classes.class[2];
//           classes_array.third_class_id = await getClassByName(
//             rawRace.classes.class[2]
//           );
//         }
//         if (rawRace.classes.class[3]) {
//           classes_array.fourth_class = rawRace.classes.class[2];
//           classes_array.fourth_class_id = await getClassByName(
//             rawRace.classes.class[3]
//           );
//         }
//         if (rawRace.classes.class[4]) {
//           classes_array.fifth_class = rawRace.classes.class[4];
//           classes_array.fifth_class_id = await getClassByName(
//             rawRace.classes.class[4]
//           );
//         }
//       } else {
//         classes_array.class = rawRace.classes.class;
//         classes_array.class_id = await getClassByName(rawRace.classes.class);
//       }
  
//       if (rawRace.classes.class && rawRace.classes.class == "Barrier Trial") {
//         raceName = "Barrier Trial";
//         shortRaceName = "Barrier Trl";
//       } else if (rawRace["@_short_name"]) {
//         shortRaceName = rawRace["@_short_name"];
//       } else if (rawRace.classes.class) {
//         raceName = helper.cleanupTextForXml(rawRace["@_name"]);
//         if (helper.getGroupType(rawRace.GroupType)) {
//           shortRaceName =
//             raceInput.raceName ??
//             rawRace["@_name"].replace(/\&quot;/g, "'").replace(/"/g, "'");
//         } else {
//           shortRaceName = helper.getShortName(
//             rawRace.restrictions["@_age"],
//             helper.getReverseSexShortname(rawRace.restrictions["@_sex"]),
//             classes_array.class,
//             rawRace.weight_type
//           );
//         }
//       }
  
//       const track_records = rawRace.records;
//       if (
//         track_records == "" &&
//         !["HK", "NZ", "SGP"].includes(track_data.TRK_COUNTRY_OF_TRACK)
//       ) {
//         // errors = await helper.logError(error_log,errors,'Error generation Track Records')
//         // error_log = await helper.logError([],error_log,'Error generation Track Records')
//       }
//       var race = {
//         "@_number": rawRace["@_number"],
//         "@_name":
//           raceInput.raceName && raceInput.raceName != ""
//             ? raceInput.raceName
//             : rawRace["@_name"].replace(/\&quot;/g, "'").replace(/"/g, "'"),
//         "@_ra_name": rawRace["@_name"]
//           .replace(/\&quot;/g, "'")
//           .replace(/"/g, "'"),
//         "@_shortname": raceInput.raceNameShort ?? shortRaceName,
//         "@_id": eventId,
//         "@_nominations_number": nomsNumber ?? "",
//         race_stage: meetingStage,
//         track: {
//           "@_name": track_data.TRK_TRACK_NAME,
//           "@_id": track_data.TRK_TRACK_DB_ID,
//           "@_track_surface": track_data.TRK_TRACK_SURFACE_TYPE,
//           "@_country": track_data.TRK_COUNTRY_OF_TRACK,
//           "@_track_3char_abbrev": track_data.TRK_TRACK_3CHAR_ABBREV,
//           "@_expected_condition": expected_condition ?? "",
//         },
//         distance: rawRace.distance,
//         restrictions: rawRace.restrictions,
//         weight_type: rawRace.weight_type,
//         min_hcp_weight: rawRace.min_hcp_weight ?? "",
//         track_type: rawRace.track_type ?? "",
//         group: rawRace.group ?? "",
//         classes: classes_array,
//         race_type: rawRace.race_type,
//         prizes: rawRace.prizes ?? "",
//         records: track_records ?? "",
//         errors: errors,
//         horses: {
//           horse: [{}],
//         },
//       };
//       if (
//         (rawRace.RaceType || "").toLowerCase().includes("hurdle") ||
//         (rawRace.RaceType || "").toLowerCase().includes("steeple")
//       ) {
//         errors = await helper.logError(
//           error_log,
//           errors,
//           "Race type contains 'hurdle' or 'steeple'"
//         );
//         error_log = await helper.logError(
//           [],
//           error_log,
//           "Race type contains 'hurdle' or 'steeple'"
//         );
//       }
//       if (rawRace.division) {
//         race.division = rawRace.division;
//       }
//       // console.log(race)
  
//       //Generate Race Data for InputMeetingData Section
//       var race_inputMeetingData = {
//         "@_name": raceInput.raceName ?? helper.capFstLtr(raceName),
//         "@_ra_name": raceName,
//         "@_number": rawRace["@_number"],
//         "@_nominations_number": nomsNumber,
//         "@_id": eventId,
//         raceName: raceInput.raceName ?? "",
//         raceNameShort: raceInput.raceNameShort ?? shortRaceName,
//         raRaceNameShort: shortRaceName,
//         start_time: acceptances.start_time,
//         horses: {
//           horse: [{}],
//         },
//         errors: errors,
//         error_log: error_log,
//       };
  
//       //Updating data for Acceptance Stage
//       if (meetingStage == "Acceptances" || meetingStage == "FinalFields") {
//         race = Object.assign(race, acceptances);
//       }
  
//       var mainArray = [];
//       // Process horseArray
//       var count = 0;
//       var sequenceCount = 10;
//       for (rawHorse of rawRace.horses.horse) {
//         mainArray.push(
//           genHorseLevelData_TISDB(
//             await meetingStage,
//             meetingDate,
//             rawHorse,
//             sequenceCount,
//             rawRace.distance["@_metres"],
//             raceInput,
//             rawRace["@_number"],
//             track_data,
//             silksReady,
//             category,
//             rawRace.group
//               ? "group " + rawRace.group.toString()
//               : classes_array.class_id
//           )
//         );
//         sequenceCount = sequenceCount + 10;
//         count++;
//         //break
//       }
//       var horseCompleted = await Promise.all(mainArray);
//       var horseArray = [];
//       var inputMeetingDataHorseArray = [];
  
//       /* Update Meeting with Race Data */
//       var updatedErrors = [];
//       for (error of race.errors) {
//         if (!error.includes("Horses with alerts:")) {
//           updatedErrors.push(error);
//         }
//       }
//       race.errors = updatedErrors;
//       if (horseCompleted) {
//         var horseErrorCount = 0;
//         var silksArray = [];
//         for (record of horseCompleted) {
//           if (
//             category != "trial" &&
//             !record.horse_processedMeetingData.scratched &&
//             (meetingStage == "Acceptances" || meetingStage == "FinalFields")
//           ) {
//             if (silksArray.includes(record.horse_processedMeetingData.colours)) {
//               for (horse of horseCompleted) {
//                 if (
//                   record.horse_processedMeetingData.colours ===
//                   horse.horse_processedMeetingData.colours
//                 ) {
//                   record.horse_processedMeetingData.errors =
//                     await helper.logError(
//                       record.horse_inputMeetingData.error_log,
//                       record.horse_processedMeetingData.errors,
//                       `Duplicate silks detected ${record.horse_processedMeetingData.colours}`
//                     );
//                   record.horse_inputMeetingData.errors = await helper.logError(
//                     record.horse_inputMeetingData.error_log,
//                     record.horse_inputMeetingData.errors,
//                     `Duplicate silks detected ${record.horse_processedMeetingData.colours}`
//                   );
//                   record.horse_inputMeetingData.error_log = await helper.logError(
//                     [],
//                     record.horse_inputMeetingData.error_log,
//                     `Duplicate silks detected ${record.horse_processedMeetingData.colours}`
//                   );
//                   horse.horse_processedMeetingData.errors = await helper.logError(
//                     horse.horse_inputMeetingData.error_log,
//                     horse.horse_processedMeetingData.errors,
//                     `Duplicate silks detected ${horse.horse_processedMeetingData.colours}`
//                   );
//                   horse.horse_inputMeetingData.errors = await helper.logError(
//                     horse.horse_inputMeetingData.error_log,
//                     horse.horse_inputMeetingData.errors,
//                     `Duplicate silks detected ${horse.horse_processedMeetingData.colours}`
//                   );
//                   horse.horse_inputMeetingData.error_log = await helper.logError(
//                     [],
//                     horse.horse_inputMeetingData.error_log,
//                     `Duplicate silks detected ${horse.horse_processedMeetingData.colours}`
//                   );
//                 }
//               }
//             }
//             silksArray.push(record.horse_processedMeetingData.colours);
//           }
//           try {
//             if (record.horse_processedMeetingData.errors.length > 0) {
//               horseErrorCount++;
//             }
//           } catch (err) {
//             console.log(
//               "((ERROR)): error adding errors to horse" + record,
//               meetingId
//             );
//           }
  
//           try {
//             horseArray.push(record.horse_processedMeetingData);
//           } catch (err) {
//             console.log(
//               "((ERROR)): error updating horse" + horseCompleted,
//               meetingId
//             );
//           }
//           inputMeetingDataHorseArray.push(record.horse_inputMeetingData);
//           console.log(
//             `Sucessfully processed horse ${record.horse_processedMeetingData["tab_number"]} ${record.horse_processedMeetingData["@_id"]} ${record.horse_processedMeetingData["@_name"]}`
//           );
//         }
//         if (horseErrorCount) {
//           race.errors = await helper.logError(
//             [],
//             race.errors,
//             `Horses with alerts: ${horseErrorCount}`
//           );
//         }
//         race.horses.horse = horseArray;
//         race_inputMeetingData.horses.horse = inputMeetingDataHorseArray;
//         var normalizedRaceData = {
//           race: race,
//           race_inputMeetingData: race_inputMeetingData,
//         };
//         return normalizedRaceData;
//       }
//       //return race
//       return "Race Data Updated";
//     } catch (error) {
//       // var sendMail = await helper.mailAlert('TIS Race Data error',`TIS Race Data couldnt generate ${meetingId}. ${error}`,'error')
//       // console.log(sendMail)
//       console.log(`((ERROR)): Race Generation Error ${meetingId}: ${error}`);
//     }
//   };
  
//   const genHorseLevelData_TISDB = async (
//     meetingStage,
//     meetingDate,
//     rawHorse,
//     sequenceCount,
//     distance,
//     raceInput,
//     raceNo,
//     meetingTrack,
//     silksReady,
//     category,
//     raceClass
//   ) => {
//     try {
//       const start = Date.now();
//       var errors = [],
//         error_log = [];
//       var name = "",
//         country = "",
//         age = "",
//         colour = "",
//         sex = "",
//         horse_id = "",
//         foaling_date = "";
//       var sire_name = "",
//         sire_country = "",
//         sire_id = "",
//         dam_name = "",
//         dam_country = "",
//         dam_id = "",
//         sire_dam_name = "",
//         sire_dam_country = "",
//         sire_dam_id;
//       var trainer_stats = "",
//         trainer_name = "",
//         trainer_firstname = "",
//         trainer_surname = "",
//         trainer_id = "",
//         training_location = "";
//       var owners = "",
//         colours = "",
//         prizmoney_won = "",
//         startsData = "",
//         last4s = "",
//         last10s = "",
//         last15s = "",
//         last20s = "";
//       var current_blinker_ind = "N",
//         win_percentage = "",
//         place_percentage = "",
//         statistics = "",
//         win_distances = "",
//         pace = [0, ""];
//       var weights = "",
//         weights_allocated = "",
//         weights_penalty = "",
//         weights_total = ""; //Weights
//       var acceptances = "",
//         tab_number = "",
//         jockey_race_entry = "",
//         barrier_no = "",
//         gear = "",
//         isEmergencyRunner = ""; //Acceptances
//       var prev_name = "",
//         running_gear = { gear_item: [] },
//         class_quality = "F";
//       var results_forms_table = "";
  
//       // Fetch Horse data from horse_names table
//       const horseData = await centaur.horses
//         .findOne()
//         .where("HRN_HORSE_ID")
//         .equals(parseInt(rawHorse["@_id"]))
//         .where("HRN_HORSE_NAME")
//         .equals(helper.upperCase(rawHorse["@_name"]))
//         .lean();
  
//       if (horseData) {
//         const horseId = horseData.HRN_HORSE_ID;
//         const horseSireId = horseData.HOR_SIRE_ID;
//         const horseDamId = horseData.HOR_DAM_ID;
  
//         name =
//           horseData.HRN_DISPLAY_NAME ??
//           helper.capFstLtr(horseData.HRN_HORSE_NAME);
//         country = horseData.HOR_COUNTRY_OF_ORIGIN;
//         age = helper.getHorseAge(horseData.HOR_FOALING_DATE);
//         colour = rawHorse["@_colour"];
//         sex = helper.getHorseSex(horseData.HOR_SEX, age);
  
//         horse_id = horseData.HRN_HORSE_ID;
//         foaling_date = helper.convertDate(horseData.HOR_FOALING_DATE);
  
//         // add input data into main list
//         var horseInput = {};
//         if (raceInput.horses && raceInput.horses.horse) {
//           for (const runner in raceInput.horses.horse) {
//             if (raceInput.horses.horse[runner]["@_id"] === horse_id) {
//               horseInput = raceInput.horses.horse[runner];
//               if (horseInput.errors) {
//                 errors = horseInput.errors;
//               }
//               if (horseInput.error_log) {
//                 error_log = horseInput.error_log;
//               }
//             }
//           }
//         }
//         if (!country) {
//           errors = await helper.logError(
//             error_log,
//             errors,
//             `${rawHorse["@_name"]} ${rawHorse["@_id"]} has no country`
//           );
//           error_log = await helper.logError(
//             [],
//             error_log,
//             `${rawHorse["@_name"]} ${rawHorse["@_id"]} has no country`
//           );
//         }
//         if (sex != rawHorse["@_sex"]) {
//           errors = await helper.logError(
//             error_log,
//             errors,
//             `${rawHorse["@_name"]} ${rawHorse["@_id"]} sex mismatch TIS: ${rawHorse["@_sex"]}, Centaur: ${sex}`
//           );
//           error_log = await helper.logError(
//             [],
//             error_log,
//             `${rawHorse["@_name"]} ${rawHorse["@_id"]} sex mismatch TIS: ${rawHorse["@_sex"]}, Centaur: ${sex}`
//           );
//         }
//         if (!horseSireId || horseSireId == 0) {
//           errors = await helper.logError(
//             error_log,
//             errors,
//             `${rawHorse["@_name"]} ${rawHorse["@_id"]} Sire Not Found`
//           );
//           error_log = await helper.logError(
//             [],
//             error_log,
//             `${rawHorse["@_name"]} ${rawHorse["@_id"]} Sire Not Found`
//           );
//         }
  
//         //Get SIRE Data from horse_name
//         const sireData = await centaur.horses
//           .findOne({ HRN_HORSE_ID: horseSireId })
//           .select("HRN_HORSE_NAME HOR_COUNTRY_OF_ORIGIN HRN_HORSE_ID")
//           .lean();
//         if (sireData) {
//           sire_name =
//             sireData.HRN_DISPLAY_NAME ??
//             helper.capFstLtr(sireData.HRN_HORSE_NAME);
//           sire_country = sireData.HOR_COUNTRY_OF_ORIGIN;
//           sire_id = sireData.HRN_HORSE_ID;
//         } else {
//           errors = await helper.logError(
//             error_log,
//             errors,
//             `${rawHorse["@_name"]} ${rawHorse["@_id"]} Sire Not Found`
//           );
//           error_log = await helper.logError(
//             [],
//             error_log,
//             `${rawHorse["@_name"]} ${rawHorse["@_id"]} Sire Not Found`
//           );
//         }
  
//         //Get DAM Data from horse_name
//         const damData = await centaur.horses
//           .findOne({ HRN_HORSE_ID: horseDamId })
//           .select("HRN_HORSE_NAME HOR_COUNTRY_OF_ORIGIN HRN_HORSE_ID HOR_SIRE_ID")
//           .lean();
//         if (damData) {
//           dam_name =
//             damData.HRN_DISPLAY_NAME ?? helper.capFstLtr(damData.HRN_HORSE_NAME);
//           dam_country = damData.HOR_COUNTRY_OF_ORIGIN;
//           dam_id = damData.HRN_HORSE_ID;
//           //Get SIRE of DAM
//           const sireOfDamData = await centaur.horses
//             .findOne({ HRN_HORSE_ID: damData.HOR_SIRE_ID })
//             .select("HRN_HORSE_NAME HOR_COUNTRY_OF_ORIGIN HRN_HORSE_ID")
//             .lean();
//           if (sireOfDamData) {
//             sire_dam_name =
//               sireOfDamData.HRN_DISPLAY_NAME ??
//               helper.capFstLtr(sireOfDamData.HRN_HORSE_NAME);
//             sire_dam_country = sireOfDamData.HOR_COUNTRY_OF_ORIGIN;
//             sire_dam_id = sireOfDamData.HRN_HORSE_ID;
//           } else {
//             errors = await helper.logError(
//               error_log,
//               errors,
//               `${rawHorse["@_name"]} ${rawHorse["@_id"]} Sire of Dam Not Found`
//             );
//             error_log = await helper.logError(
//               [],
//               error_log,
//               `${rawHorse["@_name"]} ${rawHorse["@_id"]} Sire of Dam Not Found`
//             );
//           }
//         } else {
//           errors = await helper.logError(
//             error_log,
//             errors,
//             `${rawHorse["@_name"]} ${rawHorse["@_id"]} Dam Not Found`
//           );
//           error_log = await helper.logError(
//             [],
//             error_log,
//             `${rawHorse["@_name"]} ${rawHorse["@_id"]} Dam Not Found`
//           );
//         }
  
//         const trainer_data = await centaur.trainers
//           .findOne({ TRN_TRAINER_ID: rawHorse.trainer["@_id"] })
//           .lean();
//         if (trainer_data) {
//           if (
//             rawHorse.trainer["@_name"] != trainer_data.TRN_TRAINER_DISPLAYNAME
//           ) {
//             errors = await helper.logError(
//               error_log,
//               errors,
//               `Trainer might not match. RA has the name as (${rawHorse.trainer["@_name"]}), could be in our database as (${trainer_data.TRN_TRAINER_DISPLAYNAME}) or (${trainer_data.TRN_PARTNERSHIP_DISPLAYNAME}) ID: ${rawHorse.trainer["@_id"]}`
//             );
//             error_log = await helper.logError(
//               [],
//               error_log,
//               `Trainer might not match. RA has the name as (${rawHorse.trainer["@_name"]}), could be in our database as (${trainer_data.TRN_TRAINER_DISPLAYNAME}) or (${trainer_data.TRN_PARTNERSHIP_DISPLAYNAME})ID: ${rawHorse.trainer["@_id"]}`
//             );
//           }
  
//           trainer_stats = await getTrainerStats(trainer_data.TRN_TRAINER_ID);
//           trainer_name = trainer_data.TRN_TRAINER_DISPLAYNAME;
//           trainer_firstname = trainer_data.TRN_TRAINER_FIRSTNAME;
//           trainer_surname = trainer_data.TRN_TRAINER_SURNAME;
//           trainer_id = trainer_data.TRN_TRAINER_ID;
//         } else {
//           errors = await helper.logError(
//             error_log,
//             errors,
//             `Trainer Not Found - Trainer Name: (${rawHorse.trainer["@_name"]}) ID: ${rawHorse.trainer["@_id"]}`
//           );
//           error_log = await helper.logError(
//             [],
//             error_log,
//             `Trainer Not Found - Trainer Name: (${rawHorse.trainer["@_name"]}) ID: ${rawHorse.trainer["@_id"]}`
//           );
//         }
  
//         training_location = helper.capFstLtr(rawHorse.training_location);
//         // THIS OWNERS NEETD TO COME FROM RA FILE
//         owners = helper.cleanupTextForXml(rawHorse.owners);
//         if (owners == "") {
//           // errors = await helper.logError(error_log,errors,`Owners missing for for Horse - ${horseId}`)
//           // error_log = await helper.logError([],error_log,`Owners missing for for Horse - ${horseId}`)
//         }
  
//         prizmoney_won = rawHorse.prizemoney_won;
//         // if ((parseInt(prizmoney_won) < (parseInt(rawHorse.prizemoney_won) / 2) ) || (parseInt(prizmoney_won) > (parseInt(rawHorse.prizemoney_won) * 2) )){
//         //     errors = await helper.logError(error_log,errors,`Prizemoney discrepancy for ${name} ${horseId}. TISDB: ${rawHorse.prizemoney_won} vs centaur: ${prizmoney_won}`)
//         //     error_log = await helper.logError([],error_log,`Prizemoney discrepancy for ${name} ${horseId}. TISDB: ${rawHorse.prizemoney_won} vs centaur: ${prizmoney_won}`)
//         // }
//         if (rawHorse.current_blinker_ind) {
//           current_blinker_ind = rawHorse.current_blinker_ind;
//         } else if (horseData.HOR_CURRENT_BLINKER_IND != "") {
//           current_blinker_ind = horseData.HOR_CURRENT_BLINKER_IND;
//         }
  
//         if (horseData.HRN_PREV_NAMES && horseData.HRN_PREV_NAMES[0]) {
//           prev_name = horseData.HRN_PREV_NAMES.join(", ");
//         }
  
//         //Calculate Horse Starts - Fetch latest 20 record (sort desc by form date), put FRM_FINISH_POSTION in group of 4, if date diff is more than 90 days put x
//         const formData = await centaur.form.find({ horse_id: horseId }).lean();
//         var formItems = [];
//         if (formData[0] && formData[0].form) {
//           if (
//             rawHorse.forms &&
//             rawHorse.forms.form &&
//             formData[0].form.length != rawHorse.forms.form.length
//           ) {
//             if (
//               formData[0].form.length != 1 ||
//               (rawHorse.forms.form.length && rawHorse.forms.form.length > 1)
//             ) {
//               errors = await helper.logError(
//                 error_log,
//                 errors,
//                 `${name} ${horseId} - TISDB form has ${rawHorse.forms.form.length} items, where centaur form has ${formData[0].form.length}`
//               );
//               error_log = await helper.logError(
//                 [],
//                 error_log,
//                 `${name} ${horseId} - TISDB form has ${rawHorse.forms.form.length} items, where centaur form has ${formData[0].form.length}`
//               );
//               if (formData[0].form.length < rawHorse.forms.form.length) {
//                 try {
//                   var missingform = helper.isolateMissingRuns(
//                     rawHorse.forms.form,
//                     formData[0].form
//                   );
//                   if (
//                     missingform.length > 0 &&
//                     !(
//                       missingform.length == 1 &&
//                       missingform[0].track["@_country"] &&
//                       missingform[0].track["@_country"] == "AUS"
//                     )
//                   ) {
//                     var sendMail = await helper.mailAlert(
//                       `Form to clean up for ${name} ${horseId}`,
//                       `${name} "${horseId}":\n ${JSON.stringify(missingform)
//                         .replace(/,/g, ",\n")
//                         .replace(/{/g, "{\n")
//                         .replace(/}/g, "\n}")}`,
//                       "task"
//                     );
//                     console.log(sendMail);
//                   }
//                 } catch (err) {
//                   console.log(err);
//                 }
//               }
//             }
//           }
//           var missingGradings = helper.checkMissingGrading(formData[0].form);
//           if (rawHorse.forms && rawHorse.forms.form) {
//             var taskerrorlog = missingGradings
//               ? helper.compareOldFormTIS(missingGradings, rawHorse.forms.form)
//               : "";
//             if (taskerrorlog) {
//               var sendMail = await helper.mailAlert(
//                 `Form to clean up for ${name} ${horseId}`,
//                 `${name} ${horseId}:\n ${taskerrorlog}`,
//                 "task"
//               );
//               console.log(sendMail);
//             }
//           }
//           try {
//             formItems = helper.cleanseBarrierTrials(formData[0].form);
//             if (Array.isArray(formItems)) {
//               last4s = helper.processStarts(4, formItems, meetingDate).toString();
//               last10s = helper
//                 .processStarts(10, formItems, meetingDate)
//                 .toString();
//               last15s = helper
//                 .processStarts(15, formItems, meetingDate)
//                 .toString();
//               last20s = helper
//                 .processStarts(20, formItems, meetingDate)
//                 .toString();
//               var alltrack = [
//                 "0",
//                 "1",
//                 "2",
//                 "3",
//                 "4",
//                 "5",
//                 "6",
//                 "7",
//                 "8",
//                 "9",
//                 "10",
//                 "11",
//               ];
//               win_percentage = helper.calculatePercentage(
//                 [1, 101],
//                 alltrack,
//                 formItems
//               );
//               place_percentage = helper.calculatePercentage(
//                 [1, 2, 3, 101],
//                 alltrack,
//                 formItems
//               );
//               pace = helper.getPace(formItems, distance, last20s);
//               win_distances = await getWinDistance(formItems);
//               if (
//                 meetingTrack.TRK_COUNTRY_OF_TRACK &&
//                 formItems[0] &&
//                 formItems[0].track &&
//                 formItems[0].track["@_country"] &&
//                 formItems[0].track["@_country"] !=
//                   meetingTrack.TRK_COUNTRY_OF_TRACK
//               ) {
//                 // errors = await helper.logError(error_log,errors,`${name} ${horseId} - has its last registered form item outside ${meetingTrack.TRK_COUNTRY_OF_TRACK}`)
//                 // error_log = await helper.logError([],error_log,`${name} ${horseId} - has its last registered form item outside ${meetingTrack.TRK_COUNTRY_OF_TRACK}`)
//               }
//               if (
//                 formItems[0] &&
//                 formItems[0].trainer_id &&
//                 formItems[0].trainer_id != trainer_id
//               ) {
//                 var prev_trainer = formItems[0].trainer_id;
//               }
//               if (
//                 formItems[0] &&
//                 formItems[0].running_gear &&
//                 formItems[0].running_gear.gear_item
//               ) {
//                 running_gear = formItems[0].running_gear;
//               }
//               if (formItems[0] && formItems[0].group) {
//                 class_quality = upDownClass(
//                   raceClass,
//                   "group " + formItems[0].group.toString()
//                 );
//                 // console.log(class_quality)
//               } else if (
//                 formItems[0] &&
//                 formItems[0].classes &&
//                 formItems[0].classes.class_id
//               ) {
//                 class_quality = upDownClass(
//                   raceClass,
//                   formItems[0].classes.class_id
//                 );
//                 // console.log(class_quality)
//               } else {
//                 class_quality = "F";
//               }
//             } else {
//               // errors = await helper.logError(error_log,errors,`FormData for Horse - ${horseId} - appears to be empty `)
//               // error_log = await helper.logError([],error_log,`FormData for Horse - ${horseId} - appears to be empty `)
//             }
//           } catch (err) {
//             console.log(
//               `((ERROR)): starts generation error with ${horseId}: ${err}`
//             );
//           }
//         } else if (formData[0] && rawHorse.forms && rawHorse.forms.form) {
//           errors = await helper.logError(
//             error_log,
//             errors,
//             `${name} ${horseId} - TISDB form has items, where centaur form is empty or array is missing`
//           );
//           error_log = await helper.logError(
//             [],
//             error_log,
//             `${name} ${horseId} - TISDB form has items, where centaur form is empty or array is missing`
//           );
//         } else if (age > 3) {
//           // errors = await helper.logError(error_log,errors,`FormData not found for Horse - ${horseId} `)
//           // error_log = await helper.logError([],error_log,`FormData not found for Horse - ${horseId} `)
//         }
  
//         //Weight data - to be updated at weights stage
//         var isweight = false;
//         if (
//           meetingStage == "Weights" ||
//           meetingStage == "Acceptances" ||
//           meetingStage == "FinalFields"
//         ) {
//           if (rawHorse.weight && rawHorse.weight["@_allocated"]) {
//             weights_allocated = rawHorse.weight["@_allocated"];
//             isweight = weights_allocated;
//             weights = {
//               weight: {
//                 "@_allocated": weights_allocated,
//                 "@_total": rawHorse.weight["@_total"] ?? "",
//               },
//               weight_carried: rawHorse.weight["@_allocated"] ?? "",
//             };
  
//             if (rawHorse.weight && rawHorse.weight["@_performance_penalty"]) {
//               weights = {
//                 weight: {
//                   // "@_allocated": parseInt(weights_allocated) - parseInt(rawHorse.weight['@_performance_penalty']),
//                   "@_allocated": weights_allocated,
//                   "@_total": rawHorse.weight["@_total"] ?? "",
//                   "@_penalty": rawHorse.weight["@_performance_penalty"] ?? "",
//                 },
//                 weight_carried: rawHorse.weight["@_total"] ?? "",
//               };
//               isweight = weights_total;
//             }
//             if (rawHorse.weight["@_weight_raised"]) {
//               weights.weight["@_weight_raised"] =
//                 rawHorse.weight["@_weight_raised"];
//             }
//           } else if (rawHorse.weight_carried) {
//             weights_allocated = rawHorse.weight_carried;
//             if (rawHorse.weight_adj) {
//               weights_allocated =
//                 weights_allocated - parseInt(rawHorse.weight_adj);
//             }
//             isweight = weights_allocated;
//             weights = {
//               weight: {
//                 "@_allocated": weights_allocated,
//                 "@_total": weights_allocated,
//               },
//               weight_carried: rawHorse.weight_carried,
//             };
//           } else {
//             weights = {
//               weight: {
//                 "@_allocated": 0,
//                 "@_total": 0,
//               },
//               weight_carried: 0,
//             };
//           }
//         }
//         try {
//           var rating = 0;
  
//           var ratingWfa = 0;
//           var firstUp = true;
//           if (last4s != "" && last4s.slice(last4s.length - 1) != "x") {
//             firstUp = false;
//           }
//           if (horseData.HOR_RATINGS) {
//             rating = await helper.getMR(horseData.HOR_RATINGS, distance, firstUp); //CHANGE THIS WHEN YOU FIND OUT
//             ratingWfa = rating;
//           } else {
//             rating = 0;
//             ratingWfa = 0;
//           }
//           if (rating > 0 && isweight) {
//             ratingWfa = await helper.generateBasicRating(
//               rating,
//               age,
//               sex,
//               distance,
//               isweight,
//               meetingDate
//             );
//             rating = await helper.generateRating(
//               rating,
//               age,
//               sex,
//               distance,
//               isweight,
//               meetingDate
//             );
//           }
//           if (horseInput.rating) {
//             rating = horseInput.rating;
//           }
//         } catch (err) {
//           console.log(
//             `Couldnt generate rating for ${horseData.HRN_HORSE_NAME}`,
//             err
//           );
//           rating = 0;
//         }
  
//         colours = helper
//           .capFstLtr(rawHorse.colours)
//           .replace(/"/g, "'")
//           .replace(/’/g, "'")
//           .replace(/\&quot;/g, "'"); // Need to work, take from Racing Aus
  
//         //Acceptance data - to be updated at Acceptance stage
//         if (meetingStage == "Acceptances" || meetingStage == "FinalFields") {
//           acceptances = {
//             tab_number: rawHorse.tab_no ?? rawHorse.barrier,
//             barrier: rawHorse.barrier,
//           };
//           // var checkForSilks = await helper.checkForHorseSilks(meetingDate,meetingTrack,raceNo,rawHorse.TabNumber)
//           if (
//             silksReady ||
//             (rawHorse.horse_colours_image && rawHorse.horse_colours_image != "")
//           ) {
//             var meetingDateFormat = moment(meetingDate).format("YYYY_MM_DD");
//             var path =
//               meetingDateFormat +
//               "/" +
//               meetingTrack.TRK_TRACK_NAME.replace(/ /g, "_").toLowerCase() +
//               "/" +
//               raceNo +
//               "/" +
//               rawHorse.tab_no;
//             acceptances.horse_colours_image =
//               "https://silks.medialityracing.com.au/images/jpg/" +
//               path +
//               "_front.jpg";
//             acceptances.horse_colours_image_png =
//               "https://silks.medialityracing.com.au/images/png/" +
//               path +
//               "_front.png";
//             acceptances.horse_colours_image_svg =
//               "https://silks.medialityracing.com.au/images/svg/" +
//               path +
//               "_front.svg";
//           }
//           if (rawHorse.emergency_indicator == "E") {
//             emergency = {
//               emergency_indicator: "E",
//             };
//             acceptances = Object.assign(acceptances, emergency);
//           }
  
//           if (rawHorse.scratched_indicator == "S") {
//             scratched = {
//               scratched: "true",
//             };
//             acceptances = Object.assign(acceptances, scratched);
//           } else if (horseInput.scratched) {
//             scratched = {
//               scratched: horseInput.scratched,
//             };
//             acceptances = Object.assign(acceptances, scratched);
//           } else {
//             if (
//               category != "trial" &&
//               (!rawHorse.tab_no || rawHorse.tab_no == 0)
//             ) {
//               errors = await helper.logError(
//                 error_log,
//                 errors,
//                 `TAB number not found for Horse - ${rawHorse["@_name"]} - ${horseId} `
//               );
//               error_log = await helper.logError(
//                 [],
//                 error_log,
//                 `TAB number not found for Horse - ${rawHorse["@_name"]} - ${horseId} `
//               );
//             }
//             if (
//               category != "trial" &&
//               (!rawHorse.barrier || rawHorse.barrier == 0)
//             ) {
//               errors = await helper.logError(
//                 error_log,
//                 errors,
//                 `Barrier not found for Horse - ${rawHorse["@_name"]} - ${horseId} `
//               );
//               error_log = await helper.logError(
//                 [],
//                 error_log,
//                 `Barrier not found for Horse - ${rawHorse["@_name"]} - ${horseId} `
//               );
//             }
//             if (
//               category != "trial" &&
//               horseInput.silks &&
//               horseInput.silks != colours
//             ) {
//               // errors = await helper.logError(error_log,errors,`Colours have changed for Horse - ${rawHorse['@_name']} - ${horseId} - old colours: ${horseInput.silks}`)
//               // error_log = await helper.logError([],error_log,`Colours have changed for Horse - ${rawHorse['@_name']} - ${horseId} - old colours: ${horseInput.silks}`)
//             }
//             if (horseInput.colours) {
//               colours = horseInput.colours;
//             }
//           }
  
//           if (category != "trial" && colours == "") {
//             // errors = await helper.logError(error_log,errors,`Silks missing for for Horse - ${horseId}`)
//             // error_log = await helper.logError([],error_log,`Silks missing for for Horse - ${horseId}`)
//           }
  
//           // Jockey
//           if (
//             !horseInput.scratched &&
//             !(rawHorse.scratched_indicator == "S") &&
//             rawHorse.jockey &&
//             rawHorse.jockey["@_id"]
//           ) {
//             var jockey_data = await centaur.jockeys
//               .findOne({ JOC_JOCKEY_ID: rawHorse.jockey["@_id"] })
//               .lean();
  
//             if (jockey_data) {
//               if (
//                 rawHorse.jockey["@_name"] != jockey_data.JOC_JOCKEY_DISPLAYNAME
//               ) {
//                 errors = await helper.logError(
//                   error_log,
//                   errors,
//                   `Jockey name seems off - ${rawHorse.jockey["@_name"]} vs ${jockey_data.JOC_JOCKEY_DISPLAYNAME}, ID is ${rawHorse.jockey["@_id"]} `
//                 );
//                 error_log = await helper.logError(
//                   [],
//                   error_log,
//                   `Jockey name seems off - ${rawHorse.jockey["@_name"]} vs ${jockey_data.JOC_JOCKEY_DISPLAYNAME}, ID is ${rawHorse.jockey["@_id"]} `
//                 );
//               }
//               // trainer_stats = await getTrainerStats(horseId, trainer_data.TRN_TRAINER_ID)
//               jockey_name = jockey_data.JOC_JOCKEY_DISPLAYNAME;
//               jockey_firstname = jockey_data.JOC_JOCKEY_FIRSTNAME;
//               jockey_surname = jockey_data.JOC_JOCKEY_SURNAME;
//               jockey_id = jockey_data.JOC_JOCKEY_ID;
//               jockey_apprentice_indicator =
//                 rawHorse.jockey["@_apprentice_indicator"] ?? "";
//               jockey_allowance_weight =
//                 rawHorse.jockey["@_allowance_weight"] ??
//                 (rawHorse.jockey["@_apprentice_indicator"] ? 0 : "");
//               jockey_riding_weight = rawHorse.jockey["@_riding_weight"] ?? "";
  
//               jockey_record = {
//                 jockey: {
//                   "@_name": jockey_name,
//                   "@_firstname": jockey_firstname,
//                   "@_surname": jockey_surname,
//                   "@_apprentice_indicator": jockey_apprentice_indicator,
//                   "@_allowance_weight": jockey_allowance_weight,
//                   "@_id": jockey_id,
//                   "@_riding_weight": jockey_riding_weight,
//                   statistics: await getJockeyStats(jockey_id),
//                 },
//               };
//               acceptances = Object.assign(acceptances, jockey_record);
//             } else {
//               if (rawHorse.jockey && rawHorse.jockey["@_name"]) {
//                 errors = await helper.logError(
//                   error_log,
//                   errors,
//                   `Jockey missing - ${rawHorse.jockey["@_name"]}, ID is ${rawHorse.jockey["@_id"]} `
//                 );
//                 error_log = await helper.logError(
//                   [],
//                   error_log,
//                   `Jockey missing - ${rawHorse.jockey["@_name"]}, ID is ${rawHorse.jockey["@_id"]} `
//                 );
//               }
//             }
//           }
//         }
  
//         var hcpRat = "";
//         if (rawHorse.ratings && rawHorse.ratings.rating) {
//           for (ratingli of rawHorse.ratings.rating) {
//             if (ratingli["@_type"] == "handicap") {
//               hcpRat = ratingli["@_value"];
//             }
//           }
//         }
//         var horse_betting =
//           horseInput.betting && horseInput.betting != ""
//             ? horseInput.betting
//             : rawHorse.market
//             ? rawHorse.market["@_price_decimal"]
//             : "";
//         var horse_tip =
//           horseInput.tip && horseInput.tip != ""
//             ? horseInput.tip
//             : rawHorse.selection ?? "";
//         // var horse_comment = horseInput.comment && horseInput.comment != '' ? horseInput.comment : ''
//         // var form_comment = horseInput.form_comment && horseInput.form_comment != '' ? horseInput.form_comment : ''
//         // var horse_comment = horseInput.comment && horseInput.comment != '' ? horseInput.comment : (rawHorse.comments ?? '')
//         // var form_comment = rawHorse.form_comments ?? ''
//         var horse = {
//           "@_name": name,
//           "@_country": country,
//           "@_age": age,
//           "@_colour": colour,
//           "@_sex": sex,
//           "@_id": horse_id,
//           "@_foalingdate": foaling_date,
//           sire: {
//             "@_name": sire_name,
//             "@_country": sire_country,
//             "@_id": sire_id,
//           },
//           dam: {
//             "@_name": dam_name,
//             "@_country": dam_country,
//             "@_id": dam_id,
//           },
//           sire_of_dam: {
//             "@_name": sire_dam_name,
//             "@_country": sire_dam_country,
//             "@_id": sire_dam_id,
//           },
//           trainer: {
//             statistics: trainer_stats,
//             "@_name": trainer_name,
//             "@_firstname": trainer_firstname,
//             "@_surname": trainer_surname,
//             "@_id": trainer_id,
//           },
//           training_location: training_location,
//           owners: owners,
//           colours: colours,
//           prizemoney_won: prizmoney_won,
//           last_four_starts: last4s,
//           last_ten_starts: last10s,
//           last_fifteen_starts: last15s,
//           last_twenty_starts: last20s,
//           FF5_dry: "0",
//           FF5_wet: "0",
//           FF_Dry_Rating_100: "100",
//           FF_Wet_Rating_100: "100",
//           current_blinker_ind: current_blinker_ind ?? "N",
//           win_percentage: win_percentage,
//           place_percentage: place_percentage,
//           pace_value: pace[0],
//           pace: pace[1],
//           statistics: statistics,
//           win_distances: win_distances,
//           class_quality: class_quality,
//           rating: rating,
//           rating_wfa: ratingWfa,
//           ratings: {
//             rating: [],
//           },
//           handicap_rating: hcpRat,
//           betting: horse_betting,
//           tip: horse_tip,
//           gear_changes: horseInput.gear_changes ?? { gear_change: [] },
//           running_gear: {
//             gear_item: helper.runningGears(
//               horseInput.gear_changes && horseInput.gear_changes.gear_change
//                 ? horseInput.gear_changes.gear_change
//                 : [],
//               running_gear.gear_item
//             ),
//           },
//           comment: horseInput.comment ?? "",
//           form_comments: horseInput.form_comment ?? "",
//           errors: errors,
//           //"forms": { form: [{}] }
//         };
//         if (prev_name) {
//           horse = Object.assign(horse, {
//             "@_previous_name": prev_name,
//           });
//         }
//         if (meetingStage == "Nominations" || meetingStage == "Weights") {
//           horse.sequence_no = sequenceCount;
//         }
  
//         //Updating data for Weights Stage
//         if (
//           meetingStage == "Weights" ||
//           meetingStage == "Acceptances" ||
//           meetingStage == "FinalFields"
//         ) {
//           horse = Object.assign(horse, weights);
//         }
  
//         //Updating data for Acceptance and Final Fields Stage
//         if (meetingStage == "Acceptances" || meetingStage == "FinalFields") {
//           horse = Object.assign(horse, acceptances);
//         }
  
//         if (prev_trainer) {
//           // console.log('prev_train',prev_trainer)
//           var previous_trainer = await centaur.trainers
//             .findOne({ TRN_TRAINER_ID: prev_trainer })
//             .lean();
//           // console.log(previous_trainer)
//           if (previous_trainer) {
//             horse.trainer["@_previous_trainer"] =
//               previous_trainer.TRN_TRAINER_DISPLAYNAME;
//             horse.trainer["@_previous_trainer_id"] = prev_trainer;
//             horse.trainer["@_firststart"] = "Y";
//           }
//         }
  
//         // Horse for InputDataMeeting
//         var horse_inputMeetingData = {
//           "@_name": name,
//           "@_id": horse_id,
//           // "race_entry_code": rawHorse['@_RaceEntryCode'],
//           betting: horse_betting,
//           tip: horse_tip,
//           silks: rawHorse.colours,
//           comment: horseInput.comment ?? "",
//           form_comment: horseInput.form_comment ?? "",
//           gear_changes: horseInput.gear_changes ?? { gear_change: [] },
//           running_gear: running_gear,
//           errors: errors,
//           error_log: error_log,
//           colours: horseInput.colours ?? "",
//           scratched:
//             horseInput.scratched || rawHorse.scratched_indicator == "S"
//               ? true
//               : false,
//         };
//       } else {
//         errors = await helper.logError(
//           error_log,
//           errors,
//           `Horse Not Found - ${rawHorse["@_name"]} ${rawHorse["@_id"]}`
//         );
//         error_log = await helper.logError(
//           [],
//           error_log,
//           `Horse Not Found - ${rawHorse["@_name"]} ${rawHorse["@_id"]}`
//         );
//         horse = { errors: errors };
//         horse_inputMeetingData = { errors: errors, error_log: error_log };
//       }
  
//       const horse_data = {
//         horse_processedMeetingData: horse,
//         horse_inputMeetingData: horse_inputMeetingData,
//       };
//       return horse_data;
//     } catch (err) {
//       // var sendMail = await helper.mailAlert('TIS Horse Data error',`TIS Meeting Data couldnt generate ${meetingId}. ${error}`,'error')
//       // console.log(sendMail)
//       console.log(
//         `((ERROR)): Problem normalizing horse ${rawHorse["@_name"]} ${meetingTrack} ${meetingDate} ${err}`
//       );
//     }
//   };
  
  // engine.js
//   const processResults_TISDB = async (resultsContent, meetingId, trialStatus) => {
//     // Fetch meeting data
//     const meeting = await centaur.temp_meetings
//       .findOne({ _id: meetingId })
//       .select("inputMeetingData")
//       .select("processedMeetingData")
//       .lean();
//     if (meeting) {
//       var meetingData = meeting;
//       var isTrial = 0;
//       var hasErrors = false;
  
//       var rawData = resultsContent.meeting;
//       meetingData.meetingStage = "Results";
//       meetingData.processedMeetingData.meeting.stage = "Results";
//       meetingData.processedMeetingData.meeting.rail_position =
//         rawData.rail_position;
//       meetingData.processedMeetingData.meeting.track["@_expected_condition"] =
//         rawData.track["@_expected_condition"] ??
//         (rawData.track["@_track_surface"] == "Y" ? "Y0" : "G0");
//       var raceIndex = 0;
//       var distributeResults = [];
//       // check to see if its a single race or multiple by  hecking if its an array or an object
//       // xml is weird with arrays
//       if (typeof rawData.events.event[Symbol.iterator] !== "function") {
//         // if its not an array, make it into one so we dont have to alter next process
//         raceIndex = parseInt(rawData.events.event["@_number"] - 1);
//         rawData.events.event = [rawData.events.event];
//       }
//       for (race of rawData.events.event) {
//         var nomsNumber = race["@_nomination_number"];
//         if (race.NominationsDivisor && race.NominationsDivisor > 1) {
//           nomsNumber =
//             30 +
//             parseInt(race.NominationsRaceNumber) +
//             race.NominationsDivisor * 10;
//         }
//         var theEventId = "";
//         //get the event ID, or generate it against the received data to make sure it matches
//         if (race["@_id"]) {
//           theEventId = race["@_id"];
//         } else {
//           if (trialStatus === "trial") {
//             isTrial = 1;
//             theEventId = helper.generateUniqueRaceId(
//               rawData.MeetDate,
//               meetingData.processedMeetingData.meeting.track["@_id"],
//               race["@_RaceNumber"],
//               isTrial
//             );
//           } else {
//             theEventId = helper.generateUniqueRaceId(
//               rawData.MeetDate,
//               meetingData.processedMeetingData.meeting.track["@_id"],
//               nomsNumber,
//               isTrial
//             );
//           }
//         }
//         // this variable determines whether to automatically distribute results
//         var sendrace = false;
//         // check to make sure we are loading the right race results into the race by comparing IDs
//         if (
//           theEventId ===
//           meetingData.processedMeetingData.meeting.races.race[raceIndex]["@_id"]
//         ) {
//           sendrace = true;
//           var dbRace =
//             meetingData.processedMeetingData.meeting.races.race[raceIndex];
//           // if duration is empty, it needs to be NTT (No Time Taken)
//           if (!race.duration || !race.duration.toString().match(/[1-9]/g)) {
//             dbRace.duration = "NTT";
//           } else {
//             dbRace.duration = race.duration;
//           }
//           dbRace.sectional = race.sectional ?? "";
//           dbRace.starters = race.starters;
//           dbRace.official_margin_1 = race.official_margin_1;
//           dbRace.official_margin_2 = race.official_margin_2;
//           dbRace.race_stage = "Results";
//           var trackCondition = "";
//           if (race.track_condition == "Synthetic") {
//             trackCondition = "Y0";
//           } else if (race.track_condition == "Good") {
//             trackCondition = "Good";
//           } else if (race.track_condition == "Soft") {
//             trackCondition = "Soft";
//           } else if (race.track_condition == "Heavy") {
//             trackCondition = "Heavy";
//           } else {
//             trackCondition = helper.processTrackRating(
//               race.track_condition.replace(/^.+?\(([0-9]+)\)/g, "$1")
//             );
//           }
//           if (dbRace.track) {
//             dbRace.track["@_expected_condition"] = trackCondition;
//             dbRace.track_condition = trackCondition;
//           }
//           // update race distance if req'd
//           if (
//             race.distance &&
//             dbRace.distance &&
//             race.distance["@_metres"] != dbRace.distance["@_metres"]
//           ) {
//             dbRace.distance["@_metres"] = race.distance["@_metres"];
//           }
//           var horseIndex = 0;
//           if (trialStatus === "trial") {
//             dbRace.barrier_trial_indicator = "B";
//           }
//           // loop through horses in a race to add results data
//           for (horse of dbRace.horses.horse) {
//             var theHorse = await centaur.horses
//               .findOne({ HRN_HORSE_ID: horse["@_id"] })
//               .select("HOR_TOTAL_PRIZEMONEY")
//               .select("HOR_RACE_PRIZEMONEY")
//               .lean();
//             var theHorseTotalPrize = theHorse.HOR_TOTAL_PRIZEMONEY ?? 0;
//             if (theHorse.HOR_RACE_PRIZEMONEY) {
//               for (horsePrize of theHorse.HOR_RACE_PRIZEMONEY) {
//                 if (horsePrize.event_id == theEventId) {
//                   theHorseTotalPrize = theHorseTotalPrize - horsePrize.prize;
//                 }
//               }
//             }
  
//             try {
//               var rawHorse = {};
//               // for TIS files we can matchj on horse ID
//               for (resultsHorse of race.horses.horse) {
//                 if (horse["@_id"] == resultsHorse["@_id"]) {
//                   rawHorse = resultsHorse;
//                 }
//               }
//               if (rawHorse && (rawHorse.prices || rawHorse.jockey)) {
//                 var prizemoney_race = 0;
//                 if (
//                   !horse.prizemoney_race &&
//                   rawHorse.prizemoney_won &&
//                   trialStatus != "trial"
//                 ) {
//                   // this shouldnt happen very often at all with TIS files, mostly realy rare non-tab meetings
//                   for (thePrize of race.prizes.prize) {
//                     if (
//                       thePrize["@_type"].slice(0, -2) == rawHorse.finish_position
//                     ) {
//                       prizemoney_race = parseInt(thePrize["@_value"]);
//                     }
//                   }
//                 } else if (horse.prizemoney_race) {
//                   prizemoney_race = horse.prizemoney_race;
//                 }
//                 // with TIS files, most of the data is correctly aligned already so we can use it as is
//                 results = {
//                   finish_position: rawHorse.finish_position,
//                   decimalprices: rawHorse.decimalprices ?? {},
//                   prices: rawHorse.prices ?? {},
//                   positions: rawHorse.positions ?? {},
//                   margin: rawHorse.beaten_margin ?? rawHorse.margin ?? "",
//                   beaten_margin: rawHorse.beaten_margin ?? "",
//                   margin_official: rawHorse.margin_official ?? "",
//                   prizemoney_race: prizemoney_race ?? -1,
//                   weight_carried: rawHorse.weight_carried ?? "",
//                   barrier: rawHorse.barrier ?? "",
//                   current_blinker_ind: rawHorse.blinkers_indicator ?? "",
//                   prizemoney_won: rawHorse.prizemoney_won,
//                 };
//                 results.running_gear = horse.running_gear ?? { gear_item: [] };
//                 if (rawHorse.gear_changes) {
//                   results.gear_changes = {};
//                   results.gear_changes.gear_change = convertTISgear(
//                     rawHorse.gear_changes
//                   );
//                   results.running_gear.gear_item = helper.runningGears(
//                     results.gear_changes.gear_change,
//                     results.running_gear.gear_item
//                   );
//                 }
//                 if (rawHorse.favourite_indicator) {
//                   results = Object.assign(results, {
//                     favourite_indicator: rawHorse.favourite_indicator,
//                   });
//                 }
//                 if (!horse.rating_result) {
//                   results.rating_result = {};
//                 } else {
//                   results.rating_result = horse.rating_result;
//                 }
  
//                 if (rawHorse.rating && rawHorse.rating["@_unadjusted"]) {
//                   results.rating_result["@_unadjusted"] =
//                     rawHorse.rating["@_unadjusted"];
//                 }
//                 if (horse.handicap_rating) {
//                   results.rating_result["@_handicap"] = horse.handicap_rating;
//                 }
//                 if (horse.handicap_rating_post) {
//                   results.rating_result["@_post_handicap"] =
//                     horse.handicap_rating_post;
//                 }
  
//                 var jockey_record = {
//                   jockey: {
//                     "@_name": "",
//                     "@_firstname": "",
//                     "@_surname": "",
//                     "@_apprentice_indicator": "",
//                     "@_id": "",
//                   },
//                 };
//                 // align the new jockey to the db jockey so we can add it in
//                 if (rawHorse.jockey) {
//                   var jockey_data = await centaur.jockeys
//                     .findOne({ JOC_JOCKEY_ID: rawHorse.jockey["@_id"] })
//                     .lean();
  
//                   if (jockey_data) {
//                     jockey_name = jockey_data.JOC_JOCKEY_DISPLAYNAME;
//                     jockey_firstname = jockey_data.JOC_JOCKEY_FIRSTNAME;
//                     jockey_surname = jockey_data.JOC_JOCKEY_SURNAME;
//                     jockey_id = jockey_data.JOC_JOCKEY_ID;
//                     jockey_apprentice_indicator =
//                       rawHorse.jockey["@_apprentice_indicator"] ?? "";
//                     jockey_allowance_weight =
//                       rawHorse.jockey["@_allowance_weight"] ?? "";
//                     jockey_riding_weight =
//                       rawHorse.jockey["@_riding_weight"] ?? "";
  
//                     jockey_record = {
//                       jockey: {
//                         "@_name": jockey_name,
//                         "@_firstname": jockey_firstname,
//                         "@_surname": jockey_surname,
//                         "@_apprentice_indicator": jockey_apprentice_indicator,
//                         "@_allowance_weight": jockey_allowance_weight,
//                         "@_id": jockey_id,
//                         "@_riding_weight": jockey_riding_weight,
//                       },
//                       jockey_id: jockey_id,
//                     };
//                   } else {
//                     dbRace.horses.horse[horseIndex].errors =
//                       await helper.logError(
//                         [],
//                         dbRace.horses.horse[horseIndex].errors,
//                         `Couldnt match Jockey - ${rawHorse.jockey["@_id"]} ${rawHorse.jockey["@_name"]}`
//                       );
//                     needs_check = true;
//                     hasErrors = true;
//                   }
//                 }
//                 results = Object.assign(results, jockey_record);
//                 dbRace.horses.horse[horseIndex] = Object.assign(
//                   dbRace.horses.horse[horseIndex],
//                   results
//                 );
//               } else {
//                 if (trialStatus != "trial") {
//                   dbRace.horses.horse[horseIndex].scratched = true;
//                 }
//               }
//             } catch (err) {
//               console.log(
//                 `((ERROR)): Error updating horse result for ${horse["@_name"]} ${horse["@_id"]}: ${err}`
//               );
//               hasErrors = true;
//             }
//             horseIndex++;
//           }
//           var horseErrorCount = 0;
//           var jockeysArray = [];
//           // check all the horses in the race for errors, so we can alert at race/meeting level
//           for (record of dbRace.horses.horse) {
//             if (!record.scratched) {
//               if (
//                 record.jockey &&
//                 record.jockey["@_id"] &&
//                 jockeysArray.includes(record.jockey["@_id"])
//               ) {
//                 for (horse of dbRace.horses.horse) {
//                   if (
//                     horse.jockey &&
//                     horse.jockey["@_id"] === record.jockey["@_id"]
//                   ) {
//                     horse.errors = await helper.logError(
//                       [],
//                       horse.errors,
//                       `Duplicate jockey detected ${horse.jockey["@_name"]}`
//                     );
//                     hasErrors = true;
//                   }
//                 }
//               }
//               if (record.jockey) {
//                 jockeysArray.push(record.jockey["@_id"]);
//               }
//               if (record.errors.length > 0) {
//                 horseErrorCount++;
//                 hasErrors = true;
//               }
//             }
//           }
//           if (horseErrorCount) {
//             dbRace.errors = await helper.logError(
//               [],
//               dbRace.errors,
//               `Horses with alerts: ${horseErrorCount}`
//             );
//             hasErrors = true;
//           }
//         } else {
//           // something's gone wrong, lets alert in the front end by creating a meeting level error
//           meetingData.processedMeetingData.meeting.races.race[
//             raceIndex
//           ].errors.push([
//             `${meetingData.processedMeetingData.meeting.races.race[raceIndex]["@_id"]} results race id didnt match!`,
//           ]);
//           console.log(
//             `((ERROR)): ${
//               meetingData.processedMeetingData.meeting.races.race[raceIndex][
//                 "@_id"
//               ]
//             } results race id didnt match ${helper.generateUniqueRaceId(
//               rawData.date,
//               meetingData.processedMeetingData.meeting.track["@_id"],
//               nomsNumber,
//               isTrial
//             )}!`
//           );
//           hasErrors = true;
//         }
//         if (!horseErrorCount && dbRace.duration) {
//           dbRace.results_approved = true;
//           distributeResults.push(dbRace["@_number"]);
//         }
//         if (sendrace)
//           meetingData.processedMeetingData.meeting.races.race[raceIndex] =
//             Object.assign(
//               meetingData.processedMeetingData.meeting.races.race[raceIndex],
//               dbRace
//             );
  
//         raceIndex++;
//       }
//       // update the processed meeting in the db
//       let resultsCompleted = await centaur.temp_meetings
//         .updateOne(
//           { _id: meetingId },
//           {
//             $set: meetingData,
//           }
//         )
//         .lean();
  
//       var currDate = moment();
//       var meetdate = moment(meetingData.meetingDate);
//       var dayGap = currDate.diff(meetdate, "days");
//       var distribute = false;
//       var raceNo = 0;
//       var files = "";
//       // dont auto distribute results older than 7 days
//       if (resultsCompleted && dayGap < 7) {
//         var msg = `Results Loaded for MeetingId: ${meetingId}`;
//         if (!hasErrors) {
//           distribute = true;
//           if (distributeResults.length == 1 && trialStatus != "trial") {
//             raceNo = parseInt(distributeResults[0]);
//           } else {
//             for (race of meetingData.processedMeetingData.meeting.races.race) {
//               if (
//                 ["acceptances", "finalfields"].includes(
//                   race.race_stage.toLowerCase()
//                 )
//               )
//                 distribute = false;
//             }
//           }
//           // AUTOMATED DISTRIBUTION FLAG
//           files = "RESULTS";
//         }
//       }
//       // start post meeting process - including distribution if appl
//       let tc = await triggerCompare(meetingId, files, distribute, raceNo);
//       // console.log('tc', tc)
//       return true;
//     } else {
//       // var sendMail = await helper.mailAlert('Results load error',`Failed Updating ${meetingId} Results`,'error')
//       // console.log(sendMail)
//       console.log(
//         `((ERROR)): couldnt find existing meeting to load results into ${meetingId}`
//       );
//       await triggerCompare(meetingId, "", false, 0);
//       return false;
//     }
//   };