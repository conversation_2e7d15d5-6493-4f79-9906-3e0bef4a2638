// // ## THIS IS THE ONE FOR FIXING HORSE IDS ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var checkExisting = await centaur.horses.find().where({ HRN_HORSE_ID: horse.new_id }).where({ HRN_HORSE_NAME: horse.horse_name }).lean()
//             if (!checkExisting[0]){

//                 console.log(`trying to update ${horse.old_id} to ${horse.new_id} for ${horse.horse_name}`)
//                 var theHorses = await centaur.horses.find({ HRN_HORSE_ID: horse.old_id }).lean()
//                 if (theHorses.length > 0){
//                     for (theHorse of theHorses){
//                         if (theHorse.HRN_HORSE_NAME == horse.horse_name){
//                             var theHorseForm = await centaur.form.findOne()
//                                 .where({ horse_id: horse.old_id })
//                                 .where({ horse_name: horse.horse_name }).lean()
//                             let updateform = false
//                             if (!theHorseForm){
//                                 var multiform = await centaur.form.find()
//                                 .where({ horse_id: horse.old_id }).lean()
//                                 for (formhorse of multiform){
//                                     if (formhorse.horse_name.toUpperCase() == horse.horse_name){
//                                         updateform = await centaur.form.updateOne({ _id: formhorse._id }, {
//                                             "$set": {horse_id : horse.new_id, horse_name:horse.horse_name}
//                                         }).lean()
//                                     }
//                                 }
//                             } else {
//                                 updateform = await centaur.form.updateOne({ _id: theHorseForm._id }, {
//                                     "$set": {horse_id : horse.new_id}
//                                 }).lean()
//                             }
                            

//                             if (!updateform){ 
//                                 console.log(`problem updating form for ${horse.horse_name}, ${horse.old_id}`)
//                                 continue
//                             }
//                             let updateHorse = await centaur.horses.updateOne({ _id: theHorse._id }, {
//                                 "$set": {HRN_HORSE_ID : horse.new_id, HRN_HORSE_ID : horse.new_id}
//                             }).lean()
//                             // console.log(updateHorse)
//                             // console.log(`updated ${horse.old_id} to ${horse.new_id} for ${horse.horse_name}`)
//                             // console.log(updateForm,updateHorse)
//                         } else {
                            
//                         }
//                     }
//                 } else {
//                     console.log(`Horse not found ${horse.horse_name}, ${horse.old_id}`)
//                 }
//             } else {
//                 // console.log(`Horse Seems already Correct ${horse.horse_name}, ${horse.new_id}`)
//             }

//             var checkHorses = await centaur.horses.find({ HRN_HORSE_ID: horse.new_id }).lean()
//             if (checkHorses.length > 1){
//                 for (check of checkHorses){
//                     if (check.HRN_HORSE_NAME != horse.horse_name){
//                         console.log(`CHECK HORSE: ${check.HRN_HORSE_NAME} ${horse.new_id}`)
//                     }
//                 }
//             }

//         }
//     } catch (err){
//         console.log(err)
//     }
// }

// // ## THIS IS THE ONE FOR FIXING EVENT IDS ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (race of ratFileContent.horses){
//             var indexes = await centaur.form_index.find().where({meeting_id: race.meeting_id}).where({race_no:race.old_event_id}).lean()
//             for (horse of indexes){
//                 var horse_form = await centaur.form.findOne({ horse_id: horse.horse_id }).lean()
//                 if (!horse_form){ continue }
//                 var horseFormId = horse_form._id

//                 var dataToUpdate = {}
               
//                 for (i=0;i<horse_form.form.length;i++){
//                     var formitem = horse_form.form[i]
//                     if (formitem.meeting_id == race.meeting_id){
//                         var formToUpdate = "form." + i + ".event_id"
//                         dataToUpdate[formToUpdate] = race.new_event_id
//                         break
//                     }
//                 }
//                 let updateForm = await centaur.form.updateOne({ _id: horseFormId }, {
//                     "$set": dataToUpdate
//                 }).lean()
//                 let updateIndex = await centaur.form_index.updateOne({ _id: horse._id }, {
//                     "$set": {"event_id" : race.new_event_id}
//                 }).lean()

                
        

                
//             }
//             var theMeeting =  await centaur.processed_meetings.findOne({ _id: race.meeting_id }).lean()
            
//             // var raceToUpdate = "processedMeetingData.meeting.races.race." + raceIndex + "."
//             // var inputToUpdate = "inputMeetingData.races.race." + raceIndex + "."

//             if (theMeeting){
//                 for (pmRace of theMeeting.processedMeetingData.meeting.races.race){
//                     if (parseInt(pmRace['@_number']) == race.old_event_id){
//                         pmRace['@_id'] = race.new_event_id
//                     }
//                 }
//                 for (inputRace of theMeeting.inputMeetingData.races.race){
//                     if (parseInt(inputRace['@_number']) == race.old_event_id){
//                         inputRace['@_id'] = race.new_event_id
//                     }
//                 }

//             }
            
//             var pmUpdate = await centaur.processed_meetings.updateOne({ _id: race.meeting_id }, {
//                 "$set": theMeeting
//             }).lean()
            
//             console.log('event id updated')
//             console.log(race)
//         }
//     } catch (err){

//     }
// }

// // ## THIS IS THE ONE FOR BULK ADDING HISTORICAL RATINGS ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({ horse_id: horse.horse_id }).lean()
//             if (!horse_form){ continue }
//             var horseFormId = horse_form._id

//             var dataToUpdate = {}
//             for (run of horse.ratings){
                
//                 if (horse_form && horse_form.form){
                    
//                     for (var i=0;i<horse_form.form.length;i++){
//                         var formitem = horse_form.form[i]
//                         if (formitem.event_id == run.event_id){
//                             var formToUpdate = "form." + i + ".rating"
//                             dataToUpdate[formToUpdate] = run.rating
//                             break
//                         }
//                     }
//                 } 

//             }
//             console.log(dataToUpdate)
//             let updateForm = await centaur.form.updateOne({ _id: horseFormId }, {
//                 "$set": dataToUpdate
//             }).lean()
//             console.log(`form ratings updates for ${horse_form.horse_name} ${horse.horse_id}: ${updateForm}`)
//         }

// ## THIS IS THE ONE FOR BULK ADDING HISTORICAL HANDICAP RATINGS ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({ horse_id: horse.horse_id }).lean()
//             if (!horse_form){ continue }
//             var horseFormId = horse_form._id

//             var dataToUpdate = {}
//             for (run of horse.ratings){
                
//                 if (horse_form && horse_form.form){
                    
//                     for (var i=0;i<horse_form.form.length;i++){
//                         var formitem = horse_form.form[i]
//                         if (formitem.event_id == run.event_id){
//                             var rating = formitem.rating ?? {}

//                             if (run.rating['@_handicap']) {
//                                 rating['@_handicap'] = run.rating['@_handicap']
//                                 // dataToUpdate["form." + i + ".rating.@_handicap"] = run.rating['@_handicap']
//                             }
//                             if (run.rating['@_post_handicap']) {
//                                 rating['@_post_handicap'] = run.rating['@_post_handicap']
//                                 // dataToUpdate["form." + i + ".rating.@_post_handicap"] = run.rating['@_post_handicap']
//                             }
//                             var formToUpdate = "form." + i + ".rating"
//                             dataToUpdate[formToUpdate] = rating
//                             break
//                         }
//                     }
//                 } 

//             }
//             // console.log(dataToUpdate)
//             let updateForm = await centaur.form.updateOne({ _id: horseFormId }, {
//                 "$set": dataToUpdate
//             }).lean()
//             console.log(`form ratings updates for ${horse_form.horse_name} ${horse.horse_id}: ${updateForm}`)
//         }
                    

//     }catch(err){
//         console.log('Error loading ratings file: ',err)
//         return false
//     }
//     return true

// }

// // ## THIS IS THE ONE FOR BULK ADDING STEWARDS REPORTS ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({ horse_id: horse.horse_id }).lean()
//             if (!horse_form){ continue }
//             var horseFormId = horse_form._id

//             var dataToUpdate = {}
//             for (run of horse.reports){
                
//                 if (horse_form && horse_form.form){
//                     var foundform = false
//                     for (i=0;i<horse_form.form.length;i++){
//                         var formitem = horse_form.form[i]
//                         if (formitem.event_id == run.event_id){

//                             var formToUpdate = "form." + i + ".stewards_report"
//                             dataToUpdate[formToUpdate] = run.stewards_report
//                             foundform = true
//                             break
//                         }
//                     }
//                     if (!foundform){
//                         console.log(`${horse_form.horse_name} ${horse.horse_id} form item missing or event id (${run.event_id}) incorrect`)
//                     }
//                 } 

//             }
//             console.log(dataToUpdate)
//             let updateForm = await centaur.form.updateOne({ _id: horseFormId }, {
//                 "$set": dataToUpdate
//             }).lean()
//             console.log(`form ratings updates for ${horse_form.horse_name} ${horse.horse_id}: ${updateForm}`)
//         }
                    

//     }catch(err){
//         console.log('Error loading ratings file: ',err)
//         return false
//     }
//     return true

// }

// // ## THIS IS THE ONE FOR BULK ADDING FAVOURITE INDICATORS ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({ horse_id: horse.horse_id }).lean()
//             if (!horse_form){ continue }
//             var horseFormId = horse_form._id

//             var dataToUpdate = {}
//             for (run of horse.indicators){
                
//                 if (horse_form && horse_form.form){
//                     var foundform = false
//                     for (i=0;i<horse_form.form.length;i++){
//                         var formitem = horse_form.form[i]
//                         if (formitem.event_id == run.event_id){

//                             var formToUpdate = "form." + i + ".favourite_indicator"
//                             dataToUpdate[formToUpdate] = run.favourite_indicator
//                             foundform = true
//                             break
//                         }
//                     }
//                     if (!foundform){
//                         console.log(`${horse_form.horse_name} ${horse.horse_id} form item missing or event id (${run.event_id}) incorrect`)
//                     }
//                 } 

//             }
//             // console.log(dataToUpdate)
//             let updateForm = await centaur.form.updateOne({ _id: horseFormId }, {
//                 "$set": dataToUpdate
//             }).lean()
//             console.log(`form ratings updates for ${horse_form.horse_name} ${horse.horse_id}: ${updateForm}`)
//         }
                    

//     }catch(err){
//         console.log('Error loading ratings file: ',err)
//         return false
//     }
//     return true

// }

// // ## THIS IS THE ONE FOR BULK RE-APPROVING RESULTS ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (the_meet of ratFileContent.horses){
//             var meeting = await centaur.processed_meetings.findOne({ _id: the_meet }).lean()
//             if (!meeting){ continue }
//             var lambda = new AWS.Lambda();
//             var payload = {
//                 path: '/processedmeetings',
//                 method: 'PUT',
//                 body: {
//                     meetingId: the_meet, 
//                     level: "approve_results"
//                 }
//             }
            

//             var params = {
//                 FunctionName: "MrCenLambdaApi-" + process.env.ENV,
//                 InvocationType: 'Event',
//                 Payload:  JSON.stringify(payload) ,
//             };
//             var res_4 = await lambda.invoke(params).promise();
//             console.log(`meeting approved ${the_meet}`)
//             console.log(res_4)
            
//             sleep(5000)
//         }
                    

//     }catch(err){
//         console.log('Error loading ratings file: ',err)
//         return false
//     }
//     return true

// }

// // ## THIS IS THE ONE FOR BULK ADDING GEAR CHANGES TO FORM ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({ horse_id: horse.horse_id }).lean()
//             if (!horse_form){ continue }
//             var horseFormId = horse_form._id

//             var dataToUpdate = {}
//             for (run of horse.changes){
                
//                 if (horse_form && horse_form.form){
//                     var foundform = false
//                     for (i=0;i<horse_form.form.length;i++){
//                         var formitem = horse_form.form[i]
//                         if (formitem.event_id == run.event_id){

//                             var formToUpdate = "form." + i + ".gear_changes"
//                             dataToUpdate[formToUpdate] = run.gear_changes
//                             foundform = true
//                             break
//                         }
//                     }
//                     // if (!foundform){
//                     //     console.log(`${horse_form.horse_name} ${horse.horse_id} form item missing or event id (${run.event_id}) incorrect`)
//                     // }
//                 } 

//             }
//             console.log(dataToUpdate)
//             let updateForm = await centaur.form.updateOne({ _id: horseFormId }, {
//                 "$set": dataToUpdate
//             }).lean()
//             console.log(`form gear changes updates done for ${horse_form.horse_name} ${horse.horse_id}: ${updateForm}`)
//         }
                    

//     }catch(err){
//         console.log('Error loading ratings file: ',err)
//         return false
//     }
//     return true

// }

// // ## THIS IS THE ONE FOR BULK ADDING SECTIONALS ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({ horse_id: horse.horse_id }).lean()
//             if (!horse_form){ continue }
//             var horseFormId = horse_form._id

//             var dataToUpdate = {}
//             for (run of horse.sec_runs){
                
//                 if (horse_form && horse_form.form){
//                     var foundform = false
//                     for (i=0;i<horse_form.form.length;i++){
//                         var formitem = horse_form.form[i]
//                         if (formitem.event_id == run.event_id){

//                             var formToUpdate = "form." + i + ".sectional_200"
//                             dataToUpdate[formToUpdate] = run.sectional_200
//                             foundform = true
//                             break
//                         }
//                     }
//                     if (!foundform){
//                         console.log(`horse ${horse.horse_id} form item missing or event id (${run.event_id}) incorrect`)
//                     }
//                 } 

//             }
//             // console.log(dataToUpdate)
//             let updateForm = await centaur.form.updateOne({ _id: horseFormId }, {
//                 "$set": dataToUpdate
//             }).lean()
//             // console.log(`form ratings updates for ${horse_form.horse_name} ${horse.horse_id}: ${updateForm}`)
//         }
                    

//     }catch(err){
//         console.log('Error loading ratings file: ',err)
//         return false
//     }
//     return true

// }

// // ## THIS IS THE ONE FOR FIXING MISSING TRACKS IN FORM INDEX TABLE ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (race of ratFileContent.horses){
//             var meeting = await centaur.processed_meetings.findOne({_id:race.meeting_id}).lean()
//             if (meeting){
//                 var track_id = meeting.processedMeetingData.meeting.track['@_id']
//                 var formIndexes = await centaur.form_index.find({meeting_id:race.meeting_id}).lean()
//                 if (formIndexes){
//                     for (formItem of formIndexes){
//                         var fIupdate = await centaur.form_index.updateOne({ _id: formItem._id }, {
//                                 "$set": {track_id : track_id}
//                             }).lean()
//                             // console.log(fIupdate)
//                     }
//                     console.log(`track id ${track_id} inserted at ${race.meeting_id} `)
//                 }
//             }
//         }
//     } catch (err){

//     }
// }

// // ## THIS IS THE ONE FOR REMOVING FORM_INDEX DUPLICATES ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (race of ratFileContent.horses){
//             var fIupdate = await centaur.form_index.deleteOne({ _id: race._id }).lean()
//         }
//     } catch (err){
//         console.log(err)
//     }
// }

// // ## THIS IS THE ONE FOR MAKING SURE FORM ITEMS ARE IN ORDER AND CLEANING UP EMPTY ARRAY FORM ITEMS ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({horse_id:horse}).lean()
            
//             if (horse_form && horse_form.form && horse_form.form[0]){
//                 var theForm = horse_form.form
//                 var formtodelete = -1
//                 var i = 0 
//                 for (form_item of theForm){
//                     if (!form_item.meeting_date){
//                         formtodelete = i
//                     }
//                     i++
//                 }
//                 if (formtodelete > -1) {
//                     theForm.splice(formtodelete, 1)
//                     console.log(`horse ${horse} empty array cleaned`)
//                 }

//                 theForm.sort((a,b) => b.meeting_date - a.meeting_date)
//                 // for (form_item of theForm){
//                 //     if (form_item.meeting_date){
//                 //         console.log(form_item.meeting_date)
//                 //     }
//                 // }
//                 var form_update = await centaur.form.updateOne({ _id: horse_form._id }, {
//                                                     "$set": {form : theForm}
//                                                 }).lean()
//                 console.log(`horse ${horse} form reordered`)
//             }


//         }

//     } catch (err){

//     }
// }

// // ## THIS IS THE ONE FOR MAKING RAN-WITH GEAR CHANGES ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({horse_id:horse}).lean()
            
//             if (horse_form && horse_form.form && horse_form.form[0]){
                
//                 var theForm = horse_form.form
               
//                 var formtodelete = -1
//                 var i = 0 
//                 for (form_item of theForm){
//                     if (!form_item.meeting_date){
//                         formtodelete = i
//                     }
//                     i++
//                 }
//                 if (formtodelete > -1) {
//                     theForm.splice(formtodelete, 1)
//                     console.log(`horse ${horse} empty array cleaned`)
//                 }

//                 theForm.sort((a,b) => a.meeting_date - b.meeting_date)
//                 var ran_gear = []
//                 for (form_item_gear of theForm){
//                     if (form_item_gear.gear_changes && form_item_gear.gear_changes.gear_change && form_item_gear.gear_changes.gear_change[0]){
                        // ran_gear = helper.runningGears(form_item_gear.gear_changes.gear_change,ran_gear)
                        // // for (gear_item of form_item_gear.gear_changes.gear_change){
                        // //     if (gear_item['@_option'].includes("off")){
                        // //         const item_index = ran_gear.indexOf(gear_item['@_name'])
                        // //         if (item_index > -1){
                        // //             ran_gear.splice(item_index, 1)
                        // //         } else if (gear_item['@_name'].toLowerCase() == 'blinkers'){
                        // //             for (item of ran_gear){
                        // //                 if (item.toLowerCase().includes('blinkers')){
                        // //                     ran_gear.splice(ran_gear.indexOf(item), 1)
                        // //                     break
                        // //                 }
                        // //             }
                        // //         } else if (gear_item['@_name'].toLowerCase() == 'winkers'){
                        // //             for (item of ran_gear){
                        // //                 if (item.toLowerCase().includes('winkers')){
                        // //                     ran_gear.splice(ran_gear.indexOf(item), 1)
                        // //                     break
                        // //                 }
                        // //             }
                        // //         }
                                
                        // //     } else if (!ran_gear.includes(gear_item['@_name']) && gear_item['@_name'].toLowerCase() != 'gelded') {
                        // //         ran_gear.push(gear_item['@_name'])
                        // //     }
                        // // }
//                     }
//                     form_item_gear.running_gear = {}
//                     form_item_gear.running_gear.gear_item = JSON.parse(JSON.stringify(ran_gear))
//                     // console.log(horse,form_item_gear.meeting_date,form_item_gear.running_gear)
                    
//                 }
//                 theForm.sort((a,b) => b.meeting_date - a.meeting_date)
//                 var form_update = await centaur.form.updateOne({ _id: horse_form._id }, {
//                                                     "$set": {form : theForm}
//                                                 }).lean()
//                 // console.log(`horse ${horse} form updated`)
//             }


//         }

//     } catch (err){

//     }
// }

// // ## THIS IS THE ONE FOR FINDING DUPLICATE FORM ITEMS ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({horse_id:horse}).lean()
            
//             if (horse_form && horse_form.form && horse_form.form[0]){
                
//                 var theForm = horse_form.form
               
//                 var checkdate = ''
//                 // console.log(horse)
                
//                 // for (form_item of theForm){
//                 for (var i = 0;i < theForm.length;i++){
//                     // console.log(checkdate,form_item.meeting_date)
//                     form_item = theForm[i]
//                     if (checkdate.toString() == form_item.meeting_date.toString()){
//                         prev_form = theForm[i-1]
//                         console.log((i-1),horse_form.horse_name,horse,prev_form.event_id,prev_form.meeting_date,prev_form.track['@_name'],prev_form.race['@_number'],prev_form.finish_position)
//                         console.log(i,horse_form.horse_name,horse,form_item.event_id,form_item.meeting_date,form_item.track['@_name'],form_item.race['@_number'],form_item.finish_position)
//                     }
//                     checkdate = form_item.meeting_date
                   
//                 }
                
//             }


//         }

//     } catch (err){

//     }
// }

// // ## THIS IS THE ONE FOR REMOVING EMPTY FORM ITEMS ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({horse_id:horse}).lean()
            
//             if (horse_form && horse_form.form && horse_form.form[0]){
                
//                 var theForm = horse_form.form


//                 var formtodelete = -1
//                 var i = 0 
//                 for (form_item of theForm){
//                     if (form_item.finish_position === 0){
//                         formtodelete = i
//                     }
//                     i++
//                 }
//                 if (formtodelete > -1) {
//                     theForm.splice(formtodelete, 1)
                    
//                     var form_update = await centaur.form.updateOne({ _id: horse_form._id }, {
//                         "$set": {form : theForm}
//                     }).lean()
//                     console.log(`horse ${horse} empty form item array cleaned`)
//                 }
               
                
//             }


//         }

//     } catch (err){

//     }
// }

// // ## THIS IS THE ONE FOR REORDERING AND DAYS SINCE LAST CALCULATING ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({horse_id:horse}).lean()
            
//             if (horse_form && horse_form.form && horse_form.form[0]){
                
//                 var theForm = horse_form.form

//                 theForm.sort((a,b) => a.meeting_date - b.meeting_date)

//                 var checkdate = ''

//                 for (var horseFormItem of theForm){
//                     if (checkdate){
//                         var prevDate = moment(checkdate)
//                         var meetDate = moment(horseFormItem.meeting_date)
//                         horseFormItem.days_since_last_run = meetDate.diff(prevDate, 'days')
//                     }
//                     if (horseFormItem.classes && horseFormItem.classes.class_id  && horseFormItem.classes.class_id != 90){
//                         checkdate = horseFormItem.meeting_date
//                     }
//                 }
                
//                 theForm.sort((a,b) => b.meeting_date - a.meeting_date)
//                 var form_update = await centaur.form.updateOne({ _id: horse_form._id }, {
//                                                     "$set": {form : theForm}
//                                                 }).lean()
//                 console.log(`horse ${horse} form updated`)
//             }


//         }

//     } catch (err){

//     }
// }

// // ## THIS IS THE ONE FOR FIXING SYNTHETIC TRACK RATINGS ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({horse_id:horse}).lean()
            
//             if (horse_form && horse_form.form && horse_form.form[0]){
//                 var updated = false
//                 var theForm = horse_form.form

//                 for (var horseFormItem of theForm){
//                     if (horseFormItem.track && horseFormItem.track['@_track_surface'] == 'Y'){
//                         updated = true
//                         horseFormItem.track['@_grading'] = '0'
//                         horseFormItem.track['@_condition'] = 'Synthetic'
//                     }
//                 }
                
//                 if (updated){
//                     var form_update = await centaur.form.updateOne({ _id: horse_form._id }, {
//                                                         "$set": {form : theForm}
//                                                     }).lean()
//                     console.log(`horse ${horse} form updated`)
//                 }
//             }


//         }

//     } catch (err){

//     }
// }

// // ## THIS IS THE ONE FOR FIXING DEAD TRACK BEING DRY WHEN IT SHOULD BE WET ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var data = await centaur.form.findOne({ horse_id: horse }).lean()
//             if (data){
//                 var cleansedResults = helper.cleanseBarrierTrials(data.form)

//                 // console.log('Results for : ' + data.horse_name + " - " +  data.horse_id)

//                 var ts = 0, s1 = 0, s2 = 0, s3 = 0, s4 = 0, s5 = 0
//                 var dt = 0, d1 = 0, d2 = 0, d3 = 0
//                 var wt = 0, w1 = 0, w2 = 0, w3 = 0

//                 for (run of cleansedResults){
//                     var wet = false
//                     ts++
//                     if (parseInt(run.track['@_grading']) > 4 || run.track['@_condition'] == 'D'){
//                         wt++
//                         wet = true
//                     } else {
//                         dt++
//                     }
//                     if (run.finish_position == 1){
//                         s1++
//                         if (wet){w1++} else {d1++}
//                     } else if (run.finish_position == 2){
//                         s2++
//                         if (wet){w2++} else {d2++}
//                     } else if (run.finish_position == 3){
//                         s3++
//                         if (wet){w3++} else {d3++}
//                     } else if (run.finish_position == 4){
//                         s4++
//                     } else if (run.finish_position == 5){
//                         s5++
//                     }

//                 }

//                 var dataToUpdate = {
//                     HOR_START_TOTAL: ts,
//                     HOR_H_1STS_TOTAL: s1,
//                     HOR_H_2NDS_TOTAL: s2,
//                     HOR_H_3RDS_TOTAL: s3,
//                     HOR_H_4THS_TOTAL: s4,
//                     HOR_H_5THS_TOTAL: s5,
//                     HOR_DRY_START_TOTAL: dt,
//                     HOR_DRY_1ST_TOTAL: d1,
//                     HOR_DRY_2ND_TOTAL: d2,
//                     HOR_DRY_3RD_TOTAL: d3,
//                     HOR_WET_START_TOTAL: wt,
//                     HOR_WET_1ST_TOTAL: w1,
//                     HOR_WET_2ND_TOTAL: w2,
//                     HOR_WET_3RD_TOTAL: w3
//                 }

//             }

//             let status = await centaur.horses.updateOne({ HRN_HORSE_ID: data.horse_id }, {
//                 "$set": dataToUpdate
//             }).lean()
//             console.log(`Horse ${data.horse_id} runs Updated: `)


//         }

//     } catch (err){

//     }
// }

// // ## THIS IS THE ONE FOR FIXING FORM FINISH POSITIONS ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({horse_id:horse}).lean()
            
//             if (horse_form && horse_form.form && horse_form.form[0]){
//                 var updated = false
//                 var theForm = horse_form.form

//                 for (var horseFormItem of theForm){
//                     if (["FF","PU","FL","RO","DQ","NP","LS","LR","SB","SC","BD","UN"].includes(horseFormItem.finish_position)){
//                         var new_finish = setFinishPosition(horseFormItem.finish_position)
//                         horseFormItem.finish_position = new_finish
//                         horseFormItem.finish_position = new_finish
//                         var form_index_update = await centaur.form_index.updateOne({ horse_id: horse, event_id: horseFormItem.event_id}, {
//                             "$set": {finish_pos : new_finish}
//                         }).lean()
//                         console.log(`horse ${horse} run ${horseFormItem.event_id} updated with pos ${new_finish}`)
//                         console.log(form_index_update)
//                         updated = true
//                     }
//                 }
                
//                 if (updated){
//                     var form_update = await centaur.form.updateOne({ _id: horse_form._id }, {
//                                                         "$set": {form : theForm}
//                                                     }).lean()
//                     console.log(`horse ${horse} form updated`)
//                 }
//             }


//         }

//     } catch (err){

//     }
// }

// // ## THIS IS THE ONE FOR CHECKING NUMBER OF FORM ITEMS VS NUMBER OF FORM INDEX ITEMS ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({horse_id:horse}).lean()
            
//             if (horse_form && horse_form.form && horse_form.form[0]){
//                 var theFormLength = horse_form.form.length
//                 var horse_form_index = await centaur.form_index.find({horse_id:horse}).lean()
//                 if (theFormLength != horse_form_index.length){
//                     console.log(`${horse_form.horse_name},${horse},${theFormLength},${horse_form_index.length}`)
//                 }
                
                
                
//             }


//         }

//     } catch (err){

//     }
// }

// // ## THIS IS THE ONE FOR DELETING DUPLICATE FORM INDEX ITEMS ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form_index = await centaur.form_index.find({horse_id:horse}).lean()
            
//             horse_form_index.sort((a,b) => a.meeting_date - b.meeting_date)

//             var delete_ids = []
//             i = -1
//             for (form_index of horse_form_index){
//                 if (i > -1 && form_index.meeting_date.toString() == horse_form_index[i].meeting_date.toString()){
//                     if (horse_form_index[i].track_id == form_index.track_id &&
//                         horse_form_index[i].event_id == form_index.event_id &&
//                         horse_form_index[i].finish_pos == form_index.finish_pos &&
//                         horse_form_index[i].jockey_id == form_index.jockey_id &&
//                         horse_form_index[i].meeting_id == form_index.meeting_id &&
//                         horse_form_index[i].race_no == form_index.race_no &&
//                         horse_form_index[i].trainer_id == form_index.trainer_id &&
//                         horse_form_index[i].weight_carried == form_index.weight_carried
//                         ){
//                             delete_ids.push(form_index._id)
//                         } else if (horse_form_index[i].event_id == form_index.event_id) {
//                             console.log(`something fucky about ${horse} index event ${form_index.event_id} on ${form_index.meeting_date}`)
//                         } else{
//                             console.log(`${horse} ran twice? on ${form_index.meeting_date}`)
//                         }
//                 }
//                 i++
//             }
//             // console.log(delete_ids)
//             if (delete_ids.length == 0){
//                 console.log(`${horse} found no runs to delete`)
//             }
//             for (id of delete_ids){
//                 var form_index_delete = await centaur.form_index.deleteOne({_id:id}).lean() 
//             }
//             // console.log(`${horse} extra runs deleted`)
//         }

//     } catch (err){
//         console.log(err)
//     }
// }

// // ## THIS IS THE ONE FOR ADDING GEAR CHANGE IDs ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({horse_id:horse}).lean()
            
//             if (horse_form && horse_form.form && horse_form.form[0]){
//                 var updated = false
//                 var theForm = horse_form.form

//                 for (var horseFormItem of theForm){
//                     if (horseFormItem.gear_changes && horseFormItem.gear_changes.gear_change && horseFormItem.gear_changes.gear_change.length > 0 ){
//                         updated = true
//                         for (var gear_item of horseFormItem.gear_changes.gear_change){
//                             gear_item['@_id'] = getGearID(gear_item['@_name'])
//                             if (gear_item['@_id'] === 0){
//                                 console.log(horse)
//                             }
//                         }
//                     }
//                 }
                
//                 if (updated){
//                     var form_update = await centaur.form.updateOne({ _id: horse_form._id }, {
//                                                         "$set": {form : theForm}
//                                                     }).lean()
//                     // console.log(`horse ${horse} form updated`)
//                 }
//             }


//         }

//     } catch (err){

//     }
// }

// // ## THIS IS THE ONE FOR ADDING EMPTY ARRAYS INSTAD OF NO ARRAYS ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({horse_id:horse}).lean()
            
//             if (horse_form && !horse_form.form){
//                     var form_update = await centaur.form.updateOne({ _id: horse_form._id }, {
//                                                         "$set": {form : []}
//                                                     }).lean()
//                     // console.log(`horse ${horse} form updated`)
//                 }
//         }


        

//     } catch (err){

//     }
// }

// // ## THIS IS THE ONE FOR DOWNLOADING FORM IN BULK ##
// const processFormRatings = async (ratFileContent) => {
//     var horseCount = 0
//     var AWS = require('aws-sdk');
//     var filename = ''
//     try{
//         var horseArray = []
//         for (horse of ratFileContent.horses){
            
//             var horse_form = await centaur.form.findOne({horse_id:horse}).lean()
//             horseArray.push(horse_form)
//             horseCount++
//             if (horseCount > 2500 ){
//                 filename = filename + '-' + horse.toString()
//                 try{
//                     // console.log('lets try send to s3')
//                     let S3 = new AWS.S3({ region: process.env.AWS_REGION });
//                     var params = {
//                         Bucket: 'centaur-supplier-storage-stg',
//                         Key: filename+'.json',
//                         Body: JSON.stringify(horseArray),
//                         ContentType: 'application/json',
//                     };

//                     let s3Response = await S3.upload(params).promise();

//                     let res = {
//                         'statusCode': 200,
//                         'headers': { 'Content-Type': 'application/json' },
//                         'body': JSON.stringify({
//                             "id": filename,
//                             "s3Path":s3Response.Location
//                         })
//                     }
                    
//                     console.log(res)
//                 } catch(err){
//                     console.log(`error sending to s3 ${err}`)
//                 }
//                 horseArray = []
//                 horseCount = 0
//                 filename = ''
//             }else if (horseCount == 1){
//                 filename = horse.toString()
//             }
//         }
//         try{
//             // console.log('lets try send to s3')
//             let S3 = new AWS.S3({ region: process.env.AWS_REGION });
//             var params = {
//                 Bucket: 'centaur-supplier-storage-stg',
//                 Key: filename+'.json',
//                 Body: JSON.stringify(horseArray),
//                 ContentType: 'application/json',
//             };

//             let s3Response = await S3.upload(params).promise();

//             let res = {
//                 'statusCode': 200,
//                 'headers': { 'Content-Type': 'application/json' },
//                 'body': JSON.stringify({
//                     "id": filename+'-'+ratFileContent.horses[ratFileContent.horses.length-1].toString()+'.json',
//                     "s3Path":s3Response.Location
//                 })
//             }
            
//             console.log(res)
//         } catch(err){
//             console.log(`error sending to s3 ${err}`)
//         }

        

//     } catch (err){

//     }
// }

// // ## THIS IS THE ONE FOR DOWNLOADING HORSE COLLECTION IN BULK ##
// const processFormRatings = async (ratFileContent) => {
//     var horseCount = 0
//     var AWS = require('aws-sdk');
//     var filename = ''
//     try{
//         var horseArray = []
//         for (horse of ratFileContent.horses){
            
//             var horse_entry = await centaur.horses.findOne({HRN_HORSE_ID:horse}).lean()
//             if (horse_entry) {
//                 var horse_sire = await centaur.horses.findOne({HRN_HORSE_ID:horse_entry.HOR_SIRE_ID}).lean()
//                 if (!horse_sire){
//                     horse_sire = {
//                         HRN_HORSE_NAME: '',
//                         HOR_FOALING_DATE: '',
//                         HOR_SEX: '',
//                         HOR_COUNTRY_OF_ORIGIN: ''
//                     }
//                 }
//                 var horse_dam = await centaur.horses.findOne({HRN_HORSE_ID:horse_entry.HOR_DAM_ID}).lean()
//                 if (!horse_dam){
//                     horse_dam = {
//                         HRN_HORSE_NAME: '',
//                         HOR_FOALING_DATE: '',
//                         HOR_SEX: '',
//                         HOR_COUNTRY_OF_ORIGIN: ''
//                     }
//                 }
//                 var horse_cleaned = {
//                     "horse_id":horse,
//                     "horse_name":horse_entry.HRN_HORSE_NAME,
//                     "horse_foaling_date":horse_entry.HOR_FOALING_DATE,
//                     "horse_sex":horse_entry.HOR_SEX,
//                     "horse_country":horse_entry.HOR_COUNTRY_OF_ORIGIN,
//                     "sire_id":horse_entry.HOR_SIRE_ID,
//                     "sire_name":horse_sire.HRN_HORSE_NAME,
//                     "sire_foaling_date":horse_sire.HOR_FOALING_DATE,
//                     "sire_sex":horse_sire.HOR_SEX,
//                     "sire_country":horse_sire.HOR_COUNTRY_OF_ORIGIN,
//                     "dam_id":horse_entry.HOR_DAM_ID,
//                     "dam_name":horse_dam.HRN_HORSE_NAME,
//                     "dam_foaling_date":horse_dam.HOR_FOALING_DATE,
//                     "dam_sex":horse_dam.HOR_SEX,
//                     "dam_country":horse_dam.HOR_COUNTRY_OF_ORIGIN
//                 }
//                 horseArray.push(horse_cleaned)
//                 horseCount++
//             } else {
//                 console.log(`horse ${horse} not found`)
//             }
//             if (horseCount > 2500 ){
//                 filename = filename + '-' + horse.toString()
//                 try{
//                     // console.log('lets try send to s3')
//                     let S3 = new AWS.S3({ region: process.env.AWS_REGION });
//                     var params = {
//                         Bucket: 'centaur-supplier-storage-stg',
//                         Key: filename+'.json',
//                         Body: JSON.stringify(horseArray),
//                         ContentType: 'application/json',
//                     };

//                     let s3Response = await S3.upload(params).promise();

//                     let res = {
//                         'statusCode': 200,
//                         'headers': { 'Content-Type': 'application/json' },
//                         'body': JSON.stringify({
//                             "id": filename,
//                             "s3Path":s3Response.Location
//                         })
//                     }
                    
//                     console.log(res)
//                 } catch(err){
//                     console.log(`error sending to s3 ${err}`)
//                 }
//                 horseArray = []
//                 horseCount = 0
//                 filename = ''
//             }else if (horseCount == 1){
//                 filename = horse.toString()
//             }
//         }
//         try{
//             // console.log('lets try send to s3')
//             let S3 = new AWS.S3({ region: process.env.AWS_REGION });
//             var params = {
//                 Bucket: 'centaur-supplier-storage-stg',
//                 Key: filename+'.json',
//                 Body: JSON.stringify(horseArray),
//                 ContentType: 'application/json',
//             };

//             let s3Response = await S3.upload(params).promise();

//             let res = {
//                 'statusCode': 200,
//                 'headers': { 'Content-Type': 'application/json' },
//                 'body': JSON.stringify({
//                     "id": filename+'-'+ratFileContent.horses[ratFileContent.horses.length-1].toString()+'.json',
//                     "s3Path":s3Response.Location
//                 })
//             }
            
//             console.log(res)
//         } catch(err){
//             console.log(`error sending to s3 ${err}`)
//         }

        

//     } catch (err){
//         console.log(err)
//     }
// }

// // ## THIS IS THE ONE FOR ADDING MULTI CLASSES TO FORM ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         const raceclasses = await centaur.race_classes.find().lean()
//         var allclasses = {}
//         for (var raceclass of raceclasses){
//             allclasses[raceclass.CLA_CLASS_DB_ID.toString()] = raceclass.CLA_CLASS_LONG_DISP
//         }
//         for (var the_race of ratFileContent.horses){
//             var index_form = await centaur.form_index.find({event_id:the_race.EVC_EVENT_ID}).lean()
//             if (!index_form[0]){
//                 console.log(`form race not found ${the_race.EVC_EVENT_ID}`)
//                 continue
//             }
//             var the_classes_object = {}
//             if (moment(index_form[0].meeting_date) > moment('2022-09-15')){
//                 var p_meeting =  await centaur.processed_meetings.findOne({_id:index_form[0].meeting_id})
//                 if (!p_meeting || !p_meeting.processedMeetingData){
//                     console.log(`Meeting from form_index not found: ${index_form[0].meeting_id}`)
//                     continue
//                 }
//                 var raceIndex = 0
//                 for (var dbrace of p_meeting.processedMeetingData.meeting.races.race){
//                     if (dbrace['@_id'] == the_race.EVC_EVENT_ID){
//                         if (dbrace.classes){
//                             the_classes_object = calcMultipleClasses(the_race,dbrace.classes,allclasses)
//                             let raceToUpdate = "processedMeetingData.meeting.races.race." + raceIndex + '.classes'
//                              var resp_updateMeetingData = await centaur.processed_meetings.updateOne({ _id: index_form[0].meeting_id }, {
//                                 "$set": {
//                                     [raceToUpdate]: the_classes_object
//                                 }
//                             }).lean()
//                         }

//                         break
//                     }
//                     raceIndex++
//                 }
//             } else {
//                 var formhorse = await centaur.form.findOne({horse_id:index_form[0].horse_id}).lean()
//                 for (item of formhorse.form){
//                     if (item.event_id == the_race.EVC_EVENT_ID){
//                         if (item.classes){
//                             the_classes_object = calcMultipleClasses(the_race,item.classes,allclasses)
//                             break
//                         }
//                     }
//                 }
//             }
//             // console.log(the_classes_object)
//             if (!the_classes_object.class_id){
//                 console.log(`somehow came out without proper class object for item ${the_race.EVC_EVENT_ID} ${the_race.EVC_CLASS_ORDER}`)
//                 continue
//             }

//             for (var horse of index_form){
//                 var formhorse = await centaur.form.findOne({horse_id:horse.horse_id}).lean()
//                 var indexForm = 0
//                 if (!formhorse){
//                     console.log(`couldnt find: ${horse.horse_id}`)
//                     continue
//                 }
//                 if (!formhorse.form){
//                     console.log(formhorse)
//                     console.log(the_race)
//                     continue
//                 }
//                 for (item of formhorse.form){
//                     if (item.event_id == the_race.EVC_EVENT_ID){
//                         let dataToUpdate = "form."+indexForm+".classes"
//                         var resp_updateFormData = await centaur.form.updateOne({ _id: formhorse._id }, {
//                             "$set": {
//                                 [dataToUpdate]: the_classes_object
//                             }
//                         }).lean()
//                         break
//                     }
//                     indexForm++
//                     if (indexForm == formhorse.form.length) console.log(`couldnt find ${the_race.EVC_EVENT_ID} in horse ${horse.horse_id}`)
//                 }
//             }
//         }
        


        

//     } catch (err){
//         console.log(`trouble adding multi classes: ${err}`)
//     }
// }

// // ## THIS IS THE ONE FOR FIXING TIMEZONES ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({horse_id:horse}).lean()
            
//             if (horse_form && horse_form.form && horse_form.form.length > 0){

//                 var haschanged = false
//                 for (item of horse_form.form){
//                     var meeting_date = moment(item.meeting_date)
//                     if ([9,10,11,12,13,14,15,16,17,18].includes(meeting_date.hour())){
//                         meeting_date.hour(0)
//                         meeting_date.day(meeting_date.day()+1)
//                         item.meeting_date = new Date(meeting_date.toISOString())
//                         haschanged = true
//                     }
//                 }
//                 if (haschanged) {
//                     var form_update = await centaur.form.updateOne({ _id: horse_form._id }, {
//                                                         "$set": {form : horse_form.form}
//                                                     }).lean()
//                     console.log(`horse ${horse_form.horse_name} ${horse} has had some form run timezones fixed`)
//                 }
                
//             }
//         }


        

//     } catch (err){
//         console.log(err)
//     }
// }

// // ## THIS IS THE ONE FOR CHECKING HORSES HAVE FORM ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (horse of ratFileContent.horses){
//             var horse_form = await centaur.form.findOne({horse_id:horse}).lean()
            
//             if (horse_form && !horse_form.form || horse_form.form.length < 1){
//                     // var form_update = await centaur.form.updateOne({ _id: horse_form._id }, {
//                     //                                     "$set": {form : []}
//                     //                                 }).lean()
//                     console.log(`horse ${horse_form.horse_name} ${horse} has index but no form`)
//                 }
//         }


        

//     } catch (err){

//     }
// }

// // ## THIS IS THE ONE FOR FINDING GROUP RACES ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         // for (horse of ratFileContent.horses){
//         //     var horse_form = await centaur.form.findOne({horse_id:horse}).lean()
            
//         //     if (horse_form && !horse_form.form || horse_form.form.length < 1){
//         //             // var form_update = await centaur.form.updateOne({ _id: horse_form._id }, {
//         //             //                                     "$set": {form : []}
//         //             //                                 }).lean()
//         //             console.log(`horse ${horse_form.horse_name} ${horse} has index but no form`)
//         //         }
//         // }
//         var allmeetings = await centaur.processed_meetings.find().lean()
//         for (var meeting of allmeetings){
//             if (meeting.processedMeetingData.meeting.tab_indicator != 'T') continue
//             var races = meeting.processedMeetingData.meeting.races.race
//             for (var race of races){
//                 if (race.group !== ''){
//                     console.log(`${meeting._id},${meeting.meetingDate},${meeting.processedMeetingData.meeting.track['@_name']},${race['@_id']},${race['@_number']},${race.group}`)
//                 }
//             }
//         }


        

//     } catch (err){

//     }
// }

// // ## THIS IS THE ONE FOR ADDING TROPHY PRIZEMONEY ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (var the_race of ratFileContent.horses){
//             var index_form = await centaur.form_index.find({event_id:the_race.EVP_EVENT_ID}).lean()
//             var prizemoney_trophy_total = parseInt(the_race.EVP_TROPHYPRIZE) + parseInt(the_race.EVP_TOTALPRIZE)
//             if (!index_form[0]){
//                 console.log(`form race not found ${the_race.EVP_EVENT_ID}`)
//                 continue
//             }

//             for (var horse of index_form){
//                 var formhorse = await centaur.form.findOne({horse_id:horse.horse_id}).lean()
//                 var indexForm = 0
//                 if (!formhorse){
//                     console.log(`couldnt find: ${horse.horse_id}`)
//                     continue
//                 }
//                 if (!formhorse.form){
//                     console.log(`${horse.horse_id} doesnt seem to have a form item`)
//                     console.log(formhorse)
//                     console.log(the_race)
//                     continue
//                 }
//                 for (item of formhorse.form){
//                     if (item.event_id == the_race.EVP_EVENT_ID){
//                         let dataToUpdate = "form."+indexForm+".event_prizemoney"
//                         var resp_updateFormData = await centaur.form.updateOne({ _id: formhorse._id }, {
//                             "$set": {
//                                 [dataToUpdate]: prizemoney_trophy_total
//                             }
//                         }).lean()
//                         break
//                     }
//                     indexForm++
//                     if (indexForm == formhorse.form.length) console.log(`couldnt find ${the_race.EVC_EVENT_ID} in horse ${horse.horse_id}`)
//                 }
//             }
//         }
//     } catch (err){
//         console.log(`trouble adding trophy prizemoney: ${err}`)
//     }
// }

// // ## THIS IS THE ONE FOR BULK FIXING BARRIER TRIAL CLASSES AND RE-APPROVING RESULTS  ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (the_meet of ratFileContent.horses){
//             var meeting = await centaur.processed_meetings.findOne({ _id: the_meet }).lean()
//             if (!meeting){ continue }
            
//             for (race of meeting.processedMeetingData.meeting.races.race){
//                 console.log(race.classes)
//                 if (race.classes && race.classes.class_id == 0){
//                     race.classes.class_id = 90
//                 }
//             }
//             var upd_meeting = await centaur.processed_meetings.updateOne({ _id: the_meet },{"$set":meeting}).lean()
//             var lambda = new AWS.Lambda();
//             var payload = {
//                 path: '/processedmeetings',
//                 method: 'PUT',
//                 body: {
//                     meetingId: the_meet, 
//                     level: "approve_results"
//                 }
//             }
            

//             var params = {
//                 FunctionName: "MrCenLambdaApi-" + process.env.ENV,
//                 InvocationType: 'Event',
//                 Payload:  JSON.stringify(payload) ,
//             };
//             var res_4 = await lambda.invoke(params).promise();
//             console.log(`meeting approved ${the_meet}`)
//             console.log(res_4)
            
//             sleep(5000)
//         }
                    

//     }catch(err){
//         console.log('Error loading ratings file: ',err)
//         return false
//     }
//     return true

// }

// // ## THIS IS THE ONE FOR ADDING METRO TRACK STATUS TO FORM ##
// const processFormRatings = async (ratFileContent) => {
//     try{
//         for (var the_race of ratFileContent.horses){
//             var index_form = await centaur.form_index.find({event_id:the_race}).lean()
//             if (!index_form[0]){
//                 console.log(`((ERROR)): form race not found in form index ${the_race.EVP_EVENT_ID}`)
//                 continue
//             }

//             for (var horse of index_form){
//                 var formhorse = await centaur.form.findOne({horse_id:horse.horse_id}).lean()
//                 var indexForm = 0
//                 if (!formhorse){
//                     console.log(`((ERROR)): couldnt find: ${horse.horse_id} in the form table`)
//                     continue
//                 }
//                 if (!formhorse.form){
//                     console.log(`((ERROR)): ${horse.horse_id} doesnt seem to have any form items`)
//                     // console.log(formhorse)
//                     // console.log(the_race)
//                     continue
//                 }
//                 for (item of formhorse.form){
//                     if (item.event_id == the_race){
//                         let dataToUpdate = "form."+indexForm+".track.@_location"
//                         var resp_updateFormData = await centaur.form.updateOne({ _id: formhorse._id }, {
//                             "$set": {
//                                 [dataToUpdate]: 'M'
//                             }
//                         }).lean()
//                         break
//                     }
//                     indexForm++
//                     if (indexForm == formhorse.form.length) console.log(`((ERROR)): couldnt find ${the_race.EVC_EVENT_ID} in horse ${horse.horse_id}`)
//                 }
//             }
//         }
//     } catch (err){

//         console.log(`((ERROR)): trouble adding metro status: ${err}`)
//     }
// }


// // ## THIS IS THE ONE FOR ADDING MISSING FORM ##
// const processFormRatings = async (ratFileContent) => {
//     const normalize = require("./normalize")

//     try{
//         for (const horse in ratFileContent.horses){
//             var horse_id = parseInt(horse)
//             var newform = ratFileContent.horses[horse]
//             // var horse_form = await centaur.form.findOne({horse_id:horse_id}).lean()
            
//             for (item of newform){
//                 var classes = {}
//                 if (item.classes.class[0].length == 1){
//                     classes.class_id = await normalize.getClassByName(item.classes.class)
//                     classes.class = item.classes.class
//                 } else {
//                     classes.class_id = await normalize.getClassByName(item.classes.class[0])
//                     classes.class = item.classes.class[0]
//                     if (item.classes.class[1]){
//                         classes.second_class_id = await normalize.getClassByName(item.classes.class[1])
//                         classes.second_class = item.classes.class[1]
//                         if (item.classes.class[2]){
//                             classes.third_class_id = await normalize.getClassByName(item.classes.class[2])
//                             classes.third_class = item.classes.class[2]
//                         }
//                     } 
//                 }
//                 item.classes = classes
//                 if (item.restrictions && item.restrictions['@_age']){
//                     item.restrictions['@_age'] = helper.setAgeRestriction(item.restrictions['@_age'])
//                 }
//                 if (item.restrictions && item.restrictions['@_sex']){
//                     item.restrictions['@_sex'] = helper.setSexRestriction(item.restrictions['@_sex'])
//                 }
//                 if (item.event_id) item.event_id = item.event_id.toString()
//                 if (item.race && item.race['@_number']) item.race['@_number'] = item.race['@_number'].toString()
//                 if (item.distance && item.distance['@_metres']) item.distance['@_metres'] = item.distance['@_metres'].toString()
//                 if (item.margin && helper.isNumeric(item.margin)) item.margin = parseFloat(item.margin)
//                 var or_index = 0
//                 for (other_runner in item.other_runners.other_runner){
//                     if (other_runner['@_position'] && helper.isNumeric(other_runner['@_position'])) item.other_runners.other_runner[or_index]['@_position'] = parseInt(other_runner['@_position'])
//                     if (other_runner['@_weight'] && helper.isNumeric(other_runner['@_weight'])) item.other_runners.other_runner[or_index]['@_weight'] = parseFloat(other_runner['@_weight'])
//                 }
//                 if (item.official_margin_1 && helper.isNumeric(item.official_margin_1)) item.official_margin_1 = item.official_margin_1.toString()
//                 if (item.official_margin_2 && helper.isNumeric(item.official_margin_2)) item.official_margin_2 = item.official_margin_2.toString()
                
                
//                 item.meeting_date = moment(item.meeting_date, 'DD/MM/YYYY').toISOString()
//                 item.event_id = helper.generateUniqueRaceId(item.meeting_date,item.track['@_id'],item.race['@_number'],(classes.class_id == 90 ? 1 : 0))
//                 item.method = 'PUT'
//                 var lambda = new AWS.Lambda();
//                 console.log(item)
//                 var payload = {
//                     path: '/horse-form',
//                     body: item
//                 }
//                 payload.body.horse_id = horse_id
//                 console.log(payload)
//                 var params = {
//                     FunctionName: "MrCenLambdaApi-" + process.env.ENV,
//                     InvocationType: 'Event',
//                     Payload:  JSON.stringify(payload),
//                 };
//                 var res_3 = await lambda.invoke(params).promise();
//                 console.log(res_3)
//                 helper.sleep(2000)
//             }
            
//         }


        

//     } catch (err){
//         console.log(err)
//     }
// }