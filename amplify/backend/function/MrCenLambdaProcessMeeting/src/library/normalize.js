const moment = require("moment");
const helper = require("./helper");
// Import Models
const centaur = require("@mediality/centaur");

const normalizeMeetingData_AUS = async (
  rawData,
  meetingId,
  meetingStage,
  country,
  category
) => {
  /*---Start Creating Meeting Level data---*/
  console.log("normalizeMeetingData_AUS", JSON.stringify(category));
  rawData = rawData.Meeting;
  var errors = [];
  try {
    var trial_file = category == "trial" ? "_T" : "";
    const product_directory =
      "XML_" +
      (meetingStage.substring(0, 1) == "F"
        ? "A"
        : meetingStage.substring(0, 1)) +
      trial_file; // Hard Coded
    const product_track = rawData.Track["@_VenueAbbr"];
    const meetingDateUTC = helper.convertToUTC(rawData.MeetDate);
    const RA_meeting_ID = rawData["@_MeetCode"];
    console.log('RA_meeting_ID', RA_meeting_ID);
    var track_data = await centaur.tracks
      .findOne({ TRK_RSB_TRACK_ABBREV: helper.upperCase(product_track) })
      .lean();
    if (!track_data) {
      console.log(
        `((ERROR)): Meeting Level - Track Not Found - ${product_track}. aborting.`
      );
      // console.log('Stopping Execution - Track Not Found')
      return { eventStatus: "notProcessing" };
      // process.exit(1) //Stopping execution as Track Information is missing
    } else {
      var club_name = "",
        club_id = "",
        club_code = "",
        club_state = "";
      club_code = rawData.Club["@_ClubCode"];
      // changing vic amateur TC to Melbourne RC
      if (club_code == "10054") {
        club_code = "10100";
      }

      const club_data = await centaur.clubs
        .findOne({ CLB_CLUB_ID: club_code })
        .lean();
      if (!club_data) {
        // console.log('Meeting Level - Club Not Found')
        errors.push(`Meeting Level - Club Not Found - ${club_code}`);
      } else {
        club_name = club_data.CLB_CLUB_NAME;
        club_id = club_data.CLB_CLUB_ID;
        club_state = club_data.CLB_STATE;
      }

      const product_date = helper.getDayMonth(rawData.MeetDate);
      var trial_file = category == "trial" ? "_T" : "";
      const product_file =
        track_data.TRK_TRACK_3CHAR_ABBREV + product_date + trial_file + ".XML";

      let expected_condition = "";
      if (rawData.Track.TrackRating == "Synthetic") {
        expected_condition = "Y0";
      } else if (rawData.Track.TrackRating["@_NumericTrackRating"]) {
        expected_condition = helper.processTrackRating(
          rawData.Track.TrackRating["@_NumericTrackRating"]
        );
      }

      if (helper.getDualTrack(rawData.DualTrack) === "Y") {
        errors.push(
          `Meeting file contains dual track make sure race level venue abbreviations are correct`
        );
      }
      var record_meeting = {
        meeting: {
          product: {
            "@_directory": product_directory,
            "@_track": track_data.TRK_TRACK_3CHAR_ABBREV,
            "@_date": product_date,
            "@_file": product_file,
          },
          stage: rawData.MeetingStage,
          date: helper.convertDate(rawData.MeetDate),
          rail_position: rawData.Track.RailPosition,
          tab_indicator:
            category == "trial" ? "Trial" : rawData.TabStatus ? "T" : "N",
          ra_meeting_id: RA_meeting_ID,
          dual_track: helper.getDualTrack(rawData.DualTrack),
          track: {
            "@_name": track_data.TRK_TRACK_NAME,
            "@_id": track_data.TRK_TRACK_DB_ID,
            "@_expected_condition": expected_condition,
            "@_club": club_name,
            "@_track_surface": track_data.TRK_TRACK_SURFACE_TYPE,
            "@_location": rawData.MeetingType.substring(0, 1),
            "@_country": track_data.TRK_COUNTRY_OF_TRACK,
            "@_state": rawData.Club["@_State"],
            "@_track_3char_abbrev": track_data.TRK_TRACK_3CHAR_ABBREV,
            "@_track_4char_abbrev": track_data.TRK_RSB_TRACK_ABBREV,
            "@_track_6char_abbrev": track_data.TRK_TRACK_6CHAR_ABBREV,
            "@_night_meeting": rawData.DayNight == "Night" ? "N" : "",
            penetrometer: rawData.Track.Penetrometer ?? "",
            irrigation: rawData.Track.Irrigation ?? "",
            rainfall: rawData.Track.Rainfall ?? "",
            racing_direction: rawData.Track.RacingDirection ?? "",
            weather: {
              "@_code": rawData.Track.Weather.substring(0, 1) ?? "",
              "@_description": rawData.Track.Weather ?? "",
            },
          },
          errors: errors,
          races: {
            race: [],
          },
        },
      };
    }
    /*---End Creating Meeting Level data---*/
    const meetingExists = await centaur.processed_meetings
      .findOne({ _id: meetingId })
      .lean();
    var existingRaceArray = [];
    var silksReady = false;
    // get the existing raceMap from the inputData if there is one,
    // we will use this to make sure all races continue to match correctly through meeting stages
    let raceMap = [];
    if (
      meetingExists &&
      meetingExists.inputMeetingData &&
      meetingExists.inputMeetingData.raceMap
    ) {
      raceMap = meetingExists.inputMeetingData.raceMap;
    }
    // set all racemap items to not updated,
    // we will set them to updated if the new file contains matching races
    for (let race of raceMap) race.updated = false;
    if (meetingExists && !["DELETED"].includes(meetingExists.meetingStage)) {
      if (meetingExists.inputMeetingData.silksReady) {
        silksReady = true;
      }
      existingRaceArray = meetingExists.processedMeetingData.meeting.races.race;
    }
    record_meeting.meeting.silksReady = silksReady;
    console.log(raceMap);
    console.log(existingRaceArray);
    // Build the new races array from the input file,
    // skipping if incomplete trial result.
    var races = [];
    if (
      rawData.Races &&
      rawData.Races.Race &&
      typeof rawData.Races.Race[Symbol.iterator] === "function"
    ) {
      for (const race of rawData.Races.Race) {
        if (category != "trial" || race["@_CurrentRaceStage"] != "Abandoned")
          races.push(race);
        if (
          category == "trial" &&
          ["Acceptances", "FinalFields", "Final Fields"].includes(
            race["@_CurrentRaceStage"]
          )
        ) {
          console.log(`Not processing partial trial results. aborting.`);
          // console.log('Stopping Execution - Track Not Found')
          return { eventStatus: "notProcessing" };
        }
      }
      // races = rawData.Races.Race
    } else if (rawData.Races && rawData.Races.Race) {
      races.push(rawData.Races.Race);
    }

    // // Generate Empty Races Array , it contains many races---*/
    // var raceArray = [];
    // var emptyRaceArray = [];
    // var tempRace = {};
    // for (let i = 0; i < races.length; i++) {
    //   var rawRace = races[i];
    //   if (races.length < rawRace["@_RaceNumber"] || category == "trial") {
    //     tempRace = {
    //       "@_number": i + 1,
    //     };
    //   } else {
    //     tempRace = {
    //       "@_number": rawRace["@_RaceNumber"],
    //        "NominationsRaceNumber": rawRace.NominationsRaceNumber || (i + 1).toString(),
    //        "NominationsDivisor": rawRace.NominationsDivisor || "0"
    //     };
    //   }

    //   emptyRaceArray.push(tempRace);
    // }
    // // } /*---End Creating Races data---*/

    // raceArray = raceArray.length > 0 ? raceArray : emptyRaceArray;
    // record_meeting.meeting.races.race = raceArray;
    let usedNominationNumbers = new Map();
    let i = 1;
    let highestIndex = 0;
    mapLoop: for (let race of races) {
      for (let mapRace of raceMap) {
        // match races with existing races based on race["@_RaceCode"] from input map
        // against mapRace.ra_id and then stored in inputMeetingData.raceMap
        if (parseInt(race["@_RaceNumber"]) > highestIndex)
          highestIndex = parseInt(race["@_RaceNumber"]);
        if (mapRace.ra_id === race["@_RaceCode"]) {
          usedNominationNumbers.set(mapRace.nom_num, true);

          mapRace.index = race["@_RaceNumber"];
          mapRace.updated = true;
          continue mapLoop;
        }
      }
      let nomsNumber = 0;
      if (category == "trial") {
        // Gen nominations numbers for trial races, just race index
        nomsNumber = i;
        i++;
      } else {
        // Gen nominations numbers for new races
        nomsNumber = race["NominationsRaceNumber"];
        if (race["NominationsDivisor"] && race["NominationsDivisor"] > 1) {
          nomsNumber =
            30 +
            parseInt(race["NominationsRaceNumber"]) +
            race["NominationsDivisor"] * 10;
        }
        while (
          usedNominationNumbers.has(nomsNumber) ||
          raceMap.some((r) => r.nom_num === nomsNumber)
        ) {
          nomsNumber = 12 + race["NominationsRaceNumber"];
        }
      }
      usedNominationNumbers.set(nomsNumber, true);
      race.NominationsRaceNumber = nomsNumber;
      raceMap.push({
        ra_id: race["@_RaceCode"],
        nom_num: nomsNumber,
        index: race["@_RaceNumber"],
        updated: true,
      });
    }
    console.log("finalised map", raceMap);
    raceMap.sort((a, b) => a.index - b.index);
    for (const race of raceMap) {
      // if race number clashes with existing race number, put it at the end
      if (!race.updated) {
        for (const mapRace of raceMap) {
          if (race.index === mapRace.index && mapRace.updated) {
            race.index = highestIndex;
            highestIndex++;
          }
        }
      }
    }
    raceMap.sort((a, b) => a.index - b.index);
    let raceDataArray = { races: [] };
    let inputRaceArray = [];
    // loop through all races, old and new, deciding whether to keep the existing race or reprocess
    console.log(raceMap);
    createRaceArrays: for (let race of raceMap) {
      if (race.updated) {
        record_meeting.meeting.races.race.push(race);
        raceDataArray.races.push({
          ra_id: race.ra_id,
          index: race.index,
          meetingId: meetingId,
          stage: "genRaceData",
          meetingStage: meetingStage,
          country: country,
          category: category,
          NominationNumber: race.nom_num,
        });
      } else {
        for (let existRace of existingRaceArray) {
          if (existRace["@_ra_id"] === race.ra_id) {
            existRace.errors.push(
              "Race not updated with latest file, please check."
            );
            existRace["@_number"] = race.index;
            record_meeting.meeting.races.race.push(existRace);
            break;
          }
        }
      }
      if (meetingExists && meetingExists.inputMeetingData)
        for (let inputRace of meetingExists.inputMeetingData.races.race) {
          if (inputRace["@_ra_id"] === race.ra_id) {
            inputRace["@_number"] = race.index;
            inputRaceArray.push(inputRace);
            continue createRaceArrays;
          }
        }
      inputRaceArray.push({
        "@_ra_id": race.ra_id,
        "@_number": race.index,
      });
    }

    console.log(raceDataArray.races);
    console.log(inputRaceArray);

    //Generate RaceArray for InputMeetingData Section
    var inputMeetingData = {
      raceMap: raceMap,
      races: {
        race: inputRaceArray,
      },
      silksReady: silksReady,
    };

    var normalizedData = {
      record_meeting: record_meeting,
      meetingDate: meetingDateUTC,
      meetingStage: meetingStage,
      meetingState: helper.getStateName(track_data.TRK_STATE_OF_TRACK),
      raceDataArray: raceDataArray,
      inputMeetingData: inputMeetingData,
    };
    return normalizedData;
  } catch (error) {
    var sendMail = await helper.mailAlert(
      "AUS Meeting Level Load error",
      `AUS Meeting Data couldnt generate ${meetingId} ${
        rawData.Track["@_VenueAbbr"] ?? "unknown track"
      }, ${rawData.MeetDate ?? "unknown date"}. ${error}`,
      "alert"
    );
    console.log(sendMail);
    console.log(
      `((ERROR)): Error in creating meeting level data ${meetingId} ${
        rawData.Track["@_VenueAbbr"] ?? "unknown track"
      }, ${rawData.MeetDate ?? "unknown date"}: ${error}`
    );
    return { eventStatus: "notProcessing" };
  }
};

const normalizeRaceData_AUS = async (
  rawData,
  meetingStage,
  meetingId,
  category,
  silksReady = false,
  race_index,
  ra_id,
  NominationNumber
) => {
  console.log(
    "Creating AUS Race Data - MeetingId:" + meetingId + ", RaceId:" + race_index
  );
  // console.log('NominationNumber',NominationNumber);
  // console.log('index',race_index);
  // console.log('ra_id',ra_id);
  rawData = rawData.Meeting;
  try {
    let errors = [];
    let error_log = [];
    let acceptances = "";
    let eventId = "";
    let races = [];
    if (
      rawData.Races &&
      rawData.Races.Race &&
      typeof rawData.Races.Race[Symbol.iterator] === "function"
    ) {
      races = rawData.Races.Race;
    } else if (rawData.Races && rawData.Races.Race) {
      races.push(rawData.Races.Race);
    }
    let rawRace = {};
    // if (category == "trial") {
    //   var noAbdRaces = [];
    //   for (race of races)
    //     if (race["@_CurrentRaceStage"] != "Abandoned") noAbdRaces.push(race);
    //   rawRace = noAbdRaces[index - 1];
    // } else {
    for (const filerace of races) {
      if (filerace["@_RaceCode"] === ra_id) {
        rawRace = filerace;
        break;
      }
    }
    // }
    if (!rawRace) return { eventStatus: "notProcessing" };
    const raceNo = race_index;
    const raceStage =
      category === "trial" ? "Acceptances" : rawRace["@_CurrentRaceStage"];
    if (raceStage.toLowerCase().includes("results")) {
      console.log("abort FIELDS processing of meeting at results stage");
      return { eventStatus: "notProcessing" };
    }

    //Fetch Meeting Date from Processed Meeting Table
    const meeting_record = await centaur.temp_meetings
      .findById(meetingId)
      .select("meetingDate inputMeetingData AcceptanceFileCounter")
      .lean();
    if (!meeting_record) return { eventStatus: "notProcessing" };
    const meetingDate = meeting_record.meetingDate;

    //Fetch Track data from Track table - Error is handled at meeting level
    const product_track = rawData.Track["@_VenueAbbr"];
    let track_data = null;
    if (rawRace.VenueTrack && rawRace.VenueTrack["@_VenueAbbr"]) {
      const venueAbbr = rawRace.VenueTrack["@_VenueAbbr"];
      track_data = await centaur.tracks
        .findOne({ TRK_RSB_TRACK_ABBREV: venueAbbr.toUpperCase() })
        .lean();
    } else {
      track_data = await centaur.tracks
        .findOne({ TRK_RSB_TRACK_ABBREV: helper.upperCase(product_track) })
        .lean();
    }
    if (!track_data) {
      helper.logError(
        error_log,
        errors,
        `Error finding track at race level ${rawRace.VenueTrack} ${product_track} `
      );
    }
    if (
      rawData.Track &&
      rawData.Track["@_TrackCode"] &&
      rawRace.VenueTrack &&
      rawRace.VenueTrack["@_TrackCode"] &&
      rawData.Track["@_TrackCode"] != rawRace.VenueTrack["@_TrackCode"]
    ) {
      helper.logError(
        error_log,
        errors,
        `It looks like this race isnt being held on the same track, please check. Venue track for race: ${rawRace.VenueTrack["@_TrackName"]} ${rawRace.VenueTrack.TrackSurface}.`
      );
    }

    var expected_condition = "";
    if (rawRace.VenueTrack.TrackRating == "Synthetic") {
      expected_condition = "Y0";
    } else if (rawRace.VenueTrack.TrackRating["@_NumericTrackRating"]) {
      expected_condition = helper.processTrackRating(
        rawRace.VenueTrack.TrackRating["@_NumericTrackRating"]
      );
    }
    var nomsNumber = NominationNumber;
    var prizes = helper.processPrizes(
      rawRace.PrizeMoneyDetails,
      rawRace.PrizeMoneyDetails.Trophies
        ? rawRace.PrizeMoneyDetails.Trophies
        : ""
    );
    //Acceptance data - to be updated at Acceptance stage
    if (raceStage == "Acceptances" || raceStage == "FinalFields") {
      acceptances = {
        start_time: rawRace.RaceStartTime["@_TimeAtVenue"].replace(
          /[0-9]{4}-[0-9]{2}-[0-9]{2}T([0-9]{2}):([0-9]{2}):[0-9]{2}/,
          "$1$2"
        ),
        temporary_weight_change: rawRace.WeightChange,
      };
      if (!acceptances.start_time) {
        helper.logError(error_log, errors, `start time is missing`);
      }
      if (!rawRace.RaceDistance) {
        helper.logError(error_log, errors, `race distance is missing`);
      }
      if (category != "trial" && !prizes) {
        helper.logError(error_log, errors, `race prizemoney is missing`);
      }
      if (
        !helper.formatWeightType(
          rawRace.EntryConditions.EntryCondition[1]["@_Long"]
        )
      ) {
        helper.logError(error_log, errors, `race weight_type is missing`);
      }
    }
    let raceName = "";
    let shortRaceName = "";
    let trialStatus = 0; // Updated afterward
    if (category == "trial") {
      trialStatus = 1;
      eventId = helper.generateUniqueRaceId(
        meetingDate,
        track_data.TRK_TRACK_DB_ID,
        raceNo,
        trialStatus
      );
    } else {
      eventId = helper.generateUniqueRaceId(
        meetingDate,
        track_data.TRK_TRACK_DB_ID,
        nomsNumber,
        trialStatus
      );
    }
    var raceInput = {
      horses: {
        horse: [],
      },
    };
    if (
      meeting_record.inputMeetingData.races &&
      meeting_record.inputMeetingData.races.race &&
      meeting_record.inputMeetingData.races.race.length > 0
    ) {
      for (theRace of meeting_record.inputMeetingData.races.race) {
        if (theRace && theRace["@_id"] == eventId) {
          raceInput = theRace;
          if (theRace.locked !== undefined) {
            raceInput.locked = theRace.locked;
          }
          if (raceInput.error_log) {
            error_log = error_log.concat(raceInput.error_log);
          } else if (raceInput.errors) {
            error_log = error_log.concat(raceInput.errors);
          }
          if (raceInput.errors) {
            errors = errors.concat(raceInput.errors);
          }
          console.log("errors plus race errors", errors);
          if (
            raceInput.start_time &&
            raceInput.start_time != acceptances.start_time
          ) {
            helper.logError(
              error_log,
              errors,
              `start time has changed from ${raceInput.start_time} to ${acceptances.start_time}`
            );
          }
          if (
            meeting_record.AcceptanceFileCounter > 1 &&
            raceInput["@_name"] !== "" &&
            raceInput["@_ra_name"] &&
            helper.capFstLtr(raceInput["@_ra_name"]) !=
              helper.capFstLtr(rawRace.NameRaceFull["#text"])
          ) {
            helper.logError(
              error_log,
              errors,
              `RA race name has changed from ${helper.capFstLtr(
                raceInput["@_ra_name"]
              )} to ${helper.capFstLtr(rawRace.NameRaceFull["#text"])}`
            );
          }
          if (
            meeting_record.AcceptanceFileCounter > 1 &&
            raceInput["@_name"] !== "" &&
            raceInput.raRaceNameShort &&
            raceInput.raRaceNameShort != rawRace.NameRaceNews
          ) {
            helper.logError(
              error_log,
              errors,
              `RA race Shortname has changed from ${raceInput.raRaceNameShort} to ${rawRace.NameRaceNews}, check for benchmarks`
            );
          }
          if (
            raceInput.track_id &&
            raceInput.track_id !== track_data.TRK_TRACK_DB_ID
          ) {
            let inputTrack = await centaur.tracks
              .findOne({ TRK_TRACK_DB_ID: raceInput.track_id })
              .lean();
            if (inputTrack) {
              track_data = inputTrack;
            }
          }
        }
      }
    }
    if (rawRace.EntryConditions.EntryCondition[0]["@_Id"] == "BMNT") {
      console.log("BENCHMARK issue detected");
      helper.logError(
        error_log,
        errors,
        `check NameRaceForm its ${rawRace.NameRaceForm}`
      );
    }

    var classes =
      category == "trial"
        ? await getClassData("BTRIA", {}, "")
        : await getClassData(
            rawRace.EntryConditions.EntryCondition[0]["@_Id"],
            {},
            ""
          );
    if (category == "trial") {
      classes = await getClassData(
        rawRace.EntryConditions.EntryCondition[0]["@_Id"],
        classes,
        "second_"
      );
    }
    if (classes.class && classes.class == "Barrier Trial") {
      raceName = "Barrier Trial";
      shortRaceName = "Barrier Trl";
    } else if (classes.class) {
      raceName = helper.decodeHTMLEntities(rawRace.NameRaceFull["#text"]);
      if (helper.getGroupType(rawRace.GroupType)) {
        shortRaceName = helper.decodeHTMLEntities(rawRace.NameRaceNews);
      } else {
        shortRaceName = helper.getShortName(
          helper.getAgeRestriction(
            rawRace.EntryConditions.EntryCondition[2]["@_Id"]
          ),
          helper.getSexShortname(
            rawRace.EntryConditions.EntryCondition[3]["@_Id"]
          ),
          classes.class,
          helper.formatWeightType(
            rawRace.EntryConditions.EntryCondition[1]["@_Long"]
          )
        );
      }
    } else {
      raceName = helper.decodeHTMLEntities(rawRace.NameRaceFull["#text"]);
      helper.logError(
        error_log,
        errors,
        `Class not found, ${rawRace.EntryConditions.EntryCondition[0]["@_Id"]} ${rawRace.NameRaceForm}`
      );
    }

    const track_records = await processTrackRecords(
      rawRace.TrackRecords.TrackRecord,
      track_data.TRK_TRACK_DB_ID
    );
    if (track_records == "") {
      helper.logError(error_log, errors, "Error generation Track Records");
    }
    var restrictions = {};

    if (
      helper.getAgeRestriction(
        rawRace.EntryConditions.EntryCondition[2]["@_Id"]
      )
    ) {
      restrictions["@_age"] = helper.getAgeRestriction(
        rawRace.EntryConditions.EntryCondition[2]["@_Id"]
      );
    }
    if (
      helper.getSexRestriction(
        rawRace.EntryConditions.EntryCondition[3]["@_Id"]
      )
    ) {
      restrictions["@_sex"] = helper.getSexRestriction(
        rawRace.EntryConditions.EntryCondition[3]["@_Id"]
      );
    }
    restrictions["@_jockey"] =
      rawRace.ApprenticeCanClaim == true
        ? "Apprentices Can Claim"
        : "Apprentices Cannot Claim";

    let race_type =
      rawRace.RaceType &&
      (rawRace.RaceType.toLowerCase().includes("hurdle") ||
        rawRace.RaceType.toLowerCase().includes("steeple"))
        ? "Jumps"
        : rawRace.RaceType;

    if (race_type != "Jumps" && race_type != "Flat") {
      helper.logError(
        error_log,
        errors,
        `Race type unknown, RA sends: ${race_type}`
      );
    }
    var race = {
      "@_number": raceNo,
      "@_name":
        raceInput.raceName && raceInput.raceName != ""
          ? helper.decodeHTMLEntities(raceInput.raceName)
          : helper.decodeHTMLEntities(helper.cleverCapFstLtr(raceName)),
      "@_ra_name": helper.decodeHTMLEntities(helper.capFstLtr(raceName)),
      "@_shortname":
        raceInput.raceNameShort && raceInput.raceNameShort.trim() !== ""
          ? raceInput.raceNameShort
          : shortRaceName,
      "@_id": eventId,
      "@_ra_id": ra_id,
      "@_nominations_number": nomsNumber,
      race_stage: rawRace["@_CurrentRaceStage"],
      track: {
        "@_name": track_data.TRK_TRACK_NAME,
        "@_id": track_data.TRK_TRACK_DB_ID,
        "@_track_surface": track_data.TRK_TRACK_SURFACE_TYPE,
        "@_country": track_data.TRK_COUNTRY_OF_TRACK,
        "@_track_3char_abbrev": track_data.TRK_TRACK_3CHAR_ABBREV,
        "@_expected_condition": expected_condition,
      },
      distance: {
        "@_metres": rawRace.RaceDistance,
      },
      restrictions: restrictions,
      weight_type: helper.formatWeightType(
        rawRace.EntryConditions.EntryCondition[1]["@_Long"]
      ),
      min_hcp_weight: rawRace.EntryConditions.EntryCondition[1]["@_MinWeight"]
        ? parseFloat(rawRace.EntryConditions.EntryCondition[1]["@_MinWeight"])
        : 0,
      track_type: "",
      group: helper.getGroupType(rawRace.GroupType),
      classes: classes,
      race_type: race_type,
      prizes: {
        prize: helper.processPrizes(
          rawRace.PrizeMoneyDetails,
          rawRace.PrizeMoneyDetails.Trophies
            ? rawRace.PrizeMoneyDetails.Trophies
            : ""
        ),
      },
      records: track_records,
      confidence: raceInput.confidence ?? 5,
      single_horse: raceInput.single_horse ?? false,
      locked: raceInput.locked,
      errors: errors,
      error_log: error_log,
      horses: {
        horse: [{}],
      },
    };
    if (rawRace.NominationsDivisor && parseInt(rawRace.NominationsDivisor)) {
      race.division = parseInt(rawRace.NominationsDivisor);
    }
    // console.log(race)

    //Updating data for Acceptance Stage
    if (raceStage == "Acceptances" || raceStage == "FinalFields") {
      race = Object.assign(race, acceptances);
    }

    if (meeting_record.AcceptanceFileCounter > 1) {
      const existingmeeting = await centaur.processed_meetings
        .findOne({ _id: meetingId })
        .select("processedMeetingData")
        .lean();
      if (existingmeeting) {
        let dbRace =
          existingmeeting.processedMeetingData.meeting.races.race.find(
            (db_race) => db_race["@_id"] === race["@_id"]
          );
        if (dbRace) {
          keyLoop: for (const key in race) {
            if (
              [
                "@_ra_id",
                "confidence",
                "single_horse",
                "errors",
                "error_log",
                "horses",
                "records",
                "prizes",
              ].includes(key)
            )
              continue;
            if (["string", "boolean", "number"].includes(typeof race[key])) {
              if (race[key] != dbRace[key]) {
                helper.logError(
                  error_log,
                  errors,
                  `Race ${key} doesnt match in RA file, RA sends: ${race[key]}`
                );
                if (!["start_time", "@_number"].includes(key))
                  race[key] = dbRace[key];
              }
            } else if (typeof race[key] === "object") {
              for (const subKey in race[key]) {
                if (Array.isArray(race[key][subKey])) {
                  //is probably prizes
                  for (const prize of race[key][subKey]) {
                    const matchingPrize = dbRace[key][subKey].find(
                      (oldPrize) => oldPrize["@_type"] === prize["@_type"]
                    );
                    if (prize["@_value"] !== matchingPrize["@_value"]) {
                      helper.logError(
                        error_log,
                        errors,
                        `Race ${key} for ${prize["@_type"]} doesnt match in RA file, RA sends: ${prize["@_value"]}, we have ${matchingPrize["@_value"]}`
                      );
                      race[key][subKey] = dbRace[key][subKey];
                    }
                  }
                } else {
                  if (race[key][subKey] !== dbRace[key][subKey]) {
                    if (["@_expected_condition"].includes(subKey)) continue;
                    helper.logError(
                      error_log,
                      errors,
                      `Race ${subKey} in ${key} doesnt match in RA file, RA sends: ${race[key][subKey]}, we have ${dbRace[key][subKey]}`
                    );
                    if (key === "track") {
                      race[key] = dbRace[key];
                      continue keyLoop;
                    } else {
                      if (!["distance"].includes(key))
                        race[key][subKey] = dbRace[key][subKey];
                    }
                  }
                }
              }
            }
          }
        } else {
          helper.logError(
            error_log,
            errors,
            `Race has been added after accepts, please check!`
          );
        }
      }
    }
    console.log(race);
    //Generate Race Data for InputMeetingData Section
    var race_inputMeetingData = {
      "@_name":
        helper.decodeHTMLEntities(raceInput.raceName) ??
        helper.decodeHTMLEntities(helper.capFstLtr(raceName)),
      "@_ra_name": helper.decodeHTMLEntities(raceName),
      "@_ra_id": ra_id,
      "@_number": raceNo,
      "@_nominations_number": nomsNumber,
      "@_id": eventId,
      raceName: helper.decodeHTMLEntities(raceInput.raceName) ?? "",
      raceNameShort:
        raceInput.raceNameShort && raceInput.raceNameShort.trim() !== ""
          ? helper.decodeHTMLEntities(raceInput.raceNameShort)
          : helper.decodeHTMLEntities(shortRaceName),
      raRaceNameShort: helper.decodeHTMLEntities(rawRace.NameRaceNews),
      start_time: acceptances.start_time,
      track_id: race.track["@_id"],
      classes: race.classes,
      horses: {
        horse: raceInput.horses.horse,
      },
      errors: errors,
      error_log: error_log,
      confidence: raceInput.confidence ?? 5,
      single_horse: raceInput.single_horse ?? false,
      locked: raceInput.locked,
    };

    let mainArray = [];
    // Process horseArray
    let count = 0;
    let sequenceCount = 10;
    // Check if rawRace.RaceEntries.RaceEntry is an array
    if (Array.isArray(rawRace.RaceEntries.RaceEntry)) {
      for (rawHorse of rawRace.RaceEntries.RaceEntry) {
        mainArray.push(
          genHorseLevelData_AUS(
            await raceStage,
            meetingDate,
            rawHorse,
            sequenceCount,
            raceInput,
            raceNo,
            track_data.TRK_TRACK_NAME,
            category,
            meetingId
          )
        );
        sequenceCount = sequenceCount + 10;
        count++;
      }
    } else {
      // If rawRace.RaceEntries.RaceEntry is not an array, wrap it in an array
      mainArray.push(
        genHorseLevelData_AUS(
          await raceStage,
          meetingDate,
          rawRace.RaceEntries.RaceEntry,
          sequenceCount,
          raceInput,
          raceNo,
          track_data.TRK_TRACK_NAME,
          category,
          meetingId
        )
      );
      count = 1;
    }

    var horseCompleted = await Promise.all(mainArray);

    var horseArray = [];
    var inputMeetingDataHorseArray = raceInput.horses.horse;

    /* Update Meeting with Race Data */
    var updatedErrors = [];
    for (const error of race.errors) {
      if (!error.includes("Horses with alerts:")) {
        updatedErrors.push(error);
      }
    }
    race.errors = updatedErrors;
    console.log("updated errors", errors);
    if (horseCompleted) {
      var silksArray = [];
      var barrierArray = [];
      var tabArray = [];
      let weightsErrors = [];
      for (record of horseCompleted) {
        if (
          category != "trial" &&
          !record.horse_processedMeetingData.scratched &&
          (raceStage == "Acceptances" || raceStage == "FinalFields")
        ) {
          if (silksArray.includes(record.horse_processedMeetingData.colours)) {
            if (rawData.TabStatus) {
              for (horse of horseCompleted) {
                if (
                  record.horse_processedMeetingData.colours ===
                  horse.horse_processedMeetingData.colours
                ) {
                  [
                    horse.horse_processedMeetingData.error_log,
                    horse.horse_processedMeetingData.errors,
                  ] = helper.logError(
                    record.horse_inputMeetingData.error_log,
                    record.horse_processedMeetingData.errors,
                    `Duplicate silks detected ${record.horse_processedMeetingData.colours}`
                  );
                  [
                    horse.horse_inputMeetingData.error_log,
                    horse.horse_inputMeetingData.errors,
                  ] = helper.logError(
                    record.horse_inputMeetingData.error_log,
                    record.horse_inputMeetingData.errors,
                    `Duplicate silks detected ${record.horse_processedMeetingData.colours}`
                  );
                }
              }
            }
          }
          silksArray.push(
            helper.decodeHTMLEntities(record.horse_processedMeetingData.colours)
          );
        }
        if (
          record.horse_processedMeetingData.errors.includes(
            "Horse weight missing"
          )
        ) {
          // console.log('if the weight missing error is detected it goes here')
          // console.log(record.horse_processedMeetingData.errors)
          weightsErrors.push(
            `Weights missing for ${record.horse_processedMeetingData["tab_number"]}. ${record.horse_processedMeetingData["@_name"]} ${record.horse_processedMeetingData["@_id"]}`
          );
          record.horse_inputMeetingData.error_log =
            record.horse_inputMeetingData.error_log.filter(
              (item) => item !== "Horse weight missing"
            );
          record.horse_inputMeetingData.errors =
            record.horse_inputMeetingData.errors.filter(
              (item) => item !== "Horse weight missing"
            );
          record.horse_processedMeetingData.errors =
            record.horse_processedMeetingData.errors.filter(
              (item) => item !== "Horse weight missing"
            );
          record.horse_processedMeetingData.error_log =
            record.horse_processedMeetingData.error_log.filter(
              (item) => item !== "Horse weight missing"
            );
        }
        if (raceStage == "Acceptances" || raceStage == "FinalFields")
          tabArray.push(parseInt(record.horse_processedMeetingData.tab_number));
        if (raceStage == "Acceptances" || raceStage == "FinalFields")
          barrierArray.push(
            parseInt(record.horse_processedMeetingData.barrier)
          );
      }
      if (weightsErrors.length > 2) {
        race.errors.push(
          "More than 2 horses in the race missing weights, stage may be incorrect"
        );
      } else {
        for (const weightError of weightsErrors) {
          race.errors.push(weightError);
        }
      }
      horseInputData = [];
      HorseDataLoop: for (horse of horseCompleted) {
        try {
          horseArray.push(horse.horse_processedMeetingData);
          for (let input_horse of inputMeetingDataHorseArray) {
            if (input_horse["@_id"] === horse["@_id"]) {
              input_horse = horse.horse_inputMeetingData;
              continue HorseDataLoop;
            }
          }
          inputMeetingDataHorseArray.push(horse.horse_inputMeetingData);
        } catch (err) {
          console.log("error with horse" + horseCompleted);
        }
      }
      if (
        category &&
        category.toLowerCase() === "trial" &&
        meeting_record.AcceptanceFileCounter > 1
      ) {
        let numHorsesInDB = 0;
        if (raceInput && raceInput.horses && raceInput.horses.horse) {
          numHorsesInDB = raceInput.horses.horse.length;
        }

        const numHorsesInRawData = horseArray.length;
        if (numHorsesInDB !== numHorsesInRawData) {
          helper.logError(
            error_log,
            errors,
            `Number of runners has changed from ${numHorsesInDB} to ${numHorsesInRawData} in race ${raceNo}`
          );
        }
      }

      tabArray.sort((a, b) => a - b);
      barrierArray.sort((a, b) => a - b);
      if (raceStage == "Acceptances" || raceStage == "FinalFields") {
        for (i = 0; i < horseCompleted.length; i++) {
          // console.log(i+1,barrierArray[i],tabArray[i])
          let noLog = [];
          if (!barrierArray[i] || parseInt(barrierArray[i]) != i + 1)
            [noLog, race.errors] = helper.logError(
              [],
              race.errors,
              `Horse Barriers don't match, please check`
            );
          if (!tabArray[i] || parseInt(tabArray[i]) != i + 1)
            [noLog, race.errors] = helper.logError(
              [],
              race.errors,
              `Horse TAB numbers don't match, please check`
            );
        }
      }
      race.horses.horse = horseArray;
      // if (raceStage == "Acceptances" || raceStage == "FinalFields")
      race_inputMeetingData.horses.horse = inputMeetingDataHorseArray;
      var normalizedRaceData = {
        race: race,
        race_inputMeetingData: race_inputMeetingData,
      };
      return normalizedRaceData;
    }
    //return race
    return "Race Data Updated";
  } catch (error) {
    var sendMail = await helper.mailAlert(
      "AUS Race Level Load error",
      `AUS Race Data couldnt generate for ${meetingId}, ${
        rawData.Track["@_VenueAbbr"] ?? "unknown track"
      }, ${
        rawData.MeetDate ?? "unknown date"
      }, Category: ${category}, Race ${race_index}. ${error}`,
      "alert"
    );
    console.log(sendMail);
    console.log(
      `((ERROR)): Error in creating race level data ${meetingId}, ${
        rawData.Track["@_VenueAbbr"] ?? "unknown track"
      }, ${
        rawData.MeetDate ?? "unknown date"
      }, Category: ${category}, Race ${race_index}: ${error}`
    );
    return { eventStatus: "notProcessing" };
  }
};

const genHorseLevelData_AUS = async (
  meetingStage,
  meetingDate,
  rawHorse,
  sequenceCount,
  raceInput,
  raceNo,
  meetingTrack,
  category,
  meetingId
) => {
  try {
    const start = Date.now();
    var errors = [],
      error_log = [];
    var name = "",
      country = "",
      age = "",
      colour = "",
      sex = "",
      horse_id = "",
      horse_RA_Id = "",
      foaling_date = "";
    var sire_name = "",
      sire_country = "",
      sire_id = "",
      dam_name = "",
      dam_country = "",
      dam_id = "",
      sire_dam_name = "",
      sire_dam_country = "",
      sire_dam_id;
    var trainer_stats = "",
      trainer_name = "",
      trainer_firstname = "",
      trainer_surname = "",
      trainer_id = "",
      trainer_RA_Id = "",
      training_location = "";
    var owners = "",
      colours = "",
      prizmoney_won = 0,
      startsData = "",
      last4s = "",
      last10s = "",
      last15s = "",
      last20s = "";
    var current_blinker_ind = "N",
      win_percentage = "",
      place_percentage = "",
      statistics = "",
      win_distances = "",
      pace = [0, ""];
    var weights = "",
      weights_allocated = "",
      weights_penalty = "",
      weights_total = ""; //Weights
    var acceptances = "",
      tab_number = "",
      jockey_race_entry = "",
      jockey_RA_Id = "",
      barrier_no = "",
      gear = "",
      isEmergencyRunner = ""; //Acceptances
    var results = "",
      running_gear = { gear_item: [] },
      class_quality = "F";
    var results_forms_table = "",
      prev_name = "",
      prev_trainer = "";
    let ballot_sequence = rawHorse["BallotSequence"];
    let hasBOBSBonus = false;
    if (rawHorse.BonusIndicator) {
      const bonusIndicators = Array.isArray(rawHorse.BonusIndicator)
        ? rawHorse.BonusIndicator
        : [rawHorse.BonusIndicator];
      for (const bonus of bonusIndicators) {
        if (bonus["@_SchemeName"].includes("BOBS")) {
          hasBOBSBonus = true;
          break;
        }
      }
    }

    // Fetch Horse data from horse_names table
    const horseData = await centaur.horses
      .findOne()
      .where("HRN_HORSE_NAME")
      .equals(helper.upperCase(rawHorse.Horse["@_HorseName"]))
      .where("HOR_FOALING_DATE")
      .equals(helper.sDate(rawHorse.Horse["@_FoalDate"]))
      .lean();

    if (horseData) {
      const horseId = horseData.HRN_HORSE_ID;

      //Fetch Full Horse data from horses table
      const horseSireId = horseData.HOR_SIRE_ID;
      const horseDamId = horseData.HOR_DAM_ID;
      horse_RA_Id = rawHorse.Horse["@_HorseCode"];
      name =
        horseData.HRN_DISPLAY_NAME ??
        helper.capFstLtr(horseData.HRN_HORSE_NAME);
      country = horseData.HOR_COUNTRY_OF_ORIGIN;
      if (!country) {
        helper.logError(
          error_log,
          errors,
          `${rawHorse.Horse["@_HorseName"]} ${horseId} has no country`
        );
      }
      age = helper.getHorseAge(horseData.HOR_FOALING_DATE, meetingDate);
      colour = helper.getHorseColorAbbr(rawHorse.Horse["@_Colour"]);
      sex = helper.getHorseSex(horseData.HOR_SEX, age);
      horse_id = horseData.HRN_HORSE_ID;
      foaling_date = helper.convertDate(horseData.HOR_FOALING_DATE);

      // add input data into main list
      var horseInput = {};
      if (raceInput.horses && raceInput.horses.horse) {
        for (const runner in raceInput.horses.horse) {
          if (raceInput.horses.horse[runner]["@_id"] === horse_id) {
            horseInput = raceInput.horses.horse[runner];
            if (horseInput.errors && horseInput.errors.length > 0) {
              errors = errors.concat(horseInput.errors);
            }
            if (horseInput.error_log && horseInput.error_log.length > 0) {
              error_log = error_log.concat(horseInput.error_log);
            }
          }
        }
      }

      //Get SIRE Data from horse_name
      const sireData = await centaur.horses
        .findOne({ HRN_HORSE_ID: horseSireId })
        .select("HRN_HORSE_NAME HOR_COUNTRY_OF_ORIGIN HRN_HORSE_ID")
        .lean();
      if (sireData) {
        sire_name =
          sireData.HRN_DISPLAY_NAME ??
          helper.capFstLtr(sireData.HRN_HORSE_NAME);
        sire_country = sireData.HOR_COUNTRY_OF_ORIGIN;
        sire_id = sireData.HRN_HORSE_ID;
        if (
          sire_name.toLowerCase() !=
          rawHorse.Breeding.Sire["@_HorseName"].toLowerCase()
        ) {
          helper.logError(
            error_log,
            errors,
            `Sire Might not match, RA: ${
              rawHorse.Breeding.Sire["@_HorseName"]
            } (${
              rawHorse.Breeding.Sire["@_Country"] ?? "AUS"
            }). Cen: ${sire_name} (${sire_country})`
          );
        }
      } else {
        helper.logError(error_log, errors, "Sire Not Found");
      }

      //Get DAM Data from horse_name
      const damData = await centaur.horses
        .findOne({ HRN_HORSE_ID: horseDamId })
        .select("HRN_HORSE_NAME HOR_COUNTRY_OF_ORIGIN HRN_HORSE_ID")
        .lean();
      if (damData) {
        dam_name =
          damData.HRN_DISPLAY_NAME ?? helper.capFstLtr(damData.HRN_HORSE_NAME);
        dam_country = damData.HOR_COUNTRY_OF_ORIGIN;
        dam_id = damData.HRN_HORSE_ID;
        if (
          dam_name.toLowerCase() !=
          rawHorse.Breeding.Dam["@_HorseName"].toLowerCase()
        ) {
          helper.logError(
            error_log,
            errors,
            `Dam Might not match, RA: ${
              rawHorse.Breeding.Dam["@_HorseName"]
            } (${
              rawHorse.Breeding.Dam["@_Country"] ?? "AUS"
            }). Cen: ${dam_name} (${dam_country})`
          );
        }
      } else {
        helper.logError(error_log, errors, "Dam Not Found");
      }

      //Get DAM Data from horse table
      const damFullData = await centaur.horses
        .findOne({ HRN_HORSE_ID: horseDamId })
        .select("HOR_SIRE_ID")
        .lean();

      //Get SIRE of DAM
      const sireOfDamData = await centaur.horses
        .findOne({ HRN_HORSE_ID: damFullData.HOR_SIRE_ID })
        .select("HRN_HORSE_NAME HOR_COUNTRY_OF_ORIGIN HRN_HORSE_ID")
        .lean();
      if (sireOfDamData) {
        sire_dam_name =
          sireOfDamData.HRN_DISPLAY_NAME ??
          helper.capFstLtr(sireOfDamData.HRN_HORSE_NAME);
        sire_dam_country = sireOfDamData.HOR_COUNTRY_OF_ORIGIN;
        sire_dam_id = sireOfDamData.HRN_HORSE_ID;
        if (
          sire_dam_name.toLowerCase() !=
          rawHorse.Breeding.SireOfDam["@_HorseName"].toLowerCase()
        ) {
          helper.logError(
            error_log,
            errors,
            `DamSire Might not match, RA: ${
              rawHorse.Breeding.SireOfDam["@_HorseName"]
            } (${
              rawHorse.Breeding.SireOfDam["@_Country"] ?? "AUS"
            }). Cen: ${sire_dam_name} (${sire_dam_country})`
          );
        }
      } else {
        helper.logError(error_log, errors, "Sire of Dam Not Found");
      }

      if (!rawHorse.Trainer || !rawHorse.Trainer["@_TrainerCode"]) {
        helper.logError(
          error_log,
          errors,
          `Trainer missing completely from RA file, please fix and contact RA`
        );
      } else {
        const trainer_data = await centaur.trainers
          .find({ RISATrainerID: rawHorse.Trainer["@_TrainerCode"] })
          .lean();
        training_location =
          rawHorse.Trainer.Location ?? horseData.HOR_TRAINING_LOCATION;
        trainer_RA_Id = rawHorse.Trainer["@_TrainerCode"];
        if (trainer_data[0]) {
          if (rawHorse.Trainer.Name != trainer_data[0].Name) {
            helper.logError(
              error_log,
              errors,
              `Trainer might not match. RA has the name as (${rawHorse.Trainer.Name}), could be in our database as (${trainer_data[0].TRN_TRAINER_DISPLAYNAME}) or (${trainer_data[0].TRN_PARTNERSHIP_DISPLAYNAME}) RISAID from RA: ${rawHorse.Trainer["@_TrainerCode"]}`
            );
          }

          trainer_stats = await getTrainerStats(trainer_data[0].TRN_TRAINER_ID);
          trainer_name = trainer_data[0].TRN_TRAINER_DISPLAYNAME;
          trainer_firstname = trainer_data[0].TRN_TRAINER_FIRSTNAME;
          trainer_surname = trainer_data[0].TRN_TRAINER_SURNAME;
          trainer_id = trainer_data[0].TRN_TRAINER_ID;
        } else {
          const trainer_data_2 = await centaur.trainers
            .find({ RISAPartnerID: rawHorse.Trainer["@_TrainerCode"] })
            .lean();
          if (trainer_data_2[0]) {
            if (rawHorse.Trainer.Name != trainer_data_2[0].Name) {
              helper.logError(
                error_log,
                errors,
                `Trainer might not match. RA has the name as (${rawHorse.Trainer.Name}), could be in our database as (${trainer_data_2[0].TRN_TRAINER_DISPLAYNAME}) or (${trainer_data_2[0].TRN_PARTNERSHIP_DISPLAYNAME}) RISAID from RA: ${rawHorse.Trainer["@_TrainerCode"]}, This is a SECONDARY code.`
              );
            }

            trainer_stats = await getTrainerStats(
              trainer_data_2[0].TRN_TRAINER_ID
            );
            trainer_name = trainer_data_2[0].TRN_TRAINER_DISPLAYNAME;
            trainer_firstname = trainer_data_2[0].TRN_TRAINER_FIRSTNAME;
            trainer_surname = trainer_data_2[0].TRN_TRAINER_SURNAME;
            trainer_id = trainer_data_2[0].TRN_TRAINER_ID;
          } else {
            helper.logError(
              error_log,
              errors,
              `Trainer Not Found - Trainer Name: (${rawHorse.Trainer.Name}) RISAID: ${rawHorse.Trainer["@_TrainerCode"]}`
            );
          }
        }
      }

      // THIS OWNERS NEETD TO COME FROM RA FILE (first, then if empty can use DB)
      owners = helper.decodeHTMLEntities(
        rawHorse.HorseOwnership.OfficialHorseOwners
      );
      if (owners == "" && category != "trial") {
        owners = horseData.HOR_OWNER_NAMES;
        if (owners) {
          helper.logError(
            error_log,
            errors,
            `RA FILE: Owners missing for for Horse - ${horseId}, we have used the owners from the database, this may need confirmation.`
          );
        } else {
          helper.logError(
            error_log,
            errors,
            `Owners missing for for Horse - ${horseId}, we have no owners in the database or the RA file.`
          );
        }
      }
      prizmoney_won = parseInt(horseData.HOR_TOTAL_PRIZEMONEY);
      var horse_prizes = rawHorse.Form.ResultsSummaries.ResultsSummary;
      // console.log(horse_prizes)
      for (summary of horse_prizes)
        if (summary["@_Name"] == "TotalResults" && summary["@_PrizeMoney"]) {
          var RA_prizmoney = parseInt(summary["@_PrizeMoney"]);
          if (
            RA_prizmoney - 100 > prizmoney_won ||
            RA_prizmoney + 100 < prizmoney_won
          ) {
            helper.logError(
              error_log,
              errors,
              `Check Pmoney for Horse - ${horseId}, RA: ${RA_prizmoney}, Cen: ${prizmoney_won}`
            );
          }
          break;
        }

      if (rawHorse.Gear && rawHorse.Gear["@_Blinkers"] == "true") {
        current_blinker_ind = "Y";
      } else {
        current_blinker_ind = "N";
      }
      // Fetch current track data
      let currTrack = await centaur.tracks
        .findOne({
          TRK_TRACK_NAME: meetingTrack,
        })
        .lean();

      if (!currTrack || !currTrack.TRK_COUNTRY_OF_TRACK) {
        helper.logError(
          error_log,
          errors,
          `Current track ${meetingTrack} not found or country is missing in the database.`
        );
      }
      //Calculate Horse Starts - Fetch latest 20 record (sort desc by form date), put FRM_FINISH_POSTION in group of 4, if date diff is more than 90 days put x
      const formData = await centaur.form.find({ horse_id: horseId }).lean();
      var formItems = [];
      if (formData[0] && formData[0].form) {
        try {
          formItems = helper.cleanseBarrierTrials(formData[0].form);
          if (Array.isArray(formItems)) {
            if (
              horseInput.gear_changes &&
              horseInput.gear_changes.gear_change &&
              horseInput.gear_changes.gear_change[0] &&
              horseInput.running_gear &&
              horseInput.running_gear.gear_item
            ) {
              running_gear = horseInput.running_gear;
            } else if (
              formItems[0] &&
              formItems[0].running_gear &&
              formItems[0].running_gear.gear_item
            ) {
              running_gear = formItems[0].running_gear;
            }
            if (
              formItems[0] &&
              formItems[0].trainer_id &&
              formItems[0].trainer_id != trainer_id
            ) {
              prev_trainer = formItems[0].trainer_id;
            }
          } else {
            helper.logError(
              error_log,
              errors,
              `FormData for Horse - ${horseId} - appears to be empty `
            );
          }
        } catch (err) {
          console.log(`starts generation error with ${horseId}: ${err}`);
        }
      } else if (age > 3) {
        helper.logError(
          error_log,
          errors,
          `FormData not found for Horse - ${horseId} `
        );
      }
      try {
        // Error checking for last registered form item's country
        if (currTrack && currTrack.TRK_COUNTRY_OF_TRACK != "") {
          if (
            formItems[0] &&
            formItems[0].track &&
            formItems[0].track["@_country"] &&
            formItems[0].track["@_country"] != ""
          ) {
            if (
              formItems[0].track["@_country"] != currTrack.TRK_COUNTRY_OF_TRACK
            ) {
              if (meetingStage !== "FinalFields") {
                //console.log(error_log, errors);
                console.log(
                  `${name} ${horse_id} - has its last registered form item in ${formItems[0].track["@_country"]} current is ${currTrack.TRK_COUNTRY_OF_TRACK}`
                );
                [error_log, errors] = helper.logError(
                  error_log,
                  errors,
                  `${name} ${horse_id} - has its last registered form item in ${formItems[0].track["@_country"]}`
                );
                errors.push(
                  `${name} ${horse_id} - has its last registered form item in ${formItems[0].track["@_country"]}`
                );
              }
              //console.log(error_log, errors);
            }
          } else if (
            formItems[0] &&
            formItems[0].track &&
            formItems[0].track["@_id"]
          ) {
            // Fetch lastTrack from database
            var lastTrack = await centaur.tracks
              .findOne({
                TRK_TRACK_DB_ID: formItems[0].track["@_id"],
              })
              .lean();
            if (lastTrack && lastTrack.TRK_COUNTRY_OF_TRACK != "") {
              if (
                lastTrack.TRK_COUNTRY_OF_TRACK != currTrack.TRK_COUNTRY_OF_TRACK
              ) {
                helper.logError(
                  error_log,
                  errors,
                  `${name} ${horse_id} - has its last registered form item in ${lastTrack.TRK_COUNTRY_OF_TRACK}`
                );
              }
            } else {
              helper.logError(
                error_log,
                errors,
                `${name} ${horse_id} - last form item's track country is missing or track not found in the database.`
              );
            }
          } else {
            console.log(
              `${name} ${horse_id} - can't find track information for the last form item.`
            );
          }
        }
      } catch (error) {
        console.log("TRK_COUNTRY_OF_TRACK");
        console.log(error);
      }
      //Weight data - to be updated at weights stage
      var isweight = false;
      if (
        meetingStage == "Weights" ||
        meetingStage == "Acceptances" ||
        meetingStage == "FinalFields"
      ) {
        weights_allocated = rawHorse.HandicapWeight;
        isweight = weights_allocated;
        weights = {
          weight: {
            "@_allocated": weights_allocated,
            "@_total": weights_allocated,
          },
          weight_carried: weights_allocated,
        };
        if (
          rawHorse.WeightPenalty &&
          (rawHorse.WeightPenalty != "" || rawHorse.WeightPenalty != "0")
        ) {
          weights_penalty = rawHorse.WeightPenalty;
          weights_total = rawHorse.HandicapWeight;
          weights = {
            weight: {
              "@_allocated":
                parseInt(weights_allocated) - parseInt(weights_penalty),
              "@_total": weights_total,
              "@_performance_penalty": weights_penalty,
            },
            weight_carried: weights_total,
          };
          isweight = weights_total;
        }
        if (!rawHorse.HandicapWeight && category != "trial") {
          helper.logError(error_log, errors, `Horse weight missing`);
        }
      }

      colours = helper
        .capFstLtr(rawHorse.RacingColours)
        .replace('"', "'")
        .replace("’", "'"); // Need to work, take from Racing Aus

      //Acceptance data - to be updated at Acceptance stage
      if (meetingStage == "Acceptances" || meetingStage == "FinalFields") {
        acceptances = {
          tab_number: rawHorse.TabNumber,
          barrier: rawHorse.BarrierNumber,
        };
        // var checkForSilks = await helper.checkForHorseSilks(meetingDate,meetingTrack,raceNo,rawHorse.TabNumber)
        // if (silksReady){
        var meetingDateFormat = moment(meetingDate).format("YYYY_MM_DD");
        var path =
          meetingDateFormat +
          "/" +
          meetingTrack.replace(/ /g, "_").toLowerCase() +
          "/" +
          raceNo +
          "/" +
          rawHorse.TabNumber;
        acceptances.horse_colours_image =
          "https://silks.medialityracing.com.au/images/jpg/" +
          path +
          "_front.jpg";
        acceptances.horse_colours_image_png =
          "https://silks.medialityracing.com.au/images/png/" +
          path +
          "_front.png";
        acceptances.horse_colours_image_svg =
          "https://silks.medialityracing.com.au/images/svg/" +
          path +
          "_front.svg";
        // }
        if (rawHorse.IsEmergencyRunner) {
          emergency = {
            emergency_indicator: "E",
          };
          acceptances = Object.assign(acceptances, emergency);
        }

        if (rawHorse.Scratched) {
          scratched = {
            scratched: rawHorse.Scratched,
          };
          acceptances = Object.assign(acceptances, scratched);
        } else if (horseInput.scratched) {
          scratched = {
            scratched: horseInput.scratched,
          };
          acceptances = Object.assign(acceptances, scratched);
        } else {
          if (!rawHorse.TabNumber || rawHorse.TabNumber == 0) {
            helper.logError(
              error_log,
              errors,
              `TAB number not found for Horse - ${rawHorse.Horse["@_HorseName"]} - ${horseId} `
            );
          }
          if (!rawHorse.BarrierNumber || rawHorse.BarrierNumber == 0) {
            if (horseInput.barrier && horseInput.barrier != "0") {
              acceptances.barrier = horseInput.barrier;
              helper.logError(
                error_log,
                errors,
                `RA sent blank barrier,so using existing barrier ${horseInput.barrier} from database for ${rawHorse.Horse["@_HorseName"]} - ${horseId}`
              );
            } else if (category != "trial") {
              helper.logError(
                error_log,
                errors,
                `Barrier not found for Horse - ${rawHorse.Horse["@_HorseName"]} - ${horseId}`
              );
            }
          }          
          if (horseInput.silks && horseInput.silks != colours) {
            helper.logError(
              error_log,
              errors,
              `Colours have changed for Horse - ${rawHorse.Horse["@_HorseName"]} - ${horseId} - old colours: ${horseInput.silks}`
            );
          }
          if (horseInput.colours) {
            colours = horseInput.colours;
          }
        }
        if (
          !horseInput.scratched &&
          !rawHorse.Scratched &&
          colours == "" &&
          category != "trial"
        ) {
          helper.logError(
            error_log,
            errors,
            `Silks missing for for Horse - ${horseId}`
          );
        }

        // Jockey
        if (
          !horseInput.scratched &&
          !rawHorse.Scratched &&
          rawHorse.JockeyRaceEntry.PreferredName &&
          rawHorse.JockeyRaceEntry.Surname
        ) {
          var jockey_data = await centaur.jockeys
            .findOne({
              JOC_JOCKEY_FIRSTNAME: rawHorse.JockeyRaceEntry.PreferredName,
              JOC_JOCKEY_SURNAME: rawHorse.JockeyRaceEntry.Surname,
            })
            .lean();
          if (!jockey_data && rawHorse.JockeyRaceEntry.Name) {
            jockey_data = await centaur.jockeys
              .findOne({ JOC_JOCKEY_RANAME: rawHorse.JockeyRaceEntry.Name })
              .lean();
          }
          if (jockey_data) {
            // trainer_stats = await getTrainerStats(horseId, trainer_data.TRN_TRAINER_ID)
            jockey_name = jockey_data.JOC_JOCKEY_DISPLAYNAME;
            jockey_firstname = jockey_data.JOC_JOCKEY_FIRSTNAME;
            jockey_surname = jockey_data.JOC_JOCKEY_SURNAME;
            jockey_id = jockey_data.JOC_JOCKEY_ID;
            jockey_RA_Id = rawHorse.JockeyRaceEntry["@_JockeyCode"];
            jockey_apprentice_indicator =
              rawHorse.JockeyRaceEntry["@_Apprentice"] == "true" ? "Y" : "";
            jockey_allowance_weight = rawHorse.JockeyRaceEntry.WeightClaim
              ? rawHorse.JockeyRaceEntry.WeightClaim
              : rawHorse.JockeyRaceEntry["@_apprentice"] == "true"
              ? 0
              : "";
            if (
              parseFloat(jockey_allowance_weight) > 5 ||
              parseFloat(jockey_allowance_weight) < 0
            ) {
              helper.logError(
                error_log,
                errors,
                `Jockey has an allowance weight outside standard range: ${jockey_allowance_weight}`
              );
              jockey_allowance_weight = "";
            }
            if (
              jockey_apprentice_indicator != "Y" &&
              parseFloat(rawHorse.JockeyRaceEntry.WeightClaim) > 0
            ) {
              helper.logError(
                error_log,
                errors,
                `Jockey has an allowance weight but has not been marked as an apprentice`
              );
            }

            jockey_riding_weight = parseFloat(
              rawHorse.JockeyRaceEntry.RidingWeight
            )
              ? parseFloat(rawHorse.JockeyRaceEntry.RidingWeight).toFixed(2)
              : "";

            jockey_record = {
              jockey: {
                "@_name": jockey_name,
                "@_firstname": jockey_firstname,
                "@_surname": jockey_surname,
                "@_apprentice_indicator": jockey_apprentice_indicator,
                "@_allowance_weight": jockey_allowance_weight,
                "@_id": jockey_id,
                "@_jockey_RA_Id": jockey_RA_Id,
                "@_riding_weight": jockey_riding_weight,
                statistics: [],
              },
            };
            acceptances = Object.assign(acceptances, jockey_record);
          } else {
            if (rawHorse.JockeyRaceEntry.Name) {
              helper.logError(
                error_log,
                errors,
                `Couldnt match Jockey - ${rawHorse.JockeyRaceEntry.PreferredName} ${rawHorse.JockeyRaceEntry.Surname}, RA name is ${rawHorse.JockeyRaceEntry.Name}`
              );
            }
          }
        }
      }

      if (horseData.HRN_PREV_NAMES && horseData.HRN_PREV_NAMES[0]) {
        prev_name = horseData.HRN_PREV_NAMES.join(", ");
      }

      var horse = {
        "@_name": name,
        "@_country": country,
        "@_age": age,
        "@_colour": colour,
        "@_sex": sex,
        "@_id": horse_id,
        "@_horse_RA_Id": horse_RA_Id,
        "@_foalingdate": foaling_date,
        sire: {
          "@_name": sire_name,
          "@_country": sire_country,
          "@_id": sire_id,
        },
        dam: {
          "@_name": dam_name,
          "@_country": dam_country,
          "@_id": dam_id,
        },
        sire_of_dam: {
          "@_name": sire_dam_name,
          "@_country": sire_dam_country,
          "@_id": sire_dam_id,
        },
        trainer: {
          statistics: {},
          "@_name": trainer_name,
          "@_firstname": trainer_firstname,
          "@_surname": trainer_surname,
          "@_id": trainer_id,
          "@_trainer_RA_Id": trainer_RA_Id,
        },
        training_location: training_location,
        owners: owners,
        colours: colours,
        prizemoney_won: 0,
        last_four_starts: "",
        last_ten_starts: "",
        last_fifteen_starts: "",
        last_twenty_starts: "",
        FF5_dry: "0",
        FF5_wet: "0",
        FF_Dry_Rating_100: "100",
        FF_Wet_Rating_100: "100",
        current_blinker_ind: current_blinker_ind ?? "N",
        win_percentage: 0,
        place_percentage: 0,
        pace_value: 0,
        pace: "",
        statistics: {},
        win_distances: [],
        class_quality: "F",
        rating: 0,
        rating_wfa: 0,
        ratings: { rating: [] },
        ballot_sequence: ballot_sequence,
        handicap_rating: rawHorse.HandicapRating ?? "",
        betting: horseInput.betting ?? "",
        tip: horseInput.tip ?? "",
        gear_changes: horseInput.gear_changes ?? { gear_change: [] },
        running_gear: {
          gear_item: helper.runningGears(
            horseInput.gear_changes && horseInput.gear_changes.gear_change
              ? horseInput.gear_changes.gear_change
              : [],
            running_gear.gear_item
          ),
        },
        colours_link: horseInput.colours_link ?? "",
        comment: horseInput.comment ?? "",
        form_comments: horseInput.form_comment ?? "",
        errors: errors,
        error_log: error_log,
        //"forms": { form: [{}] }
      };
      if (meetingStage == "Nominations" || meetingStage == "Weights") {
        horse.sequence_no = sequenceCount;
      }

      //Updating data for Weights Stage
      if (
        meetingStage == "Weights" ||
        meetingStage == "Acceptances" ||
        meetingStage == "FinalFields"
      ) {
        horse = Object.assign(horse, weights);
      }

      //Updating data for Acceptance and Final Fields Stage
      if (meetingStage == "Acceptances" || meetingStage == "FinalFields") {
        horse = Object.assign(horse, acceptances);
      }

      if (prev_name) {
        horse = Object.assign(horse, {
          "@_previous_name": prev_name,
        });
      }

      if (prev_trainer) {
        // console.log('prev_train',prev_trainer)
        var previous_trainer = await centaur.trainers
          .findOne({ TRN_TRAINER_ID: prev_trainer })
          .lean();
        // console.log(previous_trainer)
        if (previous_trainer) {
          horse.trainer["@_previous_trainer"] =
            previous_trainer.TRN_TRAINER_DISPLAYNAME;
          horse.trainer["@_previous_trainer_id"] = prev_trainer;
          horse.trainer["@_firststart"] = "Y";
        }
      }

      // Horse for InputDataMeeting
      var horse_inputMeetingData = {
        "@_name": name,
        "@_id": horse_id,
        // "race_entry_code": rawHorse['@_RaceEntryCode'],
        betting: horseInput.betting ?? "",
        barrier: horseInput.barrier ?? "",
        tip: horseInput.tip ?? "",
        silks: helper.decodeHTMLEntities(
          helper.capFstLtr(rawHorse.RacingColours)
        ),
        colours_link: horseInput.colours_link ?? "",
        comment: horseInput.comment ?? "",
        form_comment: horseInput.form_comment ?? "",
        gear_changes: horseInput.gear_changes ?? { gear_change: [] },
        errors: errors,
        error_log: error_log,
        colours: horseInput.colours ?? "",
        scratched: horseInput.scratched || rawHorse.Scratched ? true : false,
      };
    } else {
      helper.logError(
        error_log,
        errors,
        `Horse Not Found - ${rawHorse.Horse["@_HorseName"]} ${rawHorse.Horse["@_FoalDate"]}`
      );
      horse = { errors: errors };
      horse_inputMeetingData = { errors: errors, error_log: error_log };
    }
    if (hasBOBSBonus) {
      horse.bonus_indicator = true;
    }
    const horse_data = {
      horse_processedMeetingData: horse,
      horse_inputMeetingData: horse_inputMeetingData,
    };
    return horse_data;
  } catch (error) {
    // var sendMail = await helper.mailAlert('AUS Horse Level Load error',`AUS Horse Data couldnt generate for ${meetingDate} ${meetingTrack} ${raceNo} ${rawHorse.Horse['@_HorseName']}: ${error}`,'alert')
    // console.log(sendMail)
    console.log(
      `((ERROR)): Error in creating horse level data ${meetingDate} ${meetingTrack} ${raceNo} ${rawHorse.Horse["@_HorseName"]}: ${error}`
    );
    return {
      horse_processedMeetingData: {
        errors: `error creating horse ${rawHorse.Horse["@_HorseName"]}: ${error}`,
      },
      horse_inputMeetingData: {},
    };
  }
};

const normalizeMeetingData_NZ = async (
  rawData,
  meetingId,
  meetingStage,
  country,
  category
) => {
  /*---Start Creating Meeting Level data---*/
  var errors = [];
  try {
    const product_directory = "XML_" + meetingStage.substring(0, 1);
    const alt_track = await centaur.tracks.findOne({
      TRK_ALT_NAME: rawData.TrackName,
    });
    const product_track = alt_track
      ? alt_track.TRK_TRACK_NAME
      : rawData.TrackName;
    var meetingDate = new Date(rawData.Date);
    const meetingDateUTC = helper.sDate(meetingDate);

    var track_data = await centaur.tracks
      .findOne({ TRK_TRACK_NAME: product_track })
      .lean();
    if (track_data && track_data.TRK_FILLER_1 != "") {
      track_data = await centaur.tracks
        .findOne({ TRK_TRACK_DB_ID: parseInt(track_data.TRK_FILLER_1) })
        .lean();
    }
    // console.log(track_data)
    if (!track_data) {
      console.log(
        `((ERROR)): Meeting Level - Track Not Found - ${product_track}. aborting.`
      );
      return { eventStatus: "notProcessing" };
    } else {
      var club_name = rawData.ClubName,
        club_code = rawData.TABClubName;

      var club_data = await centaur.clubs
        .findOne({ CLB_ALT_NAME: club_name })
        .lean();
      if (!club_data) {
        club_data = await centaur.clubs
          .findOne({ CLB_ALT_NAME: club_code })
          .lean();
      }
      if (club_data) {
        club_name = club_data.CLB_CLUB_NAME;
      } else {
        club_name = club_code;
        errors.push(
          `Meeting Level - Club Not Found - put ${club_code} into alternate field on club screen`
        );
      }
      const product_date = helper.getDayMonth(meetingDate);
      const product_file =
        track_data.TRK_TRACK_3CHAR_ABBREV + product_date + ".XML";

      var record_meeting = {
        meeting: {
          product: {
            "@_directory": product_directory,
            "@_track": track_data.TRK_TRACK_3CHAR_ABBREV,
            "@_date": product_date,
            "@_file": product_file,
          },
          stage: rawData.Stage, //Acceptance Hard coded
          date: helper.convertDate(meetingDate),
          rail_position: rawData.Rail ? rawData.Rail.split(" | ")[0] : "",
          tab_indicator: category === "nonTrial" ? "T" : "Trial",
          dual_track: "N",
          track: {
            "@_name": helper.capFstLtr(track_data.TRK_TRACK_NAME),
            "@_id": track_data.TRK_TRACK_DB_ID,
            "@_expected_condition": `${rawData.TrackCondition}(${rawData.TrackConditionScale})`,
            "@_club": club_name,
            "@_track_surface": track_data.TRK_TRACK_SURFACE_TYPE,
            "@_location": track_data.TRK_LOCATION,
            "@_country": track_data.TRK_COUNTRY_OF_TRACK,
            "@_state": "NZ",
            "@_track_3char_abbrev": track_data.TRK_TRACK_3CHAR_ABBREV,
            "@_track_4char_abbrev": track_data.TRK_RSB_TRACK_ABBREV,
            "@_track_6char_abbrev": track_data.TRK_TRACK_6CHAR_ABBREV,
          },
          errors: errors,
          races: {
            race: [{}],
          },
        },
      };
    }
    /*---End Creating Meeting Level data---*/

    // Generate Empty Races Array , it contains many races---*/
    var raceArray = [];
    var tempRace = {};
    for (var i = 0; i < Object.keys(rawData.Races).length; i++) {
      var rawRace = rawData.Races[(i + 1).toString()];
      if (rawRace.Status === "Deleted") continue;
      tempRace = {
        "@_index": (i + 1).toString(),
        "@_number": rawRace.RaceNumber,
        "@_ra_id": rawRace.RaceID,
        ra_id: rawRace.RaceID,
      };

      raceArray.push(tempRace);
    } /*---End Creating Races data---*/
    record_meeting.meeting.races.race = raceArray;
    //return record_meeting

    //Generate RaceArray for InputMeetingData Section
    var inputMeetingData = {
      races: {
        race: raceArray,
      },
    };

    /*---Create Array to pass in MAP STEP FUNCTION---*/
    /* Add meetingId in Array */
    var raceDataArray = { races: [] };
    raceArray.forEach((element) => {
      temp = {
        raceId: element["@_index"],
        ra_id: element["@_ra_id"],
        meetingId: meetingId,
        eventId: "",
        stage: "genRaceData",
        meetingStage: meetingStage,
        country: country,
      };
      raceDataArray.races.push(temp);
    });

    var normalizedData = {
      record_meeting: record_meeting,
      meetingDate: meetingDateUTC,
      meetingStage: meetingStage,
      meetingState: helper.getStateName(track_data.TRK_STATE_OF_TRACK),
      raceDataArray: raceDataArray,
      inputMeetingData: inputMeetingData,
    };
    // console.log(normalizedData)
    return normalizedData;
  } catch (error) {
    var sendMail = await helper.mailAlert(
      "NZ Meeting Level Load error",
      `NZ Meeting Data couldnt generate for ${meetingId}. ${error}`,
      "alert"
    );
    console.log(sendMail);
    console.log(
      `((ERROR)): Error in creating meeting level data ${meetingId}: ${error}`
    );
    return { eventStatus: "notProcessing" };
  }
};

const normalizeRaceData_NZ = async (
  rawData,
  meetingStage,
  meetingId,
  raceId,
  ra_id,
  category
) => {
  try {
    let errors = [];
    let error_log = [];
    let eventId = "";
    let rawRace = rawData.Races[raceId.toString()]; // -1 added due to db array starting from 0

    //Fetch Meeting Date from Processed Meeting Table
    var meeting_record = await centaur.temp_meetings
      .findById(meetingId)
      .select("meetingDate")
      .select("inputMeetingData")
      .lean();
    if (!meeting_record) return { eventStatus: "notProcessing" };
    var meetingDate = meeting_record.meetingDate;
    //Fetch Track data from Track table - Error is handled at meeting level
    const alt_track = await centaur.tracks.findOne({
      TRK_ALT_NAME: rawData.TrackName,
    });
    const product_track = alt_track
      ? alt_track.TRK_TRACK_NAME
      : rawData.TrackName;
    var track_data = await centaur.tracks
      .findOne({ TRK_TRACK_NAME: product_track })
      .lean();
    var trialStatus = category == "trial" ? 1 : 0;
    eventId = helper.generateUniqueRaceId(
      meetingDate,
      track_data.TRK_TRACK_DB_ID,
      rawRace.RaceNumber,
      trialStatus
    );

    var theTime = "0000";
    var raceHours = rawRace.RaceTime.split(":");
    if (raceHours[0].length == 1) {
      theTime = "0" + raceHours[0].toString() + raceHours[1].toString();
    } else {
      theTime = raceHours[0].toString() + raceHours[1].toString();
    }

    var raceInput = {};
    // console.log(meeting_record.inputMeetingData.races)
    var silksReady = false;
    if (
      meeting_record.inputMeetingData &&
      meeting_record.inputMeetingData.silksReady
    ) {
      silksReady = true;
    }
    if (
      meeting_record.inputMeetingData.races &&
      meeting_record.inputMeetingData.races.race
    ) {
      for (var theRace of meeting_record.inputMeetingData.races.race) {
        if (theRace["@_ra_id"] == rawRace.RaceID) {
          raceInput = theRace;
          if (raceInput.error_log) {
            error_log = raceInput.error_log;
          } else if (raceInput.errors) {
            error_log = raceInput.errors;
          }
          if (raceInput.errors) errors = raceInput.errors;
          if (raceInput.start_time && raceInput.start_time != theTime) {
            helper.logError(
              error_log,
              errors,
              `start time has changed from ${raceInput.start_time} to ${theTime}`
            );
          }
          if (
            raceInput["@_ra_name"] &&
            helper.cleverCapFstLtr(raceInput["@_ra_name"]) !=
              helper.cleverCapFstLtr(rawRace.RaceName)
          ) {
            helper.logError(
              error_log,
              errors,
              `NZ race name has changed from ${
                raceInput["@_ra_name"]
              } to ${helper.cleverCapFstLtr(rawRace.RaceName)}`
            );
          }
          if (
            raceInput.raRaceNameShort &&
            raceInput.raRaceNameShort != rawRace.RaceClass
          ) {
            helper.logError(
              error_log,
              errors,
              `NZ race Shortname has changed from ${raceInput.raRaceNameShort} to ${rawRace.RaceClass}, check for benchmarks`
            );
          }
        }
      }
    }
    var classArray = await calculateNzClass(rawRace.RaceClass);
    if (classArray.classes.class_id == 999) {
      helper.logError(
        error_log,
        errors,
        `NZ race class NOT FOUND please get '${rawRace["@_raceclass"]}' added to nz_classes table (ask Luke or Puck)`
      );
    }

    var restrictions = {};
    if (classArray.age_rest) restrictions["@_age"] = classArray.age_rest;
    if (classArray.sex_rest) restrictions["@_sex"] = classArray.sex_rest;
    restrictions["@_jockey"] =
      rawRace.ApprenticeAllowance == "true"
        ? "Apprentices Can Claim"
        : "Apprentices Cannot Claim";

    var distance = parseInt(rawRace.Distance);

    let group = "";

    if (rawRace.RaceGroup.includes("Listed Race")) {
      group = "LR";
    } else if (rawRace.RaceGroup.includes("Group")) {
      group = parseInt(rawRace.RaceGroup.replace("Group ", ""));
    }

    if (!theTime) {
      helper.logError(error_log, errors, `start time is missing`);
    }
    if (!distance) {
      helper.logError(error_log, errors, `race distance is missing`);
    }
    let prizes = await helper.processPrizes_NZ(rawRace);

    if (!rawRace.TotalPool || prizes.length < 2) {
      helper.logError(error_log, errors, `race prizemoney is missing`);
    }
    if (!classArray.weight_type) {
      helper.logError(error_log, errors, `race weight_type is missing`);
    }
    var race = {
      "@_number": rawRace.RaceNumber,
      "@_name":
        raceInput.raceName && raceInput.raceName != ""
          ? raceInput.raceName
          : helper.cleverCapFstLtr(rawRace.RaceName),
      "@_id": eventId,
      "@_ra_id": ra_id,
      "@_nominations_number": rawRace.RaceNumber,
      race_stage: meetingStage,
      track: {
        "@_name": track_data.TRK_TRACK_NAME,
        "@_id": track_data.TRK_TRACK_DB_ID,
        "@_country": track_data.TRK_COUNTRY_OF_TRACK,
        "@_track_surface": track_data.TRK_TRACK_SURFACE_TYPE,
        "@_track_3char_abbrev": track_data.TRK_TRACK_3CHAR_ABBREV,
      },
      distance: {
        "@_metres": distance,
      },
      restrictions: restrictions,
      weight_type: classArray.weight_type,
      min_hcp_weight: 0,
      track_type: "",
      group: group,
      classes: classArray.classes,
      race_type: helper.getNzRaceType(rawRace.RaceClass),
      prizes: { prize: prizes },
      records: {},
      start_time: theTime,
      temporary_weight_change: "",
      starters: "",
      official_margin_1: "",
      official_margin_2: "",
      horses: {
        horse: [{}],
      },
      errors: errors,
    };

    //Generate Race Data for InputMeetingData Section
    var race_inputMeetingData = {
      "@_name": raceInput.raceName ?? helper.capFstLtr(rawRace.RaceName),
      "@_ra_name": rawRace.RaceName,
      "@_number": rawRace.RaceNumber,
      "@_ra_id": ra_id,
      "@_nominations_number": rawRace.RaceNumber,
      "@_id": eventId,
      raceName: raceInput.raceName ?? "",
      raceNameShort: raceInput.raceNameShort ?? rawRace.RaceClass,
      raceClass: rawRace.RaceClass,
      start_time: theTime,
      horses: {
        horse: [{}],
      },
      errors: errors,
      error_log: error_log,
    };
    //return race

    var mainArray = [];
    // Process horseArray
    var count = 0;
    var sequenceCount = 10;
    for (const horseKey in rawRace.Runner) {
      const rawHorse = rawRace.Runner[horseKey];
      //console.log(rawHorse);
      mainArray.push(
        await genHorseLevelData_NZ(
          meetingStage,
          meetingDate,
          meetingId,
          rawHorse,
          sequenceCount,
          distance,
          raceInput,
          rawRace.RaceNumber,
          track_data.TRK_TRACK_NAME,
          silksReady,
          classArray.classes.class_id
        )
      );
      sequenceCount = sequenceCount + 10;
      count++;
      //break
    }
    var horseCompleted = await Promise.all(mainArray);
    //race.horses.horse = horseCompleted

    var horseArray = [];
    var inputMeetingDataHorseArray = [];

    var updatedErrors = [];
    for (error of race.errors) {
      if (!error.includes("Horses with alerts:")) {
        updatedErrors.push(error);
      }
    }
    race.errors = updatedErrors;
    if (horseCompleted) {
      var barrierArray = [];
      var tabArray = [];
      for (record of horseCompleted) {
        tabArray.push(parseInt(record.horse_processedMeetingData.tab_number));
        barrierArray.push(parseInt(record.horse_processedMeetingData.barrier));
        try {
          horseArray.push(record.horse_processedMeetingData);
        } catch (err) {
          console.log("error with horse" + horseCompleted);
          console.log(record);
          console.log(horseCompleted);
        }
        inputMeetingDataHorseArray.push(record.horse_inputMeetingData);
      }
      tabArray.sort((a, b) => a - b);
      barrierArray.sort((a, b) => a - b);
      for (i = 0; i < horseCompleted.length; i++) {
        // console.log(i + 1, barrierArray[i], tabArray[i]);
        if (!barrierArray[i] || parseInt(barrierArray[i]) != i + 1)
          [[], race.errors] = helper.logError(
            [],
            race.errors,
            `Horse Barriers don't match, please check`
          );
        if (!tabArray[i] || parseInt(tabArray[i]) != i + 1)
          [[], race.errors] = helper.logError(
            [],
            race.errors,
            `Horse TAB numbers don't match, please check`
          );
      }
      race.horses.horse = horseArray;
      race_inputMeetingData.horses.horse = inputMeetingDataHorseArray;
      var normalizedRaceData = {
        race: race,
        race_inputMeetingData: race_inputMeetingData,
      };
      return normalizedRaceData;
    }

    //return race
    return "Race Data Updated";
  } catch (error) {
    console.log(error);
    var sendMail = await helper.mailAlert(
      "NZ Race Level Load error",
      `NZ Race Data couldnt generate for ${meetingId} ${raceId}. ${error}`,
      "alert"
    );
    console.log(sendMail);
    console.log(
      `((ERROR)): Error in creating race level data ${meetingId} ${raceId}: ${error}`
    );
    return { eventStatus: "notProcessing" };
  }
};

const genHorseLevelData_NZ = async (
  meetingStage,
  meetingDate,
  meetingId,
  rawHorse,
  sequenceCount,
  distance,
  raceInput,
  raceNo,
  meetingTrack,
  silksReady,
  raceClass,
  category
) => {
  try {
    const start = Date.now();
    var errors = [];
    var error_log = [];
    var dam_id = "";
    var name = "",
      country = "",
      age = "",
      colour = "",
      sex = "",
      horse_id = "",
      foaling_date = "";
    var sire_name = "",
      sire_country = "",
      dam_name = "",
      dam_country = "",
      sire_dam_name = "",
      sire_dam_country = "",
      sire_dam_id = "";
    var trainer_stats = "",
      trainer_name = "",
      trainer_firstname = "",
      trainer_surname = "",
      trainer_id = "",
      training_location = "";
    var owners = "",
      colours = "",
      prizmoney_won = "",
      startsData = "",
      last4s = "",
      last10s = "",
      last15s = "",
      last20s = "";
    var current_blinker_ind = "N",
      win_percentage = "",
      place_percentage = "",
      statistics = "",
      win_distances = {};
    var barrier = 0,
      class_quality = "F",
      pace = [0, ""];
    //Find DAM Id first
    var theInputHorse = {};
    const horseName = rawHorse.Demographics.HorseRaceBookName.replace(
      / \(.+?\)$/,
      ""
    );
    if (!horseName)
      console.log(
        `${rawHorse.Demographics.HorseRaceBookName} couldnt generate a good horsename`
      );
    if (raceInput.horses && raceInput.horses.horse) {
      for (const inputHorse of raceInput.horses.horse) {
        // console.log(rawHorse['@_runnername'],inputHorse['@_name'])
        if (horseName.toLowerCase() == inputHorse["@_name"].toLowerCase()) {
          theInputHorse = inputHorse;
        }
      }
    }

    var horseData = {};

    const damHorse = await identifyDam(
      rawHorse.Demographics.Dam.toUpperCase(),
      rawHorse.Demographics.DamSire.toUpperCase()
    );

    if (damHorse) {
      dam_id = damHorse.HRN_HORSE_ID;
    } else {
      // console.log('Horse Dam Not found: ' + rawHorse['@_runnerbreeding'])
      helper.logError(
        error_log,
        errors,
        `${horseName} dam not found ${rawHorse.Demographics.Dam}`
      );
    }

    // Fetch Horse data from horse_names table
    horseData = await centaur.horses
      .findOne()
      .where("HRN_HORSE_NAME")
      .equals(helper.upperCase(horseName))
      .where("HOR_DAM_ID")
      .equals(dam_id)
      .where("HOR_FOALING_DATE")
      .equals(helper.sDate(rawHorse.Demographics.FoalingDate))
      .lean();
    // console.log("Finding Horse: " + helper.upperCase(rawHorse['@_runnername']))
    barrier = rawHorse.Barrier;

    if (!horseData && rawHorse.Demographics.Dam) {
      var horseDataArray = await centaur.horses
        .find()
        .where("HRN_HORSE_NAME")
        .equals(helper.upperCase(horseName))
        .where("HOR_FOALING_DATE")
        .equals(helper.sDate(rawHorse.Demographics.FoalingDate))
        .lean();
      for (horse of horseDataArray) {
        var possibleDam = await centaur.horses
          .findOne()
          .where("HRN_HORSE_ID")
          .equals(horse.HOR_DAM_ID)
          .lean();
        if (
          possibleDam &&
          (possibleDam.HRN_HORSE_NAME ==
            helper.upperCase(rawHorse.Demographics.Dam) ||
            (possibleDam.HRN_PREV_NAMES &&
              possibleDam.HRN_PREV_NAMES.includes(
                helper.upperCase(rawHorse.Demographics.Dam)
              )))
        ) {
          horseData = horse;
          break;
        }
      }
    }

    if (horseData) {
      const horseId = horseData.HRN_HORSE_ID;

      //Fetch Full Horse data from horses table
      const horseSireId = horseData.HOR_SIRE_ID;
      const horseDamId = horseData.HOR_DAM_ID;

      name =
        horseData.HRN_DISPLAY_NAME ??
        helper.capFstLtr(horseData.HRN_HORSE_NAME);
      country = horseData.HOR_COUNTRY_OF_ORIGIN;
      if (!country) {
        helper.logError(
          error_log,
          errors,
          `${horseName} ${horseId} has no country`
        );
      }
      age = helper.getHorseAge(horseData.HOR_FOALING_DATE, meetingDate);
      colour = helper.getHorseColor(horseData.HOR_COLOUR);
      if (colour === "") {
        await helper.mailAlert(
          "NZ Horse Color Not Found",
          `NZ Race Horse color could not found its empty horseId: ${horseId}  horseData: ${horseData}`,
          "prod"
        );
        console.log(
          `((ERROR)): NZ Horse Color Not Found - ${horseName}, id: ${horseId}`
        );
      }
      sex = helper.getHorseSex(horseData.HOR_SEX, age);
      horse_id = horseData.HRN_HORSE_ID;
      foaling_date = helper.convertDate(horseData.HOR_FOALING_DATE);

      //Get SIRE Data from horse_name
      const sireData = await centaur.horses
        .findOne({ HRN_HORSE_ID: horseSireId })
        .select("HRN_HORSE_NAME HOR_COUNTRY_OF_ORIGIN")
        .lean();
      if (sireData) {
        sire_name =
          sireData.HRN_DISPLAY_NAME ??
          helper.capFstLtr(sireData.HRN_HORSE_NAME);
        sire_country = sireData.HOR_COUNTRY_OF_ORIGIN;
      } else {
        helper.logError(
          error_log,
          errors,
          `NZ Horse sire not found, Horse: ${horseName}, id: ${horseId}`
        );
      }

      //Get DAM Data from horse_name
      const damData = await centaur.horses
        .findOne({ HRN_HORSE_ID: horseDamId })
        .select("HRN_HORSE_NAME HOR_COUNTRY_OF_ORIGIN")
        .lean();
      if (damData) {
        dam_name =
          damData.HRN_DISPLAY_NAME ?? helper.capFstLtr(damData.HRN_HORSE_NAME);
        dam_country = damData.HOR_COUNTRY_OF_ORIGIN;
      } else {
        helper.logError(
          error_log,
          errors,
          `NZ Horse dam not found, Horse: ${horseName}, id: ${horseId}`
        );
      }

      //Get DAM Data from horse table
      const damFullData = await centaur.horses
        .findOne({ HRN_HORSE_ID: horseDamId })
        .select("HOR_SIRE_ID")
        .lean();

      //Get SIRE of DAM
      const sireOfDamData = await centaur.horses
        .findOne({ HRN_HORSE_ID: damFullData.HOR_SIRE_ID })
        .select("HRN_HORSE_NAME HOR_COUNTRY_OF_ORIGIN")
        .lean();
      if (sireOfDamData) {
        sire_dam_name =
          sireOfDamData.HRN_DISPLAY_NAME ??
          helper.capFstLtr(sireOfDamData.HRN_HORSE_NAME);
        sire_dam_country = sireOfDamData.HOR_COUNTRY_OF_ORIGIN;
        sire_dam_id = damFullData.HOR_SIRE_ID;
      } else {
        helper.logError(
          error_log,
          errors,
          `NZ Horse damsire not found, Horse: ${horseName}, id: ${horseId}`
        );
      }
      let trainer_data = null;
      if (rawHorse.Demographics.TrainerName) {
        trainer_data = await centaur.trainers
          .findOne({ NZ_Name: rawHorse.Demographics.TrainerName })
          .lean();
      }
      if (trainer_data) {
        trainer_stats = await getTrainerStats(trainer_data.TRN_TRAINER_ID);
        trainer_name = helper.capFstLtr(trainer_data.TRN_TRAINER_DISPLAYNAME);
        trainer_firstname = helper.capFstLtr(
          trainer_data.TRN_TRAINER_FIRSTNAME
        );
        trainer_surname = helper.capFstLtr(trainer_data.TRN_TRAINER_SURNAME);
        trainer_id = trainer_data.TRN_TRAINER_ID;
      } else {
        var trainer_name_srch = rawHorse.Demographics.TrainerName
          ? helper.splitNameBySpace(rawHorse.Demographics.TrainerName)
          : { firstName: "NOTRAINER", lastName: "HASTHISNAME" };
        const trainer_data_1 = await centaur.trainers
          .findOne({
            TRN_TRAINER_FIRSTNAME: trainer_name_srch.firstName,
            TRN_TRAINER_SURNAME: trainer_name_srch.lastName,
          })
          .lean();
        if (trainer_data_1) {
          trainer_stats = await getTrainerStats(trainer_data_1.TRN_TRAINER_ID);
          trainer_name = helper.capFstLtr(
            trainer_data_1.TRN_TRAINER_DISPLAYNAME
          );
          trainer_firstname = helper.capFstLtr(
            trainer_data_1.TRN_TRAINER_FIRSTNAME
          );
          trainer_surname = helper.capFstLtr(
            trainer_data_1.TRN_TRAINER_SURNAME
          );
          trainer_id = trainer_data_1.TRN_TRAINER_ID;

          helper.logError(
            error_log,
            errors,
            `NZ Trainer partial match only, make sure NZ Name is filled: ${rawHorse.Demographics.TrainerName}`
          );
        } else {
          helper.logError(
            error_log,
            errors,
            `NZ Trainer Not Found - ${rawHorse.Demographics.TrainerName}`
          );
        }
        // Attach current trainer if trainer not found
      }

      training_location =
        rawHorse.Demographics.TrainingLocation ??
        horseData.HOR_TRAINING_LOCATION;
      owners = rawHorse.Demographics.OwnerRaceBook;
      if (!owners) {
        helper.logError(error_log, errors, `horse owners missing`);
      }
      colours = helper.capFstLtr(rawHorse.Colour.replace(/&amp;/g, "&"));
      if (!colours) {
        helper.logError(error_log, errors, `horse silks missing`);
      }
      prizmoney_won = horseData.HOR_TOTAL_PRIZEMONEY;
      if (horseData.HOR_CURRENT_BLINKER_IND == "") {
        current_blinker_ind = horseData.HOR_CURRENT_BLINKER_IND;
      }
      statistics = "";

      var jockey_record = {};
      if (rawHorse.JockeyRaceBookName && rawHorse.JockeyRaceBookName != "-") {
        var jockeyname = rawHorse.JockeyRaceBookName.replace(
          / \([0-9.am]*\)/g,
          ""
        ).replace(/^Ms /g, "");
        var jockey_name_srch = helper.splitNameBySpace(
          rawHorse.JockeyRaceBookName.replace(/^Ms /g, "")
        );

        var jockey_data = await centaur.jockeys
          .findOne({ JOC_JOCKEY_NZNAME: jockeyname })
          .lean();
        if (!jockey_data) {
          jockey_data = await centaur.jockeys
            .findOne({
              JOC_JOCKEY_FIRSTNAME: jockey_name_srch.firstName,
              JOC_JOCKEY_SURNAME: jockey_name_srch.lastName,
            })
            .lean();
          if (jockey_data) {
            helper.logError(
              error_log,
              errors,
              `NZ Jockey matched Generic name, please update DB - ${jockey_data.JOC_JOCKEY_DISPLAYNAME} NZ_NAME: ${jockeyname}`
            );
          }
        }
        if (!jockey_data) {
          jockey_data = await centaur.jockeys
            .findOne({
              JOC_JOCKEY_RANAME:
                jockey_name_srch.firstName + " " + jockey_name_srch.lastName,
            })
            .lean();
          if (jockey_data) {
            helper.logError(
              error_log,
              errors,
              `NZ Jockey matched AUS name, please update DB - ${jockey_data.JOC_JOCKEY_DISPLAYNAME} NZ_NAME: ${jockeyname}`
            );
          }
        }
        if (jockey_data) {
          var jockey_name = jockey_data.JOC_JOCKEY_DISPLAYNAME;
          var jockey_firstname = jockey_data.JOC_JOCKEY_FIRSTNAME;
          var jockey_surname = jockey_data.JOC_JOCKEY_SURNAME;
          var jockey_id = jockey_data.JOC_JOCKEY_ID;
          var jockey_apprentice_indicator = "";
          if (rawHorse.LicenceAndAllowance !== null) {
            jockey_apprentice_indicator = "Y";
            jockey_allowance_weight =
              parseFloat(
                rawHorse.LicenceAndAllowance.replace("a", "").replace("-", "")
              ) ?? "";
          } else {
            jockey_apprentice_indicator = "";
            jockey_allowance_weight = "";
          }
          jockey_record = {
            jockey: {
              "@_name": jockey_name,
              "@_firstname": jockey_firstname,
              "@_surname": jockey_surname,
              "@_id": jockey_id,
              "@_apprentice_indicator": jockey_apprentice_indicator,
              "@_allowance_weight": jockey_allowance_weight,
              "@_riding_weight": parseFloat(rawHorse.Rider_NormalWeight),
              statistics: await getJockeyStats(jockey_id),
            },
          };
        } else {
          if (jockey_name_srch.firstName || jockey_name_srch.lastName) {
            helper.logError(
              error_log,
              errors,
              `Jockey Not Found - ${rawHorse.JockeyRaceBookName}`
            );
          }
        }
      } else {
        jockey_record = {
          jockey: {
            "@_name": "",
            "@_firstname": "",
            "@_surname": "",
            "@_id": "0",
            "@_apprentice_indicator": "",
            "@_allowance_weight": "",
            statistics: {},
          },
        };
      }

      var meetingDateFormat = moment(meetingDate).format("YYYY_MM_DD");
      var path =
        meetingDateFormat +
        "/" +
        meetingTrack.replace(/ /g, "_").toLowerCase() +
        "/" +
        raceNo +
        "/" +
        rawHorse.RunnerNumber;
      var rating =
        rawHorse.Rating !== null
          ? rawHorse.Rating.toString().replace("R", "")
          : 0;
      rating = parseFloat(rating);
      if (!rawHorse.RunnerNumber) {
        helper.logError(error_log, errors, `horse tab number missing`);
      }
      if (!rawHorse.Weight || parseInt(rawHorse.Weight) < 45) {
        helper.logError(error_log, errors, `horse weight missing`);
      }
      var horse = {
        "@_name": name,
        "@_country": country,
        "@_age": age,
        "@_colour": colour,
        "@_sex": sex,
        "@_id": horse_id,
        "@_foalingdate": foaling_date,
        sire: {
          "@_name": sire_name,
          "@_country": sire_country,
          "@_id": horseSireId,
        },
        dam: {
          "@_name": dam_name,
          "@_country": dam_country,
          "@_id": horseDamId,
        },
        sire_of_dam: {
          "@_name": sire_dam_name,
          "@_country": sire_dam_country,
          "@_id": sire_dam_id,
        },
        trainer: {
          statistics: trainer_stats,
          "@_name": trainer_name,
          "@_firstname": trainer_firstname,
          "@_surname": trainer_surname,
          "@_id": trainer_id,
        },
        training_location: training_location,
        owners: owners,
        colours: colours,
        prizemoney_won: prizmoney_won,
        last_four_starts: last4s,
        last_ten_starts: last10s,
        last_fifteen_starts: last15s,
        last_twenty_starts: last20s,
        FF5_dry: "",
        FF5_wet: "",
        FF_Dry_Rating_100: "",
        FF_Dry_Rating_100: "",
        current_blinker_ind: current_blinker_ind,
        win_percentage: win_percentage,
        place_percentage: place_percentage,
        pace_value: pace[0],
        pace: pace[1],
        statistics: statistics,
        win_distances: win_distances,
        weight_carried: rawHorse.Weight,
        weight: {
          "@_allocated": parseFloat(rawHorse.Weight),
          "@_total": parseFloat(rawHorse.Weight),
        },
        horse_colours_image:
          "https://silks.medialityracing.com.au/images/jpg/" +
          path +
          "_front.jpg",
        horse_colours_image_png:
          "https://silks.medialityracing.com.au/images/png/" +
          path +
          "_front.png",
        horse_colours_image_svg:
          "https://silks.medialityracing.com.au/images/svg/" +
          path +
          "_front.svg",
        tab_number: rawHorse.RunnerNumber,
        barrier: barrier,
        betting: theInputHorse.betting ?? "",
        tip: theInputHorse.tip ?? "",
        gear_changes: theInputHorse.gear_changes ?? { gear_change: [] },
        comment: theInputHorse.comment ?? "",
        errors: errors,
        rating: rating,
      };

      if (rawHorse.Scratched === "true") {
        horse.scratched = true;
      }

      if (rawHorse.Ballot && rawHorse.Ballot.includes("B")) {
        horse.emergency_indicator = "E";
      }

      horse = Object.assign(horse, jockey_record);
      // Horse for InputDataMeeting
      var horse_inputMeetingData = {
        "@_name": name,
        "@_id": horseId ?? theInputHorse["@_id"],
        betting: theInputHorse.betting ?? "",
        tip: theInputHorse.tip ?? "",
        silks: helper.capFstLtr(rawHorse.Colour),
        colours_link: theInputHorse.colours_link ?? "",
        comment: theInputHorse.comment ?? "",
        gear_changes: theInputHorse.gear_changes ?? { gear_change: [] },
        errors: errors,
        error_log: error_log,
        colours: theInputHorse.colours ?? "",
        barrier: theInputHorse.barrier ?? "",
        scratched: rawHorse.Scratched || theInputHorse.scratched ? true : false,
      };
    } else {
      helper.logError(
        error_log,
        errors,
        `Horse Not Found - ${horseName} (${rawHorse.Demographics.FoalingCountry}) - ${rawHorse.Demographics.FoalingDate} - (${rawHorse.Demographics.Sire}-${rawHorse.Demographics.Dam}(${rawHorse.Demographics.DamSire}))`
      );
      var horse = { errors: errors };
      var horse_inputMeetingData = { errors: errors };
    }

    const horse_data = {
      horse_processedMeetingData: horse,
      horse_inputMeetingData: horse_inputMeetingData,
    };
    return horse_data;
  } catch (error) {
    // var sendMail = await helper.mailAlert('NZ Horse Level Load error',`NZ Horse Data couldnt generate for ${meetingId} ${raceNo} ${rawHorse['@_runnername']}. ${error}`,'alert')
    // console.log(sendMail)
    console.log(
      `((ERROR)): Error in creating horse level data  ${meetingId} ${raceNo} ${rawHorse.Demographics.HorseRaceBookName}: ${error}`
    );
    return {
      horse_processedMeetingData: {
        errors: `error creating horse ${rawHorse.Demographics.HorseRaceBookName}: ${error}`,
      },
      horse_inputMeetingData: {},
    };
  }
};

const normalizeMeetingData_HK = async (
  rawData,
  meetingId,
  meetingStage,
  country,
  category
) => {
  /*---Start Creating Meeting Level data---*/
  var errors = [];
  try {
    const product_directory = "XML_A"; // Hard Coded
    var product_track = rawData.OfficialProgrammeFile["@_venue"];
    if (product_track == "Shatin") {
      product_track = "Sha Tin";
    }
    const ht = rawData.OfficialProgrammeFile["@_raceDate"];
    const hd = ht.split("-");
    var meetingDate = hd[2] + "/" + hd[1] + "/" + hd[0];
    const meetingDateUTC = helper.convertToUTC(ht);

    var track_data = await centaur.tracks
      .findOne({ TRK_TRACK_NAME: product_track })
      .lean();

    if (!track_data) {
      console.log(
        `((ERROR)): Meeting Level - Track Not Found - ${product_track}. aborting.`
      );
      return { eventStatus: "notProcessing" };
    } else {
      var club_name = "",
        club_id = "",
        club_code = "",
        club_state = "";
      var raw_club_name = rawData.OfficialProgrammeFile.MeetingInfo.Source;
      if (raw_club_name == "The Hong Kong Jockey Club") {
        club_code = "90301"; //HONG KONG J.C.
      } else {
        club_code = "CTC"; //CONGHUA TRAINING CENTRE or TURF CLUB
      }
      const club_data = await centaur.clubs
        .findOne({ CLB_CLUB_ID: club_code })
        .lean();
      if (!club_data) {
        // console.log(`Meeting Level - Club Not Found - ${club_code}`)
        errors.push(`Meeting Level - Club Not Found - ${club_code}`);
      } else {
        club_name = club_data.CLB_CLUB_NAME;
        club_id = club_data.CLB_CLUB_ID;
        club_state = club_data.CLB_STATE;
      }
      const product_date = helper.getDayMonth(meetingDateUTC);
      const product_file =
        track_data.TRK_TRACK_3CHAR_ABBREV + product_date + ".XML";

      var record_meeting = {
        meeting: {
          product: {
            "@_directory": product_directory,
            "@_track": track_data.TRK_TRACK_3CHAR_ABBREV,
            "@_date": product_date,
            "@_file": product_file,
          },
          stage: category == "trial" ? "Results" : "Acceptances", //Acceptance Hard coded
          date: meetingDate,
          rail_position: rawData.OfficialProgrammeFile.MeetingInfo.RailPosition
            ? rawData.OfficialProgrammeFile.MeetingInfo.RailPosition
            : "",
          tab_indicator: category == "trial" ? "Trial" : "T",
          dual_track: "N",
          track: {
            "@_name": helper.capFstLtr(track_data.TRK_TRACK_NAME),
            "@_id": track_data.TRK_TRACK_DB_ID,
            "@_expected_condition": "",
            "@_club": helper.upperCase(club_name),
            "@_track_surface": track_data.TRK_TRACK_SURFACE_TYPE,
            "@_location": track_data.TRK_LOCATION,
            "@_country": track_data.TRK_COUNTRY_OF_TRACK,
            "@_state": "HK",
            "@_track_3char_abbrev": track_data.TRK_TRACK_3CHAR_ABBREV,
            "@_track_4char_abbrev": track_data.TRK_RSB_TRACK_ABBREV,
            "@_track_6char_abbrev": track_data.TRK_TRACK_6CHAR_ABBREV,
            "@_night_meeting":
              (rawData.OfficialProgrammeFile.MeetingInfo.MeetingType = "N")
                ? "N"
                : "",
          },
          errors: errors,
          races: {
            race: [{}],
          },
        },
      };
    }
    /*---End Creating Meeting Level data---*/

    // Generate Empty Races Array , it contains many races---*/
    var raceArray = [];
    var tempRace = {};
    if (
      typeof rawData.OfficialProgrammeFile.RaceRec[Symbol.iterator] !==
      "function"
    ) {
      rawData.OfficialProgrammeFile.RaceRec = [
        rawData.OfficialProgrammeFile.RaceRec,
      ];
    }
    for (var i = 0; i < rawData.OfficialProgrammeFile.RaceRec.length; i++) {
      var rawRace = rawData.OfficialProgrammeFile.RaceRec[i];
      tempRace = {
        "@_number": rawRace["@_num"],
        "@_ra_id": i,
        ra_id: i,
      };

      raceArray.push(tempRace);
    } /*---End Creating Races data---*/
    record_meeting.meeting.races.race = raceArray;

    //Generate RaceArray for InputMeetingData Section
    var inputMeetingData = {
      races: {
        race: raceArray,
      },
    };
    //return record_meeting

    /*---Create Array to pass in MAP STEP FUNCTION---*/
    /* Add meetingId in Array */
    let raceDataArray = { races: [] };
    raceArray.forEach((element) => {
      temp = {
        raceId: element["@_number"],
        ra_id: element["@_ra_id"],
        meetingId: meetingId,
        eventId: "",
        stage: "genRaceData",
        meetingStage: meetingStage,
        country: country,
      };
      raceDataArray.races.push(temp);
    });

    var normalizedData = {
      record_meeting: record_meeting,
      meetingDate: meetingDateUTC,
      meetingStage: meetingStage,
      meetingState: helper.getStateName(track_data.TRK_STATE_OF_TRACK),
      raceDataArray: raceDataArray,
      inputMeetingData: inputMeetingData,
    };
    return normalizedData;
  } catch (error) {
    var sendMail = await helper.mailAlert(
      "HK Meeting Level Load error",
      `HK Meeting Data couldnt generate for ${meetingId}. ${error}`,
      "alert"
    );
    console.log(sendMail);
    console.log(
      `((ERROR)): Error in creating meeting level data ${meetingId}: ${error}`
    );
    return { eventStatus: "notProcessing" };
  }
};

const normalizeRaceData_HK = async (
  rawData,
  meetingStage,
  meetingId,
  raceId
) => {
  try {
    let errors = [];
    let error_log = [];
    let eventId = "";
    if (
      typeof rawData.OfficialProgrammeFile.RaceRec[Symbol.iterator] !==
      "function"
    ) {
      rawData.OfficialProgrammeFile.RaceRec = [
        rawData.OfficialProgrammeFile.RaceRec,
      ];
    }
    let rawRace = rawData.OfficialProgrammeFile.RaceRec[raceId - 1]; // -1 added due to db array starting from 0

    //Fetch Meeting Date from Processed Meeting Table
    var meeting_record = await centaur.temp_meetings
      .findById(meetingId)
      .select("meetingDate")
      .select("inputMeetingData")
      .lean();
    if (!meeting_record) return { eventStatus: "notProcessing" };
    var meetingDate = meeting_record.meetingDate;
    //Fetch Track data from Track table - Error is handled at meeting level
    var product_track = rawData.OfficialProgrammeFile["@_venue"];
    if (product_track == "Shatin") {
      product_track = "Sha Tin";
    }
    var track_data = await centaur.tracks
      .findOne({ TRK_TRACK_NAME: product_track })
      .lean();

    var trialStatus = 0; // Updated afterward
    if (rawRace.RaceClass == "Barrier Trial") trialStatus = 1;
    eventId = helper.generateUniqueRaceId(
      meetingDate,
      track_data.TRK_TRACK_DB_ID,
      parseInt(rawRace["@_num"]),
      trialStatus
    );

    var theTime = rawRace.PostTime ? rawRace.PostTime.replace(":", "") : "0000";
    var raceName = helper.capFstLtr(rawRace.RaceName);
    var raceInput = {};

    var silksReady = false;
    if (
      meeting_record.inputMeetingData &&
      meeting_record.inputMeetingData.silksReady
    ) {
      silksReady = true;
    }

    if (
      meeting_record.inputMeetingData.races &&
      meeting_record.inputMeetingData.races.race
    ) {
      for (var theRace of meeting_record.inputMeetingData.races.race) {
        if (theRace["@_id"] == eventId) {
          raceInput = theRace;
          if (raceInput.error_log) {
            error_log = raceInput.error_log;
          } else if (raceInput.errors) {
            error_log = raceInput.errors;
          }
          errors = raceInput.errors;
          if (raceInput.start_time && raceInput.start_time != theTime) {
            helper.logError(
              error_log,
              errors,
              `start time has changed from ${raceInput.start_time} to ${theTime}`
            );
          }
          if (
            raceInput["@_ra_name"] &&
            helper.cleverCapFstLtr(raceInput["@_ra_name"]) !=
              helper.cleverCapFstLtr(raceName)
          ) {
            helper.logError(
              error_log,
              errors,
              `HK race name has changed from ${
                raceInput["@_ra_name"]
              } to ${helper.cleverCapFstLtr(raceName)}`
            );
          }
          if (
            raceInput.raRaceNameShort &&
            raceInput.raRaceNameShort != rawRace.Race_Type
          ) {
            helper.logError(
              error_log,
              errors,
              `HK race class has changed from ${raceInput.raRaceNameShort} to ${rawRace.RaceClass}, check for benchmarks`
            );
          }
        }
      }
    }
    var theClass = {};
    var findclass = await centaur.race_classes
      .findOne({ CLA_CLASS_LONG_DISP: helper.capFstLtr(rawRace.RaceClass) })
      .lean();
    if (findclass) {
      theClass.class = findclass.CLA_CLASS_LONG_DISP;
      theClass.class_id = findclass.CLA_CLASS_DB_ID;
    } else {
      theClass = { class: "Open", class_id: 4 };
      helper.logError(
        error_log,
        errors,
        `HK race class '${rawRace.RaceClass}' didnt match our table`
      );
    }
    var restrictions = {};
    if (rawRace.Apprentice == "E") {
      restrictions["@_jockey"] = "Apprentices Can Claim";
    } else {
      restrictions["@_jockey"] = "Apprentices Cannot Claim";
    }
    if (
      rawRace.AgeConditionFrom &&
      rawRace.AgeConditionTo &&
      rawRace.AgeConditionFrom == rawRace.AgeConditionTo
    ) {
      restrictions["@_age"] = rawRace.AgeConditionFrom + "yo";
    } else if (rawRace.AgeConditionFrom && rawRace.AgeConditionTo) {
      restrictions["@_age"] =
        rawRace.AgeConditionFrom + "yo to " + rawRace.AgeConditionTo + "yo";
    } else if (rawRace.AgeConditionFrom) {
      restrictions["@_age"] = rawRace.AgeConditionFrom + "yo+";
    }
    var distance = rawRace.Dist;

    if (!theTime) {
      helper.logError(error_log, errors, `start time is missing`);
    }
    if (!distance) {
      helper.logError(error_log, errors, `race distance is missing`);
    }
    if (!rawRace.Prize && !trialStatus) {
      helper.logError(error_log, errors, `race prizemoney is missing`);
    }
    var race = {
      "@_number": parseInt(rawRace["@_num"]),
      "@_name": raceName,
      "@_id": eventId,
      "@_nominations_number": parseInt(rawRace["@_num"]),
      race_stage: trialStatus ? "Results" : "Acceptances",
      track: {
        "@_name": helper.capFstLtr(track_data.TRK_TRACK_NAME),
        "@_id": track_data.TRK_TRACK_DB_ID,
        "@_track_surface": track_data.TRK_TRACK_SURFACE_TYPE,
        "@_country": track_data.TRK_COUNTRY_OF_TRACK,
        "@_track_3char_abbrev": track_data.TRK_TRACK_3CHAR_ABBREV,
      },
      distance: {
        "@_metres": distance,
      },
      restrictions: restrictions,
      weight_type: rawRace.WeightCondition ?? "Handicap",
      min_hcp_weight: 0,
      track_type: helper.capFstLtr(rawRace.RaceTrack),
      classes: theClass,
      group: rawRace.IntlGroupRaceInd ?? "",
      race_type: "Flat", //rawRace['@_racetype'],
      prizes: {
        prize: rawRace.Prize
          ? helper.processPrizes_HK(rawRace.Prize.PrizeMoney)
          : [],
      },
      records: "",
      start_time: theTime,
      temporary_weight_change: "",
      starters: rawRace.NoOfStarter,
      official_margin_1: "",
      official_margin_2: "",
      duration: rawRace.RaceDuration ?? "",
      horses: {
        horse: [{}],
      },
      errors: errors,
    };
    if (
      (rawRace.RaceType || "").toLowerCase().includes("hurdle") ||
      (rawRace.RaceType || "").toLowerCase().includes("steeple")
    ) {
      helper.logError(
        error_log,
        errors,
        "Race type contains 'hurdle' or 'steeple'"
      );
    }
    //Generate Race Data for InputMeetingData Section

    var race_inputMeetingData = {
      "@_name": raceInput.raceName ?? helper.capFstLtr(rawRace.RaceName),
      "@_ra_name": rawRace.Race_Name,
      raceName: helper.decodeHTMLEntities(raceInput.raceName) ?? "",
      raceNameShort: raceInput.raceNameShort ?? rawRace.RaceClass,
      "@_number": rawRace["@_num"],
      "@_nominations_number": rawRace["@_num"],
      "@_id": eventId,
      start_time: theTime,
      silksReady: raceInput.silksReady ?? false,
      horses: {
        horse: [{}],
      },
      errors: errors,
      error_log: error_log,
    };

    //return race

    var mainArray = [];
    // Process horseArray
    var count = 0;
    var sequenceCount = 10;
    for (rawHorse of rawRace.Runner) {
      mainArray.push(
        await genHorseLevelData_HK(
          meetingDate,
          meetingId,
          rawHorse,
          count,
          raceInput,
          parseInt(rawRace["@_num"]),
          track_data.TRK_TRACK_NAME
        )
      );
      sequenceCount = sequenceCount + 10;
      count++;
      //break
    }
    var horseCompleted = await Promise.all(mainArray);
    race.horses.horse = horseCompleted;

    //return race
    var horseArray = [];
    var inputMeetingDataHorseArray = [];
    var updatedErrors = [];
    for (error of race.errors) {
      if (!error.includes("Horses with alerts:")) {
        updatedErrors.push(error);
      }
    }
    race.errors = updatedErrors;

    /* Update Meeting with Race Data */
    if (horseCompleted) {
      var barrierArray = [];
      var tabArray = [];
      for (record of horseCompleted) {
        tabArray.push(parseInt(record.horse_processedMeetingData.tab_number));
        barrierArray.push(parseInt(record.horse_processedMeetingData.barrier));
        try {
          horseArray.push(record.horse_processedMeetingData);
        } catch (err) {
          console.log("error with horse" + horseCompleted);
          console.log(record);
          console.log(horseCompleted);
        }
        inputMeetingDataHorseArray.push(record.horse_inputMeetingData);
      }
      tabArray.sort((a, b) => a - b);
      barrierArray.sort((a, b) => a - b);
      for (i = 0; i < horseCompleted.length; i++) {
        // console.log(i+1,barrierArray[i],tabArray[i])
        if (!barrierArray[i] || parseInt(barrierArray[i]) != i + 1)
          [[], race.errors] = helper.logError(
            [],
            race.errors,
            `Horse Barriers don't match, please check`
          );
        if (!tabArray[i] || parseInt(tabArray[i]) != i + 1)
          [[], race.errors] = helper.logError(
            [],
            race.errors,
            `Horse TAB numbers don't match, please check`
          );
      }
      race.horses.horse = horseArray;
      race_inputMeetingData.horses.horse = inputMeetingDataHorseArray;
      var normalizedRaceData = {
        race: race,
        race_inputMeetingData: race_inputMeetingData,
      };

      return normalizedRaceData;
    }
    //return race
    return "Race Data Updated";
  } catch (error) {
    var sendMail = await helper.mailAlert(
      "HK Race Level Load error",
      `HK Race Data couldnt generate for ${meetingId} ${raceId}. ${error}`,
      "alert"
    );
    console.log(sendMail);
    console.log(
      `((ERROR)): Error in creating race level data ${meetingId} ${raceId}: ${error}`
    );
    return { eventStatus: "notProcessing" };
  }
};

const genHorseLevelData_HK = async (
  meetingDate,
  meetingId,
  rawHorse,
  count,
  raceInput,
  raceNo,
  meetingTrack
) => {
  try {
    const start = Date.now();
    var errors = [];
    var error_log = [];
    var dam_id = "",
      sire_id = "",
      sire_dam_id = "";
    var name = "",
      country = "",
      age = "",
      colour = "",
      sex = "",
      horse_id = "",
      foaling_date = "",
      pace = [0, ""];
    var sire_name = "",
      sire_country = "",
      dam_name = "",
      dam_country = "",
      sire_dam_name = "",
      sire_dam_country = "";
    var trainer_stats = "",
      trainer_name = "",
      trainer_firstname = "",
      trainer_surname = "",
      trainer_id = "",
      training_location = "";
    var owners = "",
      colours = "",
      prizmoney_won = "",
      startsData = "",
      last4s = "",
      last10s = "",
      last15s = "",
      last20s = "";
    var current_blinker_ind = "N",
      win_percentage = "",
      place_percentage = "",
      statistics = "",
      win_distances = "",
      class_quality = "F";

    //Find DAM Id first

    const damHorse = await identifyDam_HK(
      helper.extractDamName_SGP(rawHorse.horse.Breeding.Dam),
      helper.extractDamName_SGP(rawHorse.horse.Breeding.SireOfDam)
    );

    var dam_id = 0;

    if (damHorse) {
      dam_id = damHorse.HRN_HORSE_ID;
    } else {
      console.log(
        `((ERROR)): HK Horse Dam Not found: ${rawHorse.horse.Breeding.Dam} damsire: ${rawHorse.horse.Breeding.SireOfDam}`
      );
      // console.log(`Horse Dam Not found: ${rawHorse.horse.Breeding.Dam} damsire: ${rawHorse.horse.Breeding.SireOfDam}`)
    }

    // Fetch Horse data from horse_names table
    var horse_extract_name = helper.extractHorseName_HK(rawHorse.horse.name);
    const horseData = await centaur.horses
      .findOne()
      .where("HRN_HORSE_NAME")
      .equals(helper.upperCase(horse_extract_name))
      .where("HOR_DAM_ID")
      .equals(dam_id)
      .lean();
    // console.log("Finding Horse: " + helper.upperCase(horse_extract_name))

    if (damHorse && horseData) {
      const horseId = horseData.HRN_HORSE_ID;

      //Fetch Full Horse data from horses table
      const horseSireId = horseData.HOR_SIRE_ID;
      const horseDamId = horseData.HOR_DAM_ID;

      name =
        horseData.HRN_DISPLAY_NAME ??
        helper.capFstLtr(horseData.HRN_HORSE_NAME);
      country = horseData.HOR_COUNTRY_OF_ORIGIN;
      if (!country) {
        helper.logError(
          error_log,
          errors,
          `${rawHorse.horse.name} ${horseId} has no country`
        );
      }
      age = helper.getHorseAge(horseData.HOR_FOALING_DATE, meetingDate);
      colour = helper.getHorseColor(horseData.HOR_COLOUR);
      if (colour === "") {
        await helper.mailAlert(
          "HK Horse Color Not Found",
          `HK Race Horse color could not found its empty horseId: ${horseId}  horseData: ${JSON.stringify(
            horseData
          )}`,
          "prod"
        );
        console.log(
          `((ERROR)): HK Horse Color Not Found - ${rawHorse["@_runnername"]}, id: ${horseId}`
        );
      }
      sex = helper.getHorseSex(horseData.HOR_SEX, age);
      horse_id = horseData.HRN_HORSE_ID;
      foaling_date = helper.convertDate(horseData.HOR_FOALING_DATE);
      var tabNo = rawHorse["@_cardNum"]
        ? parseInt(rawHorse["@_cardNum"])
        : count + 1;

      //Get SIRE Data from horse_name
      const sireData = await centaur.horses
        .findOne({ HRN_HORSE_ID: horseSireId })
        .select("HRN_HORSE_ID HRN_HORSE_NAME HOR_COUNTRY_OF_ORIGIN")
        .lean();
      if (sireData) {
        sire_name =
          sireData.HRN_DISPLAY_NAME ??
          helper.capFstLtr(sireData.HRN_HORSE_NAME);
        sire_country = sireData.HOR_COUNTRY_OF_ORIGIN;
        sire_id = sireData.HRN_HORSE_ID;
      } else {
        helper.logError(
          error_log,
          errors,
          `${rawHorse.horse.name} ${horseId} sire not found`
        );
      }

      dam_name =
        damHorse.HRN_DISPLAY_NAME ?? helper.capFstLtr(damHorse.HRN_HORSE_NAME);
      dam_country = damHorse.HOR_COUNTRY_OF_ORIGIN;
      dam_id = horseDamId;
      //Get SIRE of DAM
      const sireOfDamData = await centaur.horses
        .findOne({ HRN_HORSE_ID: damHorse.HOR_SIRE_ID })
        .select("HRN_HORSE_ID HRN_HORSE_NAME HOR_COUNTRY_OF_ORIGIN")
        .lean();
      if (sireOfDamData) {
        sire_dam_name =
          sireOfDamData.HRN_DISPLAY_NAME ??
          helper.capFstLtr(sireOfDamData.HRN_HORSE_NAME);
        sire_dam_country = sireOfDamData.HOR_COUNTRY_OF_ORIGIN;
        sire_dam_id = sireOfDamData.HRN_HORSE_ID;
      } else {
        errors.push("Sire of Dam Not Found");
        helper.logError(
          error_log,
          errors,
          `${rawHorse.horse.name} ${horseId} sire of dam not found`
        );
      }

      var theInputHorse = {};
      if (raceInput.horses && raceInput.horses.horse) {
        for (inputHorse of raceInput.horses.horse) {
          // console.log(rawHorse['@_runnername'],inputHorse['@_name'])
          if (name == inputHorse["@_name"]) {
            theInputHorse = inputHorse;
          }
        }
      }
      var trainer_data = await centaur.trainers
        .findOne({
          TRN_TRAINER_DISPLAYNAME: rawHorse.TodayDetails.trainerShortName,
        })
        .lean();
      if (!trainer_data) {
        trainer_data = await centaur.trainers
          .findOne({ NZ_Name: rawHorse.TodayDetails.trainerShortName })
          .lean();
      }
      if (trainer_data) {
        trainer_stats = await getTrainerStats(trainer_data.TRN_TRAINER_ID);
        trainer_name = helper.capFstLtr(trainer_data.TRN_TRAINER_DISPLAYNAME);
        trainer_firstname = helper.capFstLtr(
          trainer_data.TRN_TRAINER_FIRSTNAME
        );
        trainer_surname = helper.capFstLtr(trainer_data.TRN_TRAINER_SURNAME);
        trainer_id = trainer_data.TRN_TRAINER_ID;
        training_location = helper.capFstLtr(trainer_data.Location);
      } else {
        helper.logError(
          error_log,
          errors,
          `Trainer Not Found - ${rawHorse.TodayDetails.trainerShortName} `
        );
      }

      training_location = "Hong Kong";
      owners = helper.extractHorseName_HK(
        Array.isArray(rawHorse.horse.ownerName)
          ? rawHorse.horse.ownerName
          : [rawHorse.horse.ownerName]
      );
      console.log(owners);
      colours = helper.capFstLtr(rawHorse.horse.RacingColour);
      prizmoney_won = horseData.HOR_TOTAL_PRIZEMONEY;
      if (horseData.HOR_CURRENT_BLINKER_IND == "") {
        current_blinker_ind = horseData.HOR_CURRENT_BLINKER_IND;
      }

      statistics = "";
      // Jockey
      var jockey_data = await centaur.jockeys
        .findOne({
          JOC_JOCKEY_DISPLAYNAME: helper.cleverCapFstLtr(
            rawHorse.TodayDetails.jockeyShortName
          ),
        })
        .lean();
      if (!jockey_data) {
        jockey_data = await centaur.jockeys
          .findOne({ JOC_JOCKEY_NZNAME: rawHorse.TodayDetails.jockeyShortName })
          .lean();
      }
      var jockey_record = {};
      if (jockey_data) {
        jockey_name = jockey_data.JOC_JOCKEY_DISPLAYNAME;
        jockey_firstname = jockey_data.JOC_JOCKEY_FIRSTNAME;
        jockey_surname = jockey_data.JOC_JOCKEY_SURNAME;
        jockey_id = jockey_data.JOC_JOCKEY_ID;
        jockey_apprentice_indicator = jockey_data.JOC_JOCKEY_APPRENTICE_IND;
        jockey_allowance_weight = rawHorse.TodayDetails.apprenticeAllowance
          ? helper.poundsToHalfKilos(rawHorse.TodayDetails.apprenticeAllowance)
          : "";
        jockey_record = {
          jockey: {
            "@_name": jockey_name,
            "@_firstname": jockey_firstname,
            "@_surname": jockey_surname,
            "@_id": jockey_id,
            "@_apprentice_indicator": jockey_apprentice_indicator ?? "",
            "@_allowance_weight": jockey_allowance_weight ?? "",
            statistics: await getJockeyStats(jockey_id),
          },
        };
      } else {
        if (
          rawHorse.TodayDetails.jockeyShortName &&
          rawHorse.TodayDetails.jockeyShortName != "---"
        ) {
          helper.logError(
            error_log,
            errors,
            `HK Jockey not found: ${rawHorse.TodayDetails.jockeyShortName}`
          );
        }
        jockey_record = {
          jockey: {
            "@_name": "",
            "@_firstname": "",
            "@_surname": "",
            "@_id": "0",
            "@_apprentice_indicator": "",
            "@_allowance_weight": "",
            statistics: {},
          },
        };
      }

      var meetingDateFormat = moment(meetingDate).format("YYYY_MM_DD");
      var path =
        meetingDateFormat +
        "/" +
        meetingTrack.replace(" ", "_").toLowerCase() +
        "/" +
        raceNo +
        "/" +
        tabNo;
      let runningGearArray = {
        gear_item: [],
      };
      let gearChangeArray = {
        gear_change: [],
      };
      if (rawHorse.TodayDetails.gearsCode) {
        let horseGears = rawHorse.TodayDetails.gearsCode.split("/");
        for (const gearItem of horseGears) {
          let gearItemArray = helper.hkGearMatch(gearItem);
          if (
            gearItemArray[0] &&
            gearItemArray[0].includes("GEAR NOT FOUND:")
          ) {
            helper.logError(
              error_log,
              errors,
              gearItemArray[0] + " " + (gearItemArray[1] ?? "")
            );
          } else {
            if (gearItemArray[1]) {
              let gearId = helper.getGearID(gearItemArray[0]);
              gearChangeArray.gear_change.push({
                "@_name": gearItemArray[0],
                "@_option": gearItemArray[1],
                "@_id": gearId,
              });
              if (!gearItemArray[1].includes("off"))
                runningGearArray.gear_item.push(gearItemArray[0]);
            } else {
              runningGearArray.gear_item.push(gearItemArray[0]);
            }
          }
        }
      }

      var horse = {
        "@_name": name,
        "@_country": country,
        "@_age": age,
        "@_colour": colour,
        "@_sex": sex,
        "@_id": horse_id,
        "@_foalingdate": foaling_date,
        sire: {
          "@_name": sire_name,
          "@_country": sire_country,
          "@_id": sire_id,
        },
        dam: {
          "@_name": dam_name,
          "@_country": dam_country,
          "@_id": dam_id,
        },
        sire_of_dam: {
          "@_name": sire_dam_name,
          "@_country": sire_dam_country,
          "@_id": sire_dam_id,
        },
        trainer: {
          statistics: trainer_stats,
          "@_name": trainer_name,
          "@_firstname": trainer_firstname,
          "@_surname": trainer_surname,
          "@_id": trainer_id,
        },
        training_location: training_location,
        owners: owners,
        colours: colours,
        prizemoney_won: prizmoney_won,
        last_four_starts: last4s,
        last_ten_starts: last10s,
        last_fifteen_starts: last15s,
        last_twenty_starts: last20s,
        current_blinker_ind: current_blinker_ind,
        win_percentage: win_percentage,
        place_percentage: place_percentage,
        pace_value: pace[0],
        pace: pace[1],
        statistics: statistics,
        win_distances: win_distances,
        rating: helper.extractRating(rawHorse.TodayDetails.horseRating),
        errors: errors,
        weight_carried: helper.poundsToHalfKilos(
          rawHorse.TodayDetails.actualWeight
        ),
        weight: {
          "@_allocated": helper.poundsToHalfKilos(
            rawHorse.TodayDetails.HandicapWeight
          ),
          "@_total": helper.poundsToHalfKilos(
            rawHorse.TodayDetails.HandicapWeight
          ),
        },
        horse_colours_image:
          "https://silks.medialityracing.com.au/images/jpg/" +
          path +
          "_front.jpg",
        horse_colours_image_png:
          "https://silks.medialityracing.com.au/images/png/" +
          path +
          "_front.png",
        horse_colours_image_svg:
          "https://silks.medialityracing.com.au/images/svg/" +
          path +
          "_front.svg",
        tab_number: tabNo,
        barrier: rawHorse.TodayDetails.barrierDraw ?? tabNo,
        betting: theInputHorse.betting ?? "",
        tip: theInputHorse.tip ?? "",
        gear_changes: theInputHorse.gear_changes ?? gearChangeArray,
        running_gear: runningGearArray,
        comment: theInputHorse.comment ?? "",
        errors: errors,
      };
      horse = Object.assign(horse, jockey_record);

      if (rawHorse.TodayDetails.Positions) {
        var positions = {
          "@_settling_down":
            rawHorse.TodayDetails.Positions["@_settling"] ?? "",
          "@_m800": rawHorse.TodayDetails.Positions["@_eighthundred"] ?? "",
          "@_m400": rawHorse.TodayDetails.Positions["@_fourhundred"] ?? "",
          "@_finish": rawHorse.TodayDetails.Positions["@_finish"] ?? "",
        };
        horse.positions = positions;
        horse.finish_position = positions["@_finish"];
      }

      if (rawHorse.TodayDetails.Margin) {
        horse.margin =
          rawHorse.TodayDetails.Margin == "DNF"
            ? "FF"
            : rawHorse.TodayDetails.Margin;
      }

      if (
        rawHorse.TodayDetails.runnerStatus &&
        rawHorse.TodayDetails.runnerStatus == "STBY"
      ) {
        horse.emergency_indicator = "E";
      }

      // Horse for InputDataMeeting
      var horse_inputMeetingData = {
        "@_name": name,
        "@_id": horse_id ?? theInputHorse["@_id"],
        betting: theInputHorse.betting ?? "",
        tip: theInputHorse.tip ?? "",
        silks: colours,
        comment: theInputHorse.comment ?? "",
        gear_changes: theInputHorse.gear_changes ?? { gear_change: [] },
        errors: errors,
        error_log: error_log,
        colours: theInputHorse.colours ?? "",
        scratched:
          rawHorse["@_runnerscratched"] || theInputHorse.scratched
            ? true
            : false,
      };
    } else {
      errors.push(
        `Horse Not Found - ${helper.extractHorseName_HK(
          rawHorse.horse.name
        )} , DAM - ${helper.extractDamName_HK(rawHorse.horse.Breeding.Dam)}`
      );
      var horse = { errors: errors };
      var horse_inputMeetingData = { errors: errors };
    }
    const horse_data = {
      horse_processedMeetingData: horse,
      horse_inputMeetingData: horse_inputMeetingData,
    };
    return horse_data;
  } catch (err) {
    var sendMail = await helper.mailAlert(
      "HK Horse Level Load error",
      `HK Horse Data couldnt generate for ${meetingId} ${raceNo} ${rawHorse.horse.name}. ${error}`,
      "alert"
    );
    console.log(sendMail);
    console.log(
      `((ERROR)): Error in creating race level data  ${meetingId} ${raceNo} ${rawHorse.horse.name}: ${error}`
    );
    return {
      horse_processedMeetingData: {
        errors: `error creating horse ${rawHorse.horse.name}: ${error}`,
      },
      horse_inputMeetingData: {},
    };
  }
};

const processTrackRecords = async (rawTrackRecords, trackId) => {
  // Process Multiple Track Records (Can be one record in some case)
  var result = "";
  if (Array.isArray(rawTrackRecords)) {
    const processedTrackRecord = [];
    for (t = 0; t < rawTrackRecords.length; t++) {
      let temp = await getTrackRecord(rawTrackRecords[t], trackId);
      processedTrackRecord.push(temp);
    }
    result = {
      track_record: processedTrackRecord,
    };
  } else {
    result = {
      track_record: await getTrackRecord(rawTrackRecords, trackId),
    };
  }
  return result;
};

const getTrackRecord = async (rawData, trackId) => {
  try {
    var meeting_date = helper.convertDate(rawData.RaceDate);
    var iso_meeting_date = moment(rawData.RaceDate).toISOString();
    const recordFormIndex = await centaur.form_index
      .find()
      .where("track_id")
      .equals(trackId)
      .where("race_no")
      .equals(rawData.RaceNumber)
      .where("meeting_date")
      .equals(iso_meeting_date)
      .where("finish_pos")
      .equals(1)
      .where("weight_carried")
      .gt(0)
      .lean();

    if (recordFormIndex[0]) {
      var recordIndex = recordFormIndex[0];
      var jockeyData = await centaur.jockeys
        .findOne({ JOC_JOCKEY_ID: recordIndex.jockey_id })
        .lean();

      var horseData = await centaur.horses
        .findOne({ HRN_HORSE_ID: recordIndex.horse_id })
        .lean();

      processedTrackRecord = {
        meeting_date: meeting_date,
        race: {
          "@_number": rawData.RaceNumber,
          "@_id": recordIndex.event_id,
        },
        distance: {
          "@_metres": rawData.DistanceRace,
        },
        duration: helper.fixDuration(rawData.Time),
        horse: {
          jockey: {
            "@_name": helper.capFstLtr(jockeyData.JOC_JOCKEY_DISPLAYNAME),
            "@_firstname": jockeyData.JOC_JOCKEY_FIRSTNAME,
            "@_surname": jockeyData.JOC_JOCKEY_SURNAME,
            "@_id": recordIndex.jockey_id,
          },
          weight_carried: recordIndex.weight_carried,
          "@_name":
            horseData.HRN_DISPLAY_NAME ??
            helper.capFstLtr(horseData.HRN_HORSE_NAME),
          "@_country": horseData.HOR_COUNTRY_OF_ORIGIN,
          "@_id": recordIndex.horse_id,
        },
      };
    } else {
      console.log(
        "Unable to find track record form item " +
          meeting_date +
          "; " +
          trackId +
          "; " +
          rawData.RaceNumber
      );
      processedTrackRecord = {
        meeting_date: meeting_date,
        race: {
          "@_number": rawData.RaceNumber,
        },
        distance: {
          "@_metres": rawData.DistanceRace,
        },
        duration: helper.fixDuration(rawData.Time),
        horse: {
          "@_name": rawData.TrackRecordHorse["@_HorseName"],
          "@_country": rawData.TrackRecordHorse["@_Country"] ?? "n/a",
        },
      };
    }

    return processedTrackRecord;
    // return "in progress"
  } catch (err) {
    // var sendMail = await helper.mailAlert('Trainer Stats error',`Trainer Stats - ${err}`,'error')
    // console.log(sendMail)
    console.log("((ERROR)): Error generating Track Record: " + err);
  }
};

const getWinDistance = async (formItems) => {
  try {
    var win_distance = {};
    for (run of formItems) {
      if (run.positions && run.finish_position == 1) {
        var run_distance = run.distance["@_metres"];
        if (win_distance[run_distance]) {
          win_distance[run_distance] = win_distance[run_distance] + 1;
        } else {
          win_distance[run_distance] = 1;
        }
      }
    }
    var dataToUpdate = [];
    for (const win in win_distance) {
      let temp = {
        "@_distance": win,
        "@_wins": win_distance[win],
      };
      dataToUpdate.push(temp);
    }
    var winDistances = {
      win_distance: dataToUpdate,
    };

    return winDistances;
  } catch (err) {
    // var sendMail = await helper.mailAlert('Trainer Stats error',`Trainer Stats - ${err}`,'error')
    // console.log(sendMail)
    console.log("((ERROR)): Win Distances Error - " + err);
  }
};

const getTrainerStats = async (trainerId = 0) => {
  try {
    var st = 0;
    var s1 = 0;
    var s2 = 0;
    var s3 = 0;

    const pastDate = new Date(helper.getPastDateByDay(365)); //Calculate past year date from today
    const statsDataTotal = await centaur.form_index
      .find()
      .where("trainer_id")
      .equals(trainerId)
      .where("meeting_date")
      .gt(pastDate)
      .lean();

    // var cleansedTrainerData = helper.cleanseBarrierTrials(statsDataTotal)

    for (race of statsDataTotal) {
      if (race.weight_carried == 0) {
        continue;
      }
      st++;
      if (race.finish_pos == 1) {
        s1++;
      } else if (race.finish_pos == 2) {
        s2++;
      } else if (race.finish_pos == 3) {
        s3++;
      }
    }

    const stats = {
      statistic: {
        "@_type": "one_year", //Fix value of 365 days
        "@_total": st.toString(),
        "@_firsts": s1.toString(),
        "@_seconds": s2.toString(),
        "@_thirds": s3.toString(),
      },
    };
    return stats;
  } catch (err) {
    // var sendMail = await helper.mailAlert('Trainer Stats error',`Trainer Stats - ${err}`,'error')
    // console.log(sendMail)
    console.log("((ERROR)): Trainer Stats Error - " + err);
  }
};

const getJockeyStats = async (jockeyId = 0) => {
  try {
    var st = 0;
    var s1 = 0;
    var s2 = 0;
    var s3 = 0;

    const pastDate = new Date(helper.getPastDateByDay(365)); //Calculate past year date from today
    const statsDataTotal = await centaur.form_index
      .find()
      .where("jockey_id")
      .equals(jockeyId)
      .where("meeting_date")
      .gt(pastDate)
      .lean();

    // var cleansedJockeyData = statsDataTotal
    // var cleansedJockeyData = helper.cleanseBarrierTrials(statsDataTotal)

    for (race of statsDataTotal) {
      if (race.weight_carried == 0) {
        continue;
      }
      if (race.weight_carried > 0) {
        st++;
        if (race.finish_pos == 1) {
          s1++;
        } else if (race.finish_pos == 2) {
          s2++;
        } else if (race.finish_pos == 3) {
          s3++;
        }
      }
    }

    const stats = {
      statistic: {
        "@_type": "one_year", //Fix value of 365 days
        "@_total": st.toString(),
        "@_firsts": s1.toString(),
        "@_seconds": s2.toString(),
        "@_thirds": s3.toString(),
      },
    };
    return stats;
  } catch (err) {
    // var sendMail = await helper.mailAlert('Jockey Stats error',`Jockey Stats - ${err}`,'error')
    // console.log(sendMail)
    console.log("((ERROR)): Jockey Stats Error - " + err);
  }
};

const getClassData = async (Id, classes = {}, count = "") => {
  var result = classes; //~
  try {
    Id = Id.replace(/RTG([0-9]+)\+/, "BM$1+");
    const classData = await centaur.race_classes
      .findOne({ RACE_CONDITION_ID: Id })
      .lean();

    if (classData) {
      classInfo = classData.CLA_CLASS_LONG_DISP;
      classId = classData.CLA_CLASS_DB_ID;
      result[count + "class"] = classInfo;
      result[count + "class_id"] = classId;
    } else {
      // if (Id != "~"){
      //     var sendMail = await helper.mailAlert('ClassData error - investigate',`This class was not found in our database: ${Id}`,'task')
      //     console.log(sendMail)
      // }
      result[count + "class"] = "Open";
      result[count + "class_id"] = "4";
    }
  } catch (err) {
    // var sendMail = await helper.mailAlert('ClassData error',`ClassData error, may not actually be an open ${Id} - ${err}`,'error')
    // console.log(sendMail)
    console.log(
      `((ERROR)): ClassData error, may not actually be an open ${Id} - ${err}`
    );
  }

  return result;
};

const getClassByName = async (name) => {
  // var result = { "class": "Open" } //~
  // try{
  //     var classData = await centaur.race_classes.findOne({ CLA_CLASS_LONG_DISP: name }).lean();

  //     if (classData) {
  //         classInfo = classData.CLA_CLASS_LONG_DISP
  //         classId = classData.CLA_CLASS_DB_ID
  //         result = {
  //             "class": classInfo,
  //             "class_id": classId
  //         }
  //     } else {
  //         classData = await centaur.race_classes.findOne({ CLA_CLASS_DB_CODE: name }).lean();
  //         if (classData) {
  //             classInfo = classData.CLA_CLASS_LONG_DISP
  //             classId = classData.CLA_CLASS_DB_ID
  //             result = {
  //                 "class": classInfo,
  //                 "class_id": classId
  //             }
  //         } else {
  //             console.log('couldnt find class data for '+name)
  //         }
  //     }
  // }catch (err) {
  //     console.log("ClassData error, may not actually be an open - " + err)
  // }

  try {
    var classData = await centaur.race_classes
      .findOne({ CLA_CLASS_LONG_DISP: name })
      .lean();

    if (classData) {
      // classInfo = classData.CLA_CLASS_LONG_DISP
      // classId = classData.CLA_CLASS_DB_ID
      // result = {
      //     "class": classInfo,
      //     "class_id": classId
      // }
      return classData.CLA_CLASS_DB_ID;
    } else {
      classData = await centaur.race_classes
        .findOne({ CLA_CLASS_DB_CODE: name.toUpperCase() })
        .lean();
      if (classData) {
        // classInfo = classData.CLA_CLASS_LONG_DISP
        // classId = classData.CLA_CLASS_DB_ID
        // result = {
        //     "class": classInfo,
        //     "class_id": classId
        // }
        return classData.CLA_CLASS_DB_ID;
      } else {
        console.log("couldnt find class data for " + name);
      }
    }
  } catch (err) {
    console.log("ClassData error, may not actually be an open - " + err);
  }

  return 0;
};

const identifyDam = async (damName, damSireName) => {
  damName = damName.replace(".", "").replace(/ \([A-z]{2,}\)$/, "");
  damSireName = damSireName.replace(".", "").replace(/ \([A-z]{2,}\)$/, "");
  let damData = await centaur.horses
    .find()
    .where("HRN_HORSE_NAME")
    .equals(damName)
    .lean();

  if (damData.length > 1) {
    for (dam of damData) {
      let damSireData = await centaur.horses
        .find()
        .where("HRN_HORSE_NAME")
        .equals(damSireName)
        .select("HRN_HORSE_ID")
        .lean();
      for (damSire of damSireData) {
        if (damSire.HRN_HORSE_ID == dam.HOR_SIRE_ID) {
          return dam;
        }
      }
    }
  } else if (damData[0]) {
    let theDamSire = await centaur.horses
      .find()
      .where("HRN_HORSE_NAME")
      .equals(damSireName)
      .select("HRN_HORSE_ID")
      .lean();
    for (damSire of theDamSire) {
      if (damData[0].HOR_SIRE_ID == damSire.HRN_HORSE_ID) {
        return damData[0];
      }
    }
  }
};

const identifyDam_HK = async (damName, damSireName) => {
  var theDamName = helper.upperCase(damName[0].replace(".", ""));
  var theDamSireName = helper.upperCase(damSireName[0].replace(".", ""));
  theDamSireName = theDamSireName.replace(".", "");
  var damData = [];
  if (damName[1]) {
    damData = await centaur.horses
      .find()
      .where("HRN_HORSE_NAME")
      .equals(theDamName)
      .where("HOR_COUNTRY_OF_ORIGIN")
      .equals(damName[1])
      .lean();
  } else {
    damData = await centaur.horses
      .find()
      .where("HRN_HORSE_NAME")
      .equals(theDamName)
      .lean();
  }

  if (damData.length > 1) {
    for (dam of damData) {
      var damSireData = [];
      if (damSireName[1]) {
        damSireData = await centaur.horses
          .find()
          .where("HRN_HORSE_NAME")
          .equals(theDamSireName)
          .where("HOR_COUNTRY_OF_ORIGIN")
          .equals(damSireName[1])
          .select("HRN_HORSE_ID")
          .lean();
      } else {
        damSireData = await centaur.horses
          .find()
          .where("HRN_HORSE_NAME")
          .equals(theDamSireName)
          .select("HRN_HORSE_ID")
          .lean();
      }
      for (damSire of damSireData) {
        if (damSire.HRN_HORSE_ID == dam.HOR_SIRE_ID) {
          return dam;
        }
      }
    }
  } else if (damData[0]) {
    var theDamSire = [];
    if (damSireName[1]) {
      theDamSire = await centaur.horses
        .find()
        .where("HRN_HORSE_NAME")
        .equals(theDamSireName)
        .where("HOR_COUNTRY_OF_ORIGIN")
        .equals(damSireName[1])
        .select("HRN_HORSE_ID")
        .lean();
    } else {
      theDamSire = await centaur.horses
        .find()
        .where("HRN_HORSE_NAME")
        .equals(theDamSireName)
        .select("HRN_HORSE_ID")
        .lean();
    }

    for (damSire of theDamSire) {
      if (damData[0].HOR_SIRE_ID == damSire.HRN_HORSE_ID) {
        return damData[0];
      }
    }
  }
};

const matchHorseBySireAndDam = async (horseName, damName, SireName) => {
  horseName = horseName.replace(".", "");
  damName = helper.extractDamName_SGP(damName.replace(".", ""));
  SireName = helper.extractDamName_SGP(SireName.replace(".", ""));

  var horseArray = await centaur.horses
    .find({ HRN_HORSE_NAME: horseName })
    .lean();
  var damArray = {};
  if (damName[1]) {
    damArray = await centaur.horses
      .find()
      .where({ HRN_HORSE_NAME: damName[0] })
      .where({ HOR_COUNTRY_OF_ORIGIN: damName[1] })
      .lean();
  } else {
    damArray = await centaur.horses
      .find()
      .where({ HRN_HORSE_NAME: damName[0] })
      .lean();
  }

  var sireArray = {};
  if (SireName[1]) {
    sireArray = await centaur.horses
      .find()
      .where({ HRN_HORSE_NAME: SireName[0] })
      .where({ HOR_COUNTRY_OF_ORIGIN: SireName[1] })
      .lean();
  } else {
    sireArray = await centaur.horses
      .find()
      .where({ HRN_HORSE_NAME: SireName[0] })
      .lean();
  }

  var horseSireDamArray = [];
  for (runner of horseArray) {
    for (dam of damArray) {
      if (runner.HOR_DAM_ID == dam.HRN_HORSE_ID) {
        for (sire of sireArray) {
          if (runner.HOR_SIRE_ID == sire.HRN_HORSE_ID) {
            horseSireDamArray = [runner, sire, dam];

            return horseSireDamArray;
          }
        }
      }
    }
  }
  console.log(
    `((ERROR)): horse not found: ${horseName},${SireName},${damName}`
  );
  return ["", ""];
};

const calculateNzClass = async (nzclass) => {
  var nz_rest = {};
  var nzClassDb = await centaur.nz_classes
    .findOne({ NZC_NZRC_CLASS: nzclass })
    .lean();
  // console.log(`nz class input: ${nzclass}`)
  // console.log(nzClassDb)
  if (nzClassDb) {
    nz_rest.weight_type = helper.getWeightTypeByCode(nzClassDb.NZC_WEIGHT_TYPE);
    nz_rest.age_rest = helper.getAgeRestrictionByCode(
      nzClassDb.NZC_AGE_RESTRICTION
    );
    nz_rest.sex_rest = helper.getSexRestrictionByCode(
      nzClassDb.NZC_SEX_RESTRICTION
    );
    var theClass = await centaur.race_classes
      .findOne({ CLA_CLASS_DB_ID: nzClassDb.NZC_CLASS_DB_ID })
      .lean();
    nz_rest.classes = {};
    if (theClass) {
      nz_rest.classes.class = theClass.CLA_CLASS_LONG_DISP;
      nz_rest.classes.class_id = theClass.CLA_CLASS_DB_ID;
    } else {
      nz_rest.classes.class = nzclass;
    }
  } else {
    nz_rest = {
      weight_type: "",
      age_rest: "",
      sex_rest: "",
      classes: {
        class: "CLASS NOT FOUND",
        class_id: 999,
      },
    };
  }
  // console.log(`nz class: `)
  // console.log(nz_rest)
  return nz_rest;
};

const upDownClass = (next_class, last_class) => {
  // console.log(`updownclass`,last_class,next_class)
  if (
    next_class.toString().includes("group") &&
    !last_class.toString().includes("group")
  )
    return "U";
  if (
    !next_class.toString().includes("group") &&
    last_class.toString().includes("group")
  )
    return "D";
  if (
    next_class.toString().includes("group") &&
    last_class.toString().includes("group")
  ) {
    if (next_class === last_class) return "S";
    if (next_class === "group 1") return "U";
    if (next_class === "group 2" && last_class !== "group 1") return "U";
    if (next_class === "group 3" && last_class === "group LR") return "U";
    return "D";
  }
  let last = centaur.race_classes
    .findOne({ CLA_CLASS_DB_ID: last_class })
    .select("CLA_RANK")
    .lean();
  let next = centaur.race_classes
    .findOne({ CLA_CLASS_DB_ID: next_class })
    .select("CLA_RANK")
    .lean();
  return last > next ? "D" : next > last ? "U" : "S";
};

module.exports = {
  normalizeMeetingData_AUS,
  normalizeRaceData_AUS,
  genHorseLevelData_AUS,
  normalizeMeetingData_NZ,
  normalizeRaceData_NZ,
  genHorseLevelData_NZ,
  normalizeMeetingData_HK,
  normalizeRaceData_HK,
  genHorseLevelData_HK,
  getClassByName,
};
