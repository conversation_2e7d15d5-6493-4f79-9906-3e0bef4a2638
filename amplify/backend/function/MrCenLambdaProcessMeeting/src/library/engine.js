const mongoose = require("mongoose");
const moment = require("moment");
const helper = require("./helper");
const getUuid = require("uuid-by-string");
const uuid = require("uuid");
const AWS = require("aws-sdk");
// Import Models
const centaur = require("@mediality/centaur");

mongoose.set("debug", false);

const genMeetingLevelData = async (
  normalizedData,
  meetingId,
  meetingStage,
  country,
  historyitem
) => {
  var errors = [];
  try {
    //Check if Meeting Exists
    const recordsExists = await centaur.processed_meetings
      .findOne({ _id: meetingId })
      .lean();

    if (recordsExists && recordsExists.meetingStage != "DELETED") {
      let acceptanceFileCount = recordsExists.AcceptanceFileCounter ?? 0;
      if (meetingStage == "Acceptances" || meetingStage == "FinalFields") {
        //If Acceptance File Received, increment AcceptanceFileCount
        acceptanceFileCount++;
      }
      //Updating Meeting
      var updateRecord = await centaur.temp_meetings.findByIdAndUpdate(
        meetingId,
        {
          meetingDate: normalizedData.meetingDate,
          meetingStage: normalizedData.meetingStage,
          meetingState: normalizedData.meetingState,
          meetingCountry: country,
          AcceptanceFileCounter: acceptanceFileCount,
          processedMeetingData: normalizedData.record_meeting,
          inputMeetingData: normalizedData.inputMeetingData,
        }
      );

      console.log(updateRecord);
    } else {
      var history = [];
      var inputData = normalizedData.inputMeetingData;
      // if meeting is deleted, save the inputdata
      if (recordsExists && recordsExists.meetingStage == "DELETED") {
        history = recordsExists.meetingLoadHistory;
      } else {
        history.push(historyitem);
      }
      //Creating New Meeting
      var newData = {
        meetingDate: normalizedData.meetingDate,
        meetingStage: normalizedData.meetingStage,
        meetingState: normalizedData.meetingState,
        meetingCountry: country,
        AcceptanceFileCounter:
          normalizedData.meetingStage == "Acceptances" ||
          normalizedData.meetingStage == "FinalFields"
            ? 1
            : 0,
        processedMeetingData: normalizedData.record_meeting,
        inputMeetingData: inputData,
        meetingLoadHistory: history,
      };

      var tempMeetUpdate = await centaur.temp_meetings
        .updateOne(
          { _id: meetingId },
          {
            $set: newData,
          }
        )
        .lean();
    }
  } catch (err) {
    // var sendMail = await helper.mailAlert('Meeting Engine load error',`Error Updating meeting ${meetingId}, ${err}`,'error')
    // console.log(sendMail)
    console.log(
      `((ERROR)): Engine Error in creating meeting level data for ${meetingId}: ${err}`
    );
  }

  if (normalizedData.inputMeetingData.silksReady) {
    normalizedData.raceDataArray.silksReady = true;
  }

  /*---Array to Pass in MAP STEP FUNCTION---*/

  return normalizedData.raceDataArray;
};
const isValid = (value) => {
  // Check for undefined or null
  if (value === undefined || value === null) {
    return false;
  }

  // Convert to string if it's not already a string
  const strValue = typeof value === "string" ? value : String(value);

  // Trim and check for empty string
  if (strValue.trim() === "") {
    return false;
  }

  // Check for "N/A" variants
  if (["N/A", "n/a", "N/a", "NA", "na", "Na", "nA"].includes(strValue)) {
    return false;
  }

  // Check if it's zero (as a number or string representation of zero)
  const numValue = Number(value);
  if (!isNaN(numValue) && numValue === 0) {
    return false;
  }

  // If we've passed all checks, the value is valid
  return true;
};
const genRaceLevelData = async (normalizedRaceData, meetingId, ra_id) => {
  console.log(`genracedata ${meetingId} ${ra_id}`);
  try {
    // Update ProcessedMeeting Race
    // get the index number from the temp meeting
    const meetingRecord = await centaur.temp_meetings
      .findOne({ _id: meetingId })
      .select("processedMeetingData inputMeetingData")
      .lean();
    let race_index = -1;
    let input_index = -1;
    for (const [
      index,
      race,
    ] of meetingRecord.processedMeetingData.meeting.races.race.entries()) {
      if (ra_id === race.ra_id) race_index = index;
    }
    for (const [
      index,
      race,
    ] of meetingRecord.inputMeetingData.races.race.entries()) {
      if (ra_id === race["@_ra_id"]) input_index = index;
    }
    console.log("indexestoupdate", race_index, input_index);
    var raceToUpdate = "processedMeetingData.meeting.races.race." + race_index;
    var dataToUpdate = normalizedRaceData.race;
    var resp = await centaur.temp_meetings
      .updateOne(
        { _id: meetingId },
        {
          $set: {
            [raceToUpdate]: dataToUpdate,
          },
        }
      )
      .lean();
    if (!resp) {
      console.log(
        `((ERROR)): Error Updating Race from meeting ${meetingId}: ${ra_id}`
      );
    }

    // Update InputMeetingData when meeting is in Nominations, Weights or Acceptance (first time only) stage

    var raceToUpdate_inputMeetingData =
      "inputMeetingData.races.race." + input_index;
    var dataToUpdate_inputMeetingData =
      normalizedRaceData.race_inputMeetingData;

    var resp_inputMeetingData = await centaur.temp_meetings
      .updateOne(
        { _id: meetingId },
        {
          $set: {
            [raceToUpdate_inputMeetingData]: dataToUpdate_inputMeetingData,
          },
        }
      )
      .lean();
    if (!resp_inputMeetingData) {
      console.log(
        `((ERROR)): Error Updating Race inputdata from meeting ${meetingId}: ${ra_id}`
      );
    }
  } catch (err) {
    // var sendMail = await helper.mailAlert('Race Engine load error',`Error Updating Race from meeting ${meetingId}: ${(raceId - 1)}, ${err}`,'error')
    // console.log(sendMail)
    console.log(
      `((ERROR)): Error Updating Race from meeting ${meetingId}: ${ra_id}, ${err}`
    );
  }
};
function capMargin(marginValue) {
  if (!marginValue) return marginValue;

  // Parse the margin value
  const parsedMargin = parseFloat(marginValue);

  // Check if it's a valid number
  if (isNaN(parsedMargin)) return marginValue;

  // Cap margins over 100 lengths to 99.9
  return parsedMargin > 100 ? 99.9 : parsedMargin;
}
const processResults = async (resultsContent, meetingId, trialStatus) => {
  // Helper function to check if a value is blank
  function isBlank(value) {
    return (
      value === null ||
      value === undefined ||
      (typeof value === "string" && value.trim() === "")
    );
  }

  // Helper function to log errors only once per data point
  function logError(errorLog, errors, message) {
    if (!errors.includes(message)) {
      errors.push(message);
      errorLog.push(message);
    }
    return [errorLog, errors];
  }
  function mergePositions(
    existingPositions = {},
    newPositions,
    horseName,
    horseScratched,
    error_log,
    errors,
    isTrial
  ) {
    // Check existing positions to make sure none are removed
    for (let posKey in existingPositions) {
      if (
        existingPositions[posKey] &&
        parseInt(existingPositions[posKey]) > 0 &&
        (!newPositions[posKey] || parseInt(newPositions[posKey]) == 0)
      ) {
        if (
          !horseScratched &&
          !isTrial &&
          !isBlank(existingPositions[posKey])
        ) {
          [error_log, errors] = logError(
            error_log,
            errors,
            `RA sent blank position ${posKey} for horse ${horseName}, previously ${existingPositions[posKey]}`
          );
        }
      }
    }
    for (let posKey in newPositions) {
      if (newPositions[posKey] && parseInt(newPositions[posKey]) > 0) {
        existingPositions[posKey] = newPositions[posKey];
      }
    }
    return existingPositions;
  }

  const nestedObjectKeys = [
    "sire",
    "dam",
    "sire_of_dam",
    "trainer",
    "weight",
    "jockey",
    "positions",
    "decimalprices",
    "prices",
  ];
  function isNumericFinishPosition(pos) {
    return /^\d+$/.test(pos);
  }

  // Fetch InputMeetingData for Meeting Id
  console.log("processResults");
  const meeting = await centaur.temp_meetings
    .findOne({ _id: meetingId })
    .select("inputMeetingData")
    .select("meetingCountry")
    .select("processedMeetingData")
    .lean();
  try {
    if (meeting) {
      // RA results file, for an RA generated meeting
      var meetingData = meeting;
      var isTrial = trialStatus === "trial" ? 1 : 0;
      var rawData = resultsContent.Meeting;
      // Initialize error arrays if not present
      meetingData.errors = meetingData.errors || [];
      meetingData.error_log = meetingData.error_log || [];

      // Update meetingStage
      if (!isBlank(rawData.MeetingStage)) {
        meetingData.meetingStage = rawData.MeetingStage;
        if (
          meetingData.processedMeetingData &&
          meetingData.processedMeetingData.meeting
        ) {
          meetingData.processedMeetingData.meeting.stage = rawData.MeetingStage;
        }
      } else {
        if (!isBlank(meetingData.meetingStage)) {
          [meetingData.error_log, meetingData.errors] = logError(
            meetingData.error_log,
            meetingData.errors,
            "RA sent blank MeetingStage"
          );
        }
      }
   // Find the existing code block around line 156-167 where allRacesAbandoned is checked
// Replace that section with this updated code:

const allRacesAbandoned = await helper.checkIfAllRacesAbandoned(rawData);
if (allRacesAbandoned) {
  console.log("All races are abandoned, updating meeting and race stages");
  
  // Update meeting stage to Abandoned
  meetingData.meetingStage = "Abandoned";
  if (meetingData.processedMeetingData && meetingData.processedMeetingData.meeting) {
    meetingData.processedMeetingData.meeting.stage = "Abandoned";
  }
  
  // Update all race stages to Abandoned
  if (meetingData.processedMeetingData && meetingData.processedMeetingData.meeting && 
      meetingData.processedMeetingData.meeting.races && 
      meetingData.processedMeetingData.meeting.races.race) {
    
    for (let race of meetingData.processedMeetingData.meeting.races.race) {
      race.race_stage = "Abandoned";
    }
  }
    // Save to temp_meetings as well to ensure consistency
    await centaur.temp_meetings.updateOne(
      { _id: meetingId },
      { $set: meetingData }
    );
  console.log("Meeting and races marked as abandoned, exiting process");
  await triggerCompare(meetingId, "", false, 0);
  return true; // Exit the function after updating abandoned status
}
      // Update rail_position
      if (!isBlank(rawData.Track.RailPosition)) {
        meetingData.processedMeetingData.meeting.rail_position =
          rawData.Track.RailPosition;
      } else {
        [meetingData.error_log, meetingData.errors] = logError(
          meetingData.error_log,
          meetingData.errors,
          "RA sent blank RailPosition"
        );
      }

      // Update track rating
      if (
        rawData.Track.TrackRating["@_NumericTrackRating"] &&
        rawData.Track.TrackRating["@_NumericTrackRating"].trim() !== "" &&
        !["N/A", "n/a", "N/a", "NA", "na", "Na", "nA"].includes(
          rawData.Track.TrackRating["@_NumericTrackRating"]
        )
      ) {
        meetingData.processedMeetingData.meeting.track["@_expected_condition"] =
          helper.processTrackRating(
            rawData.Track.TrackRating["@_NumericTrackRating"]
          );
      } else {
        [meetingData.error_log, meetingData.errors] = logError(
          meetingData.error_log,
          meetingData.errors,
          "RA sent blank or invalid TrackRating"
        );
        // Do not overwrite existing value
      }

      // Additional checks for TrackSurface
      if (
        (!rawData.Track.TrackRating ||
          !rawData.Track.TrackRating["@_NumericTrackRating"] ||
          rawData.Track.TrackRating["@_NumericTrackRating"].trim() === "" ||
          ["N/A", "n/a", "N/a", "NA", "na", "Na", "nA"].includes(
            rawData.Track.TrackRating["@_NumericTrackRating"]
          )) &&
        rawData.Track.TrackSurface &&
        /dirt|sand|wet/i.test(rawData.Track.TrackSurface)
      ) {
        meetingData.processedMeetingData.meeting.track["@_expected_condition"] =
          "Good(0)";
      }

      // Ensure Races.Race is an array
      if (typeof rawData.Races.Race[Symbol.iterator] !== "function") {
        rawData.Races.Race = [rawData.Races.Race];
      }

      for (const race of rawData.Races.Race) {
        if (
          trialStatus === "trial" &&
          race["@_CurrentRaceStage"] == "Abandoned"
        )
          continue;

        let raceIndex = -1;
        if (
          meetingData.inputMeetingData.raceMap &&
          meetingData.inputMeetingData.raceMap.length > 0
        ) {
          for (const [
            i,
            dBRace,
          ] of meetingData.processedMeetingData.meeting.races.race.entries()) {
            if (race["@_RaceCode"] === dBRace["@_ra_id"]) raceIndex = i;
          }
        } else {
          for (const [
            i,
            dBRace,
          ] of meetingData.processedMeetingData.meeting.races.race.entries()) {
            if (parseInt(race["@_RaceNumber"]) === parseInt(dBRace["@_number"]))
              raceIndex = i;
          }
        }

        if (raceIndex < 0) {
          await helper.mailAlert(
            `Results ${meeting.processedMeetingData.meeting.track["@_name"]} ${meeting.processedMeetingData.meeting.date} Race ${race["@_RaceNumber"]} not found in DB`,
            `Unable to match race ${race["@_RaceNumber"]} in RA file, please download latest results file and check`,
            "alert"
          );
          continue;
        } else if (
          meetingData.processedMeetingData.meeting.races.race[
            raceIndex
          ]?.horses?.horse[0]["@_name"]?.toLowerCase() !==
          race.RaceEntries.RaceEntry[0]?.Horse["@_HorseName"]?.toLowerCase()
        ) {
          await helper.mailAlert(
            `Results ${meeting.processedMeetingData.meeting.track["@_name"]} ${meeting.processedMeetingData.meeting.date} Race ${race["@_RaceNumber"]} not matching in DB`,
            `Race ${race["@_RaceNumber"]} appears to have different horse in the RA file, please download latest results file and check`,
            "alert"
          );
          continue;
        }

        // Check if race is resulted
        let resultedrace = race["@_CurrentRaceStage"]
          .toLowerCase()
          .includes("results")
          ? true
          : false;

        var dbRace =
          meetingData.processedMeetingData.meeting.races.race[raceIndex];

        // Initialize error arrays if not present
        dbRace.errors = dbRace.errors || [];
        dbRace.error_log = dbRace.error_log || [];

        // Update race duration
        if (
          resultedrace &&
          race.RaceTiming.WinningTime &&
          race.RaceTiming.WinningTime.trim() !== "" &&
          !/^[0:\.]0[0:\.]$/.test(race.RaceTiming.WinningTime.trim())
        ) {
          dbRace.duration = race.RaceTiming.WinningTime;
        } else {
          if (dbRace.duration && !isBlank(dbRace.duration)) {
            dbRace.duration = "NTT";
            [dbRace.error_log, dbRace.errors] = helper.logError(
              dbRace.error_log,
              dbRace.errors,
              "Missing race duration, using NTT"
            );
          }
        }
         // Update sectional
         dbRace.sectional = {
          "@_distance": race.RaceTiming.DistanceSectional ?? "",
          "@_time":
            race.RaceTiming.TimeSectional == "00:00:00.000"
              ? ""
              : race.RaceTiming.TimeSectional,
          "@_location": "Last",
        };
        // Update starters
        if (!isBlank(race.Starters) || race?.Starters !== 0) {
          dbRace.starters = race.Starters;
        } else {
          if (!isBlank(dbRace.starters)) {
            [dbRace.error_log, dbRace.errors] = logError(
              dbRace.error_log,
              dbRace.errors,
              `RA sent blank Starters for race ${race["@_RaceNumber"]}`
            );
          }
        }

        dbRace.official_margin_1 = "";
        dbRace.official_margin_2 = "";
        delete dbRace.track_condition;

        // Update track_condition
        if (
          race.VenueTrack &&
          race.VenueTrack.TrackRating &&
          race.VenueTrack.TrackRating["@_NumericTrackRating"] &&
          race.VenueTrack.TrackRating["@_NumericTrackRating"].trim() !== ""
        ) {
          dbRace.track_condition = helper.processTrackRating(
            race.VenueTrack.TrackRating["@_NumericTrackRating"]
          );
        } else if (
          race.VenueTrack &&
          race.VenueTrack.TrackRating &&
          !["N/A", "n/a", "N/a", "NA", "na", "Na", "nA"].includes(
            race.VenueTrack.TrackRating
          ) &&
          helper.processTrackRating(race.VenueTrack.TrackRating) !==
            race.VenueTrack.TrackRating
        ) {
          dbRace.track_condition = helper.processTrackRating(
            race.VenueTrack.TrackRating
          );
        } else {
          // Keep existing value
          dbRace.track_condition =
            meetingData.processedMeetingData.meeting.track[
              "@_expected_condition"
            ];
        }

        dbRace.race_stage = race["@_CurrentRaceStage"];
        // Initialize record indicator
        let recordType = "N"; // Default to no record

        // Check if there's a track record indicator in RaceTiming
        // Notice the @_ prefix for attributes
        if (
          race.RaceTiming &&
          race.RaceTiming["@_IsNewTrackRecord"] === "true"
        ) {
          // A race record was set
          // Check Comments to determine exact record type
          if (race.Comments && race.RaceTiming.TimingMethod != "Manual") {
            const comments = String(race.Comments).toLowerCase();

            // Determine if it's an equal record or new record
            const isEqual =
              comments.includes("equal") || comments.includes("equalled");

            // Determine record type based on comments text
            if (
              comments.includes("track record") ||
              comments.includes("course record")
            ) {
              recordType = isEqual ? "EC" : "C"; // Equal Course or Course Record
            } else if (comments.includes("class record")) {
              recordType = isEqual ? "ECL" : "CL"; // Equal Class or Class Record
            } else if (comments.includes("australian record")) {
              recordType = isEqual ? "EA" : "A"; // Equal Australian or Australian Record
            } else if (
              comments.includes("nz record") ||
              comments.includes("new zealand record")
            ) {
              recordType = isEqual ? "ENZ" : "NZ"; // Equal NZ or NZ Record
            } else if (comments.includes("world record")) {
              recordType = isEqual ? "EW" : "W"; // Equal World or World Record
            } else if (comments.includes("race record")) {
              recordType = isEqual ? "ER" : "R"; // Equal Race or Race Record
            }
            console.log("recordType", recordType);
            if (dbRace.ra_comments && race.Comments != dbRace.ra_comments) {
              [dbRace.error_log, dbRace.errors] = helper.logError(
                dbRace.error_log,
                dbRace.errors,
                `Race comment changed from RA. Please check..`
              );
            }
          }
          // Check other record indicators if we haven't found a record type yet
          if (recordType !== "N") {
            dbRace.race_record_indicator = recordType;
            [dbRace.error_log, dbRace.errors] = helper.logError(
              dbRace.error_log,
              dbRace.errors,
              `Check Race Record from RA`
            );
          }
        }

        // Continue with the existing code (next line: if (!isBlank(race.Comments)) { ... })
        if (dbRace.track) {
          if (
            race.VenueTrack &&
            race.VenueTrack.TrackRating &&
            race.VenueTrack.TrackRating["@_NumericTrackRating"] &&
            race.VenueTrack.TrackRating["@_NumericTrackRating"].trim() !== "" &&
            !["N/A", "n/a", "N/a"].includes(
              race.VenueTrack.TrackRating["@_NumericTrackRating"]
            )
          ) {
            dbRace.track["@_expected_condition"] = helper.processTrackRating(
              race.VenueTrack.TrackRating["@_NumericTrackRating"]
            );
          } else if (
            race.VenueTrack &&
            race.VenueTrack.TrackRating &&
            typeof race.VenueTrack.TrackRating === "string" &&
            race.VenueTrack.TrackRating.includes("{")
          ) {
            // Do not overwrite existing value
          } else {
            dbRace.track["@_expected_condition"] =
              meetingData.processedMeetingData.meeting.track[
                "@_expected_condition"
              ];
          }
        }

        if (resultedrace && !dbRace.track_condition) {
          [dbRace.error_log, dbRace.errors] = helper.logError(
            dbRace.error_log,
            dbRace.errors,
            `Missing track condition`
          );
        }

        if (!isBlank(race.Comments)) {
          dbRace.ra_comments = race.Comments;
        }

        // Update race distance
        if (
          race.RaceDistance &&
          dbRace.distance &&
          parseInt(race.RaceDistance) != parseInt(dbRace.distance["@_metres"])
        ) {
          dbRace.distance["@_metres"] = parseInt(race.RaceDistance);
        }

        if (trialStatus === "trial") {
          if (dbRace.restrictions["@_jockey"] === "Apprentices Cannot Claim")
            dbRace.restrictions["@_jockey"] = "";
          dbRace.barrier_trial_indicator = "B";
        }

        var horseIndex = 0;
        // Loop through horses in a race to add results data
        for (let horse of dbRace.horses.horse) {
          // Initialize error arrays if not present
          horse.errors = horse.errors || [];
          horse.error_log = horse.error_log || [];

          var theHorse = await centaur.horses
            .findOne({ HRN_HORSE_ID: horse["@_id"] })
            .select("HOR_TOTAL_PRIZEMONEY")
            .select("HOR_RACE_PRIZEMONEY")
            .lean();

          var theHorseTotalPrize = theHorse.HOR_TOTAL_PRIZEMONEY ?? 0;

          if (theHorse.HOR_RACE_PRIZEMONEY) {
            for (let horsePrize of theHorse.HOR_RACE_PRIZEMONEY) {
              if (horsePrize.event_id == dbRace["@_id"]) {
                theHorseTotalPrize = parseInt(
                  theHorseTotalPrize - horsePrize.prize
                );
              }
            }
          }

          try {
            if (
              horse["@_name"].toLowerCase() ===
              race.RaceEntries.RaceEntry[horseIndex]?.Horse[
                "@_HorseName"
              ].toLowerCase()
            ) {
              let errors = horse.errors;
              let error_log = horse.error_log;
              var rawHorse = race.RaceEntries.RaceEntry[horseIndex];
              // Check for unscratching
              if (horse.scratched && !rawHorse.Scratched) {
                [horse.error_log, horse.errors] = logError(
                  horse.error_log,
                  horse.errors,
                  `Horse was previously scratched but is now a runner`
                );
                delete horse.scratched;
              }
              if (!horse.scratched && rawHorse.Scratched) {
                // Only log the error if the horse had a valid finish position before
                if (
                  !isBlank(horse.finish_position) &&
                  horse.finish_position !== "SCR"
                ) {
                  [horse.error_log, horse.errors] = logError(
                    horse.error_log,
                    horse.errors,
                    `Was a runner now scratched , Manually delete the form item`
                  );
                }
                // Update the horse's scratched status
                horse.scratched = true;
              }
              var finish_position = "",
                prizemoney_race = 0,
                prizemoney_bonus = 0;
              var decimalprices_opening = "",
                decimalprices_mid = "",
                decimalprices_mid_2 = "",
                decimalprices_starting = "",
                favourite_indicator = "";
              var prices_opening = "",
                prices_starting = "",
                prices_mid = "",
                prices_mid_2 = "",
                mid = "";
              var positions_settling_down = "",
                positions_m1200 = "",
                positions_m800 = "",
                positions_m400 = "";
              var margin = "",
                beaten_margin = "",
                margin_official = "",
                stewards_report = "";
              var horse_time = "";

              if (
                rawHorse.RaceResult &&
                !isBlank(rawHorse.RaceResult.Time) &&
                !(horse.scratched || rawHorse.Scratched)
              ) {
                horse_time = rawHorse.RaceResult.Time;
              } else {
                if (
                  !(horse.scratched || rawHorse.Scratched) &&
                  !isBlank(horse.horse_time)
                ) {
                  [error_log, errors] = logError(
                    error_log,
                    errors,
                    `RA sent blank Time for horse ${horse["@_name"]}`
                  );
                }
              }

              // Process finish position and margins
              if (rawHorse.RaceResult && rawHorse.RaceResult.FinishPosition) {
                finish_position = rawHorse.RaceResult.FinishPosition["#text"];
                if (
                  isBlank(finish_position) &&
                  !(horse.scratched || rawHorse.Scratched) &&
                  !isBlank(horse.finish_position)
                ) {
                  [horse.error_log, horse.errors] = logError(
                    horse.error_log,
                    horse.errors,
                    `RA sent blank FinishPosition for ${horse["@_name"]}`
                  );
                }
                if(!isBlank(finish_position) &&  !(horse.scratched || rawHorse.Scratched)){
                  if(horse.finish_position && horse.finish_position != finish_position){
                    [horse.error_log, horse.errors] = logError(
                      horse.error_log,
                      horse.errors,
                      `RA sent different FinishPosition for ${horse["@_name"]}. It was ${horse.finish_position} but RA sent ${finish_position}`
                    );
                  }
                }
                if (finish_position == "LP") {
                  finish_position = "NP";
                } else if (finish_position == "F") {
                  finish_position = "FL";
                } else if (finish_position == "1") {
                  if (
                    rawHorse.RaceResult.FinishPosition["@_DeadHeat"] &&
                    rawHorse.RaceResult.FinishPosition["@_DeadHeat"] == "true"
                  ) {
                    margin = "0.0";
                    beaten_margin = "0.0";
                    dbRace.official_margin_1 = "DH";
                  } else if (
                    rawHorse.RaceResult.FinishPosition["@_WinningMargin"]
                  ) {
                    beaten_margin =
                      rawHorse.RaceResult.FinishPosition["@_WinningMargin"];
                    margin =
                      beaten_margin < 1
                        ? Math.ceil(
                            parseFloat(
                              rawHorse.RaceResult.FinishPosition[
                                "@_WinningMargin"
                              ]
                            ) * 10
                          ) / 10
                        : Math.round(
                            parseFloat(
                              rawHorse.RaceResult.FinishPosition[
                                "@_WinningMargin"
                              ]
                            ) * 10
                          ) / 10;
                    if (isNaN(margin) || margin === "") {
                      margin = 0;
                    }
                  }
                } else if (
                  finish_position == "2" &&
                  rawHorse.RaceResult.FinishPosition["@_DeadHeat"] &&
                  rawHorse.RaceResult.FinishPosition["@_DeadHeat"] == "true"
                ) {
                  dbRace.official_margin_2 = "DH";
                  beaten_margin =
                      rawHorse.RaceResult.FinishPosition["@_BeatenMargin"];
                    beaten_margin = capMargin(beaten_margin);
                    margin =
                      beaten_margin < 1
                        ? Math.ceil(parseFloat(beaten_margin) * 10) / 10
                        : Math.round(parseFloat(beaten_margin) * 10) / 10;
                    if (isNaN(margin) || margin === "") {
                      margin = 0;
                    }
                    dbRace.official_margin_1 = helper.convertExtendedMargin(
                      rawHorse.RaceResult.FinishPosition["@_BeatenMargin"]
                    );
                } else {
                  if (rawHorse.RaceResult.FinishPosition["@_BeatenMargin"]) {
                    beaten_margin =
                      rawHorse.RaceResult.FinishPosition["@_BeatenMargin"];
                    beaten_margin = capMargin(beaten_margin);
                    margin =
                      beaten_margin < 1
                        ? Math.ceil(parseFloat(beaten_margin) * 10) / 10
                        : Math.round(parseFloat(beaten_margin) * 10) / 10;
                    if (isNaN(margin) || margin === "") {
                      margin = 0;
                    }
                  }

                  if (
                    !isBlank(
                      rawHorse.RaceResult.FinishPosition["@_OfficialMargin"]
                    )
                  ) {
                    if (finish_position == "2") {
                      margin_official = helper.marginSelect(
                        rawHorse.RaceResult.FinishPosition["@_OfficialMargin"]
                      );
                      dbRace.official_margin_1 = helper.marginSelect(
                        rawHorse.RaceResult.FinishPosition["@_OfficialMargin"]
                      );
                    }
                    if (finish_position == "3") {
                      margin_official = helper.marginSelect(
                        rawHorse.RaceResult.FinishPosition["@_OfficialMargin"]
                      );
                      dbRace.official_margin_2 = helper.marginSelect(
                        rawHorse.RaceResult.FinishPosition["@_OfficialMargin"]
                      );
                    }
                  }
                }
                if (isNaN(margin) || margin === "") {
                  margin = 0;
                }
                if (!beaten_margin) {
                  if (
                    !(horse.scratched || rawHorse.Scratched) &&
                    !isBlank(horse.beaten_margin) &&
                    isNumericFinishPosition(finish_position)
                  ) {
                    [horse.error_log, horse.errors] = logError(
                      horse.error_log,
                      horse.errors,
                      `Horse margin missing`
                    );
                  }
                }
              } else {
                if (rawHorse.Scratched || horse.scratched) {
                  finish_position = "SCR";
                  prizemoney_race = -1;
                } else if (resultedrace) {
                  if (!(horse.scratched || rawHorse.Scratched)) {
                    [horse.error_log, horse.errors] = logError(
                      horse.error_log,
                      horse.errors,
                      `Finish position missing`
                    );
                  }
                }
              }

              // Process betting fluctuations
              if (
                rawHorse.RaceResult &&
                rawHorse.RaceResult.BettingFluctuations
              ) {
                if (rawHorse.RaceResult.BettingFluctuations["@_PriceOpen"]) {
                  decimalprices_opening =
                    rawHorse.RaceResult.BettingFluctuations["@_PriceOpen"];
                  decimalprices_opening = parseFloat(
                    decimalprices_opening.replace("$", "")
                  );
                  prices_opening = helper.convertOdds(
                    parseFloat(decimalprices_opening)
                  );
                  if (resultedrace && parseFloat(decimalprices_opening) > 349) {
                    [horse.error_log, horse.errors] = logError(
                      horse.error_log,
                      horse.errors,
                      `Betting seems out, as opening price is ${decimalprices_opening}`
                    );
                  }
                }

                if (rawHorse.RaceResult.BettingFluctuations["@_PriceMove1"]) {
                  decimalprices_mid =
                    rawHorse.RaceResult.BettingFluctuations["@_PriceMove1"];
                  decimalprices_mid = parseFloat(
                    decimalprices_mid.replace("$", "")
                  );
                  if (resultedrace && parseFloat(decimalprices_mid) > 349) {
                    [horse.error_log, horse.errors] = logError(
                      horse.error_log,
                      horse.errors,
                      `Betting seems out, as mid price is ${decimalprices_mid}`
                    );
                  }
                }

                if (rawHorse.RaceResult.BettingFluctuations["@_PriceMove2"]) {
                  decimalprices_mid_2 =
                    rawHorse.RaceResult.BettingFluctuations["@_PriceMove2"];
                  decimalprices_mid_2 = parseFloat(
                    decimalprices_mid_2.replace("$", "")
                  );
                  prices_mid_2 = helper.convertOdds(
                    parseFloat(decimalprices_mid_2)
                  );
                  if (resultedrace && parseFloat(decimalprices_mid_2) > 349) {
                    [horse.error_log, horse.errors] = logError(
                      horse.error_log,
                      horse.errors,
                      `Betting seems out, as mid_2 price is ${decimalprices_mid_2}`
                    );
                  }
                }

                if (rawHorse.RaceResult.StartingWinPrice) {
                  if (
                    rawHorse.RaceResult.StartingWinPrice["@_IsEqualFavourite"]
                  ) {
                    favourite_indicator = "E";
                    if (
                      isValid(rawHorse.RaceResult.StartingWinPrice["#text"])
                    ) {
                      decimalprices_starting =
                        rawHorse.RaceResult.StartingWinPrice["#text"];
                    } else {
                      [horse.error_log, horse.errors] = logError(
                        horse.error_log,
                        horse.errors,
                        `RA sent empty or invalid StartingWinPrice`
                      );
                    }
                  } else if (
                    rawHorse.RaceResult.StartingWinPrice["@_IsFavourite"]
                  ) {
                    favourite_indicator = "F";
                    if (
                      isValid(rawHorse.RaceResult.StartingWinPrice["#text"])
                    ) {
                      decimalprices_starting =
                        rawHorse.RaceResult.StartingWinPrice["#text"];
                    } else {
                      [horse.error_log, horse.errors] = logError(
                        horse.error_log,
                        horse.errors,
                        `RA sent empty or invalid StartingWinPrice`
                      );
                    }
                  } else {
                    decimalprices_starting =
                      rawHorse.RaceResult.StartingWinPrice;
                  }
                  decimalprices_starting = decimalprices_starting.replace(
                    "$",
                    ""
                  );
                  decimalprices_starting = decimalprices_starting.replace(
                    "F",
                    ""
                  );
                  decimalprices_starting = parseFloat(
                    decimalprices_starting.replace("E", "")
                  );
                  prices_starting = helper.convertOdds(
                    parseFloat(decimalprices_starting)
                  );
                }

                var mid_var = 0,
                  mid_2_var = 0;

                if (
                  decimalprices_mid_2 &&
                  decimalprices_opening &&
                  decimalprices_starting
                ) {
                  if (
                    decimalprices_mid_2 > decimalprices_opening &&
                    decimalprices_mid_2 > decimalprices_starting
                  ) {
                    mid_2_var =
                      decimalprices_mid_2 -
                      (decimalprices_opening + decimalprices_starting) / 2;
                  } else if (
                    decimalprices_mid_2 < decimalprices_opening &&
                    decimalprices_mid_2 < decimalprices_starting
                  ) {
                    mid_2_var =
                      (decimalprices_opening + decimalprices_starting) / 2 -
                      decimalprices_mid_2;
                  }
                }
                if (
                  decimalprices_mid &&
                  decimalprices_opening &&
                  decimalprices_starting
                ) {
                  if (
                    decimalprices_mid > decimalprices_opening &&
                    decimalprices_mid > decimalprices_starting
                  ) {
                    mid_var =
                      decimalprices_mid -
                      (decimalprices_opening + decimalprices_starting) / 2;
                  } else if (
                    decimalprices_mid < decimalprices_opening &&
                    decimalprices_mid < decimalprices_starting
                  ) {
                    mid_var =
                      (decimalprices_opening + decimalprices_starting) / 2 -
                      decimalprices_mid;
                  }
                }
                mid =
                  mid_2_var > mid_var
                    ? parseFloat(decimalprices_mid_2).toFixed(2)
                    : mid_var > 0
                    ? parseFloat(decimalprices_mid).toFixed(2)
                    : "";
                prices_mid = mid ? helper.convertOdds(parseFloat(mid)) : "";
                decimalprices_starting = parseFloat(
                  decimalprices_starting
                ).toFixed(2);
                decimalprices_opening = parseFloat(
                  decimalprices_opening
                ).toFixed(2);
              } else if (
                rawHorse.RaceResult &&
                rawHorse.RaceResult.StartingWinPrice
              ) {
                if (rawHorse.RaceResult.StartingWinPrice["@_IsFavourite"]) {
                  favourite_indicator = "F";
                  decimalprices_starting =
                    rawHorse.RaceResult.StartingWinPrice["#text"];
                } else {
                  decimalprices_starting = rawHorse.RaceResult.StartingWinPrice;
                }
                decimalprices_starting = decimalprices_starting.replace(
                  "$",
                  ""
                );
                decimalprices_starting = parseFloat(
                  decimalprices_starting.replace("F", "")
                ).toFixed(2);
                prices_starting = helper.convertOdds(
                  parseFloat(decimalprices_starting)
                );
              }
              if (resultedrace && parseFloat(decimalprices_starting) > 349) {
                [horse.error_log, horse.errors] = logError(
                  horse.error_log,
                  horse.errors,
                  `Betting seems out, as starting price is ${decimalprices_starting}`
                );
              }

              // Process positions in running
              if (rawHorse.RaceResult && rawHorse.RaceResult.InRunning) {
                var InRunning = rawHorse.RaceResult.InRunning;
                if (Array.isArray(InRunning)) {
                  if (
                    (found = InRunning.find(
                      (x) => x["@_Distance"] == "Settled"
                    ))
                  ) {
                    if (
                      found["@_Position"] &&
                      found["@_Position"] !== "0" &&
                      found["@_Position"] !== 0
                    ) {
                      console.log(found["@_Position"]);
                      positions_settling_down = found["@_Position"];
                    }
                  }

                  if (
                    (found = InRunning.find((x) => x["@_Distance"] == "1200"))
                  ) {
                    if (
                      found["@_Position"] &&
                      found["@_Position"] !== "0" &&
                      found["@_Position"] !== 0
                    ) {
                      console.log(found["@_Position"]);
                      positions_m1200 = found["@_Position"];
                    }
                  }

                  if (
                    (found = InRunning.find((x) => x["@_Distance"] == "800"))
                  ) {
                    if (
                      found["@_Position"] &&
                      found["@_Position"] !== "0" &&
                      found["@_Position"] !== 0
                    ) {
                      console.log(found["@_Position"]);
                      positions_m800 = found["@_Position"];
                    }
                  }

                  if (
                    (found = InRunning.find((x) => x["@_Distance"] == "400"))
                  ) {
                    if (
                      found["@_Position"] &&
                      found["@_Position"] !== "0" &&
                      found["@_Position"] !== 0
                    ) {
                      console.log(found["@_Position"]);
                      positions_m400 = found["@_Position"];
                    }
                  }
                } else {
                  if (
                    InRunning["@_Distance"] &&
                    InRunning["@_Distance"] == "400"
                  ) {
                    if (
                      InRunning["@_Position"] &&
                      InRunning["@_Position"] !== "0" &&
                      InRunning["@_Position"] !== 0
                    ) {
                      positions_m400 = InRunning["@_Position"];
                      positions_settling_down = InRunning["@_Position"];
                    }
                  }
                }
              }

              // Process prizemoney
              if (
                rawHorse.RaceResult &&
                rawHorse.RaceResult.MoneyPrize !== undefined &&
                rawHorse.RaceResult.MoneyPrize !== null &&
                !isNaN(parseFloat(rawHorse.RaceResult.MoneyPrize))
              ) {
                prizemoney_race = parseInt(rawHorse.RaceResult.MoneyPrize);
              } else {
                if (
                  !(horse.scratched || rawHorse.Scratched) &&
                  !isTrial &&
                  horse.prizemoney_race &&
                  parseInt(horse.prizemoney_race) !== 0
                ) {
                  [horse.error_log, horse.errors] = logError(
                    horse.error_log,
                    horse.errors,
                    `Invalid or empty prize money`
                  );
                }
              }

              var weight_carried = horse.weight_carried;
              if (
                rawHorse.RaceResult &&
                parseFloat(rawHorse.RaceResult.WeightCarried) &&
                !(
                  horse.weight_carried - 5 >
                  parseFloat(rawHorse.RaceResult.WeightCarried)
                ) &&
                !(
                  horse.weight_carried + 10 <
                  parseFloat(rawHorse.RaceResult.WeightCarried)
                )
              ) {
                weight_carried = parseFloat(rawHorse.RaceResult.WeightCarried);
              } else if (!isTrial && rawHorse.RaceResult) {
                [horse.error_log, horse.errors] = logError(
                  horse.error_log,
                  horse.errors,
                  `Horse carried weight seems well off, RA have sent as ${rawHorse.RaceResult.WeightCarried}`
                );
              }

              let results = {
                finish_position: finish_position,
                prizemoney_race: prizemoney_race,
                horse_time: horse_time,
                positions: {
                  "@_settling_down": positions_settling_down
                    ? positions_settling_down
                    : positions_m800,
                  "@_m1200": positions_m1200,
                  "@_m800": positions_m800,
                  "@_m400": positions_m400,
                  "@_finish": finish_position,
                },
                decimalprices: {
                  "@_opening": decimalprices_opening,
                  "@_mid": mid,
                  "@_starting": decimalprices_starting,
                },
                prices: {
                  "@_opening": prices_opening,
                  "@_mid": prices_mid,
                  "@_starting": prices_starting,
                },
                margin: margin,
                beaten_margin: beaten_margin,
                margin_official: margin_official,
                weight_carried: weight_carried,
                barrier:
                  rawHorse.BarrierNumber != 0
                    ? rawHorse.BarrierNumber
                    : rawHorse.TabNumber,
              };
              // Process horse comments
              if (rawHorse.comment) {
                if (horse.comment && horse.comment !== rawHorse.comment) {
                  [horse.error_log, horse.errors] = logError(
                    horse.error_log,
                    horse.errors,
                    `Horse comment changed from "${horse.comment}" to "${rawHorse.comment}"`
                  );
                }
              }
              if (
                rawHorse.RaceResult &&
                rawHorse.RaceResult.FinishPosition &&
                rawHorse.RaceResult.FinishPosition["@_DeadHeat"] &&
                rawHorse.RaceResult.FinishPosition["@_DeadHeat"] == "true"
              ) {
                results.dead_heat_indicator = "D";
              }
              if (
                resultedrace &&
                !(rawHorse.Scratched || horse.scratched) &&
                !isTrial &&
                !results.weight_carried &&
                !isBlank(horse.weight_carried)
              ) {
                helper.logError(error_log, errors, `Horse missing weight`);
              }

              if (favourite_indicator && decimalprices_starting) {
                results = Object.assign(results, {
                  favourite_indicator: favourite_indicator,
                });
              }
              if (isBlank(horse_time)) delete results.horse_time;

              if (rawHorse.Scratched) {
                let scratched = {
                  scratched: rawHorse.Scratched,
                };
                results = Object.assign(results, scratched);
              }
              if (rawHorse.RaceResult && rawHorse.RaceResult.MoneyBonus) {
                prizemoney_bonus = rawHorse.RaceResult.MoneyBonus;
                results = Object.assign(results, {
                  prizemoney_bonus: prizemoney_bonus,
                  prizemoney_won: parseInt(
                    theHorseTotalPrize + prizemoney_race + prizemoney_bonus
                  ),
                });
              } else {
                results = Object.assign(results, {
                  prizemoney_won: parseInt(
                    theHorseTotalPrize + prizemoney_race
                  ),
                });
              }

              // Adding extra fields to add into FORMS table
              if (
                rawHorse.JockeyRaceEntry &&
                rawHorse.JockeyRaceEntry.Name &&
                !rawHorse.Scratched &&
                !horse.scratched
              ) {
                var jockey_data = await centaur.jockeys
                  .findOne({
                    JOC_JOCKEY_FIRSTNAME:
                      rawHorse.JockeyRaceEntry.PreferredName,
                    JOC_JOCKEY_SURNAME: rawHorse.JockeyRaceEntry.Surname,
                  })
                  .lean();
                if (!jockey_data) {
                  jockey_data = await centaur.jockeys
                    .findOne({
                      JOC_JOCKEY_RANAME: rawHorse.JockeyRaceEntry.Name,
                    })
                    .lean();
                }
                if (jockey_data) {
                  var jockey_name = jockey_data.JOC_JOCKEY_DISPLAYNAME;
                  var jockey_firstname = jockey_data.JOC_JOCKEY_FIRSTNAME;
                  var jockey_surname = jockey_data.JOC_JOCKEY_SURNAME;
                  var jockey_id = jockey_data.JOC_JOCKEY_ID;
                  var jockey_apprentice_indicator =
                    rawHorse.JockeyRaceEntry["@_Apprentice"] === "true"
                      ? "Y"
                      : "";
                  if (
                    jockey_apprentice_indicator != "Y" &&
                    parseFloat(rawHorse.JockeyRaceEntry.WeightClaim) > 0
                  ) {
                    helper.logError(
                      error_log,
                      errors,
                      `Jockey has an allowance weight but has not been marked as an apprentice`
                    );
                  }
                  let jockey_allowance_weight = rawHorse.JockeyRaceEntry
                    .WeightClaim
                    ? parseFloat(rawHorse.JockeyRaceEntry.WeightClaim)
                    : "";
                  if (
                    parseFloat(jockey_allowance_weight) > 5 ||
                    parseFloat(jockey_allowance_weight) < 0
                  ) {
                    helper.logError(
                      error_log,
                      errors,
                      `Jockey has an allowance weight outside standard range: ${jockey_allowance_weight}`
                    );
                  }
                  let jockey_riding_weight = rawHorse.JockeyRaceEntry
                    .RidingWeight
                    ? parseFloat(rawHorse.JockeyRaceEntry.RidingWeight)
                    : "";
                  let jockey_record = {
                    jockey: {
                      "@_name": jockey_name,
                      "@_firstname": jockey_firstname,
                      "@_surname": jockey_surname,
                      "@_apprentice_indicator": jockey_apprentice_indicator,
                      "@_allowance_weight": jockey_allowance_weight,
                      "@_id": jockey_id,
                      "@_riding_weight": jockey_riding_weight,
                    },
                    jockey_id: jockey_id,
                  };
                  if (!horse.jockey || !horse.jockey["@_id"]) {
                    results = Object.assign(results, jockey_record);
                  }
                  if (
                    !horse.jockey ||
                    !horse.jockey["@_id"] ||
                    (horse.jockey &&
                      jockey_data.JOC_JOCKEY_ID != horse.jockey["@_id"])
                  ) {
                    helper.logError(
                      [],
                      errors,
                      `Jockey has changed in RA file: ${
                        jockey_record.jockey["@_name"]
                      } ${
                        jockey_record.jockey["@_id"]
                      } ${jockey_apprentice_indicator}${
                        jockey_allowance_weight > 0
                          ? " (a" + jockey_allowance_weight + ")"
                          : ""
                      }`
                    );
                  } else {
                    if (
                      horse.jockey &&
                      horse.jockey["@_allowance_weight"] &&
                      parseFloat(horse.jockey["@_allowance_weight"]) > 0 &&
                      parseFloat(jockey_allowance_weight) !=
                        parseFloat(horse.jockey["@_allowance_weight"])
                    ) {
                      helper.logError(
                        error_log,
                        errors,
                        `Jockey allowance weight has changed in the RA file from ${horse.jockey["@_allowance_weight"]} to ${jockey_allowance_weight}, please update manually`
                      );
                    }
                  }

                  horse.errors = errors;
                  horse.error_log = error_log;
                } else {
                  [horse.error_log, horse.errors] = helper.logError(
                    horse.error_log,
                    horse.errors,
                    `Couldn't match Jockey - ${rawHorse.JockeyRaceEntry.PreferredName} ${rawHorse.JockeyRaceEntry.Surname}, RA name is ${rawHorse.JockeyRaceEntry.Name}`
                  );
                }
              } else {
                if (resultedrace && !horse.scratched && !results.scratched) {
                  [horse.error_log, horse.errors] = helper.logError(
                    horse.error_log,
                    horse.errors,
                    `Jockey Missing from results file`
                  );
                } else {
                  if (horse.scratched) {
                    // Set empty jockey information for scratched horses
                    let jockey_record = {
                      jockey: {},
                    };
                    results = Object.assign(results, jockey_record);
                  } else {
                    // Retain the existing jockey information for non-scratched horses in non-resulted races
                    let jockey_record = {
                      jockey: horse.jockey,
                    };
                    results = Object.assign(results, jockey_record);
                  }
                }
              }
              // Assign results to horse, with checks to avoid overwriting with blank values
              for (let key in results) {
                if (!isBlank(results[key])) {
                  if (nestedObjectKeys.includes(key)) {
                    // Merge positions without overwriting existing non-blank values
                    horse[key] = mergePositions(
                      horse[key],
                      results[key],
                      horse["@_name"],
                      horse.scratched,
                      horse.error_log,
                      horse.errors,
                      isTrial
                    );
                  } else {
                    horse[key] = results[key];
                  }
                } else {
                  // Error logging logic
                  if (
                    parseInt(horse.finish_position) < 4 &&
                    key == "margin_official" &&
                    !horse.margin &&
                    !isTrial &&
                    !isBlank(horse[key])
                  ) {
                    [horse.error_log, horse.errors] = logError(
                      horse.error_log,
                      horse.errors,
                      `RA sent blank ${key} for horse ${horse["@_name"]}`
                    );
                  }
                  if (
                    key !== "margin_official" &&
                    !horse.scratched &&
                    !isTrial &&
                    !isBlank(horse[key])
                  ) {
                    [horse.error_log, horse.errors] = logError(
                      horse.error_log,
                      horse.errors,
                      `RA sent blank ${key} for horse ${horse["@_name"]}`
                    );
                  }
                  // Do not overwrite existing value
                }
              }

              dbRace.horses.horse[horseIndex] = horse;
            } else {
              console.log(
                `((ERROR)): couldn't find existing horse in race to load results into`
              );
              console.log(race["@_CurrentRaceStage"], race["@_RaceNumber"]);
              console.log(
                horse["@_name"],
                horseIndex,
                race.RaceEntries.RaceEntry[horseIndex]?.Horse
              );
              [horse.error_log, horse.errors] = helper.logError(
                horse.error_log,
                horse.errors,
                "couldn't match horse for results"
              );
            }
          } catch (err) {
            console.log(
              `((ERROR)): Error updating horse result for ${race.RaceEntries.RaceEntry[horseIndex]?.Horse["@_HorseName"]}: ${err}`
            );
          }
          horseIndex++;
        }
        // After processing all horses, compare the counts

        // Ensure RaceEntries.RaceEntry is an array
        if (!Array.isArray(race.RaceEntries.RaceEntry)) {
          race.RaceEntries.RaceEntry = [race.RaceEntries.RaceEntry];
        }

        // Collect names of non-scratched horses from dbRace
        let dbHorseNames = dbRace.horses.horse
          .filter((h) => !h.scratched)
          .map((h) => h["@_name"].toLowerCase());

        // Collect names of non-scratched horses from results content
        let resultHorseNames = race.RaceEntries.RaceEntry.filter(
          (h) => !h.Scratched
        ).map((h) => h.Horse["@_HorseName"].toLowerCase());

        // Get counts
        let dbHorseCount = dbHorseNames.length;
        let resultHorseCount = resultHorseNames.length;

        // Identify missing horses
        let missingInResults = dbHorseNames.filter(
          (name) => !resultHorseNames.includes(name)
        );
        // Identify extra horses in results
        let extraInResults = resultHorseNames.filter(
          (name) => !dbHorseNames.includes(name)
        );

        if (dbHorseCount !== resultHorseCount && isTrial) {
          let errorMessage = `Mismatch in number of runners: Database has ${dbHorseCount} runners, results file has ${resultHorseCount} runners in race ${race["@_RaceNumber"]}.`;

          if (missingInResults.length > 0) {
            errorMessage += ` Missing horses in results: ${missingInResults.join(
              ", "
            )}.`;
          }

          if (extraInResults.length > 0) {
            errorMessage += ` Extra horses in results: ${extraInResults.join(
              ", "
            )}.`;
          }

          [dbRace.error_log, dbRace.errors] = logError(
            dbRace.error_log,
            dbRace.errors,
            errorMessage
          );
        }

        var horseErrorCount = 0;
        var silksArray = [];
        var jockeysArray = [];
        var startMissing = false;
        for (let record of dbRace.horses.horse) {
          if (!record.scratched) {
            if (
              record.jockey &&
              record.jockey["@_id"] &&
              jockeysArray.includes(record.jockey["@_id"])
            ) {
              for (let horse of dbRace.horses.horse) {
                if (
                  horse.jockey &&
                  horse.jockey["@_id"] === record.jockey["@_id"]
                ) {
                  [horse.error_log, horse.errors] = helper.logError(
                    horse.error_log,
                    horse.errors,
                    `Duplicate jockey detected ${horse.jockey["@_name"]}`
                  );
                }
              }
            }
            if (record.jockey) {
              jockeysArray.push(record.jockey["@_id"]);
            }
            if (!record.decimalprices["@_starting"] && !isTrial)
              startMissing = true;
          }
          if (record.errors && record.errors.length > 0) {
            horseErrorCount++;
          }
        }
        if (resultedrace && startMissing) {
          [dbRace.error_log, dbRace.errors] = helper.logError(
            dbRace.error_log,
            dbRace.errors,
            `Some starting prices are missing`
          );
        }

        meetingData.processedMeetingData.meeting.races.race[raceIndex] = dbRace;
      }

      let resultsCompleted = await centaur.temp_meetings
        .updateOne(
          { _id: meetingId },
          {
            $set: meetingData,
          }
        )
        .lean();

      if (resultsCompleted) {
        var currDate = moment();
        var meetdate = moment(meetingData.meetingDate);
        var dayGap = currDate.diff(meetdate, "days");
        var distribute = false;
        var raceNo = 0;
        var files = "";
        if (resultsCompleted && dayGap < 7) {
          //     var msg = `Results Loaded for MeetingId: ${meetingId}`
          //     console.log(msg)
          //     if (!hasErrors){
          //         console.log(distributeResults,trialStatus)
          //         if (distributeResults.length == 1 && trialStatus != 'trial'){
          //             raceNo = parseInt(distributeResults[0])
          //         }
          // distribute = true
          // files = 'RESULTS'
          //     }
        }
        let tc = await triggerCompare(meetingId, files, distribute, raceNo);
        // console.log("tc", tc);
        return true;
      } else {
        return false;
      }
    } else {
      console.log(
        `((ERROR)): couldn't find existing meeting to load results into ${meetingId}`
      );
      await triggerCompare(meetingId, "", false, 0);
      return false;
    }
  } catch (error) {
    console.log("Error in processResults ", error);
  }
};

const processSectionals = async (secContent, meetingId) => {
  const meeting = await centaur.temp_meetings
    .findOne({ _id: meetingId })
    .select("inputMeetingData")
    .select("meetingCountry")
    .select("processedMeetingData")
    .lean();
  var dataToUpdate = {};
  var raceIndex = 0;
  for (race of secContent.races) {
    const dbRace = meeting.processedMeetingData.meeting.races.race[raceIndex];
    var race_errors = dbRace.errors;

    for (const horse of race) {
      var horseIndex = 0;
      var foundhorse = false;
      for (const dbHorse of dbRace.horses.horse) {
        if (horse.horse_name.toLowerCase() == dbHorse["@_name"].toLowerCase()) {
          dataToUpdate[
            "processedMeetingData.meeting.races.race." +
              raceIndex +
              ".horses.horse." +
              horseIndex +
              ".sectional_200"
          ] = horse.l200;
          foundhorse = true;
          break;
        }
        horseIndex++;
      }
      if (!foundhorse) {
        race_errors.push(
          `Cant match horse for sectional data. file: ${horse.horse_name} database: ${dbHorse["@_name"]}`
        );
      }
    }
    dataToUpdate[
      "processedMeetingData.meeting.races.race." + raceIndex + ".errors"
    ] = race_errors;
    raceIndex++;
  }
  // console.log(dataToUpdate);
  let sectionalsCompleted = await centaur.temp_meetings
    .updateOne(
      { _id: meetingId },
      {
        $set: dataToUpdate,
      }
    )
    .lean();
  // console.log(sectionalsCompleted);
  var sendMail = await helper.mailAlert(
    "Sectional 200 file received",
    `Meeting at ${meeting.processedMeetingData.meeting.track["@_name"]} ${meeting.processedMeetingData.meeting.date} has been updated`,
    "alert"
  );
  // console.log(sendMail);
  let tc = await triggerCompare(meetingId, "RESULTS", false, 0);
  console.log("tc", tc);
  return true;
};

const processGearChanges = async (gearContent, meetingId) => {
  // Fetch InputMeetingData for Meeting Id
  const meeting = await centaur.temp_meetings
    .findOne({ _id: meetingId })
    .select("inputMeetingData")
    .select("processedMeetingData")
    .lean();
  inputMeetingData = meeting.inputMeetingData;
  processedMeetingData = meeting.processedMeetingData;
  var raceIndex = 0;
  var raceHorseGearArray = [];
  for (race of gearContent.Meeting.Races.Race) {
    const inputRaceIndex = inputMeetingData.races.race.findIndex(
      (element) =>
        element["@_id"] ===
        processedMeetingData.meeting.races.race[raceIndex]["@_id"]
    );
    if (race.RaceEntries && race.RaceEntries.RaceEntry) {
      // check if gear file has gears as an array, or an object (single horse becomes an object)
      // then create an array of gears to add with horse names for matching
      if (typeof race.RaceEntries.RaceEntry[Symbol.iterator] === "function") {
        for (horse of race.RaceEntries.RaceEntry) {
          var outputArray = [];
          if (horse.Gear["@_FirstTimeGearChanges"] != "") {
            var t = horse.Gear["@_FirstTimeGearChanges"].split(",");
            for (var element of t) {
              var theEach = helper.processGearElement(element);
              theEach["@_id"] = helper.getGearID(theEach["@_name"]);
              outputArray.push(theEach);
            }
          }

          var d = {
            HorseName: horse["@_HorseName"],
            RaceEntryCode: horse["@_RaceEntryCode"],
            GearData: outputArray,
          };
          raceHorseGearArray.push(d);
        }
      } else {
        var horse = race.RaceEntries.RaceEntry;
        var outputArray = [];
        if (horse.Gear["@_FirstTimeGearChanges"] != "") {
          var t = horse.Gear["@_FirstTimeGearChanges"].split(",");
          for (var element of t) {
            var theEach = helper.processGearElement(element);
            theEach["@_id"] = helper.getGearID(theEach["@_name"]);
            outputArray.push(theEach);
          }
        }

        var d = {
          HorseName: horse["@_HorseName"], //helper.upperCase(horse['@_HorseName']),
          RaceEntryCode: horse["@_RaceEntryCode"],
          GearData: outputArray,
        };
        raceHorseGearArray.push(d);
      }
    }
    // call function which matches the gears and adds them to the appropriate horses in the db
    var gearSuccess = await updateGears(
      meetingId,
      processedMeetingData.meeting.races.race[raceIndex],
      inputMeetingData.races.race[inputRaceIndex],
      raceIndex,
      inputRaceIndex,
      raceHorseGearArray
    );
    // move to next race
    raceIndex++;
  }

  var msg = `Gear Updated for MeetingId: ${meetingId}`;
  // console.log(msg);
  // distribute the gear files if approprate
  // var distribute = ['Acceptances','FinalFields','Final Fields','InterimResults','Interim Results','Results'].includes(meeting.meetingStage) ? true : false
  var distribute = true;
  var raceNo = 0;
  var files = "GEAR";
  let tc = await triggerCompare(meetingId, files, distribute, raceNo);
  return true;
};

const updateGears = async (
  meetingId,
  processedMeetingRaceData,
  inputMeetingRaceData,
  raceIndex,
  inputRaceIndex,
  raceHorseGearArray
) => {
  // Search for Horse Index matching exclusively by @_id
  let pmHorseArray = [];
  const horseToUpdate =
    "inputMeetingData.races.race." + inputRaceIndex + ".horses.horse.";
  const raceToUpdate =
    "processedMeetingData.meeting.races.race." + raceIndex + ".horses.horse";
  let dataToUpdate = {};
  for (var i = 0; i < processedMeetingRaceData.horses.horse.length; i++) {
    // Search raceHorseGearArray with this horse
    let pmHorse = processedMeetingRaceData.horses.horse[i];
    const inputIndex = inputMeetingRaceData.horses.horse.findIndex(
      (element) => element["@_id"] === pmHorse["@_id"]
    );
    // console.log(inputMeetingRaceData.horses.horse);
    // console.log(pmHorse["@_id"], inputIndex);

    let horse = inputMeetingRaceData.horses.horse[inputIndex];
    if (!horse || !horse["@_name"]) {
      console.log(`Missing horse name data at index ${inputIndex}`);
      pmHorseArray.push(pmHorse);
      continue;
    }
    let t = raceHorseGearArray.find((o, i) => {
      if (o.HorseName.toLowerCase() == horse["@_name"].toLowerCase()) {
        return true;
      }
    });

    if (t) {
      let tt = { gear_changes: { gear_change: t.GearData } };
      Object.assign(horse, tt);

      tt.running_gear = {};
      if (horse.running_gear && horse.running_gear.gear_item) {
        tt.running_gear.gear_item = helper.runningGears(
          t.GearData,
          horse.running_gear.gear_item
        );
      } else {
        horse.running_gear =
          pmHorse.running_gear && pmHorse.running_gear.gear_item
            ? pmHorse.running_gear
            : { gear_item: [] };
        tt.running_gear.gear_item = helper.runningGears(
          t.GearData,
          horse.running_gear.gear_item
        );
      }
      Object.assign(pmHorse, tt);
      pmHorseArray.push(pmHorse);
    } else {
      pmHorseArray.push(pmHorse);
    }

    dataToUpdate[horseToUpdate + inputIndex] = horse;
  }
  dataToUpdate[raceToUpdate] = pmHorseArray;
  let status = await centaur.temp_meetings
    .updateOne(
      { _id: meetingId },
      {
        $set: dataToUpdate,
      }
    )
    .lean();

  if (status) {
    console.log(`Successfully Updated InputMeetingData Race ${raceIndex} Gear`);
  } else {
    // var sendMail = await helper.mailAlert('Gear Update error',`Failed Updating ${meetingId} InputMeetingData Race ${raceIndex} Gear`,'error')
    // console.log(sendMail)
    console.log(
      `((ERROR)): Failed Updating ${meetingId} InputMeetingData Race ${raceIndex} Gear`
    );
  }
};

const processScratchings = async (scratchingContent, meetingId) => {
  // Fetch InputMeetingData for Meeting Id
  const meeting = await centaur.temp_meetings
    .findOne({ _id: meetingId })
    .select("inputMeetingData")
    .select("processedMeetingData")
    .select("meetingStage")
    .select("meetingErrorCount")
    .lean();
  // if (helper.checkResultsStage(meeting.meetingStage)){
  //     let tc = await triggerCompare(meetingId,'',false,0)
  //     return true
  // }
  var meetingErrorCount = meeting.meetingErrorCount;
  inputMeetingData = meeting.inputMeetingData;
  processedMeetingData = meeting.processedMeetingData;
  var raceIndex = 0;

  var updateContent = {};
  var riders = "";
  if (scratchingContent.MeetingScratchings) {
    updateContent = scratchingContent.MeetingScratchings;
    riders = "LateRiders_Alterations";
  } else if (scratchingContent.RiderUpdate) {
    updateContent = scratchingContent.RiderUpdate;
    riders = "UpdatedRiders";
  }
  var rawTrack = updateContent.Track;
  var dbTrack = processedMeetingData.meeting.track;
  // dbTrack['@_expected_condition'] = rawTrack.TrackRating['@_Name'] + "("+(rawTrack.TrackRating['@_NumericRating'] ?? 0)+")"
  dbTrack["penetrometer"] = rawTrack.Penetrometer;
  dbTrack["weather"] = rawTrack.Weather;
  // dbTrack['track_comments'] = rawTrack.Comments
  dbTrack["irrigation"] = rawTrack.Irrigation;
  dbTrack["rainfall"] = rawTrack.Rainfall;
  dbTrack["track_info"] = rawTrack.TrackInfo;
  let status = await centaur.temp_meetings
    .updateOne(
      { _id: meetingId },
      {
        $set: { "processedMeetingData.meeting.track": dbTrack },
      }
    )
    .lean();
  for (race of updateContent.Races.Race)
    if (
      !processedMeetingData.meeting.races.race[raceIndex].race_stage ||
      !helper.checkResultsStage(
        processedMeetingData.meeting.races.race[raceIndex].race_stage
      )
    ) {
      var raceHorseScratchingsArray = [];
      var updateTheJockeys;
      var updateTheScratchings;
      var undeclareTheJockeys;
      var raceHorseJockeysArray = [];
      const inputRaceIndex = inputMeetingData.races.race.findIndex(
        (element) =>
          element["@_id"] ===
          processedMeetingData.meeting.races.race[raceIndex]["@_id"]
      );
      console.log(
        processedMeetingData.meeting.races.race[raceIndex]["@_id"],
        raceIndex,
        inputRaceIndex
      );
      if (
        race.RaceScratchings &&
        race.RaceScratchings.Scratching &&
        typeof race.RaceScratchings.Scratching[Symbol.iterator] === "function"
      ) {
        for (horse of race.RaceScratchings.Scratching) {
          var d = {
            HorseName: horse.Horse["@_HorseName"], //helper.upperCase(horse['@_HorseName']),
            RaceEntryCode: horse["@_RaceEntryCode"],
          };
          raceHorseScratchingsArray.push(d);
        }
        updateTheScratchings = await updateScratchings(
          meetingId,
          processedMeetingData.meeting.races.race[raceIndex],
          inputMeetingData.races.race[inputRaceIndex],
          raceIndex,
          inputRaceIndex,
          raceHorseScratchingsArray
        );
        if (!updateTheScratchings) {
          console.log(
            `failed to update scratchings for race ${raceIndex} at meeting ${meetingId}`
          );
        }
      } else if (race.RaceScratchings && race.RaceScratchings.Scratching) {
        var d = {
          HorseName: race.RaceScratchings.Scratching.Horse["@_HorseName"], //helper.upperCase(horse['@_HorseName']),
          RaceEntryCode: race.RaceScratchings.Scratching["@_RaceEntryCode"],
        };
        raceHorseScratchingsArray.push(d);
        updateTheScratchings = await updateScratchings(
          meetingId,
          processedMeetingData.meeting.races.race[raceIndex],
          inputMeetingData.races.race[inputRaceIndex],
          raceIndex,
          inputRaceIndex,
          raceHorseScratchingsArray
        );
        if (!updateTheScratchings) {
          console.log(
            `failed to update scratchings for race ${raceIndex} at meeting ${meetingId}`
          );
        }
      }
      // Update Jockeys from scratching file
      if (
        race[riders] &&
        race[riders].RaceEntry &&
        typeof race[riders].RaceEntry[Symbol.iterator] === "function"
      ) {
        for (horse of race[riders].RaceEntry) {
          var d = {
            HorseName: horse.Horse["@_HorseName"],
            RaceEntryCode: horse["@_RaceEntryCode"],
            errors: [],
          };
          if (horse.Jockey) {
            const jockey_data = await centaur.jockeys
              .findOne({
                JOC_JOCKEY_FIRSTNAME: horse.Jockey.PreferredName,
                JOC_JOCKEY_SURNAME: horse.Jockey.Surname,
              })
              .lean();
            if (jockey_data) {
              var jockeydata = {
                "@_name": jockey_data.JOC_JOCKEY_DISPLAYNAME
                  ? jockey_data.JOC_JOCKEY_DISPLAYNAME
                  : "",
                "@_firstname": jockey_data.JOC_JOCKEY_FIRSTNAME
                  ? jockey_data.JOC_JOCKEY_FIRSTNAME
                  : "",
                "@_surname": jockey_data.JOC_JOCKEY_SURNAME
                  ? jockey_data.JOC_JOCKEY_SURNAME
                  : "",
                "@_apprentice_indicator":
                  horse.Jockey["@_Apprentice"] == "true" ? "Y" : "",
                "@_allowance_weight":
                  horse.Jockey.WeightClaim &&
                  parseFloat(horse.Jockey.WeightClaim)
                    ? parseFloat(horse.Jockey.WeightClaim)
                    : horse.Jockey["@_Apprentice"] == "true"
                    ? 0
                    : "",
                "@_id": jockey_data.JOC_JOCKEY_ID
                  ? jockey_data.JOC_JOCKEY_ID
                  : "",
                "@_riding_weight": horse.Jockey.RidingWeight
                  ? parseFloat(horse.Jockey.RidingWeight)
                  : "",
                statistics: jockey_data.JOC_JOCKEY_ID
                  ? await getJockeyStats(jockey_data.JOC_JOCKEY_ID)
                  : [],
              };
              d.jockey = jockeydata;
            } else {
              // d.jockey = "remove";
              meetingErrorCount++;
              d.errors = d.errors.push(
                `jockey not found: ${horse.Jockey.PreferredName} ${horse.Jockey.Surname}`
              );
            }
          } else {
            if (!helper.checkResultsStage(race.race_stage)) {
              d.jockey = "remove";
              meetingErrorCount++;
              d.errors = d.errors.push(`jockey removed by scratchings file.`);
            } else {
              // If the race has been run, skip updating the jockey
              continue;
            }
          }

          raceHorseJockeysArray.push(d);
        }
        updateTheJockeys = await updateJockeys(
          meetingId,
          processedMeetingData.meeting.races.race[raceIndex],
          inputMeetingData.races.race[inputRaceIndex],
          raceIndex,
          inputRaceIndex,
          raceHorseJockeysArray
        );
        if (!updateTheJockeys) {
          console.log(
            `failed to update jockeys for race ${raceIndex} at meeting ${meetingId}`
          );
        }
      } else if (race[riders] && race[riders].RaceEntry) {
        var d = {
          HorseName: race[riders].RaceEntry.Horse["@_HorseName"],
          RaceEntryCode: race[riders].RaceEntry["@_RaceEntryCode"],
          errors: [],
        };
        if (race[riders].RaceEntry.Jockey) {
          let jockey_data = await centaur.jockeys
            .findOne({
              JOC_JOCKEY_FIRSTNAME: race[riders].RaceEntry.Jockey.PreferredName,
              JOC_JOCKEY_SURNAME: race[riders].RaceEntry.Jockey.Surname,
            })
            .lean();
          if (!jockey_data) {
            jockey_data = await centaur.jockeys
              .findOne({
                JOC_JOCKEY_RANAME:
                  race[riders].RaceEntry.Jockey.PreferredName +
                  " " +
                  race[riders].RaceEntry.Jockey.Surname,
              })
              .lean();
          }
          if (jockey_data) {
            var jockeydata = {
              "@_name": jockey_data.JOC_JOCKEY_DISPLAYNAME ?? "",
              "@_firstname": jockey_data.JOC_JOCKEY_FIRSTNAME ?? "",
              "@_surname": jockey_data.JOC_JOCKEY_SURNAME ?? "",
              "@_apprentice_indicator":
                race[riders].RaceEntry.Jockey["@_Apprentice"] == "true"
                  ? "Y"
                  : "",
              "@_allowance_weight":
                race[riders].RaceEntry.Jockey.WeightClaim &&
                parseFloat(race[riders].RaceEntry.Jockey.WeightClaim)
                  ? parseFloat(race[riders].RaceEntry.Jockey.WeightClaim)
                  : race[riders].RaceEntry.Jockey["@_Apprentice"] == "true"
                  ? 0
                  : "",
              "@_id": jockey_data.JOC_JOCKEY_ID
                ? jockey_data.JOC_JOCKEY_ID
                : "",
              "@_riding_weight": race[riders].RaceEntry.Jockey.RidingWeight
                ? parseFloat(race[riders].RaceEntry.Jockey.RidingWeight)
                : "",
              statistics: jockey_data.JOC_JOCKEY_ID
                ? await getJockeyStats(jockey_data.JOC_JOCKEY_ID)
                : [],
            };
            d.jockey = jockeydata;
          } else {
            meetingErrorCount++;
            d.errors = d.errors.push(
              `jockey not found: ${race[riders].RaceEntry.Jockey.PreferredName} ${race[riders].RaceEntry.Jockey.Surname}`
            );
          }
        } else {
          d.jockey = "remove";
        }
        raceHorseJockeysArray.push(d);
        updateTheJockeys = await updateJockeys(
          meetingId,
          processedMeetingData.meeting.races.race[raceIndex],
          inputMeetingData.races.race[inputRaceIndex],
          raceIndex,
          inputRaceIndex,
          raceHorseJockeysArray
        );
        if (!updateTheJockeys) {
          console.log(
            `failed to update jockeys for race ${raceIndex} at meeting ${meetingId}`
          );
        }
      }

      if (
        race.UndeclaredRiders &&
        race.UndeclaredRiders.RaceEntry &&
        typeof race.UndeclaredRiders.RaceEntry[Symbol.iterator] === "function"
      ) {
        for (horse of race.UndeclaredRiders.RaceEntry) {
          var d = {
            HorseName: horse.Horse["@_HorseName"],
            RaceEntryCode: horse["@_RaceEntryCode"],
            jockey: "remove",
          };
          raceHorseJockeysArray.push(d);
        }
        undeclareTheJockeys = await updateJockeys(
          meetingId,
          processedMeetingData.meeting.races.race[raceIndex],
          inputMeetingData.races.race[inputRaceIndex],
          raceIndex,
          inputRaceIndex,
          raceHorseJockeysArray
        );
        if (!undeclareTheJockeys) {
          console.log(
            `failed to undeclare jockeys for race ${raceIndex} at meeting ${meetingId}`
          );
        }
      } else if (race.UndeclaredRiders && race.UndeclaredRiders.RaceEntry) {
        var d = {
          HorseName: race.UndeclaredRiders.RaceEntry.Horse["@_HorseName"], //helper.upperCase(horse['@_HorseName']),
          RaceEntryCode: race.UndeclaredRiders.RaceEntry["@_RaceEntryCode"],
          jockey: "remove",
        };
        raceHorseJockeysArray.push(d);
        undeclareTheJockeys = await updateJockeys(
          meetingId,
          processedMeetingData.meeting.races.race[raceIndex],
          inputMeetingData.races.race[inputRaceIndex],
          raceIndex,
          inputRaceIndex,
          raceHorseJockeysArray
        );
        if (!undeclareTheJockeys) {
          console.log(
            `failed to undeclare jockeys for race ${raceIndex} at meeting ${meetingId}`
          );
        }
      }
      raceIndex++;
    } else {
      raceIndex++;
    }
  let updErr = await centaur.temp_meetings
    .updateOne(
      { _id: meetingId },
      {
        $set: {
          meetingErrorCount: meetingErrorCount,
        },
      }
    )
    .lean();

  var msg = `Scratchings: ${updateTheScratchings}, Jockeys: ${updateTheJockeys}, and Undeclared riders: ${undeclareTheJockeys} Updated for MeetingId: ${meetingId}`;
  console.log(msg);
  const marketProcessingSuccess = await processRaceMarkets(
    meetingId,
    processedMeetingData
  );
  if (!marketProcessingSuccess) {
    console.error(`Failed to process markets for meeting ${meetingId}`);
  }
  // var distribute = ['Acceptances','FinalFields','Final Fields','InterimResults','Interim Results','Results'].includes(meeting.meetingStage) ? true : false
  var distribute = true;
  var raceNo = 0;
  var files = "SCRATCHINGS";
  let tc = await triggerCompare(meetingId, files, distribute, raceNo);
  // console.log(tc)
  return true;
};
const processRaceMarkets = async (meetingId, processedMeetingData) => {
  try {
    const CONFIDENCE_LEVEL = 5;

    // Process each race
    for (let race of processedMeetingData.meeting.races.race) {
      // Skip if race is already in results stage
      if (helper.checkResultsStage(race.race_stage)) {
        continue;
      }

      // Extract horses from race
      const horses = race.horses.horse.map((horse) => ({
        id: horse["@_id"],
        rating: parseFloat(horse.rating || 0),
        scratched: horse.scratched === true,
        tip: horse.tip || null,
      }));

      // Apply market calculations
      const processedHorses = helper.enterMarket(horses, CONFIDENCE_LEVEL);

      // Update the race horses with new betting and tip information
      race.horses.horse = race.horses.horse.map((existingHorse) => {
        const processedHorse = processedHorses.find(
          (h) => h.id === existingHorse["@_id"]
        );
        if (processedHorse) {
          return {
            ...existingHorse,
            betting: processedHorse.betting,
            tip: processedHorse.tip,
          };
        }
        return existingHorse;
      });
    }

    // Update the database with processed market data
    await centaur.temp_meetings.updateOne(
      { _id: meetingId },
      {
        $set: {
          "processedMeetingData.meeting.races":
            processedMeetingData.meeting.races,
        },
      }
    );

    return true;
  } catch (error) {
    console.error("Error processing race markets:", error);
    return false;
  }
};
const processNZUpdates = async (scratchingContent, meetingId) => {
  // Fetch InputMeetingData for Meeting Id
  // console.log(scratchingContent);
  const meeting = await centaur.temp_meetings
    .findOne({ _id: meetingId })
    .select("inputMeetingData")
    .select("processedMeetingData")
    .select("meetingStage")
    .lean();
  let dataToUpdate = {};

  let inputDb = meeting.inputMeetingData;
  let inputRaces = inputDb.races.race;
  let meetingDb = meeting.processedMeetingData.meeting;
  let rawData = scratchingContent;
  let raceIndex = 0;
  let totalerrors = 0;
  dataToUpdate["processedMeetingData.meeting.track.@_expected_condition"] =
    helper.convertNzTrackRating(rawData.meeting["@_meetinglatesttrack"]);
  dataToUpdate["processedMeetingData.meeting.rail_position"] = rawData.meeting[
    "@_meetinglatestrail"
  ]
    ? rawData.meeting["@_meetinglatestrail"].split(" | ")[0]
    : "";
  for (rawRace of rawData.meeting.races.race) {
    let inputRace = inputRaces[raceIndex];
    let dbRace = meetingDb.races.race[raceIndex];
    let race_errors = inputRace.errors;
    let race_error_log = inputRace.error_log;
    if (rawRace["@_racename"] != inputRace["@_ra_name"]) {
      [race_error_log, race_errors] = helper.logError(
        race_error_log,
        race_errors,
        `NZ race name has changed from ${inputRace["@_ra_name"]} to ${rawRace["@_racename"]}`
      );
      inputRace["@_ra_name"] = rawRace["@_racename"];
    }
    if (rawRace["@_raceclass"] != inputRace["raceClass"]) {
      [race_error_log, race_errors] = helper.logError(
        race_error_log,
        race_errors,
        `NZ race class has changed from ${inputRace["raceClass"]} to ${rawRace["@_raceclass"]}`
      );
      inputRace["raceClass"] = rawRace["@_raceclass"];
    }
    let theTime = "0000";
    let raceHours = rawRace["@_racestarttime"].split(/(:|am|pm)/);
    if (raceHours[3] == "pm") {
      raceHours[0] = (parseInt(raceHours[0]) + 12).toString();
      if (raceHours[0] == "24") {
        raceHours[0] = "12";
      }
    }
    if (raceHours[0].length == 1) {
      theTime = "0" + raceHours[0] + raceHours[2];
    } else {
      theTime = raceHours[0] + raceHours[2];
    }
    if (inputRace["start_time"] && theTime != inputRace["start_time"]) {
      [race_error_log, race_errors] = helper.logError(
        race_error_log,
        race_errors,
        `NZ race time has changed from ${inputRace["start_time"]} to ${theTime}`
      );
      inputRace["start_time"] = theTime;
      dbRace.start_time = theTime;
    }
    let distance = parseInt(rawRace["@_racelength"].replace("m", ""));
    if (distance != dbRace.distance["@_metres"]) {
      [race_error_log, race_errors] = helper.logError(
        race_error_log,
        race_errors,
        `NZ race distance has changed from ${dbRace.distance["@_metres"]} to ${distance}`
      );
      dbRace.distance["@_metres"] = distance;
    }
    dbRace.restrictions["@_jockey"] =
      rawRace["@_raceallowances"] == "true"
        ? "Apprentices Can Claim"
        : "Apprentices Cannot Claim";
    let race_error_count = 0;
    for (let i = 0; i < rawRace.runners.runner.length; i++) {
      let horse = rawRace.runners.runner[i];
      let dbHorse = dbRace.horses.horse[i];
      let inputHorse = inputRace.horses.horse[i];
      if (
        horse["@_runnername"].toLowerCase() != dbHorse["@_name"].toLowerCase()
      ) {
        [race_error_log, race_errors] = helper.logError(
          race_error_log,
          race_errors,
          `NZ race HORSES DONT MATCH, consider deleting and reloading`
        );
        break;
      }
      let horse_errors = inputHorse.errors;
      let horse_error_log = inputHorse.error_log;
      if (horse["@_runnerscratched"]) {
        dbRace.horses.horse[i].scratched = true;
        dbRace.horses.horse[i].jockey = {
          "@_name": "",
          "@_firstname": "",
          "@_surname": "",
          "@_id": "0",
          "@_apprentice_indicator": "",
          "@_allowance_weight": "",
          statistics: {},
        };
        continue;
      } else {
        if (helper.capFstLtr(horse["@_runnersilks"]) != inputHorse.silks) {
          [horse_error_log, horse_errors] = helper.logError(
            horse_error_log,
            horse_errors,
            `Silks have changed from ${inputHorse.silks} to ${helper.capFstLtr(
              horse["@_runnersilks"]
            )}`
          );
          inputRace.horses.horse[i].silks = helper.capFstLtr(
            horse["@_runnersilks"]
          );
          dbRace.horses.horse[i].colours = helper.capFstLtr(
            horse["@_runnersilks"]
          );
        }
        if (
          horse["@_runnerweightcarried"] !=
          dbRace.horses.horse[i].weight_carried
        ) {
          if (
            horse["@_runnerweightcarried"] >
              parseInt(dbRace.horses.horse[i].weight_carried) - 5 &&
            horse["@_runnerweightcarried"] <
              parseInt(dbRace.horses.horse[i].weight_carried) + 10
          ) {
            [horse_error_log, horse_errors] = helper.logError(
              horse_error_log,
              horse_errors,
              `Horse weight updated from ${dbRace.horses.horse[i].weight_carried} to ${horse["@_runnerweightcarried"]}`
            );
            dbRace.horses.horse[i].weight_carried =
              horse["@_runnerweightcarried"];
            dbRace.horses.horse[i]["weight"] = {
              "@_allocated": horse["@_runnerweightcarried"],
              "@_total": horse["@_runnerweightcarried"],
            };
          } else {
            [horse_error_log, horse_errors] = helper.logError(
              horse_error_log,
              horse_errors,
              `Horse weight from file seems incorrect at ${horse["@_runnerweightcarried"]}, not updated`
            );
          }
        }
        var rating = horse["@_runnerrating"]
          ? parseFloat(horse["@_runnerrating"].replace("R", ""))
          : dbRace.horses.horse[i].rating;
        dbRace.horses.horse[i].rating = rating;
        if (horse["@_runnertote"] != dbHorse.tab_number) {
          [horse_error_log, horse_errors] = helper.logError(
            horse_error_log,
            horse_errors,
            `Tab number has changed from ${dbHorse.tab_number} to ${horse["@_runnertote"]}`
          );
          dbRace.horses.horse[i].tab_number = horse["@_runnertote"];
        }
        // turn this back on one day if they want barrier updates
        // dbRace.horses.horse[i].barrier = horse['@_runnerbarrier']
        if (
          horse["@_runneremergency"] &&
          horse["@_runneremergency"].includes("E")
        ) {
          dbRace.horses.horse[i].emergency_indicator = "E";
        }

        if (horse["@_runnerjockey"] && horse["@_runnerjockey"] != "-") {
          var jockey_name_srch = helper.splitNameBySpace(
            horse["@_runnerjockey"].replace(/^Ms /g, "")
          );
          var jockeyname = horse["@_runnerjockey"]
            .replace(/ \([0-9.am]*\)/g, "")
            .replace(/^Ms /g, "");

          var jockey_data = await centaur.jockeys
            .findOne({ JOC_JOCKEY_NZNAME: jockeyname })
            .lean();
          if (!jockey_data) {
            jockey_data = await centaur.jockeys
              .findOne({
                JOC_JOCKEY_RANAME:
                  jockey_name_srch.firstName + " " + jockey_name_srch.lastName,
              })
              .lean();
            if (jockey_data) {
              [horse_error_log, horse_errors] = helper.logError(
                horse_error_log,
                horse_errors,
                `NZ Jockey matched AUS name, please update DB - ${jockey_data.JOC_JOCKEY_DISPLAYNAME} NZ_NAME: ${jockeyname}`
              );
              race_error_count++;
            }
          }
          if (!jockey_data) {
            jockey_data = await centaur.jockeys
              .findOne({
                JOC_JOCKEY_FIRSTNAME: jockey_name_srch.firstName,
                JOC_JOCKEY_SURNAME: jockey_name_srch.lastName,
              })
              .lean();
            if (jockey_data) {
              [horse_error_log, horse_errors] = helper.logError(
                horse_error_log,
                horse_errors,
                `NZ Jockey matched Generic name, please update DB - ${jockey_data.JOC_JOCKEY_DISPLAYNAME} NZ_NAME: ${jockeyname}`
              );
              race_error_count++;
            }
          }
          if (jockey_data) {
            if (
              !dbHorse.jockey ||
              jockey_data.JOC_JOCKEY_ID != dbHorse.jockey["@_id"] ||
              (jockey_name_srch.claim &&
                parseFloat(
                  jockey_name_srch.claim.replace("a", "").replace("-", "")
                ) != parseFloat(dbHorse.jockey["@_allowance_weight"]))
            ) {
              var jockey_name = jockey_data.JOC_JOCKEY_DISPLAYNAME;
              var jockey_firstname = jockey_data.JOC_JOCKEY_FIRSTNAME;
              var jockey_surname = jockey_data.JOC_JOCKEY_SURNAME;
              var jockey_id = jockey_data.JOC_JOCKEY_ID;
              var jockey_apprentice_indicator =
                jockey_data.JOC_JOCKEY_APPRENTICE_IND;
              if (jockey_name_srch.claim) {
                jockey_apprentice_indicator = "Y";
                jockey_allowance_weight = parseFloat(
                  jockey_name_srch.claim.replace("a", "").replace("-", "")
                );
              } else {
                jockey_apprentice_indicator = "";
                jockey_allowance_weight = "";
              }
              dbRace.horses.horse[i].jockey = {
                "@_name": jockey_name,
                "@_firstname": jockey_firstname,
                "@_surname": jockey_surname,
                "@_id": jockey_id,
                "@_apprentice_indicator": jockey_apprentice_indicator,
                "@_allowance_weight": jockey_allowance_weight,
                statistics: await getJockeyStats(jockey_id),
              };
            }
          } else {
            [horse_error_log, horse_errors] = helper.logError(
              horse_error_log,
              horse_errors,
              `Jockey present in file but not matched - ${horse["@_runnerjockey"]}`
            );
            // }
          }
        } else {
          dbRace.horses.horse[i].jockey = {
            "@_name": "",
            "@_firstname": "",
            "@_surname": "",
            "@_id": "0",
            "@_apprentice_indicator": "",
            "@_allowance_weight": "",
            statistics: {},
          };
        }
        dbRace.horses.horse[i].errors = horse_errors;
        inputRace.horses.horse[i].errors = horse_errors;
        inputRace.horses.horse[i].error_log = horse_error_log;
      }
    }
    if (race_error_count > 0) {
      race_errors.push(`Horses with errors: ${race_error_count}`);
    }
    dbRace.errors = race_errors;
    inputRace.errors = race_errors;
    inputRace.error_log = race_error_log;
    totalerrors = totalerrors + (race_errors.length + race_error_count);
    dataToUpdate[`processedMeetingData.meeting.races.race.${raceIndex}`] =
      dbRace;
    dataToUpdate[`inputMeetingData.races.race.${raceIndex}`] = inputRace;
    raceIndex++;
  }

  dataToUpdate.meetingErrorCount = totalerrors;
  var tempMeetUpdate = await centaur.temp_meetings
    .updateOne(
      { _id: meetingId },
      {
        $set: dataToUpdate,
      }
    )
    .lean();

  // var distribute = ['Acceptances','FinalFields','Final Fields','InterimResults','Interim Results','Results'].includes(meeting.meetingStage) ? true : false
  var distribute = true;
  var raceNo = 0;
  var files = "SCRATCHINGS";
  let tc = await triggerCompare(meetingId, files, distribute, raceNo);
  // console.log(tc)
  return true;
};

const processNZResults = async (resultsData, decodedKey, bucket) => {
  console.log(JSON.stringify(resultsData));

  // Use regex to match the date (8 digits) and track name
  const match = decodedKey.match(/(\d{8})\s*[-_\s]*(.+?)\/\d/);

  let date, track;
  if (match) {
    date = match[1];
    track = match[2].replace(/_/g, " ");
  }
  console.log("date", date);
  console.log("track is ", track);

  const year = date.substring(4, 8);
  const month = date.substring(2, 4);
  const day = date.substring(0, 2);
  const isoDateString = `${year}-${month}-${day}T00:00:00.000Z`;

  const processedMeeting = await centaur.processed_meetings
    .findOne({
      meetingDate: isoDateString,
      meetingCountry: "NZ",
      "processedMeetingData.meeting.track.@_name": track,
    })
    .lean();

  if (!processedMeeting) {
    console.log("No matching processed meeting found");
    await helper.mailAlert(
      "No matching for NZ result track meeting found",
      `track they sent  ${track} `,
      "error"
    );
    return false;
  }

  console.log("processedMeeting", processedMeeting._id);
  // Create a history item for this update
  const historyItem = {
    time: moment().toISOString(),
    bucket: bucket,
    file_path: decodedKey,
    trigger: "New File",
  };
  // Get the existing history or initialize a new array
  let meetingLoadHistory = processedMeeting.meetingLoadHistory || [];

  // Add the new history item
  meetingLoadHistory.unshift(historyItem);

  let tempMeeting = new centaur.temp_meetings({
    ...processedMeeting,
    meetingLoadHistory: meetingLoadHistory,
  });
  let tempResults = await tempMeeting.save();
  console.log("tempResults", tempResults);

  await centaur.processed_meetings
    .updateOne(
      { _id: processedMeeting._id },
      {
        $set: {
          meetingLocked: "load",
        },
      }
    )
    .lean();

  // Find the matching race in the processedMeetingData
  const matchingRace = tempResults.processedMeetingData.meeting.races.race.find(
    (race) => race["@_number"] == resultsData.Race["@_Number"]
  );

  if (!matchingRace) {
    console.log("No matching race found");
    return false;
  }
  // Find the winning horse
  const winningHorse = resultsData.Race.Result.find(
    (result) => result["@_FinishPos"] === "1"
  );

  // Update the race duration if a winning horse is found
  if (winningHorse) {
    matchingRace.duration =
      winningHorse["@_ResultTime"] || matchingRace.duration;
  }
  // Calculate the number of starters (excluding SCR, LS, SCB)
  const actualStarters = resultsData.Race.Result.filter(
    (result) => !["SCR", "LS", "SCB"].includes(result["@_FinishPos"])
  ).length;

  matchingRace.starters = actualStarters;
  // Helper function to normalize horse names
  const normalizeHorseName = (name) =>
    name
      .replace(/\s*\([^)]*\)/, "")
      .trim()
      .toLowerCase();

  // Helper function to parse weight
  const parseWeight = (weightString) => {
    const match = weightString.match(/(\d+(\.\d+)?)/);
    if (!match) return null;
    let weight = parseFloat(match[1]);
    // Round weight to the nearest 0.5 or whole number
    weight = Math.round(weight * 2) / 2;
    return weight;
  };

  const safeParseFloat = (value) => {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? null : parsed;
  };
  let matchedHorseCount = 0;
  // Update the horse data in the matching race
  matchingRace.horses.horse = matchingRace.horses.horse.map((horse) => {
    const resultHorse = resultsData.Race.Result.find(
      (result) =>
        normalizeHorseName(result["@_Horse"]) ===
        normalizeHorseName(horse["@_name"])
    );
    if (resultHorse) {
      matchedHorseCount++;
      let horse_errors = resultHorse.errors;
      let horse_error_log = resultHorse.error_log;
      if (isNaN(resultHorse["@_FinishPos"])) {
        [horse_error_log, horse_errors] = helper.logError(
          [horse_error_log],
          [horse_errors],
          `${resultHorse["@_Horse"]} got an unexpected finish position: ${resultHorse["@_FinishPos"]}`
        );
      }

      return {
        ...horse,
        barrier: resultHorse["@_Barrier"] || horse["barrier"],
        finish_position: resultHorse["@_FinishPos"] || horse["finish_position"],
        margin:
          safeParseFloat(resultHorse["@_DecimalMargin"]) || horse["margin"],
        beaten_margin:
          safeParseFloat(resultHorse["@_DecimalMargin"]) ||
          horse["beaten_margin"],
        weight_carried:
          parseWeight(resultHorse["@_Weight"]) || horse.weight_carried,
        errors: horse_errors,
        error_log: horse_error_log,
      };
    } else {
      if (horse["scratched"]) {
        matchedHorseCount++;
      }
    }
    return horse;
  });
  let race_errors = matchingRace.errors;
  let race_error_log = matchingRace.error_log;
  if (matchedHorseCount !== matchingRace.horses.horse.length) {
    console.log(
      `Mismatch in matched horses: ${matchedHorseCount} horses matched out of ${matchingRace.horses.horse.length} in resultsData`
    );
    [race_error_log, race_errors] = helper.logError(
      race_error_log,
      race_errors,
      `There is missmatch in the horse number in this race NZ sends ${matchingRace.horses.horse.length} we have ${matchedHorseCount}`
    );
    matchingRace.errors = race_errors;
    matchingRace.error_log = race_error_log;
  }
  // Update the document in the database
  await centaur.temp_meetings.updateOne(
    { _id: processedMeeting._id },
    {
      $set: {
        "processedMeetingData.meeting.races":
          processedMeeting.processedMeetingData.meeting.races,
        meetingLocked: "",
        meetingStage: "Results",
        meetingLoadHistory: meetingLoadHistory,
        "processedMeetingData.meeting.stage": "Results",
      },
    }
  );

  await triggerCompare(processedMeeting._id, "", false, 0);

  return true;
};

const updateScratchings = async (
  meetingId,
  processedMeetingRaceData,
  inputMeetingRaceData,
  raceIndex,
  inputRaceIndex,
  raceHorseScratchingsArray
) => {
  // Rebuilding the Horse array
  // Search for Horse Index matching by @_name
  let pmHorseArray = [];

  const horseToUpdate =
    "inputMeetingData.races.race." + inputRaceIndex + ".horses.horse.";
  const raceToUpdate =
    "processedMeetingData.meeting.races.race." + raceIndex + ".horses.horse";
  let dataToUpdate = {};
  for (var i = 0; i < processedMeetingRaceData.horses.horse.length; i++) {
    // Search raceHorseGearArray with this horse
    let pmHorse = processedMeetingRaceData.horses.horse[i];
    const inputIndex = inputMeetingRaceData.horses.horse.findIndex(
      (element) => element["@_id"] === pmHorse["@_id"]
    );
    let horse = inputMeetingRaceData.horses.horse[inputIndex];
    console.log(inputMeetingRaceData);
    console.log(raceIndex, inputIndex, horse);
    console.log(pmHorse);
    let t = raceHorseScratchingsArray.find((o, i) => {
      if (o.HorseName.toLowerCase() == horse["@_name"].toLowerCase()) {
        return true;
      }
    });
    if (t) {
      let tt = { scratched: true, betting: "", tip: "" };
      Object.assign(horse, tt);
      Object.assign(pmHorse, tt);
      if (pmHorse !== null) {
        pmHorseArray.push(pmHorse);
      }
    } else {
      if (pmHorse !== null) {
        pmHorseArray.push(pmHorse);
      }
    }
    dataToUpdate[horseToUpdate + inputIndex] = horse;
  }
  dataToUpdate[raceToUpdate] = pmHorseArray;
  let status = await centaur.temp_meetings
    .updateOne(
      { _id: meetingId },
      {
        $set: dataToUpdate,
      }
    )
    .lean();

  if (status) {
    return true;
  } else {
    // var sendMail = await helper.mailAlert('Scratchings Update error',`Failed Updating ${meetingId} InputMeetingData Race ${raceIndex} Scratchings`,'error')
    // console.log(sendMail)
    console.log(
      `((ERROR)): Failed Updating ${meetingId} Race ${raceIndex} Scratchings`
    );
    return false;
  }
};

const updateJockeys = async (
  meetingId,
  processedMeetingRaceData,
  inputMeetingRaceData,
  raceIndex,
  inputRaceIndex,
  raceHorseJockeysArray
) => {
  // Rebuilding the Horse array
  // Search for Horse Index matching @_name
  let dataToUpdate = {};
  let pmHorseArray = [];
  let inputHorseArray = [];
  let inputRace = inputMeetingRaceData.horses.horse;
  let processedRace = processedMeetingRaceData.horses.horse;
  let raceErrors = processedMeetingRaceData.errors;
  for (let i = 0; i < processedRace.length; i++) {
    // Search raceHorseScratchingsArray with this horse
    let pmHorse = processedRace[i];
    const inputIndex = inputRace.findIndex(
      (element) => element["@_id"] === pmHorse["@_id"]
    );
    let horse = inputRace[inputIndex];
    let t = raceHorseJockeysArray.find((o, i) => {
      if (o.HorseName.toLowerCase() == horse["@_name"].toLowerCase()) {
        return true;
      }
    });

    if (t) {
      if (t.jockey === "remove") {
        delete pmHorse.jockey;
      } else if (t.jockey) {
        let tt = { jockey: t.jockey };
        Object.assign(pmHorse, tt);
      }
      if (t.errors && t.errors.length > 0) {
        for (const theError of t.errors) {
          [pmHorse.error_log, pmHorse.errors] = helper.logError(
            pmHorse.error_log,
            pmHorse.errors,
            theError
          )[(horse.error_log, horse.errors)] = helper.logError(
            horse.error_log,
            horse.errors,
            theError
          );
        }
      }
    }
    pmHorseArray.push(pmHorse);
    dataToUpdate[
      "inputMeetingData.races.race." +
        inputRaceIndex +
        ".horses.horse." +
        inputIndex
    ] = horse;
  }

  dataToUpdate[
    "processedMeetingData.meeting.races.race." + raceIndex + ".horses.horse"
  ] = pmHorseArray;
  dataToUpdate[
    "processedMeetingData.meeting.races.race." + raceIndex + ".errors"
  ] = raceErrors;
  dataToUpdate["inputMeetingData.races.race." + inputRaceIndex + ".errors"] =
    raceErrors;
  let status = await centaur.temp_meetings
    .updateOne(
      { _id: meetingId },
      {
        $set: dataToUpdate,
      }
    )
    .lean();

  if (status) {
    return true;
  } else {
    // var sendMail = await helper.mailAlert('Jockey Update error',`Failed Updating ${meetingId} InputMeetingData Race ${raceIndex} Jockeys`,'error')
    // console.log(sendMail)
    console.log(
      `((ERROR)): Failed Updating ${meetingId} InputMeetingData Race ${raceIndex} Jockeys`
    );
    return false;
  }
};

const triggerCompare = async (
  meetingId,
  files,
  distribute = false,
  raceNo = 0
) => {
  // activate and call the meeting comparison lambda which then activates the meeting distribute API call
  var params = {
    FunctionName: "MRCentCompareMeeting-" + process.env.ENV,
    InvocationType: "RequestResponse",
    Payload: JSON.stringify({
      meetingId: meetingId,
      files: files,
      raceNo: raceNo,
      checkstats: false,
      compareType: "load",
      distribute: distribute,
    }),
  };
  var res_3 = await lambdaInvokeAsync(params);
  console.log(res_3);
  return "success";
};

const lambdaInvokeAsync = async (params) => {
  var lambda = new AWS.Lambda();
  return new Promise((resolve, reject) => {
    lambda.invoke(params, function (err, data) {
      if (err) {
        console.error("Error while calling MRCentCompareMeeting", err);
        reject(err);
      } else {
        console.log(
          "Successfully called MRCentCompareMeeting asynchronously",
          data
        );
        resolve(data);
      }
    });
  });
};
// const params = {
//   FunctionName: `MRCentCompareMeeting-${process.env.ENV}`,
//   InvocationType: "RequestResponse",
//   Payload: JSON.stringify({ meetingId: meetingId, files: files, checkstats: checkstats, compareType: compareType, distribute: distribute }),
// };
// //step 5: call the lambda function to compare the meeting data
// // and make sure no errors during the process
// const result = await lambdaInvokeAsync(params);
// console.log("Compare Lambda invoke result:", result);

const checkMeetingStage = async (meetingId, newMeetingStage) => {
  const record = await centaur.processed_meetings
    .findById(meetingId)
    .select("meetingStage")
    .lean();
  if (record) {
    var currentMeetingStage = record.meetingStage;
    if (
      currentMeetingStage == "Abandoned" ||
      currentMeetingStage == "Postponed"
    ) {
      return false;
    }
    if (currentMeetingStage == "Weights") {
      if (newMeetingStage == "Nominations") {
        return false;
      }
    }

    if (currentMeetingStage == "Acceptances") {
      if (newMeetingStage == "Nominations" || newMeetingStage == "Weights") {
        return false;
      }
    }

    if (
      currentMeetingStage == "FinalFields" ||
      currentMeetingStage == "Final Fields"
    ) {
      if (
        newMeetingStage == "Nominations" ||
        newMeetingStage == "Weights" ||
        newMeetingStage == "Acceptances"
      ) {
        return false;
      }
    }
    if (helper.checkResultsStage(currentMeetingStage)) {
      if (
        newMeetingStage == "Nominations" ||
        newMeetingStage == "Weights" ||
        newMeetingStage == "Acceptances" ||
        newMeetingStage == "FinalFields" ||
        newMeetingStage == "Final Fields"
      ) {
        return false;
      }
    }
  }
  return true;
};
const isResultsStage = async (meetingId, newMeetingStage) => {
  const record = await centaur.processed_meetings
    .findById(meetingId)
    .select("meetingStage")
    .lean();

  if (record) {
    const currentMeetingStage = record.meetingStage;

    // Check if both current and new stages are Results stages
    return (
      helper.checkResultsStage(currentMeetingStage) &&
      helper.checkResultsStage(newMeetingStage)
    );
  }

  return false;
};
const processRegistrationAU = async (regFileContent) => {
  var regFileDate = regFileContent["Rego"]["@_GenerationDate"];
  const fileId = await getUuid(`${regFileDate}-${process.env.ENV}`);
  const recordsExists = await centaur.registration_files
    .findOne({ _id: fileId })
    .lean();
  var waitfordelete = false;
  if (recordsExists) {
    waitfordelete = await centaur.registration_files
      .deleteOne({ _id: fileId })
      .lean();
  }

  if (!recordsExists || waitfordelete) {
    var newData = new centaur.registration_files({
      _id: fileId,
      regFileDate: regFileDate,
    });
    var d = await newData.save();
    if (d) {
      // console.log(d)
      console.log("New Registration File Created with Id: " + fileId);
    }
  }

  var completeEntries = [];
  var count = 0;
  var totalCount =
    regFileContent.Rego.Registrations.HorseRegistrationEntry.length;
  for (horse of regFileContent.Rego.Registrations.HorseRegistrationEntry) {
    var foalDate = helper.sDate(horse.Horse["@_FoalDate"]);
    var horseName = helper.upperCase(horse.Horse["@_HorseName"]);
    var damName = helper.upperCase(horse.Breeding.Dam["@_HorseName"]);
    var damCountry = "AUS";
    if (horse.Breeding.Dam["@_HorseCountryAbbr"]) {
      damCountry = horse.Breeding.Dam["@_HorseCountryAbbr"];
    }
    var sireName = helper.upperCase(horse.Breeding.Sire["@_HorseName"]);
    var sireCountry = "AUS";
    if (horse.Breeding.Sire["@_HorseCountryAbbr"]) {
      sireCountry = horse.Breeding.Sire["@_HorseCountryAbbr"];
    }
    var damSireName = helper.upperCase(horse.Breeding.GrandSire["@_HorseName"]);
    var damSireCountry = "AUS";
    if (horse.Breeding.GrandSire["@_HorseCountryAbbr"]) {
      damSireCountry = horse.Breeding.GrandSire["@_HorseCountryAbbr"];
    }
    var namePrev = helper.upperCase(horse.Horse["@_NamePrev"]);
    completeEntries.push(
      await processRegistrationAU_verify(
        fileId,
        foalDate,
        horseName,
        damName,
        damCountry,
        sireName,
        sireCountry,
        damSireName,
        damSireCountry,
        namePrev,
        horse
      )
    );
    // break
    count++;
    if (count == 100) {
      totalCount = totalCount - 100;
      console.log(
        `Waited for some time in processing next, Current Count - ${totalCount} `
      );
      helper.sleep(10000);
      count = 0;
      //break
    }
  }
  var registrationCompleted = await Promise.all(completeEntries);
  if (registrationCompleted) {
    // var msg = 'Registration File Processed!'
    // console.log(msg)
    return true;
  } else {
    // var sendMail = await helper.mailAlert('Rego error',`Failed loading registration file`,'error')
    // console.log(sendMail)
    console.log(`((ERROR)): Failed loading registration file`);
  }
};

const processRegistrationAU_verify = async (
  fileId,
  foalDate,
  horseName,
  damName,
  damCountry,
  sireName,
  sireCountry,
  damSireName,
  damSireCountry,
  namePrev,
  rawData
) => {
  let showHorseInRego = true;
  const horse_log = `HorseName: ${horseName}, Dam Name: ${damName}, NamePrev: ${namePrev}`;
  const theDam = await damCheck(
    fileId,
    horseName,
    damName,
    damCountry,
    damSireName,
    damSireCountry
  );

  if (theDam && theDam.HRN_HORSE_ID) {
    const damId = theDam.HRN_HORSE_ID;
    let horseData = await centaur.horses
      .findOne()
      .where("HRN_HORSE_NAME")
      .equals(horseName)
      .where("HOR_FOALING_DATE")
      .equals(foalDate)
      .where("HOR_DAM_ID")
      .equals(damId)
      .lean();
    if (!horseData) {
      horseData = await centaur.horses
        .findOne({ HRN_PREV_NAMES: horseName })
        .where("HOR_FOALING_DATE")
        .equals(foalDate)
        .where("HOR_DAM_ID")
        .equals(damId)
        .lean();
      if (horseData) {
        rawData.report = `Horse name appears to be reverted: '${horseData.HRN_HORSE_NAME}' to '${horseName}' id: ${horseData.HRN_HORSE_ID}`;
        var update_nameChange = await centaur.registration_files.updateOne(
          { _id: fileId },
          { $push: { nameChange: rawData } }
        );
        if (update_nameChange) {
          // console.log(`Name Change Updated in DB - ${horse_log}`)
        }
      }
    }
    if (horseData) {
      var horseId = horseData.HRN_HORSE_ID;
      // Update fields in horses table
      //Compare the fields and check which ones are updated, Update them and also create the log in Registration Files with what is updated in updatedHorses section

      var dataToUpdate = {};
      if (horseData.HOR_SEX != rawData.Horse["@_Sex"]) {
        if (horseData.HOR_SEX == "G") {
          var sendMail = await helper.mailAlert(
            `REGO error: horse ungelded by RA ${horseData.HRN_HORSE_ID}`,
            `horse has been UNGELDED in RA Rego, please review: ${horseData.HRN_HORSE_NAME} ${horseData.HRN_HORSE_ID}`,
            "task"
          );
          console.log(sendMail);
        } else {
          dataToUpdate.HOR_SEX = rawData.Horse["@_Sex"];
        }
      }
      if (!horseData.HRN_DISPLAY_NAME) {
        dataToUpdate.HRN_DISPLAY_NAME = rawData.Horse["@_HorseName"];
      }

      // Need mapping table for colours
      if (
        horseData.HOR_COLOUR !=
        Number(helper.getHorseColorCode(rawData.Horse["@_Colour"]))
      ) {
        dataToUpdate.HOR_COLOUR = Number(
          helper.getHorseColorCode(rawData.Horse["@_Colour"])
        );
      }

      var horseStatus = "";
      if (rawData.Horse["@_HorseStatus"] != "") {
        if (rawData.Horse["@_HorseStatus"] == "Retired") {
          horseStatus = "R";
          showHorseInRego = false;
        }
        if (rawData.Horse["@_HorseStatus"] == "Dead") {
          horseStatus = "D";
          showHorseInRego = false;
        }

        if (horseStatus == "R" || horseStatus == "D") {
          if (horseData.HRN_ROW_STATUS != horseStatus) {
            dataToUpdate.HRN_ROW_STATUS = horseStatus;
          }
        }
      }

      if (rawData.Trainer.Location != "") {
        if (
          horseData.HOR_TRAINING_LOCATION !=
          helper.upperCase(rawData.Trainer.Location)
        ) {
          dataToUpdate.HOR_TRAINING_LOCATION = helper.upperCase(
            rawData.Trainer.Location
          );
        }
      }

      var blinkers = "";
      if (rawData.Blinkers != "") {
        if (rawData.Blinkers == true) {
          blinkers = "Y";
        } else {
          blinkers = "";
        }
        if (horseData.HOR_CURRENT_BLINKER_IND != blinkers) {
          dataToUpdate.HOR_CURRENT_BLINKER_IND = blinkers;
        }
      }

      if (rawData.RacebookOwnersName != "") {
        if (
          horseData.HOR_OWNER_NAMES !=
          helper.upperCase(rawData.RacebookOwnersName)
        ) {
          dataToUpdate.HOR_OWNER_NAMES = helper.upperCase(
            rawData.RacebookOwnersName
          );
        }
      }

      if (rawData.RacingColours != "") {
        if (
          horseData.HOR_RACING_COLOURS !=
          helper.upperCase(rawData.RacingColours)
        ) {
          dataToUpdate.HOR_RACING_COLOURS = helper.upperCase(
            rawData.RacingColours
          );
        }
      }

      if (
        rawData.Trainer["@_TrainerCode"] != "" &&
        rawData.Trainer["@_TrainerCode"] != "0" &&
        rawData.Trainer["@_TrainerCode"] != 0
      ) {
        var trainerData = await centaur.trainers
          .findOne({ RISATrainerID: rawData.Trainer["@_TrainerCode"] })
          .lean();
        if (trainerData) {
          if (horseData.HOR_TRAINER_ID != trainerData.TRN_TRAINER_ID) {
            dataToUpdate.HOR_TRAINER_ID = trainerData.TRN_TRAINER_ID;
          }
        } else {
          trainerData = await centaur.trainers
            .findOne({ RISAPartnerID: rawData.Trainer["@_TrainerCode"] })
            .lean();
          if (trainerData) {
            if (horseData.HOR_TRAINER_ID != trainerData.TRN_TRAINER_ID) {
              dataToUpdate.HOR_TRAINER_ID = trainerData.TRN_TRAINER_ID;
            }
          } else {
            console.log(`trainer missing from rego ${rawData.Trainer.Name}`);
            var sendMail = await helper.mailAlert(
              `REGO error: trainer missing ${rawData.Trainer.Name}`,
              `trainer is missing: ${JSON.stringify(
                rawData.Trainer
              )} for horse ${horseData.HRN_HORSE_NAME} ${
                horseData.HRN_HORSE_ID
              }`,
              "task"
            );
            console.log(sendMail);
          }
        }
      } else {
        dataToUpdate.HOR_TRAINER_ID = 0;
      }

      if (!helper.isEmptyObject(dataToUpdate)) {
        //Update Horse
        let status = await centaur.horses
          .updateOne(
            { HRN_HORSE_ID: horseId },
            {
              $set: dataToUpdate,
            }
          )
          .lean();
        if (status) {
          //Log the entry of the horse in registrationfiles table in updatedHorses
          if (showHorseInRego) {
            var update_updatedHorses =
              await centaur.registration_files.updateOne(
                { _id: fileId },
                {
                  $push: {
                    updatedHorses: {
                      RAData: rawData,
                      horseId: horseId,
                      updatedData: dataToUpdate,
                    },
                  },
                }
              );
            if (update_updatedHorses) {
              // console.log(dataToUpdate)
              // console.log(`UpdatedHorses Updated in DB - ${horse_log}`)
            }
          }
        } else {
          return console.log(
            `((ERROR)): Problem Updating Rego Horses in DB - ${horse_log}`
          );
        }
      }
    } else {
      const horseData2 = await centaur.horses
        .findOne()
        .where("HRN_HORSE_NAME")
        .equals(namePrev)
        .where("HOR_FOALING_DATE")
        .equals(foalDate)
        .where("HOR_DAM_ID")
        .equals(damId)
        .lean();
      if (horseData2) {
        //Log the entry of the horse in registrationfiles table in nameChange
        rawData.report = `Horse has changed name from '${namePrev}' to '${horseName}' id: ${horseData2.HRN_HORSE_ID}`;
        var update_nameChange = await centaur.registration_files.updateOne(
          { _id: fileId },
          { $push: { nameChange: rawData } }
        );
        if (update_nameChange) {
          // console.log(`Name Change Updated in DB - ${horse_log}`)
        }
      } else {
        // console.log(`couldnt find horse, checking from sire ${sireName}`)
        // Search for Sire (Dam is already checked above)

        const horseData3 = await centaur.horses
          .findOne()
          .where("HOR_FOALING_DATE")
          .equals(foalDate)
          .where("HOR_DAM_ID")
          .equals(damId)
          .lean();

        if (horseData3) {
          rawData.report = `Horse might already exist, name: ${horseData3.HRN_HORSE_NAME} id: ${horseData3.HRN_HORSE_ID}`;
          var update_notFound = await centaur.registration_files.updateOne(
            { _id: fileId },
            { $push: { notFound: rawData } }
          );
          if (update_notFound) {
            // console.log(`NotFound Updated in DB - ${horse_log}`)
          }
        } else {
          const horseData4 = await centaur.horses
            .findOne()
            .where("HRN_HORSE_NAME")
            .equals(horseName)
            .where("HOR_FOALING_DATE")
            .equals(foalDate)
            .lean();

          if (horseData4) {
            rawData.report = `Horse may exist with non-matching DAM, name: ${horseData4.HRN_HORSE_NAME} id: ${horseData4.HRN_HORSE_ID}`;
            var update_notFound = await centaur.registration_files.updateOne(
              { _id: fileId },
              { $push: { notFound: rawData } }
            );
            if (update_notFound) {
              // console.log(`NotFound Updated in DB - ${horse_log}`)
            }
          } else {
            const horseData5 = await centaur.horses
              .findOne()
              .where("HRN_HORSE_NAME")
              .equals(horseName)
              .where("HOR_DAM_ID")
              .equals(damId)
              .lean();
            if (horseData5) {
              rawData.report = `Horse may have the wrong foaling date, id: ${
                horseData5.HRN_HORSE_ID
              } foaling: ${moment(horseData5.HOR_FOALING_DATE).format(
                "YYYY-MM-DD"
              )}`;
              var update_notFound = await centaur.registration_files.updateOne(
                { _id: fileId },
                { $push: { notFound: rawData } }
              );
              if (update_notFound) {
                // console.log(`NotFound Updated in DB - ${horse_log}`)
              }
            } else {
              const sireData = await centaur.horses
                .find()
                .where("HRN_HORSE_NAME")
                .equals(sireName)
                .where("HOR_COUNTRY_OF_ORIGIN")
                .equals(sireCountry)
                .lean();

              let sireId = 0;
              let reason = "";

              if (sireData.length == 1) {
                console.log("maySire sex", sireData[0].HOR_SEX);
                console.log("maySire foaldate", sireData[0].HOR_FOALING_DATE);
                if (
                  !["M", "F", "G"].includes(sireData[0].HOR_SEX) &&
                  moment(foalDate).diff(
                    moment(sireData[0].HOR_FOALING_DATE),
                    "year"
                  ) < 25 &&
                  moment(foalDate).diff(
                    moment(sireData[0].HOR_FOALING_DATE),
                    "year"
                  ) > 3
                ) {
                  sireId = sireData[0].HRN_HORSE_ID;
                } else {
                  if (!["M", "F", "G"].includes(sireData[0].HOR_SEX))
                    reason =
                      reason +
                      ` Sire ${sireData[0].HRN_HORSE_ID} may not have the right gender.`;
                  if (
                    moment(foalDate).diff(
                      moment(sireData[0].HOR_FOALING_DATE),
                      "year"
                    ) < 25 &&
                    moment(foalDate).diff(
                      moment(sireData[0].HOR_FOALING_DATE),
                      "year"
                    ) > 3
                  ) {
                    reason =
                      reason +
                      ` Sire ${sireData[0].HRN_HORSE_ID} does not appear to be the correct age.`;
                  }
                }
              } else if (sireData.length > 1) {
                let potentialSires = [];
                for (const maySire of sireData) {
                  console.log("maySire sex", maySire.HOR_SEX);
                  if (["M", "F", "G"].includes(maySire.HOR_SEX)) continue;
                  console.log("maySire foaldate", maySire.HOR_FOALING_DATE);
                  console.log(
                    "maySire diff",
                    moment(foalDate).diff(
                      moment(maySire.HOR_FOALING_DATE),
                      "year"
                    )
                  );
                  if (
                    moment(foalDate).diff(
                      moment(maySire.HOR_FOALING_DATE),
                      "year"
                    ) > 25
                  )
                    continue;
                  if (
                    moment(foalDate).diff(
                      moment(maySire.HOR_FOALING_DATE),
                      "year"
                    ) < 3
                  )
                    continue;
                  potentialSires.push(maySire);
                }
                if (potentialSires.length > 1) {
                  let narrowedSires = [];
                  for (const maySire of potentialSires) {
                    let progCount = await centaur.horses
                      .find({ HOR_SIRE_ID: maySire.HRN_HORSE_ID })
                      .select("HOR_FOALING_DATE")
                      .lean();
                    if (progCount.length > 0) narrowedSires.push(maySire);
                  }
                  if (narrowedSires.length === 1) {
                    sireId = narrowedSires[0].HRN_HORSE_ID;
                  }
                  reason =
                    "Multiple matching sires in age range (3-25) with correct name, gender and country.";
                } else if (potentialSires.length === 1) {
                  sireId = potentialSires[0].HRN_HORSE_ID;
                } else {
                  reason =
                    "No sire in age range (3-25) matches correct name, gender and country.";
                }
              } else {
                reason = "No sire matches correct name and country, at all.";
              }
              var foalingDate = helper.sDate(rawData.Horse["@_FoalDate"]);
              var currDate = new Date();

              var payload = {};
              payload.HRN_HORSE_NAME = helper.upperCase(
                rawData.Horse["@_HorseName"]
              );
              payload.HRN_DISPLAY_NAME = rawData.Horse["@_HorseName"];
              payload.HOR_FOALING_DATE = helper.sDate(
                rawData.Horse["@_FoalDate"]
              );
              payload.HOR_SIRE_ID = sireId;
              payload.HOR_DAM_ID = damId;
              payload.HOR_SEX = rawData.Horse["@_Sex"];
              payload.HOR_COLOUR = Number(
                helper.getHorseColorCode(rawData.Horse["@_Colour"])
              );
              payload.HOR_COUNTRY_OF_NAME = "AUS";
              payload.HOR_COUNTRY_OF_ORIGIN = "AUS";
              if (rawData.Horse["@_Country"]) {
                payload.HOR_COUNTRY_OF_ORIGIN = rawData.Horse["@_Country"];
              }

              var horseStatus = "";
              if (rawData.Horse["@_HorseStatus"] != "") {
                if (rawData.Horse["@_HorseStatus"] == "Retired") {
                  horseStatus = "R";
                  showHorseInRego = false;
                }
                if (rawData.Horse["@_HorseStatus"] == "Dead") {
                  horseStatus = "D";
                  showHorseInRego = false;
                }
                if (horseStatus == "R" || horseStatus == "D") {
                  payload.HRN_ROW_STATUS = horseStatus;
                }
              }

              if (rawData.Trainer.Location != "") {
                payload.HOR_TRAINING_LOCATION = helper.upperCase(
                  rawData.Trainer.Location
                );
              }

              var blinkers = "";
              if (rawData.Blinkers != "") {
                if (rawData.Blinkers == true) {
                  blinkers = "Y";
                } else {
                  blinkers = "";
                }
              }
              payload.HOR_CURRENT_BLINKER_IND = blinkers;

              if (rawData.RacebookOwnersName != "") {
                payload.HOR_OWNER_NAMES = helper.upperCase(
                  rawData.RacebookOwnersName
                );
              }

              if (rawData.RacingColours != "") {
                payload.HOR_RACING_COLOURS = helper.upperCase(
                  rawData.RacingColours
                );
              }

              if (
                rawData.Trainer["@_TrainerCode"] != "" &&
                rawData.Trainer["@_TrainerCode"] != "0" &&
                rawData.Trainer["@_TrainerCode"] != 0
              ) {
                var trainerData = await centaur.trainers
                  .findOne({ RISATrainerID: rawData.Trainer["@_TrainerCode"] })
                  .lean();
                if (trainerData) {
                  payload.HOR_TRAINER_ID = trainerData.TRN_TRAINER_ID;
                } else {
                  trainerData = await centaur.trainers
                    .findOne({
                      RISAPartnerID: rawData.Trainer["@_TrainerCode"],
                    })
                    .lean();
                  if (trainerData) {
                    payload.HOR_TRAINER_ID = trainerData.TRN_TRAINER_ID;
                  } else {
                    console.log(
                      `trainer missing from rego ${rawData.Trainer.Name}`
                    );
                    var sendMail = await helper.mailAlert(
                      `REGO error: trainer missing ${rawData.Trainer.Name}`,
                      `trainer is missing: ${JSON.stringify(
                        rawData.Trainer
                      )} for horse ${payload.HRN_HORSE_NAME} ${
                        payload.HRN_HORSE_ID
                      }`,
                      "task"
                    );
                    console.log(sendMail);
                  }
                }
              } else {
                payload.HOR_TRAINER_ID = 0;
              }
              if (showHorseInRego) {
                var createNewHorse = await horseCreate(payload);
                if (createNewHorse) {
                  if (
                    sireId != 0 &&
                    currDate.getFullYear() - foalingDate.getFullYear() <= 4 &&
                    (payload.HOR_COUNTRY_OF_ORIGIN == "NZ" ||
                      payload.HOR_COUNTRY_OF_ORIGIN == "AUS") &&
                    moment(foalDate).month() > 6
                  ) {
                    if (rawData && rawData != "") {
                      var new_newHorses =
                        await centaur.registration_files.updateOne(
                          { _id: fileId },
                          { $push: { newHorses: rawData } }
                        );
                      if (new_newHorses) {
                        console.log(`NewHorses Updated in DB - ${horse_log}`);
                      }
                    }
                  } else {
                    if (rawData && rawData != "") {
                      rawData.report = "";
                      if (sireId == 0) {
                        rawData.report = rawData.report + reason;
                      }
                      if (
                        currDate.getFullYear() - foalingDate.getFullYear() >
                        4
                      ) {
                        rawData.report = rawData.report + "Older than 4yo. ";
                      }
                      if (
                        payload.HOR_COUNTRY_OF_ORIGIN != "NZ" &&
                        payload.HOR_COUNTRY_OF_ORIGIN != "AUS"
                      ) {
                        rawData.report = rawData.report + "Foreign horse. ";
                      }
                      if (moment(foalDate).month() < 7) {
                        rawData.report =
                          rawData.report + "Horse born before August.";
                      }
                      var new_newHorsesNoSire =
                        await centaur.registration_files.updateOne(
                          { _id: fileId },
                          { $push: { alertHorses: rawData } }
                        );
                      if (new_newHorsesNoSire) {
                        // console.log(`NewHorses Updated in DB - ${horse_log}`)
                      }
                    }
                  }
                }
              } else {
                rawData.report =
                  "Horse was New, and also dead or retired. Not sure we need it?";
                var update_notFound =
                  await centaur.registration_files.updateOne(
                    { _id: fileId },
                    { $push: { notFound: rawData } }
                  );
                if (update_notFound) {
                  // console.log(`NotFound Updated in DB - ${horse_log}`)
                }
              }
            }
          }
        }
      }
    }
  } else {
    if (rawData && rawData != "") {
      rawData.report = "Dam Not Found, " + theDam.reason;
      var update_notFound = await centaur.registration_files.updateOne(
        { _id: fileId },
        { $push: { notFound: rawData } }
      );
      if (update_notFound) {
        // console.log(`NotFound Updated in DB - ${horse_log}`)
      }
    }
  }
};

const damCheck = async (
  fileId,
  horseName,
  damName,
  damCountry,
  damSireName,
  damSireCountry
) => {
  let reason = "";
  horseName = horseName.replace(".", "");
  damName = damName.replace(".", "");
  damSireName = damSireName.replace(".", "");
  damSireName = damSireName.replace(".", "");
  let damData = await centaur.horses
    .find()
    .where("HRN_HORSE_NAME")
    .equals(damName)
    .where("HOR_COUNTRY_OF_ORIGIN")
    .equals(damCountry)
    .where("HOR_SEX")
    .equals("M")
    .lean();
  const damSireData = await centaur.horses
    .find()
    .where("HRN_HORSE_NAME")
    .equals(damSireName)
    .where("HOR_COUNTRY_OF_ORIGIN")
    .equals(damSireCountry)
    .where("HOR_SEX")
    .equals("H")
    .select("HRN_HORSE_ID")
    .lean();

  if (!damData || damData.length == 0) {
    damData = await centaur.horses
      .find({ HRN_PREV_NAMES: damName })
      .where("HOR_COUNTRY_OF_ORIGIN")
      .equals(damCountry)
      .where("HOR_SEX")
      .equals("M")
      .lean();
    // console.log(damData)
  }
  if (!damData || damData.length == 0) {
    const badGender = await centaur.horses
      .find()
      .where("HRN_HORSE_NAME")
      .equals(damName)
      .where("HOR_COUNTRY_OF_ORIGIN")
      .equals(damCountry)
      .lean();
    if (badGender.length === 1) {
      reason = `Dam ${badGender[0].HRN_HORSE_ID} appears to have incorrect or undefined gender`;
    } else if (badGender.length > 1) {
      reason =
        "multiple dams with name/prev_name and country exist, but incorrect gender";
    } else {
      reason = "no Dam with matching name/prev_name and country exists";
    }
  } else {
    if (damData.length > 1) {
      var filteredDams = [];
      for (const dam of damData) {
        for (const damSire of damSireData) {
          if (damSire.HRN_HORSE_ID == dam.HOR_SIRE_ID) {
            filteredDams.push(dam);
          }
        }
      }
      if (filteredDams.length == 1) return filteredDams[0];
      reason = "because multiple dams matched";
    } else if (damData[0]) {
      for (const damSire of damSireData) {
        if (damData[0].HOR_SIRE_ID == damSire.HRN_HORSE_ID) {
          return damData[0];
        }
      }
      reason = `Sire of dam ${damData[0].HRN_HORSE_ID} didnt match`;
    } else {
      console.log("we really shouldnt be able to get here, wtaf");
      console.log(damData);
      // var newDam = {};
      // newDam.HRN_HORSE_NAME = damName;
      // newDam.HOR_COUNTRY_OF_ORIGIN = damCountry;
      // if (damSireData.length == 1) {
      //   newDam.HOR_SIRE_ID = damSireData[0].HRN_HORSE_ID;
      // }
      // var createNewDam = await horseCreate(newDam);
      // if (createNewDam) {
      // var newDamReport = {
      //   Horse: {
      //     "@_FoalDate": "",
      //     "@_NamePrev": "",
      //     "@_LifeNumber": "",
      //     "@_Age": "",
      //     "@_Sex": "",
      //     "@_Colour": "",
      //     "@_HorseStatus": "",
      //     "@_HorseCode": "",
      //     "@_HorseName": damName,
      //     "@_Country": damCountry,
      //   },
      //   BreedersName: "",
      //   MicrochipNumber: 0,
      //   Trainer: {
      //     Name: "",
      //     Location: "",
      //     State: "",
      //     PostCode: 0,
      //     Title: "",
      //     PreferredName: "",
      //     Surname: "",
      //     Initials: "",
      //     "@_TrainerCode": "",
      //   },
      //   Blinkers: false,
      //   RacebookOwnersName: "",
      //   RacingColours: "",
      //   StableReturnDate: "",
      //   Breeding: {
      //     Sire: {
      //       "@_HorseCode": "",
      //       "@_HorseName": damSireName,
      //       "@_HorseCountryAbbr": damSireCountry,
      //     },
      //     Dam: {
      //       "@_HorseCode": "",
      //       "@_HorseName": "",
      //     },
      //     GrandSire: {
      //       "@_HorseCode": "",
      //       "@_HorseName": "",
      //     },
      //   },
      //   report: `Dam NEEDS creation or verification: ${damName} (by ${damSireName}) for: ${horseName}`,
      // };

      // var new_newHorses = await centaur.registration_files.updateOne(
      //   { _id: fileId },
      //   { $push: { notFound: newDamReport } }
      // );
      // }
    }
  }
  return {
    result: false,
    reason: reason,
  };
};

const horseCreate = async (payload) => {
  let latest_horse = await centaur.horses
    .findOne()
    .sort({ HRN_HORSE_ID: -1 })
    .limit(1);
  var horseId = parseInt(latest_horse.HRN_HORSE_ID) + 1;
  var id = uuid.v4();
  var dataToCreate = {
    _id: id,
    HRN_HORSE_ID: horseId,
    HRN_COUNTRY_OF_NAME: payload.HRN_COUNTRY_OF_NAME
      ? helper.upperCase(payload.HRN_COUNTRY_OF_NAME)
      : "",
    HRN_HORSE_NAME: payload.HRN_HORSE_NAME
      ? helper.upperCase(payload.HRN_HORSE_NAME)
      : "",
    HRN_DISPLAY_NAME: payload.HRN_DISPLAY_NAME ?? "",
    HOR_HORSE_DB_ID: horseId,
    HOR_FOALING_DATE: payload.HOR_FOALING_DATE ? payload.HOR_FOALING_DATE : "",
    HOR_COUNTRY_OF_ORIGIN: payload.HOR_COUNTRY_OF_ORIGIN
      ? helper.upperCase(payload.HOR_COUNTRY_OF_ORIGIN)
      : "",
    HOR_SEX: payload.HOR_SEX ? payload.HOR_SEX : "",
    HOR_SIRE_ID: parseInt(payload.HOR_SIRE_ID ? payload.HOR_SIRE_ID : 0),
    HOR_DAM_ID: parseInt(payload.HOR_DAM_ID ? payload.HOR_DAM_ID : 0),
    HOR_TRAINER_ID: parseInt(
      payload.HOR_TRAINER_ID ? payload.HOR_TRAINER_ID : 0
    ),
    HOR_TRAINING_LOCATION: payload.HOR_TRAINING_LOCATION
      ? payload.HOR_TRAINING_LOCATION
      : "",
    HOR_OWNER_NAMES: payload.HOR_OWNER_NAMES ? payload.HOR_OWNER_NAMES : "",
    HOR_RACING_COLOURS: payload.HOR_RACING_COLOURS
      ? payload.HOR_RACING_COLOURS
      : "",
    HOR_COLOUR: payload.HOR_COLOUR ? payload.HOR_COLOUR : "",
    HOR_ROW_STATUS: payload.HOR_ROW_STATUS ? payload.HOR_ROW_STATUS : "",
    HOR_START_TOTAL: 0,
    HOR_H_1STS_TOTAL: 0,
    HOR_H_2NDS_TOTAL: 0,
    HOR_H_3RDS_TOTAL: 0,
    HOR_H_4THS_TOTAL: 0,
    HOR_H_5THS_TOTAL: 0,
    HOR_TOTAL_PRIZEMONEY: 0,
    HOR_CURRENT_BLINKER_IND: "",
    HOR_BOBS_STATE: 0,
    HOR_WET_START_TOTAL: 0,
    HOR_WET_1ST_TOTAL: 0,
    HOR_WET_2ND_TOTAL: 0,
    HOR_WET_3RD_TOTAL: 0,
    HOR_DRY_START_TOTAL: 0,
    HOR_DRY_1ST_TOTAL: 0,
    HOR_DRY_2ND_TOTAL: 0,
    HOR_DRY_3RD_TOTAL: 0,
    HRN_PREV_NAMES: [],
    HOR_RATINGS: {},
  };
  let newRecord = new centaur.horses(dataToCreate);
  var status = await newRecord.save();
  var formId = uuid.v4();
  var formEntry = {
    _id: formId,
    horse_id: horseId,
    horse_name: payload.HRN_HORSE_NAME
      ? helper.upperCase(payload.HRN_HORSE_NAME)
      : "",
  };
  let newForm = new centaur.form(formEntry);
  var formStatus = await newForm.save();

  // console.log("New Horse Created from Registration file: " + status)
  if (status) {
    return true;
  } else {
    return false;
  }
};

/**
 * Processes horse racing data from a given file content, updating database records for meetings and horses based on the ratings provided.
 * Handles different scenarios based on the content structure and updates various entities in the database accordingly.
 *
 * @param {Object} ratFileContent An object containing horse and meeting data, which may include arrays of horses and meetings with their respective details and ratings.
 * @returns {boolean} Returns true if all operations succeed without errors, otherwise returns false.
 */
const processRatings = async (ratFileContent) => {
  console.log(`processRatings`);
  console.log(ratFileContent);
  let meetingNo = 0;
  try {
    if (ratFileContent.horses) {
      await processFormRatings(ratFileContent);
      return true;
    }

    for (const meeting of ratFileContent.meetings) {
      const event_id_meeting = meeting.races[0].raceId;
      const meeting_id = await centaur.form_index
        .findOne({ event_id: event_id_meeting })
        .select("meeting_id")
        .lean();
      const processed_meeting = await centaur.processed_meetings
        .findOne({ _id: meeting_id.meeting_id })
        .lean();
      let used_ids = [];
      let meetingDataToUpdate = {};
      let race_index = 0;
      for (const race of meeting.races) {
        if (used_ids.includes(race.raceId)) {
          console.log("skipped duplicate race!");
          continue;
        }
        used_ids.push(race.raceId);
        let dbRace =
          processed_meeting.processedMeetingData.meeting.races.race.find(
            (db_race) => db_race["@_id"] === race.raceId
          );

        if (!dbRace) {
          dbRace =
            processed_meeting.processedMeetingData.meeting.races.race.find(
              (db_race) => db_race["@_id"] === race.raceId.toString()
            );
        }
        // if (dbRace.locked) {
        //   console.log(`Race ${race.raceId} is locked, skipping.`);
        //   continue;
        // }
        // console.log('file:',meeting.date,meeting.venue,race.raceNumber)
        // console.log('db:',dbRace.track['@_name'],dbRace['@_number'])
        for (const horse of race.runners) {
          const dbHorse = dbRace.horses.horse.find(
            (db_horse) => db_horse["@_id"] === horse.horseId
          );
          meetingDataToUpdate[
            `processedMeetingData.meeting.races.race.${race_index}.horses.horse.${dbRace.horses.horse.indexOf(
              dbHorse
            )}.rating_result`
          ] = {
            "@_unadjusted": horse.rating,
            "@_adjusted": horse.rating,
            "@_handicap": dbHorse.handicap_rating ?? 0,
            "@_post_handicap": dbHorse.rating_post_handicap ?? 0,
          };

          const masterRatings = {
            HOR_RATINGS: {
              mr1: horse.mr1,
              mr1_d: horse.mr1_d,
              mr2: horse.mr2,
              mr2_d: horse.mr2_d,
              mr3: horse.mr3,
              mr3_d: horse.mr3_d,
              mr4: horse.mr4,
              mr4_d: horse.mr4_d,
              mrj: horse.mrj,
            },
          };
          await centaur.horses
            .updateOne({ HRN_HORSE_ID: horse.horseId }, { $set: masterRatings })
            .lean();
        }
        race_index++;
      }
      const checkIfTemp = await centaur.temp_meetings
        .findOne({ _id: meeting_id.meeting_id })
        .lean();
      if (checkIfTemp) {
        console.log("ratings failed", meeting.date, meeting.venue);
        await helper.mailAlert(
          `RATINGS FOR MEETING FAILED`,
          `temp meeting already exists for: ${meeting_id.meeting_id}`,
          "alert"
        );
      } else {
        const addrego = {
          time: moment().toISOString(),
          bucket: "",
          file_path: "RATINGS",
          trigger: "TRB FILE",
        };
        processed_meeting.meetingLoadHistory.unshift(addrego);
        let updatePM = await centaur.processed_meetings
          .updateOne(
            { _id: meeting_id.meeting_id },
            {
              $set: {
                meetingLoadHistory: processed_meeting.meetingLoadHistory,
              },
            }
          )
          .lean();
        console.log(updatePM);
        const updateRecord = new centaur.temp_meetings(processed_meeting);
        let newRecord = await updateRecord.save();
        try {
          let updateTemp = await centaur.temp_meetings
            .updateOne(
              { _id: meeting_id.meeting_id },
              { $set: meetingDataToUpdate }
            )
            .lean();
          console.log(updateTemp);
          let tc = await triggerCompare(
            meeting_id.meeting_id,
            "RESULTS",
            true,
            0
          );
          console.log(tc);
        } catch (err) {
          const errorMessage = `Error loading ratings file for meeting "${meeting.venue}" on ${meeting.date}: ${err}`;
          const sendMail = await helper.mailAlert(
            `Ratings Failed for meeting ${meeting.venue} ${meeting.date}`,
            errorMessage,
            "prod"
          );
          console.log(sendMail);
        }
      }
      await sleep(2000);
      meetingNo++;
    }
  } catch (err) {
    console.log("((ERROR)): Error loading ratings file: ", err);
    const meetingDate = ratFileContent.meetings[meetingNo].date;
    const meetingName =
      ratFileContent.meetings[meetingNo].venue || "Unknown Meeting";
    const errorMessage = `Error loading ratings file for meeting "${meetingName}" on ${meetingDate}: ${err}`;
    const sendMail = await helper.mailAlert(
      "Ratings Error",
      errorMessage,
      "prod"
    );
    console.log(sendMail);

    return false;
  }
  return true;
};
function formatDate(dateString) {
  const dateParts = dateString.split("/");
  const day = parseInt(dateParts[0], 10);
  const month = parseInt(dateParts[1], 10) - 1;
  const year = parseInt(dateParts[2], 10);

  const date = new Date(year, month, day);

  const suffixes = ["th", "st", "nd", "rd"];
  const dayWithSuffix =
    day + (suffixes[(day - 20) % 10] || suffixes[day] || "th");

  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];
  const monthName = monthNames[date.getMonth()];

  return `${dayWithSuffix} of ${monthName} ${date.getFullYear()}`;
}

// ## THIS IS THE ONE FOR ADDING MISSING FORM ##
const processFormRatings = async (ratFileContent) => {
  const normalize = require("./normalize");

  try {
    console.log("processFormRatings");
    for (const horse in ratFileContent.horses) {
      var horse_id = parseInt(horse);
      var newform = ratFileContent.horses[horse];
      // var horse_form = await centaur.form.findOne({horse_id:horse_id}).lean()

      for (item of newform) {
        var classes = {};
        if (item.classes.class[0].length == 1) {
          classes.class_id = await normalize.getClassByName(item.classes.class);
          classes.class = item.classes.class;
        } else {
          classes.class_id = await normalize.getClassByName(
            item.classes.class[0]
          );
          classes.class = item.classes.class[0];
          if (item.classes.class[1]) {
            classes.second_class_id = await normalize.getClassByName(
              item.classes.class[1]
            );
            classes.second_class = item.classes.class[1];
            if (item.classes.class[2]) {
              classes.third_class_id = await normalize.getClassByName(
                item.classes.class[2]
              );
              classes.third_class = item.classes.class[2];
            }
          }
        }
        item.classes = classes;
        if (item.restrictions && item.restrictions["@_age"]) {
          item.restrictions["@_age"] = helper.setAgeRestriction(
            item.restrictions["@_age"]
          );
        }
        if (item.restrictions && item.restrictions["@_sex"]) {
          item.restrictions["@_sex"] = helper.setSexRestriction(
            item.restrictions["@_sex"]
          );
        }
        if (item.event_id) item.event_id = item.event_id.toString();
        if (item.race && item.race["@_number"])
          item.race["@_number"] = item.race["@_number"].toString();
        if (item.distance && item.distance["@_metres"])
          item.distance["@_metres"] = item.distance["@_metres"].toString();
        if (item.margin && helper.isNumeric(item.margin))
          item.margin = parseFloat(item.margin);
        var or_index = 0;
        for (other_runner in item.other_runners.other_runner) {
          if (
            other_runner["@_position"] &&
            helper.isNumeric(other_runner["@_position"])
          )
            item.other_runners.other_runner[or_index]["@_position"] = parseInt(
              other_runner["@_position"]
            );
          if (
            other_runner["@_weight"] &&
            helper.isNumeric(other_runner["@_weight"])
          )
            item.other_runners.other_runner[or_index]["@_weight"] = parseFloat(
              other_runner["@_weight"]
            );
        }
        if (item.official_margin_1 && helper.isNumeric(item.official_margin_1))
          item.official_margin_1 = item.official_margin_1.toString();
        if (item.official_margin_2 && helper.isNumeric(item.official_margin_2))
          item.official_margin_2 = item.official_margin_2.toString();

        item.meeting_date = moment(
          item.meeting_date,
          "DD/MM/YYYY"
        ).toISOString();
        item.event_id = helper.generateUniqueRaceId(
          item.meeting_date,
          item.track["@_id"],
          item.race["@_number"],
          classes.class_id == 90 ? 1 : 0
        );
        item.method = "PUT";
        var lambda = new AWS.Lambda();
        console.log(item);
        var payload = {
          path: "/horse-form",
          body: item,
        };
        payload.body.horse_id = horse_id;
        console.log(payload);
        var params = {
          FunctionName: "MrCenLambdaApi-" + process.env.ENV,
          InvocationType: "Event",
          Payload: JSON.stringify(payload),
        };
        var res_3 = await lambda.invoke(params).promise();
        console.log(res_3);
        helper.sleep(2000);
      }
    }
  } catch (err) {
    console.log(err);
  }
};

const setFinishPosition = (Id) => {
  var map = new Map([
    ["FF", 25],
    ["failed to finish", 25],
    ["PU", 26],
    ["FL", 27],
    ["RO", 28],
    ["DQ", 29],
    ["NP", 30],
    ["LS", 31],
    ["LR", 32],
    ["SB", 33],
    ["SC", 34],
    ["BD", 35],
    ["UN", 36],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = Id;
  return result;
};

const convertTISgear = (gear) => {
  var eachgear = gear.split(".");
  var gear_changes = [];
  for (change of eachgear) {
    if (change) {
      // console.log(change)
      if (/ OFF FIRST TIME$/.test(change)) {
        var gear_name = helper.cleverCapFstLtr(
          change.replace(/ OFF FIRST TIME$/, "")
        );
        var gear_id = helper.getGearID(gear_name);
        gear_changes.push({
          "@_id": gear_id,
          "@_name": gear_name,
          "@_option": "off first time",
        });
      } else if (/ FIRST TIME$/.test(change)) {
        var gear_name = helper.cleverCapFstLtr(
          change.replace(/ FIRST TIME$/, "")
        );
        var gear_id = helper.getGearID(gear_name);
        gear_changes.push({
          "@_id": gear_id,
          "@_name": gear_name,
          "@_option": "first time",
        });
      } else if (/ BACK ON$/.test(change)) {
        var gear_name = helper.cleverCapFstLtr(change.replace(/ BACK ON$/, ""));
        var gear_id = helper.getGearID(gear_name);
        gear_changes.push({
          "@_id": gear_id,
          "@_name": gear_name,
          "@_option": "again",
        });
      } else if (/ ON AGAIN$/.test(change)) {
        var gear_name = helper.cleverCapFstLtr(
          change.replace(/ ON AGAIN$/, "")
        );
        var gear_id = helper.getGearID(gear_name);
        gear_changes.push({
          "@_id": gear_id,
          "@_name": gear_name,
          "@_option": "again",
        });
      } else if (/ OFF AGAIN$/.test(change)) {
        var gear_name = helper.cleverCapFstLtr(
          change.replace(/ OFF AGAIN$/, "")
        );
        var gear_id = helper.getGearID(gear_name);
        gear_changes.push({
          "@_id": gear_id,
          "@_name": gear_name,
          "@_option": "off again",
        });
      } else if (/ ON$/.test(change)) {
        var gear_name = helper.cleverCapFstLtr(change.replace(/ ON$/, ""));
        var gear_id = helper.getGearID(gear_name);
        gear_changes.push({
          "@_id": gear_id,
          "@_name": gear_name,
          "@_option": "on",
        });
      } else if (/ OFF$/.test(change)) {
        var gear_name = helper.cleverCapFstLtr(change.replace(/ OFF$/, ""));
        var gear_id = helper.getGearID(gear_name);
        gear_changes.push({
          "@_id": gear_id,
          "@_name": gear_name,
          "@_option": "off",
        });
      } else {
        var gear_id = helper.getGearID(helper.cleverCapFstLtr(change));
        gear_changes.push({
          "@_id": gear_id,
          "@_name": helper.cleverCapFstLtr(change),
        });
      }
    }
  }
  // console.log(gear_changes)
  return gear_changes;
};

const sleep = async (miliseconds) => {
  var currentTime = new Date().getTime();

  while (currentTime + miliseconds >= new Date().getTime()) {}
};

const processDeleteFormHorses = async (ratFileContent) => {
  try {
    for (horse of ratFileContent.horsedelete) {
      var horse_form = await centaur.form
        .deleteOne()
        .where("horse_name")
        .equals(horse.horse_name)
        .where("horse_id")
        .equals(horse.horse_id)
        .lean();
    }
  } catch (err) {
    console.log("((ERROR)): Error deleting horses ratings file: ", err);
    return false;
  }
  return true;
};

const calcMultipleClasses = (new_class, classObject, allclasses) => {
  var theObject = {};
  theObject.class_id = classObject.class_id;
  theObject.class = classObject.class;
  var filled = false;
  if (classObject.second_class_id) {
    theObject.second_class_id = classObject.second_class_id;
    theObject.second_class = classObject.second_class;
    if (theObject.second_class_id == new_class.EVC_CLASS_CODE) {
      theObject.second_class =
        allclasses[new_class.EVC_CLASS_CODE.toString()] ?? "";
      filled = true;
    }
  } else if (!filled) {
    theObject.second_class_id = new_class.EVC_CLASS_CODE;
    theObject.second_class =
      allclasses[new_class.EVC_CLASS_CODE.toString()] ?? "";
    filled = true;
  }
  if (classObject.third_class_id) {
    theObject.third_class_id = classObject.third_class_id;
    theObject.third_class = classObject.third_class;
    if (theObject.third_class_id == new_class.EVC_CLASS_CODE) {
      theObject.third_class =
        allclasses[new_class.EVC_CLASS_CODE.toString()] ?? "";
      filled = true;
    }
  } else if (!filled) {
    theObject.third_class_id = new_class.EVC_CLASS_CODE;
    theObject.third_class =
      allclasses[new_class.EVC_CLASS_CODE.toString()] ?? "";
    filled = true;
  }
  if (classObject.fourth_class_id) {
    theObject.fourth_class_id = classObject.fourth_class_id;
    theObject.fourth_class = classObject.fourth_class;
    if (theObject.fourth_class_id == new_class.EVC_CLASS_CODE) {
      theObject.fourth_class =
        allclasses[new_class.EVC_CLASS_CODE.toString()] ?? "";
      filled = true;
    }
  } else if (!filled) {
    theObject.fourth_class_id = new_class.EVC_CLASS_CODE;
    theObject.fourth_class =
      allclasses[new_class.EVC_CLASS_CODE.toString()] ?? "";
    filled = true;
  }
  if (classObject.fifth_class_id) {
    theObject.fifth_class_id = classObject.fifth_class_id;
    theObject.fifth_class = classObject.fifth_class;
    if (theObject.fifth_class_id == new_class.EVC_CLASS_CODE) {
      theObject.fifth_class =
        allclasses[new_class.EVC_CLASS_CODE.toString()] ?? "";
      filled = true;
    }
  } else if (!filled) {
    theObject.fifth_class_id = new_class.EVC_CLASS_CODE;
    theObject.fifth_class =
      allclasses[new_class.EVC_CLASS_CODE.toString()] ?? "";
    filled = true;
  }
  return theObject;
};

const getJockeyStats = async (jockeyId = 0) => {
  try {
    var st = 0;
    var s1 = 0;
    var s2 = 0;
    var s3 = 0;

    const pastDate = new Date(helper.getPastDateByDay(365)); //Calculate past year date from today
    const statsDataTotal = await centaur.form_index
      .find()
      .where("jockey_id")
      .equals(jockeyId)
      .where("meeting_date")
      .gt(pastDate)
      .lean();

    // var cleansedJockeyData = statsDataTotal
    var cleansedJockeyData = helper.cleanseBarrierTrials(statsDataTotal);

    for (race of cleansedJockeyData) {
      st++;
      if (race.finish_pos == 1) {
        s1++;
      } else if (race.finish_pos == 2) {
        s2++;
      } else if (race.finish_pos == 3) {
        s3++;
      }
    }

    const stats = {
      statistic: {
        "@_type": "one_year", //Fix value of 365 days
        "@_total": st.toString(),
        "@_firsts": s1.toString(),
        "@_seconds": s2.toString(),
        "@_thirds": s3.toString(),
      },
    };
    return stats;
  } catch (err) {
    // var sendMail = await helper.mailAlert('Jockey Stats error',`Jockey Stats error ${jockeyId}. ${error}`,'error')
    // console.log(sendMail)
    console.log("((ERROR)): Jockey Stats Error - " + err);
  }
};

const chk = (val) => {
  if (val == null || val == "undefined" || val == "null") return "";
};
module.exports = {
  genMeetingLevelData,
  genRaceLevelData,
  checkMeetingStage,
  processResults,
  processGearChanges,
  processScratchings,
  processNZUpdates,
  processNZResults,
  processRegistrationAU,
  processRatings,
  processSectionals,
  isResultsStage,
};
