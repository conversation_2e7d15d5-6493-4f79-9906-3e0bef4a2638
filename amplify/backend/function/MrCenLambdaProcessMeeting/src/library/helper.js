const util = require("util");
const AWS = require("aws-sdk");
const moment = require("moment");
const centaur = require("@mediality/centaur");
const mongoose = require("mongoose");
mongoose.set("debug", false);

function decodeHTMLEntities(text = "") {
  // Replace numeric entities (decimal)
  text = text.replace(/&#(\d+);/g, function (match, dec) {
    return String.fromCharCode(dec);
  });
  // Replace numeric entities (hexadecimal)
  text = text.replace(/&#x([0-9A-Fa-f]+);/g, function (match, hex) {
    return String.fromCharCode(parseInt(hex, 16));
  });
  // Replace common named entities
  text = text
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'")
    .replace(/&amp;/g, "&")
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&nbsp;/g, " ");
  return text;
}
const inspectObject = async (jsonObject) => {
  console.log(
    util.inspect(jsonObject, { showHidden: false, depth: null, colors: true })
  );
};

const mailAlert = async (subject, body, mail_list = "error") => {
  // console.log('trying to send mail')
  var ses = new AWS.SES({ region: "ap-southeast-2" });
  if (mail_list.toLowerCase() === "prod" && process.env.ENV != "prd") {
    mail_list = "test";
  }
  var level = {
    task: [
      "<EMAIL>",
      "<EMAIL>",
    ],
    error: [
      "<EMAIL>",
      "<EMAIL>",
    ],
    prod: [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
    ],
    alert: [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
    ],
    alarm: [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
    ],
    test: ["<EMAIL>"],
  };
  if (process.env.ENV != "prd") {
    level = {
      task: [
        "<EMAIL>",
        "<EMAIL>",
      ],
      error: [
        "<EMAIL>",
        "<EMAIL>",
      ],
      alert: [
        "<EMAIL>",
        "<EMAIL>",
      ],
      alarm: [
        "<EMAIL>",
        "<EMAIL>",
      ],
      test: ["<EMAIL>"],
    };
  }
  const emailParams = {
    Destination: {
      ToAddresses: level[mail_list],
    },
    Message: {
      Body: {
        Text: { Data: body },
      },
      Subject: { Data: "Pegasus DB " + mail_list.toUpperCase() + " " + subject },
    },
    Source: "<EMAIL>",
  };

  try {
    let key = await ses.sendEmail(emailParams).promise();
    // console.log('mail sent');
  } catch (e) {
    console.log("mail failed", e);
  }
  return "mail process complete";
};

const readS3File = async (fileName, bucketName) => {
  const AWS = require("aws-sdk");
  const s3 = new AWS.S3({ apiVersion: "2006-03-01" });
  var response = "";
  var params_s3 = {
    Bucket: bucketName,
    Key: fileName,
  };
  try {
    let data = await s3.getObject(params_s3).promise();
    response = data.Body.toString("utf-8");
  } catch (err) {
    console.log("S3 Error: " + err);
  }

  return response;
};

const uploadFileS3 = async (bucketName, key, bodyData) => {
  try {
    const AWS = require("aws-sdk");
    const s3 = new AWS.S3({});
    const objectData = JSON.stringify(bodyData, null, 2);
    const params = {
      Bucket: bucketName,
      Key: key,
      Body: objectData,
    };
    const result = await s3.putObject(params).promise();
  } catch (error) {
    console.log("S3 File Upload Error: " + error);
  }
};

const getSecrets = async (secretName) => {
  const AWS = require("aws-sdk");
  var secretsmanager = new AWS.SecretsManager();
  var params = {
    SecretId: secretName,
  };
  const fetchSecretString = await secretsmanager
    .getSecretValue(params)
    .promise();
  aws_secrets = JSON.parse(fetchSecretString.SecretString);
  return aws_secrets;
};

const xmlToJs = async (xmlData) => {
  const { XMLParser, XMLBuilder, XMLValidator } = require("fast-xml-parser");
  const options = {
    ignoreAttributes: false,
    attributeNamePrefix: "@_",
  };
  const parser = new XMLParser(options);
  try {
    let jsonData = parser.parse(xmlData);
    return jsonData;
  } catch (err) {
    console.log(err);
  }
};

const jsonToXml = async (jsonData) => {
  const { XMLParser, XMLBuilder, XMLValidator } = require("fast-xml-parser");
  const options = {
    ignoreAttributes: false,
    suppressEmptyNode: true,
  };
  const builder = new XMLBuilder(options);
  const xmlContent = builder.build(jsonData);
  return xmlContent;
};

const validateXml = async (xmlData) => {
  const { XMLValidator } = require("fast-xml-parser");
  try {
    const result = XMLValidator.validate(xmlData, {
      allowBooleanAttributes: true,
    });
    if (result == true) {
      return true;
    } else {
      // console.log('Invalid XML')
      // console.log(result)
      return false;
    }
  } catch (error) {
    console.log("Error parsing XML: " + error);
    return false;
  }
};

const checkFileTypeCountry = async (jsData) => {
  var result = {
    meetingStage: "",
    meetingDate: "",
    venueAbbr: "",
    trackName: "",
    country: "",
    category: "",
  };

  try {
    //Verify Racing Australia
    if (jsData["Meeting"]) {
      // Check Gear Changes file
      if (checkGearFile(jsData["Meeting"]["XMLSchemaVersion"])) {
        result.meetingStage = "GearChanges";
        result.meetingDate = jsData["Meeting"]["@_MeetDate"];
        result.venueAbbr = jsData["Meeting"]["@_VenueAbbr"];
        result.trackName = jsData["Meeting"]["@_TrackName"];
        result.country = "AUS";
        if (jsData["Meeting"]["@_MeetingCategory"] == "Trial") {
          result.category = "trial";
        }
      } else if (jsData["Meeting"]["MeetingCategory"] == "Trial") {
        result.meetingStage = jsData["Meeting"]["MeetingStage"];
        result.meetingDate = jsData["Meeting"]["MeetDate"];
        result.venueAbbr = jsData["Meeting"]["Track"]["@_VenueAbbr"];
        result.trackName = jsData["Meeting"]["Track"]["@_TrackName"];
        (result.country = "AUS"), (result.category = "trial");
        result.phase = jsData["Meeting"]["MeetingPhase"] ?? "";
      } else {
        result.meetingStage = jsData["Meeting"]["MeetingStage"];
        result.meetingDate = jsData["Meeting"]["MeetDate"];
        result.venueAbbr = jsData["Meeting"]["Track"]["@_VenueAbbr"];
        result.trackName = jsData["Meeting"]["Track"]["@_TrackName"];
        result.country = "AUS";
        result.category = "nonTrial";
      }
    }

    if (
      jsData["MeetingScratchings"] &&
      checkScratchingsFile(jsData["MeetingScratchings"]["XMLSchemaVersion"])
    ) {
      result.meetingStage = "Scratchings";
      result.meetingDate = jsData["MeetingScratchings"]["@_MeetDate"];
      result.venueAbbr = jsData["MeetingScratchings"]["Track"]["@_VenueAbbr"];
      result.trackName = jsData["MeetingScratchings"]["Track"]["@_TrackName"];
      result.country = "AUS";
      if (jsData["MeetingScratchings"]["MeetingCategory"] == "Trial") {
        result.category = "trial";
      }
    }

    if (
      jsData["RiderUpdate"] &&
      checkScratchingsFile(jsData["RiderUpdate"]["XMLSchemaVersion"])
    ) {
      result.meetingStage = "Scratchings";
      result.meetingDate = jsData["RiderUpdate"]["@_MeetDate"];
      result.venueAbbr = jsData["RiderUpdate"]["Track"]["@_VenueAbbr"];
      result.trackName = jsData["RiderUpdate"]["Track"]["@_TrackName"];
      result.country = "AUS";
    }

    // Check Australia Registration File
    if (jsData["Rego"]) {
      if (checkRegistrationFile(jsData["Rego"]["XMLSchemaVersion"])) {
        result.meetingStage = "RegistrationAU";
        result.meetingDate = jsData["Rego"]["GenerationDate"];
        result.venueAbbr = "";
        result.trackName = "";
        result.country = "AUS";
      }
    }
    //verify ratings
    if (jsData["meetings"] || jsData["horses"] || jsData["horsedelete"]) {
      result.meetingStage = "Ratings";
      result.meetingDate = jsData["meetings"]
        ? jsData["meetings"][0]["date"]
        : new Date();
      result.venueAbbr = "";
      result.trackName = "";
      result.country = "AUS";
    }

    //verify csv sectionals
    if (jsData["filetype"] && jsData["filetype"] == "sectional_200") {
      result.meetingStage = "Sectionals";
      result.meetingDate = jsData.meeting_date;
      result.venueAbbr = "";
      result.trackName = jsData.meeting_track;
      result.country = "AUS";
    }

    //Verify NewZealand
    if (jsData["meeting"] && jsData["meeting"]["@_meetingcountry"]) {
      result.meetingStage = "Acceptances";
      result.meetingDate = jsData["meeting"]["@_meetingdate"];
      const centaur = require("@mediality/centaur");
      var nz_track = await centaur.tracks.findOne({
        TRK_ALT_NAME: jsData["meeting"]["@_meetingtrackid"],
      });
      if (nz_track) {
        result.venueAbbr = nz_track.TRK_RSB_TRACK_ABBREV;
        result.trackName = nz_track.TRK_TRACK_NAME;
      } else {
        result.venueAbbr = jsData["meeting"]["@_meetingJetbettrackid"];
        result.trackName = jsData["meeting"]["@_meetingtrackid"];
      }
      result.country = "NZ";
    }

    //Verify Singapore
    if (jsData["RaceCard"]) {
      const sm = jsData.RaceCard["@_meetingname"];
      const st = sm.split(" ");
      result.meetingStage = "Acceptances";
      result.meetingDate = st[2] + "/" + st[3] + "/" + st[4];
      result.venueAbbr = "SINGAPORE";
      result.trackName = "SINGAPORE";
      result.country = "SGP";
    }

    //Verify Hong Kong
    if (jsData["OfficialProgrammeFile"]) {
      result.meetingStage = "Acceptances";
      const ht = jsData.OfficialProgrammeFile["@_raceDate"];
      const hd = ht.split("-");
      result.meetingDate = hd[2] + "/" + hd[1] + "/" + hd[0];
      result.trackName = jsData.OfficialProgrammeFile["@_venue"];
      result.country = "HK";
      result.venueAbbr = jsData.OfficialProgrammeFile.MeetingInfo.Source;
      result.category =
        jsData.OfficialProgrammeFile["@_trial"] == "true"
          ? "trial"
          : "nonTrial";
      const centaur = require("@mediality/centaur");
      const trackData = await centaur.tracks
        .findOne({ TRK_TRACK_NAME: result.trackName })
        .lean();
      if (trackData) {
        result.venueAbbr = trackData.TRK_TRACK_3CHAR_ABBREV;
      }
    }

    return result;
  } catch (error) {
    console.log("Error parsing Meeting stage, Venue, Track and Meeting date");
    console.log(error);
  }
};

const checkHongKongFile = (fileContent) => {
  let count = (fileContent.match(/OfficialProgrammeFile/) || []).length;
  if (count > 0) return true;
  else return false;
};
const fixHongKongFile = (fileContent) => {
  var newContent = "";
  fileContent.split(/\r?\n/).forEach((line) => {
    if (line.startsWith("<!") || line.startsWith("]>")) {
    } else {
      newContent = newContent + line;
    }
  });
  return newContent;
};

const convertDate = (dt) => {
  //  dd/mm/yyyy

  return moment(dt).format("DD/MM/YYYY");
};

const sDate = (dt) => {
  return new Date(dt);
  //return moment(dt).format().toString();
};

const getDayMonth = (dt) => {
  //  ddmm
  return moment(dt).format("YYYYMMDD").toString();
};

const convertToUTC = (dt) => {
  var r = moment(dt).utc().toString();
  return r;
};
const convertToUTC_TISDB = (dt) => {
  var r = moment(dt, "DD/MM/YYYY").utc().toString();
  return r;
};

const processTrackRating = (numericTrackRating) => {
  let result = "";
  switch (numericTrackRating) {
    case "1":
      result = "M1";
      break;
    case "2":
      result = "M2";
      break;
    case "3":
      result = "G3";
      break;
    case "4":
      result = "G4";
      break;
    case "5":
      result = "O5";
      break;
    case "6":
      result = "O6";
      break;
    case "7":
      result = "O7";
      break;
    case "8":
      result = "H8";
      break;
    case "9":
      result = "H9";
      break;
    case "10":
      result = "H10";
      break;
    case "Fast":
      result = "M0";
      break;
    case "Synthetic":
      result = "Y0";
      break;
    case "Good":
      result = "G0";
      break;
    case "Sand":
      result = "G0";
      break;
    case "Dirt":
      result = "G0";
      break;
    case "Wet":
      result = "G0";
      break;
    case "Wet Fast":
      result = "G0";
      break;
    default:
      result = numericTrackRating;
  }
  return result;
};

const processPrizes = (priceData, trophies) => {
  if (priceData.Prizes != "") {
    var prizeArray = [];
    priceData.Prizes.PrizeMoney.forEach((data) => {
      var temp = {
        "@_type": numberSuffix(data["@_Position"]),
        "@_value": parseInt(data["@_Value"]),
      };
      prizeArray.push(temp);
    });
    prizeArray.push({
      "@_type": "total_value",
      "@_value": parseInt(priceData["@_TotalExcludingBonuses"]),
    });

    if (priceData.WelfareFund && parseInt(priceData.WelfareFund) > 0) {
      prizeArray.push({
        "@_type": "welfare_fund",
        "@_value": parseInt(priceData.WelfareFund),
      });
    }
    if (trophies) {
      if (Array.isArray(trophies.Trophy)) {
        var totalTrophyVal = 0;
        for (trophy of trophies.Trophy) {
          // console.log(trophy['@_Value'])
          totalTrophyVal = totalTrophyVal + parseInt(trophy["@_Value"]);
        }
        prizeArray.push({
          "@_type": "trophy_total_value",
          "@_value": parseInt(totalTrophyVal),
        });
      } else {
        if (trophies.Trophy["@_Value"]) {
          prizeArray.push({
            "@_type": "trophy_total_value",
            "@_value": parseInt(trophies.Trophy["@_Value"]),
          });
        }
      }
    }
    return prizeArray;
  } else {
    return [];
  }
};

const processPrizes_NZ = (totalprize, prizeData) => {
  totalprize = totalprize.replace("$", "");
  prizeData = prizeData.replace(/\$/g, "");
  // console.log(`total: ${totalprize}, prizeData: ${prizeData}`)
  prizeData = prizeData.replace(/, [0-9]+th-/g, ", ");
  var arr = prizeData.split(", ");
  var prizeOn = parseInt(arr[arr.length - 1]);
  var prizeArray = [];
  var p = 1;
  var sum = 0;
  // console.log(`total: ${totalprize}, prizeOn: ${prizeOn}, array:`)
  // console.log(arr)
  for (i = 0; i < arr.length; i++) {
    var amount = parseInt(arr[i]);
    sum = sum + amount;
    var place = "";
    if ([1, 21].includes(p)) place = p.toString() + "st";
    else if ([2, 22].includes(p)) place = p.toString() + "nd";
    else if ([3, 23].includes(p)) place = p.toString() + "rd";
    else place = p.toString() + "th";
    var temp = {
      "@_type": place,
      "@_value": amount,
    };
    p++;
    prizeArray.push(temp);
    // console.log(sum)
  }
  while (sum < totalprize) {
    sum = sum + prizeOn;
    if ([1, 21].includes(p)) place = p.toString() + "st";
    else if ([2, 22].includes(p)) place = p.toString() + "nd";
    else if ([3, 23].includes(p)) place = p.toString() + "rd";
    else place = p.toString() + "th";
    var temp = {
      "@_type": place,
      "@_value": prizeOn,
    };
    p++;
    prizeArray.push(temp);
  }
  prizeArray.push({
    "@_type": "total_value",
    "@_value": totalprize,
  });

  return prizeArray;
};

const processPrizes_HK = (priceData) => {
  var prizeArray = [];
  var sum = 0;
  for (i = 0; i < priceData.length; i++) {
    var amount = priceData[i]["#text"];
    sum = sum + parseInt(amount);
    var temp = {
      "@_type": numberSuffix(priceData[i]["@_pos"]),
      "@_value": amount,
    };
    prizeArray.push(temp);
  }
  prizeArray.push({
    "@_type": "total_value",
    "@_value": sum,
  });
  return prizeArray;
};

const extractDamName_NZ = (str) => {
  if (str) {
    let damAndDamSire = str.replace(/\([A-Z]+\)/g, "");
    damAndDamSire = damAndDamSire.replace(/(\(| )by /g, ",");
    damAndDamSire = damAndDamSire.replace(/^.+?\-(.+?) *,(.+?)\)$/g, "$1,$2");
    damAndDamSire = damAndDamSire.replace(".", "");
    var data = damAndDamSire.split(",");
    return data;
  } else {
    return "";
  }
};

const splitNameBySpace = (str) => {
  var t = str.split(" ");
  if (t[0] && t[1] && t[2]) {
    var result = {
      firstName: t[0].trim(),
      lastName: t[1].trim(),
      claim: t[2].replace("(", "").replace(")", ""),
    };
    return result;
  } else if (t[0] && t[1]) {
    var result = {
      firstName: t[0].trim(),
      lastName: t[1].trim(),
    };
    return result;
  } else {
    return str;
  }
};

const numberSuffix = (num) => {
  return moment.localeData().ordinal(num);
};

const getPastDateByDay = (days = 0) => {
  var date = moment();
  var result = date.subtract(days, "day").format("YYYY-MM-DD"); //+ "T00:00:00.000+00.00";
  return result;
};

const capFstLtr = (str = "") => {
  if (!str) return "";
  return str
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

const cleverCapFstLtr = (str = "") => {
  if (!str) return "";
  str = str
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
  str = str
    .split("'")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join("'");
  str = str
    .split(".")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(".");
  str = str
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join("-");
  str = str
    .split("Mc")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join("Mc");
  str = str
    .split("&")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join("&");
  str = str
    .split("(")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join("(");
  str = str.replace(/('S |'S$)/g, "'s ");
  return str.trim();
};

const upperCase = (str = "") => {
  if (!str) return "";
  return str.toUpperCase();
};

const fixDuration = (str = "") => {
  if (!str) return "";
  let t = str.replace(/^0(?:0:0?)?/, "");
  const arr = t.split(".");
  let r = arr[1];
  if (r) {
    r = "." + r.substring(0, 2);
  } else {
    r = "";
  }
  let result = arr[0] + r;
  return result;
};

const split = (str, separator, limit) => {
  str = str.split(separator);
  if (str.length > limit) {
    var ret = str.splice(0, limit);
    ret.push(str.join(separator));
    return ret;
  }
  return str;
};

const cleanseBarrierTrials = (data) => {
  var cleanseddata = [];
  for (const raceEvent of data) {
    // if (raceEvent.weight_carried > 0){
    //     cleanseddata.push(raceEvent)
    // }
    if (
      (raceEvent.classes &&
        raceEvent.classes.class_id &&
        raceEvent.classes.class_id != 90 &&
        (!raceEvent.classes.second_class_id ||
          raceEvent.classes.second_class_id != 90)) ||
      (raceEvent.classes &&
        raceEvent.classes.class &&
        raceEvent.classes.class == "Open" &&
        (!raceEvent.classes.second_class_id ||
          raceEvent.classes.second_class_id != 90))
    ) {
      cleanseddata.push(raceEvent);
    }
  }
  return cleanseddata;
};

const processStarts = (num, data, meetingDate) => {
  try {
    var result = "";
    var spell = 84;
    if (data.length == 0) return result;
    var x = moment(meetingDate);
    var y = moment(data[0].meeting_date);
    var gap = x.diff(y, "days");
    result = gap >= spell ? "x" : "";
    if (data.length == 1) {
      result = convertFP(data[0].finish_position) + result;
    }
    if (data.length >= 2) {
      for (let i = 0; i < num; i++) {
        if (data[i] && data[i + 1]) {
          var a = moment(data[i].meeting_date);
          var b = moment(data[i + 1].meeting_date);
          var gaptwo = a.diff(b, "days");
          result =
            (gaptwo >= spell ? "x" : "") +
            convertFP(data[i].finish_position) +
            result;
        } else if (data[i]) {
          result = convertFP(data[i].finish_position) + result;
        } else {
          continue;
        }
      }
      if (num >= 4) {
        var final_value = result.substring(result.length - num, result.length);
        return final_value;
      }
    }
  } catch (err) {
    console.log("Error Generating process Starts " + err);
    return result;
  }

  return result;
};

const convertFP = (finish) => {
  var fp_map = finish.toString();
  var map = new Map([
    ["25", "0"],
    ["26", "0"],
    ["27", "F"],
    ["28", "0"],
    ["29", "Q"],
    ["30", "0"],
    ["31", "0"],
    ["32", "L"],
    ["33", "0"],
    ["34", "0"],
    ["35", "0"],
    ["36", "0"],
    ["FF", "0"],
    ["PU", "0"],
    ["FL", "F"],
    ["RO", "0"],
    ["DQ", "Q"],
    ["NP", "0"],
    ["LS", "0"],
    ["LR", "L"],
    ["SB", "0"],
    ["SC", "0"],
    ["BD", "0"],
    ["UN", "0"],
    ["failed to finish", "0"],
  ]);
  let result = map.get(fp_map);
  if (result == undefined) {
    if (finish > 9) {
      result = "0";
    } else {
      result = fp_map;
    }
  }

  return result;
};

const calculatePercentage = (position, trackRating, data) => {
  var starts = 0;
  var targets = 0;
  for (form of data) {
    starts++;
    if (
      form.track["@_grading"] in trackRating &&
      form.finish_position in position
    ) {
      targets++;
    }
  }
  if (targets == 0) {
    return 0;
  } else {
    return Math.round((targets / starts) * 100);
  }
};

const getPace = (formItems, distance, starts) => {
  var startsSplit = starts.split("");
  var inRunningCount = 0;
  var inRunningArray = [];
  // console.log('paceStarts')
  // console.log(startsSplit)
  for (var i = 0; i < startsSplit.length; i++) {
    var numberInRunning = 0;
    var sumInRunning = 0;
    if (startsSplit[i] == "x") {
      continue;
    } else if (startsSplit[i + 1] && startsSplit[i + 1] == "x") {
      continue;
    } else if (!startsSplit[i + 1]) {
      break;
    } else if (inRunningCount > 7) {
      break;
    }
    if (formItems[i]) {
      // console.log(formItems[i].distance)
      // console.log(formItems[i].positions)
      if (
        formItems[i].distance["@_metres"] < distance - 400 ||
        formItems[i].distance["@_metres"] > distance + 200
      ) {
        continue;
      }
      if (formItems[i].positions["@_m400"] > 0) {
        numberInRunning++;
        sumInRunning =
          sumInRunning + parseInt(formItems[i].positions["@_m400"]);
      }
      if (formItems[i].positions["@_m800"] > 0) {
        numberInRunning++;
        sumInRunning =
          sumInRunning + parseInt(formItems[i].positions["@_m800"]);
      }
      if (formItems[i].positions["@_m1200"] > 0) {
        numberInRunning++;
        sumInRunning =
          sumInRunning + parseInt(formItems[i].positions["@_m1200"]);
      }
      if (formItems[i].positions["@_settling_down"] > 0) {
        numberInRunning++;
        sumInRunning =
          sumInRunning + parseInt(formItems[i].positions["@_settling_down"]);
      }
      if (numberInRunning > 0) {
        inRunningCount++;
        // console.log(sumInRunning, 'divided by ', numberInRunning, 'equals', (sumInRunning / numberInRunning))
        inRunningArray.push(sumInRunning / numberInRunning);
      }
    }
  }
  // console.log(inRunningArray)
  var finalFigure = 0;
  if (inRunningArray.length > 0) {
    for (inRun of inRunningArray) {
      finalFigure = finalFigure + inRun;
    }
    finalFigure = finalFigure / inRunningArray.length;
  }
  // console.log(finalFigure)
  var paceRank = [0, ""];
  if (finalFigure > 10.05) {
    paceRank = [6, "Rear"];
  } else if (finalFigure > 7.15) {
    paceRank = [5, "Mid"];
  } else if (finalFigure > 6.05) {
    paceRank = [4, "Handy/Mid"];
  } else if (finalFigure > 3.95) {
    paceRank = [3, "Handy"];
  } else if (finalFigure > 2.55) {
    paceRank = [2, "Lead/Handy"];
  } else if (finalFigure > 0) {
    paceRank = [1, "Lead"];
  } else {
    paceRank = [7, "?"];
  }

  return paceRank;
};

const getHorseColor = (colorCode) => {
  var result = 0;
  switch (colorCode) {
    case 1:
      // "CHESTNUT"
      result = "ch";
      break;
    case 2:
      // "BAY"
      result = "b";
      break;
    case 3:
      // "BROWN"
      result = "br";
      break;
    case 4:
      // "BLACK"
      result = "bl";
      break;
    case 5:
      // "GREY"
      result = "gr";
      break;
    case 9:
      // "WHITE"
      result = "wh";
      break;
    case 14:
      // "BAY OR BROWN"
      result = "b/br";
      break;
    case 15:
      // "BROWN OR BLACK"
      result = "br/bl";
      break;
    case 16:
      // "GREY-CHESTNUT"
      result = "gr/ch";
      break;
    case 17:
      // "GREY-BAY"
      result = "gr/b";
      break;
    case 18:
      // "GREY-BROWN"
      result = "gr/br";
      break;
    case 19:
      // "GREY-BLACK"
      result = "gr/bl";
      break;
    case 20:
      // "GREY-ROAN"
      result = "gr/ro";
      break;
    case 21:
      // "ROAN"
      result = "ro";
      break;
    default:
      result = "";
  }
  return result;
};

const getHorseColorCode = (color) => {
  var result = "";
  switch (color) {
    case "CHESTNUT":
      result = 1;
      break;
    case "BAY":
      result = 2;
      break;
    case "BROWN":
      result = 3;
      break;
    case "BLACK":
      result = 4;
      break;
    case "GREY":
      result = 5;
      break;
    case "WHITE":
      result = 9;
      break;
    case "BAY OR BROWN":
      result = 14;
      break;
    case "BROWN OR BLACK":
      result = 15;
      break;
    case "GREY-CHESTNUT":
      result = 16;
      break;
    case "GREY-BAY":
      result = 17;
      break;
    case "GREY-BROWN":
      result = 18;
      break;
    case "GREY-BLACK":
      result = 19;
      break;
    case "GREY-ROAN":
      result = 20;
      break;
    case "ROAN":
      result = 21;
      break;
    default:
      result = 0;
  }
  return result;
};

const getHorseColorAbbr = (color) => {
  var result = "";
  switch (color) {
    case "CHESTNUT":
      result = "ch";
      break;
    case "BAY":
      result = "b";
      break;
    case "BROWN":
      result = "br";
      break;
    case "BLACK":
      result = "bl";
      break;
    case "GREY":
      result = "gr";
      break;
    case "WHITE":
      result = "wh";
      break;
    case "BAY OR BROWN":
      result = "b/br";
      break;
    case "BROWN OR BLACK":
      result = "br/bl";
      break;
    case "GREY-CHESTNUT":
      result = "gr/ch";
      break;
    case "GREY-BAY":
      result = "gr/b";
      break;
    case "GREY-BROWN":
      result = "gr/br";
      break;
    case "GREY-BLACK":
      result = "gr/bl";
      break;
    case "GREY-ROAN":
      result = "gr/ro";
      break;
    case "ROAN":
      result = "ro";
      break;
    default:
      result = "b";
  }
  return result;
};

const getTrackCondition = (trackCondition = "") => {
  return trackCondition.charAt(0);
};

const getTrackGrading = (trackCondition = "1") => {
  //return last character
  return trackCondition.charAt(trackCondition.length - 1);
};

const getGroup = (groupCode) => {
  //EVT_GROUP_NO - 1-1,2-2,3-3,4-LR
  var result = "";
  switch (groupCode) {
    case "0":
      result = "0";
      break;
    case "1":
      result = "1";
      break;
    case "2":
      result = "2";
      break;
    case "3":
      result = "3";
      break;
    case "4":
      result = "LR";
      break;
    default:
      result = "Not Found";
  }
  return result;
};

const getAgeRestriction = (Id) => {
  var map = new Map([
    ["2", "2yo"],
    ["2U", "2yo+"],
    ["24", "2,3,4yo"],
    ["3", "3yo"],
    ["3U", "3yo+"],
    ["4", "4yo"],
    ["4U", "4yo+"],
    ["5", "5yo"],
    ["5U", "5yo+"],
    ["6", "6yo"],
    ["A", "Aged"],
    ["23", "2,3yo"],
    ["34", "3,4yo"],
    ["35", "3,4,5yo"],
    ["45", "4,5yo"],
    ["46", "4,5,6yo"],
    ["36", "3,4,5,6"],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = "";
  return result;
};

const getDurationMinutes = (duration) => {
  var minutes = duration / 3600;
  return minutes.toFixed(2);
};

const getHorseAge = (foalingDate, meetingDate) => {
  var a = moment(foalingDate);
  if (a.month() < 6) {
    a.subtract(1, "years");
  }
  a.set({ month: 0, date: 1 });
  var b = moment(meetingDate);
  if (b.get("month") < 7) {
    b.subtract(1, "years");
  }
  b.set({ month: 0, date: 1 });
  result = b.diff(a, "years");
  return result;
};

const getOffMargin = (Id) => {
  var map = new Map([
    ["DH", "DH"],
    ["NS", "Ns"],
    ["SHH", "Sh1/2Hd"],
    ["HH", "Hf Hd"],
    ["SH", "Sh Hd"],
    ["H", "Hd"],
    ["LH", "Lg Hd"],
    ["HN", "Hf Nk"],
    ["SN", "Sh Nk"],
    ["NK", "Nk"],
    ["LN", "Lg Nk"],
    ["003", "1/4"],
    ["005", "1/2"],
    ["008", "3/4"],
    ["DS", "Dist"],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = "Not Found";
  return result;
};

const marginSelect = (margin) => {
  var length = "";
  switch (margin) {
    case "Dead Heat":
      length = "DH";
      break;
    case "Nose":
      length = "NS";
      break;
    case "Short Half Head":
      length = "SHH";
      break;
    case "Short 1/2 Head":
      length = "SHH";
      break;
    case "Half Head":
      length = "HH";
      break;
    case "1/2 Head":
      length = "HH";
      break;
    case "Short Head":
      length = "SH";
      break;
    case "H":
      length = "HD";
      break;
    case "Head":
      length = "HD";
      break;
    case "Long Head":
      length = "LH";
      break;
    case "Half Neck":
      length = "HN";
      break;
    case "1/2 Neck":
      length = "HN";
      break;
    case "Short Neck":
      length = "SN";
      break;
    case "Neck":
      length = "NK";
      break;
    case "Long Neck":
      length = "LN";
      break;
    case "1/4 Len":
      length = 0.3;
      break;
    case "1/2 Len":
      length = 0.5;
      break;
    case "3/4 Len":
      length = 0.8;
      break;
    case "1/4 Length":
      length = 0.3;
      break;
    case "1/2 Length":
      length = 0.5;
      break;
    case "3/4 Length":
      length = 0.8;
      break;
    case "Distanced":
      length = "DS";
      break;
    default:
      var marginSplit = margin.split(" ");
      // console.log(marginSplit)
      if (
        marginSplit.length > 1 &&
        (marginSplit[marginSplit.length - 1] == "Length" ||
          marginSplit[marginSplit.length - 1] == "Len")
      ) {
        var decimalMargin;
        if (marginSplit[1] == "1/4") {
          decimalMargin = 0.3;
        } else if (marginSplit[1] == "1/2") {
          decimalMargin = 0.5;
        } else if (marginSplit[1] == "3/4") {
          decimalMargin = 0.8;
        }
        length = (parseFloat(marginSplit[0]) + decimalMargin).toFixed(1);
      } else {
        length = margin;
      }
      break;
  }
  return length;
};

const getWeightType = (Id) => {
  var map = new Map([
    ["H", "Handicap"],
    ["W", "Weight For Age"],
    ["X", "Weight For Age With Penalties"],
    ["S", "Set Weight"],
    ["T", "Set Weight With Penalties"],
    ["U", "Set Weight With Penalties and Allowances"],
    ["P", "Special Weight"],
    ["C", "Catch Weight"],
    ["F", "Fly Weight"],
    ["A", "Set Weight With Allowances"],
    ["Q", "Quality"],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = "Not Found";
  return result;
};

const openDBConnection = async (env) => {
  const local = require("../local");
  const mongoose = require("mongoose");

  //if ((env == "local") || (env == "abhinav")) {
  if (env == "local") {
    //Local environment

    var cert_path = "./rds-cert/global-bundle.pem";
    console.log("Running Locally");
    DB_USERNAME = local.DB_USERNAME;
    DB_PASSWORD = local.DB_PASSWORD;
    DB_URL = local.DB_URL;
    DB_NAME = local.DB_NAME;
    CONNECTION_STRING =
      "mongodb://" +
      DB_USERNAME +
      ":" +
      DB_PASSWORD +
      "@" +
      DB_URL +
      "/" +
      DB_NAME;
    CONNECTION_STRING =
      "mongodb://root:Test.123!@127.0.0.1:27000/dev-centaur?directConnection=true&ssl=true&retrywrites=false&tls=true";
    await mongoose
      .connect(CONNECTION_STRING, {
        connectTimeoutMS: 1000,
        tlsCAFile: cert_path,
        directConnection: true,
        ssl: true,
        sslValidate: false,
        maxPoolSize: 10,
      })
      .then(
        () => {
          console.log("Connected to Local");
          response = "Connect to Local";
        },
        (err) => {
          console.log("Not connected:" + err);
          response = "Not Connected to Local";
        }
      );
  } else {
    console.log("Running Online");
    var aws_secrets = await getSecrets(process.env.centaurSecrets);
    DB_USERNAME = aws_secrets.DB_USERNAME;
    DB_PASSWORD = aws_secrets.DB_PASSWORD;
    DB_URL = aws_secrets.DB_URL;
    DB_NAME = aws_secrets.DB_NAME;
    CONNECTION_STRING =
      "mongodb://" +
      DB_USERNAME +
      ":" +
      DB_PASSWORD +
      "@" +
      DB_URL +
      "/" +
      DB_NAME +
      "?tls=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false";
    await mongoose
      .connect(CONNECTION_STRING, {
        connectTimeoutMS: 1000,
        tlsCAFile: cert_path,
        directConnection: true,
        ssl: true,
        sslValidate: false,
        maxPoolSize: 10,
      })
      .then(
        () => {
          console.log("Connected to Online");
          response = "Connect Online";
        },
        (err) => {
          console.log("Not connected:" + err);
          response = "Not Connected Online";
        }
      );
  }
  return mongoose;
};

const closeDBConnection = async (con) => {
  con.connection.close();
  console.log("Connection Closed");
};
const getCapitalFirstLetterOnly = (str) => {
  return capFstLtr(str.toString().charAt(0));
};

const checkResultsStage = (stage = "") => {
  let temp = stage.toLowerCase();
  return temp.includes("results");
};

const checkGearFile = (stage = "") => {
  let temp = stage.toLowerCase();
  return temp.includes("gearchanges");
};

const checkScratchingsFile = (stage = "") => {
  let temp = stage.toLowerCase();
  // console.log('checking scratchings',temp.includes('meetingscratchings'))
  var isScratchings = false;
  if (temp.includes("meetingscratchings") || temp.includes("riderupdate")) {
    isScratchings = true;
  }
  return isScratchings;
};

const checkRegistrationFile = (stage = "") => {
  let temp = stage.toLowerCase();
  return temp.includes("registration");
};

const getDualTrack = (track) => {
  return track ? "Y" : "N";
};

const readFile = async (filename) => {
  const fs = require("fs");
  var result = fs.readFileSync(filename).toString("utf8");
  return result;
};

const writeFile = async (filename, data) => {
  const fs = require("fs");
  var result = await fs.writeFile(filename, data, "utf-8", function (err) {
    if (err) {
      return console.log(err);
    }
    console.log("The file was saved!");
  });
  return result;
};

const convertOdds = (num) => {
  var map = new Map([
    [1.05, "1/20"],
    [1.06, "1/16"],
    [1.07, "1/14"],
    [1.08, "1/12"],
    [1.09, "1/11"],
    [1.1, "1/10"],
    [1.12, "1/8"],
    [1.14, "1/7"],
    [1.16, "1/6"],
    [1.18, "2/11"],
    [1.2, "1/5"],
    [1.22, "2/9"],
    [1.24, "1/4"],
    [1.26, "4/15"],
    [1.28, "2/7"],
    [1.3, "4/13"],
    [1.35, "4/11"],
    [1.4, "2/5"],
    [1.45, "4/9"],
    [1.5, "1/2"],
    [1.55, "8/15"],
    [1.6, "4/7"],
    [1.65, "4/6"],
    [1.7, "8/11"],
    [1.75, "8/11"],
    [1.8, "4/5"],
    [1.85, "9/10"],
    [1.9, "9/10"],
    [1.95, "9/10"],
    [2, "1/1"],
    [2.05, "10/9"],
    [2.1, "10/9"],
    [2.15, "10/9"],
    [2.2, "5/4"],
    [2.25, "5/4"],
    [2.3, "5/4"],
    [2.35, "11/8"],
    [2.4, "11/8"],
    [2.45, "6/4"],
    [2.5, "6/4"],
    [2.6, "13/8"],
    [2.7, "7/4"],
    [2.8, "7/4"],
    [2.9, "15/8"],
    [3, "2/1"],
    [3.1, "2/1"],
    [3.2, "9/4"],
    [3.3, "9/4"],
    [3.4, "5/2"],
    [3.5, "5/2"],
    [3.6, "5/2"],
    [3.7, "11/4"],
    [3.8, "11/4"],
    [3.9, "3/1"],
    [4, "3/1"],
    [4.2, "13/4"],
    [4.4, "7/2"],
    [4.6, "7/2"],
    [4.8, "15/4"],
    [5, "4/1"],
    [5.5, "9/2"],
    [6, "5/1"],
    [6.5, "11/2"],
    [7, "6/1"],
    [7.5, "13/2"],
    [8, "7/1"],
    [8.5, "15/2"],
    [9, "8/1"],
    [9.5, "8/1"],
    [10, "9/1"],
    [11, "10/1"],
    [12, "11/1"],
    [13, "12/1"],
    [14, "12/1"],
    [15, "14/1"],
    [16, "15/1"],
    [17, "16/1"],
    [18, "16/1"],
    [19, "20/1"],
    [20, "20/1"],
    [21, "20/1"],
    [26, "25/1"],
    [31, "30/1"],
    [41, "40/1"],
    [51, "50/1"],
    [61, "60/1"],
    [71, "66/1"],
    [81, "80/1"],
    [91, "100/1"],
    [101, "100/1"],
    [151, "150/1"],
    [201, "200/1"],
    [301, "300/1"],
    [401, "400/1"],
    [501, "500/1"],
  ]);
  let result = map.get(num);
  while (result == undefined) {
    if (num < 1.05) num = 1.05;
    else if (num < 1.1) num = Math.round(num * 100) / 100;
    else if (num < 1.3) num = Math.round(num * 50) / 50;
    else if (num < 2.5) num = Math.round(num * 20) / 20;
    else if (num < 4) num = Math.round(num * 10) / 10;
    else if (num < 5) num = Math.round(num * 5) / 5;
    else if (num < 10) num = Math.round(num * 2) / 2;
    else if (num < 21) num = Math.round(num);
    else if (num < 31) num = Math.round(num / 5) * 5 + 1;
    else if (num < 101) num = Math.round(num / 10) * 10 + 1;
    else if (num < 201) num = Math.round(num / 50) * 50 + 1;
    else if (num < 501) num = Math.round(num / 100) * 100 + 1;
    else if (num > 501) num = 501;
    result = map.get(num);
    if (result == undefined) return "";
  }
  return result;
};

const getMeetingDate_SGP = (str) => {
  let t = str.split(" ");
  if (t[2] && t[3] && t[4]) {
    var result = t[2] + "/" + t[3] + "/" + t[4];
    x = new Date(result);
    return moment(x).format().toString();
  } else {
    return "";
  }
};

const extractDamName_SGP = (str) => {
  if (str) {
    var theSplit = str.split("(");
    if (theSplit[1]) {
      theSplit[1] = theSplit[1].replace(")", "");
      theSplit[0] = theSplit[0].trim();
      return theSplit;
    } else {
      return [theSplit[0].trim(), ""];
    }
  } else {
    return "";
  }
};

const extractDamName_HK = (str) => {
  if (str) {
    if (str.lastIndexOf("(")) {
      var r = str.substring(0, str.lastIndexOf("("));
      return r.trim().toUpperCase();
    } else {
      return "";
    }
  } else {
    return "";
  }
};

const extractHorseName_HK = (str) => {
  if (str) {
    for (val of str) {
      if (val["@_lang"] == "en-GB") {
        return val["#text"];
      }
    }
  } else {
    return "";
  }
};

const poundsToHalfKilos = (lbs) => {
  return Math.round(lbs * 0.45359237 * 2) / 2;
};

const getStateName = (Id) => {
  if (Id) {
    var map = new Map([
      [1, "ACT"],
      [2, "NSW"],
      [3, "VIC"],
      [4, "QLD"],
      [5, "SA"],
      [6, "WA"],
      [7, "TAS"],
      [8, "NT"],
      [9, "NZ"],
      [10, "HK"],
      [11, "SGP"],
    ]);
    let result = map.get(Id);
    if (result == undefined) result = "Not Found";
    return result;
  } else {
    return "";
  }
};
const generateUniqueRaceId = (
  meetingDate,
  trackId,
  raceNumber,
  trialResult
) => {
  var yy = moment(meetingDate).format("YY").toString();
  var dd = moment(meetingDate).format("DDD").toString();
  if (dd < 10) {
    dd = "00" + dd.toString();
  } else if (dd < 100) {
    dd = "0" + dd.toString();
  } else dd = dd.toString();
  var raceNumberString = raceNumber.toString();
  if (raceNumber < 10) {
    raceNumberString = "0" + raceNumberString;
  }
  if (trialResult) {
    trackId = (parseInt(trackId) + 5000).toString();
  } else {
    if (parseInt(trackId) < 1000) {
      if (parseInt(trackId) < 100) {
        if (parseInt(trackId) < 10) {
          trackId = "000" + trackId.toString();
        } else {
          trackId = "00" + trackId.toString();
        }
      } else {
        trackId = "0" + trackId.toString();
      }
    }
  }
  var id = yy + dd + raceNumberString + trackId;
  return id;
};
const getGroupType = (Id) => {
  var map = new Map([
    ["G1", "1"],
    ["G2", "2"],
    ["G3", "3"],
    ["Group 1", "1"],
    ["Group 2", "2"],
    ["Group 3", "3"],
    ["LR", "LR"],
    ["Listed", "LR"],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = "";
  return result;
};

const checkMissingGrading = (form) => {
  var gradingsMissing = {};
  for (item of form) {
    if (item.track && item.track["@_condition"] === "")
      gradingsMissing[
        item.meeting_date.toString().replace("T00:00:00.000+00:00", "")
      ] = ["Track condition missing."];
    // if (!item.classes.class_id || (item.classes.second_class && !item.classes.second_class_id)) gradingsMissing.push(`Class ID missing from form: ${item.track['@_name']} on ${item.meeting_date.toString().replace('T00:00:00.000+00:00','')}`)
    if (
      !item.classes.class_id ||
      (item.classes.second_class && !item.classes.second_class_id)
    ) {
      if (
        gradingsMissing[
          item.meeting_date.toString().replace("T00:00:00.000+00:00", "")
        ]
      )
        gradingsMissing[
          item.meeting_date.toString().replace("T00:00:00.000+00:00", "")
        ].push("Class ID missing.");
      else
        gradingsMissing[
          item.meeting_date.toString().replace("T00:00:00.000+00:00", "")
        ] = ["Class ID missing."];
    }
    // if (!item.weight_carried && ((item.classes.class_id && item.classes.class_id != 90) && (!item.classes.second_class_id || item.classes.second_class_id != 90))) gradingsMissing.push(`No weight in form item: ${item.track['@_name']} on ${item.meeting_date.toString().replace('T00:00:00.000+00:00','')}`)
    if (
      !item.weight_carried &&
      item.classes.class_id &&
      item.classes.class_id != 90 &&
      (!item.classes.second_class_id || item.classes.second_class_id != 90)
    ) {
      if (
        gradingsMissing[
          item.meeting_date.toString().replace("T00:00:00.000+00:00", "")
        ]
      )
        gradingsMissing[
          item.meeting_date.toString().replace("T00:00:00.000+00:00", "")
        ].push("Carried weight missing.");
      else
        gradingsMissing[
          item.meeting_date.toString().replace("T00:00:00.000+00:00", "")
        ] = ["Weight missing."];
    }
  }
  return gradingsMissing;
};

const isolateMissingRuns = (oldForm, form) => {
  var formMissing = [];
  eachForm: for (item of oldForm) {
    var formitemmoment = moment(item.meeting_date, "DD/MM/YYYY");
    for (newItem of form) {
      if (formitemmoment.isSame(moment(newItem.meeting_date)))
        continue eachForm;
    }
    formMissing.push(item);
  }
  return formMissing;
};

const compareOldFormTIS = (missingGradings, inputForm) => {
  var taskerrorlog = "";
  for (item in missingGradings) {
    var formerrormoment = moment(item);
    var formerrordata = missingGradings[item];
    var oldForm = {};
    var oldFormString = "";
    for (old_item of inputForm) {
      // console.log(formerrormoment,moment(old_item.meeting_date, "DD/MM/YYYY"))
      if (formerrormoment.isSame(moment(old_item.meeting_date, "DD/MM/YYYY"))) {
        oldForm = old_item;
        break;
      }
    }
    // console.log(missingGradings)
    // console.log(formerrordata)
    if (oldForm && oldForm.track) {
      oldFormString = oldForm.track["@_name"] + " ";
      if (formerrordata.includes("Track condition missing."))
        oldFormString =
          oldFormString +
          "Track condition missing, input file Track object:" +
          JSON.stringify(oldForm.track)
            .replace(/,/g, ",\n")
            .replace(/{/g, "{\n")
            .replace(/}/g, "\n}") +
          "\n";
      if (formerrordata.includes("Class ID missing."))
        oldFormString =
          oldFormString +
          "Class ID missing, input file Class object:" +
          JSON.stringify(oldForm.classes)
            .replace(/,/g, ",\n")
            .replace(/{/g, "{\n")
            .replace(/}/g, "\n}") +
          "\n";
      if (formerrordata.includes("Weight missing."))
        oldFormString =
          oldFormString +
          "Carried weight missing, input file weight object:" +
          oldForm.weight_carried.toString() +
          "\n";
    } else {
      // console.log(`Oldform not found! ${formerrormoment}`)
      oldFormString =
        oldFormString +
        `Oldform not found!!!!! deeply check this form item: ${formerrormoment}`;
      oldFormString = formerrordata.join(" ");
    }
    taskerrorlog =
      taskerrorlog +
      formerrormoment.toISOString().replace("T00:00:00.000Z", "") +
      ": " +
      "\n" +
      oldFormString +
      "\n";
  }
  return taskerrorlog;
};

const getHorseSex = (sex, age) => {
  if (["F", "C"].includes(sex) && parseInt(age) > 3) {
    if (sex == "F") sex = "M";
    if (sex == "C") sex = "H";
  }
  return sex;
};

const getSexRestriction = (Id) => {
  var result = "";
  var map = new Map([
    ["F", "Fillies"],
    ["M", "Mares"],
    ["C", "Colts"],
    ["G", "Geldings"],
    ["FM", "Fillies & Mares"],
    ["CG", "Colts & Geldings"],
    ["CF", "Colts & Fillies"],
    ["A", "Fillies, Colts & Geldings"],
    ["EC", "Colts, Horses & Geldings"],
  ]);
  result = map.get(Id);
  if (result == undefined) return result;
  return result;
};

const getSexShortname = (Id) => {
  var result = "";
  var map = new Map([
    ["F", "F"],
    ["M", "M"],
    ["C", "C"],
    ["G", "G"],
    ["H", "H"],
    ["FM", "F&M"],
    ["CG", "C&G"],
    ["HG", "E&G"],
    ["EC", "E&G"],
  ]);
  result = map.get(Id);
  if (result == undefined) return result;
  return result;
};

const getReverseSexShortname = (Id) => {
  var result = "";
  var map = new Map([
    ["Fillies", "F"],
    ["Mares", "M"],
    ["Colts", "C"],
    ["Geldings", "G"],
    ["Horses", "H"],
    ["Fillies & Mares", "F&M"],
    ["Colts & Geldings", "C&G"],
    ["Horses & Geldings", "E&G"],
    ["Colts, Horses & Geldings", "E&G"],
  ]);
  result = map.get(Id);
  if (result == undefined) return result;
  return result;
};

const processGearElement = (str) => {
  // Check OFF FIRST TIME, FIRST TIME, AGAIN, OFF AGAIN

  if (str.includes("FIRST TIME")) {
    if (str.includes("OFF FIRST TIME")) {
      let temp = {
        "@_name": str.replace("OFF FIRST TIME", "").trim(),
        "@_option": "off first time",
      };
      return temp;
    } else {
      let temp = {
        "@_name": str.replace("FIRST TIME", "").trim(),
        "@_option": "first time",
      };
      return temp;
    }
  }

  if (str.includes("AGAIN")) {
    if (str.includes("OFF AGAIN")) {
      let temp = {
        "@_name": str.replace("OFF AGAIN", "").trim(),
        "@_option": "off again",
      };
      return temp;
    } else {
      let temp = {
        "@_name": str.replace("AGAIN", "").trim(),
        "@_option": "again",
      };
      return temp;
    }
  }

  //Default
  let temp = {
    "@_name": str.trim(),
    "@_option": "",
  };
  return temp;
};

const sleep = (milliseconds) => {
  const date = Date.now();
  let currentDate = null;
  do {
    currentDate = Date.now();
  } while (currentDate - date < milliseconds);
};

const isEmptyObject = (object) => {
  return Object.keys(object).length === 0;
};

// const convertNzTrackRating = (rating) =>{
//     if (rating == "Fast1" || rating == "Fast"){
//         return "F1"
//     }
//     if (rating == "Good2"){
//         return "F2"
//     }
//     if (rating == "Good3" || rating == "Good"){
//         return "G3"
//     }
//     if (rating == "Dead4"){
//         return "G4"
//     }
//     if (rating == "Dead5" || rating == "Dead"){
//         return "S5"
//     }
//     if (rating == "Dead6"){
//         return "S6"
//     }
//     if (rating == "Slow7" || rating == "Slow"){
//         return "S7"
//     }
//     if (rating == "Slow8"){
//         return "H8"
//     }
//     if (rating == "Slow9"){
//         return "H9"
//     }
//     if (rating == "Heavy10" || rating == "Heavy"){
//         return "H10"
//     }
//     if (rating == "Heavy11"){
//         return "H11"
//     }

// }

const logError = (error_log = [], errors = [], newError) => {
  if (!error_log.includes(newError) && !errors.includes(newError)) {
    errors.push(newError);
    error_log.push(newError);
  }
  return [error_log, errors];
};

const convertNzTrackRating = (rating) => {
  if (rating == "Fast1" || rating == "Firm1") {
    return "Firm(1)";
  }
  if (
    rating == "Fast2" ||
    rating == "Fast" ||
    rating == "Firm2" ||
    rating == "Firm"
  ) {
    return "Firm(2)";
  }
  if (rating == "Good3") {
    return "Good(3)";
  }
  if (rating == "Good4") {
    return "Good(4)";
  }
  if (rating == "Slow5" || rating == "Soft5") {
    return "Soft(5)";
  }
  if (rating == "Slow6" || rating == "Soft6") {
    return "Soft(6)";
  }
  if (rating == "Slow7" || rating == "Soft7") {
    return "Soft(7)";
  }
  if (rating == "Heavy8") {
    return "Heavy(8)";
  }
  if (rating == "Heavy9") {
    return "Heavy(9)";
  }
  if (rating == "Heavy10") {
    return "Heavy(10)";
  }
  if (rating == "Heavy11") {
    return "Heavy(11)";
  }
  if (rating == "Fast" || rating == "Firm") {
    return "Firm(0)";
  }
  if (rating == "Good") {
    return "Good(0)";
  }
  if (rating == "Slow" || rating == "Soft") {
    return "Soft(0)";
  }
  if (rating == "Heavy") {
    return "Heavy(0)";
  }
};

const getShortName = (age, sex, theClass, weight) => {
  var shortname = "";

  if (theClass.includes("+")) {
    theClass = theClass.replace(/(Rating |Benchmark )/g, "Bm");
    if (age) shortname = age;
    if (sex) shortname = shortname + " " + sex;
    if (theClass) shortname = shortname + " (" + theClass + ")";
  } else if (theClass == "Maiden" && weight == "Set Weight") {
    theClass = "Mdn Plate";
    if (age) shortname = age;
    if (sex) shortname = shortname + " " + sex;
    if (theClass) shortname = shortname + " " + theClass;
  } else if (theClass == "Maiden") {
    theClass = "Mdn";
    if (age) shortname = age;
    if (sex) shortname = shortname + " " + sex;
    if (theClass) shortname = shortname + " " + theClass;
  } else if (theClass == "Open" && weight == "Handicap") {
    theClass = "Open Hcp";
    if (age) shortname = age;
    if (sex) shortname = shortname + " " + sex;
    if (theClass) shortname = shortname + " " + theClass;
  } else {
    theClass = theClass.replace("Restricted ", "");
    theClass = theClass.replace("Rating ", "");
    theClass = theClass.replace("Benchmark ", "Bm");
    theClass = theClass.replace("Class ", "C");
    if (age) shortname = age;
    if (sex) shortname = shortname + " " + sex;
    if (theClass) shortname = shortname + " (" + theClass + ")";
  }

  shortname = shortname.trim();
  return shortname;
};

const generateRating = async (
  master_rating,
  age,
  sex,
  distance,
  weight,
  meeting_date
) => {
  var wfa = await getWfa(age, sex, distance, meeting_date);

  var ld = Math.log(distance);
  var awfa = weight - wfa;
  var ld2 = ld * ld;

  var wamr = master_rating - (-0.184 * ld2 + (3.3981 * ld - 14.016)) * awfa;
  wamr = Math.round(wamr * 2) / 2;

  return wamr;
};

const generateBasicRating = async (
  master_rating,
  age,
  sex,
  distance,
  weight,
  meeting_date
) => {
  var wfa = await getWfa(age, sex, distance, meeting_date);
  var wamr = master_rating + (wfa - weight);
  wamr = Math.round(wamr * 2) / 2;

  return wamr;
};

const getMR = async (ratings, distance, firstUp) => {
  var ratingSelect = "";
  if (distance < 1251) {
    ratingSelect = "mr1";
  } else if (distance < 1500) {
    ratingSelect = "mr2";
  } else if (distance < 1800) {
    ratingSelect = "mr3";
  } else {
    ratingSelect = "mr4";
  }
  if (firstUp) {
    ratingSelect = ratingSelect + "_d";
  }
  if (ratingSelect != "") {
    return ratings[ratingSelect];
  }
};

const getWfa = async (age, sex, distance, date) => {
  if (age > 4) {
    age = "5+";
  } else {
    age = age.toString();
  }
  if (distance < 1201) {
    distance = "1200";
  } else if (distance < 1401) {
    distance = "1400";
  } else if (distance < 1601) {
    distance = "1600";
  } else if (distance < 2001) {
    distance = "2000";
  } else if (distance < 2401) {
    distance = "2400";
  } else {
    distance = "3200";
  }
  var theDate = moment(date);
  var theMonth = theDate.month() + 1;

  if (theMonth < 10) {
    theMonth = "0" + theMonth.toString();
  }

  var sexAdjust = 0;

  if (["M", "F"].includes(sex)) {
    sexAdjust = 2;
  }

  var wfa = {
    1200: {
      2: {
        "08": 42.5,
        "09": 42.5,
        10: 42.5,
        11: 42.5,
        12: 42.5,
        "01": 45.0,
        "02": 46.0,
        "03": 47.0,
        "04": 48.0,
        "05": 49.0,
        "06": 50.0,
        "07": 51.0,
      },
      3: {
        "08": 51.5,
        "09": 52.0,
        10: 53.0,
        11: 53.5,
        12: 54.5,
        "01": 55.0,
        "02": 55.5,
        "03": 56.0,
        "04": 56.5,
        "05": 57.0,
        "06": 57.5,
        "07": 58.0,
      },
      4: {
        "08": 58.5,
        "09": 58.5,
        10: 58.5,
        11: 58.5,
        12: 58.5,
        "01": 58.5,
        "02": 58.5,
        "03": 58.5,
        "04": 58.5,
        "05": 58.5,
        "06": 58.5,
        "07": 58.5,
      },
      "5+": {
        "08": 58.5,
        "09": 58.5,
        10: 58.5,
        11: 58.5,
        12: 58.5,
        "01": 58.5,
        "02": 58.5,
        "03": 58.5,
        "04": 58.5,
        "05": 58.5,
        "06": 58.5,
        "07": 58.5,
      },
    },
    1400: {
      2: {
        "08": 42.5,
        "09": 42.5,
        10: 42.5,
        11: 42.5,
        12: 42.5,
        "01": 44.0,
        "02": 45.0,
        "03": 46.0,
        "04": 47.0,
        "05": 48.0,
        "06": 49.0,
        "07": 50.0,
      },
      3: {
        "08": 50.5,
        "09": 51.0,
        10: 52.0,
        11: 53.0,
        12: 54.0,
        "01": 54.5,
        "02": 55.5,
        "03": 56.0,
        "04": 56.5,
        "05": 57.0,
        "06": 57.5,
        "07": 58.0,
      },
      4: {
        "08": 58.5,
        "09": 58.5,
        10: 58.5,
        11: 59.0,
        12: 59.0,
        "01": 59.0,
        "02": 59.0,
        "03": 59.0,
        "04": 59.0,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
      "5+": {
        "08": 59.0,
        "09": 59.0,
        10: 59.0,
        11: 59.0,
        12: 59.0,
        "01": 59.0,
        "02": 59.0,
        "03": 59.0,
        "04": 59.0,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
    },
    1600: {
      2: {
        "08": 42.5,
        "09": 42.5,
        10: 42.5,
        11: 42.5,
        12: 42.5,
        "01": 43.5,
        "02": 44.5,
        "03": 45.5,
        "04": 46.5,
        "05": 47.5,
        "06": 48.5,
        "07": 49.5,
      },
      3: {
        "08": 50.0,
        "09": 50.5,
        10: 51.0,
        11: 52.0,
        12: 53.0,
        "01": 54.0,
        "02": 55.0,
        "03": 56.0,
        "04": 56.5,
        "05": 57.0,
        "06": 57.5,
        "07": 58.0,
      },
      4: {
        "08": 58.5,
        "09": 58.5,
        10: 58.5,
        11: 59.0,
        12: 59.0,
        "01": 59.0,
        "02": 59.0,
        "03": 59.0,
        "04": 59.0,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
      "5+": {
        "08": 59.0,
        "09": 59.0,
        10: 59.0,
        11: 59.0,
        12: 59.0,
        "01": 59.0,
        "02": 59.0,
        "03": 59.0,
        "04": 59.0,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
    },
    2000: {
      2: {
        "08": 42.5,
        "09": 42.5,
        10: 42.5,
        11: 42.5,
        12: 42.5,
        "01": 42.5,
        "02": 43.5,
        "03": 44.5,
        "04": 45.5,
        "05": 46.5,
        "06": 47.5,
        "07": 48.5,
      },
      3: {
        "08": 49.0,
        "09": 49.5,
        10: 50.0,
        11: 51.0,
        12: 52.0,
        "01": 53.0,
        "02": 54.0,
        "03": 54.5,
        "04": 55.5,
        "05": 56.5,
        "06": 57.0,
        "07": 57.5,
      },
      4: {
        "08": 58.0,
        "09": 58.0,
        10: 58.0,
        11: 58.5,
        12: 58.5,
        "01": 58.5,
        "02": 59.0,
        "03": 59.0,
        "04": 59.0,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
      "5+": {
        "08": 59.0,
        "09": 59.0,
        10: 59.0,
        11: 59.0,
        12: 59.0,
        "01": 59.0,
        "02": 59.0,
        "03": 59.0,
        "04": 59.0,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
    },
    2400: {
      2: {
        "08": 42.5,
        "09": 42.5,
        10: 42.5,
        11: 42.5,
        12: 42.5,
        "01": 42.5,
        "02": 42.5,
        "03": 42.5,
        "04": 42.5,
        "05": 42.5,
        "06": 42.5,
        "07": 42.5,
      },
      3: {
        "08": 48.5,
        "09": 49.0,
        10: 49.5,
        11: 50.5,
        12: 51.0,
        "01": 52.0,
        "02": 53.0,
        "03": 54.0,
        "04": 54.5,
        "05": 55.5,
        "06": 56.0,
        "07": 57.0,
      },
      4: {
        "08": 57.5,
        "09": 57.5,
        10: 57.5,
        11: 58.0,
        12: 58.0,
        "01": 58.0,
        "02": 58.5,
        "03": 58.5,
        "04": 58.5,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
      "5+": {
        "08": 59.0,
        "09": 59.0,
        10: 59.0,
        11: 59.0,
        12: 59.0,
        "01": 59.0,
        "02": 59.0,
        "03": 59.0,
        "04": 59.0,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
    },
    3200: {
      2: {
        "08": 42.5,
        "09": 42.5,
        10: 42.5,
        11: 42.5,
        12: 42.5,
        "01": 42.5,
        "02": 42.5,
        "03": 42.5,
        "04": 42.5,
        "05": 42.5,
        "06": 42.5,
        "07": 42.5,
      },
      3: {
        "08": 48.0,
        "09": 48.5,
        10: 49.0,
        11: 50.0,
        12: 50.5,
        "01": 51.5,
        "02": 52.5,
        "03": 53.5,
        "04": 54.0,
        "05": 55.0,
        "06": 55.5,
        "07": 56.0,
      },
      4: {
        "08": 57.5,
        "09": 57.5,
        10: 57.5,
        11: 58.0,
        12: 58.0,
        "01": 58.0,
        "02": 58.5,
        "03": 58.5,
        "04": 58.5,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
      "5+": {
        "08": 59.5,
        "09": 59.5,
        10: 59.5,
        11: 59.5,
        12: 59.5,
        "01": 59.5,
        "02": 59.5,
        "03": 59.5,
        "04": 59.5,
        "05": 59.5,
        "06": 59.5,
        "07": 59.5,
      },
    },
  };

  if (wfa[distance] && wfa[distance][age] && wfa[distance][age][theMonth]) {
    return wfa[distance][age][theMonth] - sexAdjust;
  } else {
    console.log("Error with ratings: wfa not found");
  }
};

const getAgeRestrictionByCode = (Id) => {
  var map = new Map([
    [1, "2yo"],
    [2, "2yo+"],
    [3, "2,3,4yo"],
    [4, "3yo"],
    [5, "3yo+"],
    [6, "4yo"],
    [7, "4yo+"],
    [8, "5yo"],
    [9, "5yo+"],
    [10, "6yo"],
    [11, "Aged"],
    [12, "2,3yo"],
    [13, "3,4yo"],
    [14, "3,4,5yo"],
    [15, "4,5yo"],
    [16, "4,5,6yo"],
    [17, "3,4,5,6"],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = "";
  return result;
};

const setAgeRestriction = (Id) => {
  var map = new Map([
    ["2yo", 1],
    ["2yo+", 2],
    ["2,3,4yo", 3],
    ["3yo", 4],
    ["3yo+", 5],
    ["4yo", 6],
    ["4yo+", 7],
    ["5yo", 8],
    ["5yo+", 9],
    ["6yo", 10],
    ["Aged", 11],
    ["2,3yo", 12],
    ["3,4yo", 13],
    ["3,4,5yo", 14],
    ["4,5yo", 15],
    ["4,5,6yo", 16],
    ["3,4,5,6", 17],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = Id;
  return result;
};

const getSexRestrictionByCode = (Id) => {
  // console.log('sex id',Id)
  var map = new Map([
    [1, "Fillies"],
    [2, "Mares"],
    [3, "Colts"],
    [4, "Geldings"],
    [5, "Horses"],
    [6, "Fillies & Mares"],
    [7, "Colts & Horses"],
    [8, "Colts & Geldings"],
    [9, "Horses & Geldings"],
    [10, "Horses & Mares"],
    [11, "Colts & Fillies"],
    [12, "Colts, Horses & Geldings"],
    [13, "Fillies, Colts & Geldings"],
    [14, "Mares, Horses & Geldings"],
    [15, "Fillies, Mares, Colts & Horses"],
    [16, "Fillies, Mares, Colts & Geldings"],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = "";
  return result;
};

const setSexRestriction = (Id) => {
  console.log("sex id", Id);
  var map = new Map([
    ["Fillies", 1],
    ["Mares", 2],
    ["Colts", 3],
    ["Geldings", 4],
    ["Horses", 5],
    ["Fillies & Mares", 6],
    ["Colts & Horses", 7],
    ["Colts & Geldings", 8],
    ["Horses & Geldings", 9],
    ["Horses & Mares", 10],
    ["Colts & Fillies", 11],
    ["Colts, Horses & Geldings", 12],
    ["Fillies, Colts & Geldings", 13],
    ["Mares, Horses & Geldings", 14],
    ["Fillies, Mares, Colts & Horses", 15],
    ["Fillies, Mares, Colts & Geldings", 16],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = Id;
  return result;
};

const formatJockeyRestrictionsByCode = (rest) => {
  var map = new Map([
    ["A", "Apprentice Riders Only"],
    ["C", "Apprentices Can Claim"],
    ["E", "Eligible Riders Can Claim"],
    ["G", "Gentlemen Riders Only"],
    ["I", "Invited Riders"],
    ["L", "Lady Riders Only"],
    ["N", "Apprentices Cannot Claim"],
    ["X", "Eligible Riders Cannot Claim"],
    ["M", "Amateur Riders"],
    ["H", "Hurdle Jockeys Only"],
  ]);
  let result = map.get(rest);
  if (result == undefined) result = "";
  return result;
};

const getWeightTypeByCode = (Id) => {
  var map = new Map([
    ["H", "Handicap"],
    ["W", "Weight For Age"],
    ["X", "Weight For Age With Penalties"],
    ["S", "Set Weight"],
    ["T", "Set Weight With Penalties"],
    ["U", "Set Weight With Penalties and Allowances"],
    ["P", "Special Weight"],
    ["C", "Catch Weight"],
    ["F", "Fly Weight"],
    ["A", "Set Weight With Allowances"],
    ["Q", "Quality"],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = "";
  return result;
};

const formatWeightType = (Id) => {
  var map = new Map([
    ["Standard Weight for Age", "Weight For Age"],
    ["Weight For Age Plus Penalties", "Weight For Age With Penalties"],
    ["Weight For Age plus Penalties", "Weight For Age With Penalties"],
    ["Weight For Age & Penalties", "Weight For Age With Penalties"],
    ["Weight For Age with Penalties", "Weight For Age With Penalties"],
    ["Weight For Age With Penalties", "Weight For Age With Penalties"],
    ["Set Weights", "Set Weight"],
    ["Set Weights With Penalties", "Set Weight With Penalties"],
    ["Set Weights with Penalties", "Set Weight With Penalties"],
    ["Set Weights Plus Penalties", "Set Weight With Penalties"],
    ["Set Weights plus Penalties", "Set Weight With Penalties"],
    [
      "Set Weights With Penalties and Allowances",
      "Set Weight With Penalties and Allowances",
    ],
    [
      "Set Weights Plus Penalties and Allowances",
      "Set Weight With Penalties and Allowances",
    ],
    [
      "Set Weights with Penalties and Allowances",
      "Set Weight With Penalties and Allowances",
    ],
    [
      "Set Weights plus Penalties and Allowances",
      "Set Weight With Penalties and Allowances",
    ],
    [
      "Set Weights & Penalties and Allowances",
      "Set Weight With Penalties and Allowances",
    ],
    ["Set Weights With Allowances", "Set Weight With Allowances"],
    ["Set Weights Plus Allowances", "Set Weight With Allowances"],
    ["Set Weights & Allowances", "Set Weight With Allowances"],
    ["Set Weights with Allowances", "Set Weight With Allowances"],
    ["Set Weights plus Allowances", "Set Weight With Allowances"],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = Id;
  return result;
};

const getNzRaceType = (raceclass) => {
  var result = "Flat";
  if (
    raceclass.toLowerCase().includes("hdl") ||
    raceclass.toLowerCase().includes("stp")
  )
    result = "Jumps";
  return result;
};

const meetingStageSelect = (Id) => {
  var map = new Map([
    ["N", "Nominations"],
    ["W", "Weights"],
    ["A", "Acceptances"],
    ["R", "Results"],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = Id;
  return result;
};

const cleanupTextForXml = (text) => {
  text = text.replace(/\&quot;/g, "'");
  text = text.replace(/"/g, "'");
  text = text.replace(/\&gt;/g, ">");
  text = text.replace(/\&lt;/g, "<");
  text = text.replace(/\&amp;/g, "&");
  return text;
};

const runningGears = (changes, running_gear) => {
  var ran_gear = running_gear;
  for (gear_item of changes) {
    if (gear_item["@_option"].includes("off")) {
      const item_index = ran_gear.indexOf(gear_item["@_name"]);
      if (item_index > -1) {
        ran_gear.splice(item_index, 1);
      } else if (gear_item["@_name"].toLowerCase() == "blinkers") {
        for (item of ran_gear) {
          if (item.toLowerCase().includes("blinkers")) {
            ran_gear.splice(ran_gear.indexOf(item), 1);
            break;
          }
        }
      } else if (gear_item["@_name"].toLowerCase() == "winkers") {
        for (item of ran_gear) {
          if (item.toLowerCase().includes("winkers")) {
            ran_gear.splice(ran_gear.indexOf(item), 1);
            break;
          }
        }
      } else {
        var x = tryMatchGear(gear_item["@_name"], ran_gear);
        if (x > -1) {
          ran_gear.splice(x, 1);
        }
      }
    } else if (
      !ran_gear.includes(gear_item["@_name"]) &&
      gear_item["@_name"].toLowerCase() != "gelded"
    ) {
      ran_gear.push(gear_item["@_name"]);
    }
  }
  return ran_gear;
};

const tryMatchGear = (gear_item, running_gear) => {
  the_gear = gear_item.replace(/[ '_()-]/g, "").toLowerCase();
  i = 0;
  for (ran_gear of running_gear) {
    if (ran_gear.replace(/[ '_()-]/g, "").toLowerCase() == the_gear) {
      return i;
    }
    i++;
  }
  return -1;
};
function isNumeric(str) {
  if (typeof str != "string") return false; // we only process strings!
  return (
    !isNaN(str) && // use type coercion to parse the _entirety_ of the string (`parseFloat` alone does not do this)...
    !isNaN(parseFloat(str))
  ); // ...and ensure strings of whitespace fail
}

const convertCSVtoJSON = (csvraw) => {
  var jsonData = {
    filetype: "sectional_200",
  };
  var splitCSV = csvraw.split("\n");
  if (!splitCSV[0].includes("Date:,,")) return false;
  if (!splitCSV[1].includes("MEETING:,,")) return false;
  var meetingDateRaw = splitCSV[0].replace(/^Date:,,"(.+?)",+\r*/, "$1");
  var meetingTrackRaw = splitCSV[1].replace(/^MEETING:,,([^,]+?),+\r*/, "$1");
  console.log(meetingDateRaw);
  console.log(meetingTrackRaw);
  var meetingDate = moment(meetingDateRaw);
  jsonData.meeting_date = meetingDate
    .toISOString()
    .replace("T00:00:00.000Z", "");
  jsonData.meeting_track = meetingTrackRaw;
  var race = 0;
  var currentrace = [];
  jsonData.races = [];
  for (line of splitCSV) {
    if (line.includes("RACE ") && line.includes(",,,,,,,,,,,,,,,,,")) {
      if (currentrace.length > 0) {
        jsonData.races.push(currentrace);
        console.log(currentrace);
      }
      currentrace = [];
    }
    var linesplit = line.split(",");
    if (
      linesplit[0] &&
      linesplit[1] &&
      linesplit[2] &&
      linesplit[6] &&
      !line.includes(",DISTANCE,")
    ) {
      currentrace.push({
        horse_name: linesplit[2],
        offtime: linesplit[3],
        ivrtime: linesplit[4],
        tdiff: linesplit[5],
        l200: linesplit[6],
      });
    }
  }
  if (currentrace.length > 0) jsonData.races.push(currentrace);

  return jsonData;
};
// Function to determine track rating

const getBaseRatingByClass = (classId) => {
  var classRatingList = {
    196: 20,
    212: 20,
    213: 20,
    214: 20,
    197: 21,
    215: 21,
    216: 21,
    217: 21,
    218: 21,
    80: 22,
    81: 22,
    82: 22,
    19: 22,
    30: 22,
    83: 22,
    84: 22,
    97: 22,
    186: 22,
    187: 22,
    188: 22,
    189: 22,
    190: 22,
    198: 22,
    199: 22,
    200: 22,
    219: 22,
    220: 22,
    221: 22,
    222: 22,
    223: 22,
    224: 22,
    225: 22,
    226: 22,
    268: 22,
    269: 22,
    270: 22,
    271: 22,
    272: 22,
    273: 22,
    274: 22,
    275: 22,
    276: 22,
    315: 22,
    153: 23,
    201: 23,
    227: 23,
    228: 23,
    229: 23,
    230: 23,
    231: 23,
    277: 23,
    278: 23,
    279: 23,
    135: 24,
    154: 24,
    155: 24,
    156: 24,
    202: 24,
    211: 24,
    232: 24,
    233: 24,
    234: 24,
    235: 24,
    236: 24,
    280: 24,
    281: 24,
    282: 24,
    283: 24,
    284: 24,
    285: 24,
    286: 24,
    287: 24,
    288: 24,
    316: 24,
    157: 25,
    203: 25,
    237: 25,
    238: 25,
    239: 25,
    289: 25,
    290: 25,
    85: 26,
    240: 26,
    291: 26,
    292: 26,
    317: 26,
    293: 27,
    294: 27,
    18: 28,
    86: 28,
    98: 28,
    136: 28,
    158: 28,
    159: 28,
    204: 28,
    241: 28,
    242: 28,
    243: 28,
    244: 28,
    295: 28,
    296: 28,
    318: 28,
    137: 29,
    245: 29,
    246: 29,
    297: 29,
    54: 30,
    87: 30,
    138: 30,
    139: 30,
    140: 30,
    160: 30,
    161: 30,
    162: 30,
    205: 30,
    298: 30,
    299: 30,
    300: 30,
    301: 30,
    302: 30,
    319: 30,
    141: 31,
    142: 31,
    247: 31,
    303: 31,
    320: 31,
    17: 32,
    99: 32,
    143: 32,
    144: 32,
    145: 32,
    146: 32,
    163: 32,
    164: 32,
    165: 32,
    166: 32,
    248: 32,
    321: 32,
    147: 33,
    148: 33,
    249: 33,
    250: 33,
    304: 33,
    305: 33,
    322: 33,
    206: 34,
    251: 34,
    252: 34,
    306: 34,
    323: 34,
    88: 35,
    93: 35,
    100: 35,
    149: 35,
    167: 35,
    168: 35,
    169: 35,
    170: 35,
    171: 35,
    253: 35,
    254: 35,
    307: 35,
    309: 35,
    310: 35,
    311: 35,
    312: 35,
    313: 35,
    314: 35,
    324: 35,
    150: 36,
    151: 36,
    152: 36,
    207: 36,
    255: 36,
    256: 36,
    308: 36,
    325: 36,
    89: 37,
    172: 37,
    173: 37,
    174: 37,
    175: 37,
    257: 37,
    258: 37,
    326: 37,
    176: 38,
    177: 38,
    178: 38,
    179: 38,
    208: 38,
    259: 38,
    260: 38,
    180: 39,
    181: 39,
    182: 39,
    183: 39,
    184: 39,
    261: 39,
    262: 39,
    16: 40,
    185: 40,
    191: 40,
    192: 40,
    193: 40,
    194: 40,
    195: 40,
    209: 40,
    263: 40,
    264: 40,
    210: 41,
    265: 41,
    266: 41,
  };
  if (classRatingList[classId]) {
    return classRatingList[classId];
  } else {
    return 30;
  }
};

const hkGearMatch = (gear) => {
  const gearLegend = {
    B: "Blinkers",
    BO: "Blinkers (one eyed)",
    CC: "Cornell Collar",
    CP: "Sheepskin Cheek Pieces",
    CO: "Sheepskin Cheek Pieces (One Side)",
    E: "Ear Plugs",
    H: "Hood",
    P: "Pacifiers",
    PC: "Pacifier with cowls",
    PS: "Pacifier with One Cowl",
    SB: "Sheepskin Browband",
    SR: "Shadow Roll",
    TT: "Tongue Tie",
    V: "Visors",
    VO: "Visor with one cowl",
    XB: "Cross Over Noseband",
  };
  const gearOption = {
    1: "first time",
    2: "again",
    "-": "off",
  };

  if (gearOption[gear.slice(-1)]) {
    // is gear change
    if (gearLegend[gear.slice(0, -1)]) {
      return [gearLegend[gear.slice(0, -1)], gearOption[gear.slice(-1)]];
    } else {
      return [
        `GEAR NOT FOUND: ${gear.slice(0, -1)}`,
        gearOption[gear.slice(-1)],
      ];
    }
  } else if (gearLegend[gear]) {
    return [gearLegend[gear]];
  } else {
    return [`GEAR NOT FOUND: ${gear}`];
  }
};

const getGearID = (Id) => {
  var map = new Map([
    ["Approved Race Plates (front and Hind)", "416"],
    ["Approved Race Plates Front", "3"],
    ["Approved Race Plates Hind", "2"],
    ["Approved Race Plates", "106"],
    ["Bandages (all round)", "205"],
    ["Bandages (All Round)", "205"],
    ["Bandages (Bumper)", "133"],
    ["Bandages (fetlocks)", "251"],
    ["Bandages (front and both hocks)", "138"],
    ["Bandages (front and hind)", "189"],
    ["Bandages (Front bumpers)", "287"],
    ["Bandages (Front Fetlocks)", "202"],
    ["Bandages (Front, Hind bumpers)", "159"],
    ["Bandages (Front)", "71"],
    ["Bandages (hind bumpers)", "177"],
    ["Bandages (hind fetlocks)", "174"],
    ["Bandages (hind hocks)", "315"],
    ["Bandages (Hind)", "87"],
    ["Bandages (Hocks)", "219"],
    ["Bandages (Near Fore)", "143"],
    ["Bandages (near hind hock)", "386"],
    ["Bandages (Off Fore Fetlock)", "308"],
    ["Bandages (Off Hind Fetlock)", "361"],
    ["Bandages (off hock)", "191"],
    ["Bandages Near Hind", "110"],
    ["Bandages (Near Hind)", "110"],
    ["Bandages Off Fore", "5"],
    ["Bandages Off Hind", "111"],
    ["Bandages (Off Fore)", "5"],
    ["Bandages (Off Hind)", "111"],
    ["Bandages", "4"],
    ["Bar Plate (off side Front & Hind)", "418"],
    ["Bar Plates (Aluminium bonded off fore)", "314"],
    ["Bar Plates (Egg Bar near fore)", "224"],
    ["Bar Plates (Egg Bars - front)", "299"],
    ["Bar Plates (Egg Bars)", "259"],
    ["Bar Plates (Front Heart Bars)", "296"],
    ["Bar Plates (Front)", "7"],
    ["Bar plates (Heart bars)", "223"],
    ["Bar Plates (Hind (Egg Bar))", "301"],
    ["Bar Plates (Hind)", "6"],
    ["Bar Plates (Near Fore)", "141"],
    ["Bar Plates (Near Side Front)", "281"],
    ["Bar Plates (Near Side Hind)", "294"],
    ["Bar Plates (Off Fore)", "300"],
    ["Bar plates (Off hind)", "346"],
    ["Bar Plates (Off Side Hind)", "293"],
    ["Bar Plates (Offside Front)", "212"],
    ["Bar Plates (Straight Bar (fronts))", "175"],
    ["Bar Plates (Straight Bar)", "154"],
    ["Bar Plates (Wedge Plates Front)", "369"],
    ["Bar Plates (Wedges - hind)", "360"],
    ["Bar Plates with Pads (Front)", "311"],
    ["Bar Plates with Pads (Fronts and Hinds)", "371"],
    ["bar plates with pads (hind)", "370"],
    ["Bar plates with pads (near fore)", "389"],
    ["Bar Plates with Pads (Near Side Front)", "401"],
    ["Bar Plates with Pads (Offside Front)", "318"],
    ["Bar plates with Syntheti (Fronts)", "349"],
    ["Bar Plates with Syntheti (Near Side Hind)", "406"],
    ["Bar Plates with Syntheti (Off-Side Front)", "372"],
    ["Bar Plates: Egg bar/ Heart (off side front)", "278"],
    ["Bar Plates: Egg bar/Heart (Fronts) Heart (Hinds)", "277"],
    ["Bar Plates: Egg bar/Heart (Fronts)", "307"],
    ["Bar Plates: Egg bar/Heart (Hinds)", "295"],
    ["Bar Plates: Egg bar/Heart (near-side front)", "276"],
    ["Bar Plates: Synthetic hoof repair & pad (NSF)", "429"],
    ["Bar Plates", "67"],
    ["Bar Shoe (Front)", "139"],
    ["Bar shoe (left fore)", "167"],
    ["Bar shoe (near fore)", "192"],
    ["Bar Shoe (Near Front)", "206"],
    ["Bar Shoe (Near Hind)", "115"],
    ["Bar Shoe (Near Side Fore)", "97"],
    ["Bar Shoe (Near Side Front)", "242"],
    ["Bar Shoe (Near side)", "243"],
    ["Bar Shoe (Off Hind)", "117"],
    ["Bar Shoe (Off Side Fore)", "100"],
    ["Bar Shoe (off side front)", "183"],
    ["Bar Shoe (Off/Front)", "184"],
    ["Bar Shoe", "208"],
    ["Bar Shoes - Rolled Toe", "233"],
    ["Bar Shoes (all round)", "156"],
    ["Bar Shoes (both front)", "83"],
    ["Bar Shoes (Front)", "182"],
    ["Bar shoes (Hind)", "94"],
    ["Bar Shoes", "136"],
    ["Bare - Near Side Hind", "425"],
    ["Bare-Front", "282"],
    ["Bare-Hind", "280"],
    ["Barrier Blanket", "8"],
    ["Barrier Extension", "108"],
    ["Bars Plates (Egg Bars - Front)", "158"],
    ["Bell Boots (Front)", "343"],
    ["Bell Boots", "187"],
    ["Bit Burr (both sides)", "262"],
    ["Bit Burr (Near side)", "130"],
    ["Bit Burr (Off Side)", "197"],
    ["Bit Burr", "230"],
    ["Bit Lifter", "12"],
    ["Bits (D-Bit)", "366"],
    ["Bits (Egg Butt)", "235"],
    ["Bits (Happy Mouth)", "395"],
    ["Bits (JR-Lugging)", "210"],
    ["Bits (kyneton)", "238"],
    ["Bits (Lugging - Straight Bar)", "147"],
    ["Bits (Lugging)", "377"],
    ["Bits (Norton)", "196"],
    ["Bits (ring)", "195"],
    ["Bits (Running Rings)", "368"],
    ["Bits (Victor)", "227"],
    ["Bits (W)", "169"],
    ["Bits Half Spoon", "10"],
    ["Bits Rubber", "11"],
    ["Bits", "9"],
    ["Blindfold", "79"],
    ["Blinker With One Cowl", "21"],
    ["Blinkers (cup)", "124"],
    ["Blinkers (Near Cup)", "114"],
    ["Blinkers (Near Side)", "20"],
    ["Blinkers (Off Cup)", "170"],
    ["Blinkers (one eyed) (Near Side)", "161"],
    ["Blinkers (one eyed) (Off Side)", "144"],
    ["Blinkers (one eyed)", "228"],
    ["Blinkers (Pre-Race Only)", "422"],
    ["Blinkers Near Side", "82"],
    ["Blinkers Off Side", "116"],
    ["Blinkers (Near Side)", "82"],
    ["Blinkers (Off Side)", "116"],
    ["Blinkers", "356"],
    ["Bonded Shoes (Front)", "236"],
    ["Bonded shoes", "260"],
    ["Boots (Bell boot off hind)", "231"],
    ["Boots (Bumper)", "160"],
    ["Boots (Coronet Ring)", "331"],
    ["Boots (Fetlock) Front", "186"],
    ["Boots (Front)", "107"],
    ["Boots (Hind)", "113"],
    ["Boots (Near Hind)", "381"],
    ["Boots (near side hind hock)", "367"],
    ["Boots (Off Hind)", "297"],
    ["Boots (Rubber Ring - Near Fore)", "332"],
    ["Boots (Rubber Ring Off Hind)", "365"],
    ["Boots (Rubber rings) Hind", "302"],
    ["Boots (Rubber Rings)", "309"],
    ["Boots (Scalping rings - hind)", "211"],
    ["Boots", "15"],
    ["Brow Band", "22"],
    ["Brushing Boots (front)", "198"],
    ["Brushing Boots (Front)", "198"],
    ["Brushing Boots (Hind)", "70"],
    ["Brushing Boots", "14"],
    ["Bubble Cheeker (Burr Bit-Near Side)", "341"],
    ["Bubble Cheeker (Near and Offside)", "298"],
    ["Bubble Cheeker Near Side", "72"],
    ["Bubble Cheeker (Near Side)", "72"],
    ["Bubble Cheeker Off Side", "23"],
    ["Bubble Cheeker (Off Side)", "23"],
    ["Bubble Cheeker", "91"],
    ["Bubble Cheekers", "445"],
    ["Bumper BA - Fronts", "400"],
    ["Bumper BA (Hinds)", "397"],
    ["bumper boots (front)", "391"],
    ["Bumper Boots (hind)", "188"],
    ["bumper boots (Front)", "391"],
    ["Bumper Boots (Hind)", "188"],
    ["Bumper Boots", "16"],
    ["Butterfly bit with W tongue clip", "249"],
    ["Butterfly Bit", "24"],
    ["Cheekers (bubble off side only)", "221"],
    ["Cheekers (near side)", "207"],
    ["Cheekers (off side)", "180"],
    ["Cheekers", "66"],
    ["Compression Hood with Ear Muffs", "446"],
    ["Compression Hood With Ear Muffs", "446"],
    ["Concussion Plates (Easy Walkers-Front)", "217"],
    ["Concussion Plates (Front and Hind)", "417"],
    ["Concussion Plates (Front)", "104"],
    ["Concussion Plates (Gliderz-Front)", "334"],
    ["Concussion Plates (gliderz)", "222"],
    ["Concussion Plates (Hind)", "176"],
    ["Concussion Plates (Wedge off fore)", "317"],
    ["Concussion Plates Lamina (Fronts)", "316"],
    ["concussion plates shock near side front", "431"],
    ["Concussion Plates-Shock (Front and Hind)", "337"],
    ["Concussion Plates: Lamina (Hinds)", "436"],
    ["concussion plates: lamina (offside front)", "375"],
    ["Concussion Plates: Shock (Front)", "303"],
    ["Concussion Plates: Shock (Hind)", "304"],
    ["Concussion plates: Shock (Off Front)", "347"],
    ["Concussion Plates", "26"],
    ["Cornell Collar", "25"],
    ["Cornell Head Collar", "290"],
    ["Coronet Boots (Front)", "330"],
    ["Coronet Boots (Hind)", "329"],
    ["Cross Over Noseband", "27"],
    ["Cross-over Nose Band", "27"],
    ["Cross-over Nose Band (Cavasham)", "190"],
    ["Cross-over Nose Band (Hanoverian)", "313"],
    ["Dually Headcollar", "412"],
    ["Ear Muffs (Pre-Race Only)", "413"],
    ["Ear Muffs", "29"],
    ["Ear Plugs (Pre-race only)", "440"],
    ["Ear Plugs", "28"],
    ["Egg Bar Plates (Front)", "216"],
    ["Egg Bar Shoes", "166"],
    ["Egg Bars (Hind)", "148"],
    ["Equicast", "272"],
    ["Equiloc Racing Pads (Front)", "239"],
    ["Equilock Shoe Repair (Front & N/S Hind)", "132"],
    ["Equilox (Fore)", "81"],
    ["Equilox Hoof Repair (off front)", "246"],
    ["Equilox Off Fore", "84"],
    ["Equilox Off Hind On", "85"],
    ["Equithane Hoof Repair", "226"],
    ["Equithane Repair (off-side front)", "273"],
    ["Gelded", "30"],
    ["Glider Shoes", "172"],
    ["Gliderz shoes (front)", "264"],
    ["Gliderz Shoes", "271"],
    ["Glu-Shoes", "155"],
    ["Glue on Plates (front)", "253"],
    ["Glue On Shoe (Near Fore)", "336"],
    ["Glue On Shoes (Equipac) Front", "362"],
    ["Glue On Shoes (Front & Hind)", "420"],
    ["Glue on Shoes (Front)", "105"],
    ["Glue On Shoes (Hind)", "164"],
    ["Glue on Shoes (Hind)", "164"],
    ["Glue On Shoes (Off-side Hind)", "352"],
    ["Glue On Shoes", "78"],
    ["Half Spoon Bit", "424"],
    ["Hanging Bit", "32"],
    ["Hanoverian Nose Band", "399"],
    ["Heart Bar (Near Front)", "213"],
    ["Heart Bar (off fore)", "263"],
    ["Heart Bar Shoes", "89"],
    ["Heart Bars (Front)", "76"],
    ["Heart Bars (hind)", "265"],
    ["Hock Boot (Near Hind)", "444"],
    ["Hock Boot (Off Hind)", "419"],
    ["Hock Boots", "33"],
    ["Hood", "34"],
    ["Hoof Pads (All Round)", "291"],
    ["Hoof Pads (Mac pads - Front)", "323"],
    ["Hoof Pads (Mac Pads)", "327"],
    ["Hoof Pads (Moulded front)", "240"],
    ["Hoof Pads (Near Fore)", "256"],
    ["Hoof Pads (Off Fore)", "306"],
    ["Hoof Pads (Off Hind)", "335"],
    ["Hoof Pads (Wedges - Front)", "237"],
    ["Hoof Pads (Wedges all round)", "218"],
    ["Hoof Pads Front", "36"],
    ["Hoof Pads", "35"],
    ["Kyneton Nose Band (Hanoverian)", "342"],
    ["Kyneton Nose Band", "37"],
    ["Lugging Bit (JR)", "376"],
    ["Lugging Bit (Rubber)", "350"],
    ["Lugging Bit", "38"],
    ["Nasal Strip", "269"],
    ["Natural Balance Shoes (Front)", "250"],
    ["Natural Balance Shoes", "254"],
    ["Non Standard Shoes (Bare - Fronts)", "385"],
    ["Non Standard Shoes (Bare - Hinds)", "383"],
    ["Non Standard Shoes (Gliderz-Front)", "122"],
    ["Non Standard Shoes (Gliderz)", "152"],
    ["Non Standard Shoes (Near Side Front)", "379"],
    ["Non Standard Shoes", "39"],
    ["Norton Bit", "40"],
    ["Nose Band", "41"],
    ["Nose Roll", "42"],
    ["One Eyed Blinker (Near Side)", "92"],
    ["One Eyed Blinker (Off Side)", "93"],
    ["One Eyed Blinker (Right)", "126"],
    ["One Eyed Blinker", "17"],
    ["One Eyed Blinkers (Left)", "125"],
    ["One Eyed Winker (Left)", "268"],
    ["One Eyed Winker (Right)", "241"],
    ["One Eyed Winkers", "64"],
    ["Pacifier with cowls", "44"],
    ["Pacifier with One Cowl", "410"],
    ["Pacifiers (Pre-Race Only)", "415"],
    ["Pacifiers", "43"],
    ["Pad (near fore)", "261"],
    ["Pad (Off Fore)", "98"],
    ["Pad Shoes", "163"],
    ["Pads - plastic/leather/rub (Front & Hind)", "328"],
    ["Pads - plastic/leather/rub (front)", "283"],
    ["Pads - plastic/leather/rub (hind)", "284"],
    ["Pads - plastic/leather/rub offside front", "288"],
    ["Pads - plastic/leather/rub offside hind", "310"],
    ["Pads (Front)", "101"],
    ["Pads (Hind)", "339"],
    ["Pads (Near Side Front)", "123"],
    ["Pads (Near Side Hind)", "405"],
    ["Pads (Off Side Front)", "402"],
    ["Pads (Offside Hind)", "403"],
    ["Pads: Plastic/leather/Rub Near Side front", "321"],
    ["Pads: Plastic/leather/rub near side hind", "340"],
    ["Pads", "135"],
    ["Plates (3/4 hind)", "157"],
    ["Pulling Bit", "279"],
    ["Race Plates (3/4 shoe near fore)", "200"],
    ["Race Plates (3/4) Front", "178"],
    ["Race Plates (3/4) Hinds", "373"],
    ["Race Plates (Bonded Fronts)", "378"],
    ["Race Plates (Bonded)", "382"],
    ["Race Plates (Easy walkers) front", "173"],
    ["Race Plates (Ezy Walkers)", "234"],
    ["Race Plates (Gliderz) Front", "146"],
    ["Race Plates (Hoof Filler)", "393"],
    ["Race Plates (Shock Tamers)", "204"],
    ["Race Plates (Side Clips)", "324"],
    ["Race Plates (Speedy Toe-Front)", "46"],
    ["Race Plates (Speedy Toes)", "292"],
    ["Race Plates (Square Toe - Fronts)", "258"],
    ["Race Plates (Standard)", "274"],
    ["Race Plates (Tips)", "326"],
    ["Race Plates (Wedges - Off Fore)", "333"],
    ["Race Plates (Wedges-All round)", "338"],
    ["Race Plates (Wedges-Front)", "193"],
    ["Race Plates (Wedges) Hind", "179"],
    ["Race Plates (Wedges)", "118"],
    ["Race Plates (World Plates - front)", "199"],
    ["Race Plates (World)", "285"],
    ["Race Plates Front", "47"],
    ["Race Plates Square Toed", "120"],
    ["Race Plates", "45"],
    ["Racing Bare - Hind", "380"],
    ["Racing Without Shoes Front", "50"],
    ["Racing Without Shoes Hind And Front", "51"],
    ["Racing Without Shoes Hind", "49"],
    ["Racing Without Shoes", "48"],
    ["Raised Heel", "185"],
    ["Reverse Egg Bar Shoes", "203"],
    ["Ring Bit", "112"],
    ["Rolled Toe (egg-bar) shoes", "247"],
    ["Rolled Toe Shoes", "214"],
    ["Rubber Ring", "52"],
    ["Scalping Boots", "432"],
    ["Shadow Roll", "55"],
    ["Sheepskin Browband", "56"],
    ["Sheepskin Cheek Pieces (One Side)", "409"],
    ["Sheepskin Cheek Pieces", "408"],
    ["Shock Pads", "127"],
    ["Shock Shod Shoes (Easy Walkers) Front", "153"],
    ["Shock Shod Shoes (Front)", "137"],
    ["Shock Shod Shoes (Glider shoes)", "248"],
    ["Shock Shod Shoes (Gliderz-Front)", "201"],
    ["Shock Shod Shoes (Shock Tamers)", "257"],
    ["Shock Shod Shoes", "69"],
    ["Shock Shoes (Gliderz)", "142"],
    ["Shock Tamer (Off-Fore)", "414"],
    ["Shoes - Shock Tamers (Front)", "215"],
    ["Side Winker One Side", "54"],
    ["Square Toe Egg Bar (Front)", "140"],
    ["Square Toe Shoes", "232"],
    ["Stallion Chain", "80"],
    ["Standard Bit (D)", "392"],
    ["Standard Bit (ring bit)", "57"],
    ["Standard Plates (No pads)", "427"],
    ["Standard Plates", "53"],
    ["Standard Race Tips (Front)", "129"],
    ["Standard Race Tips (Hind)", "165"],
    ["Standard Race Tips", "131"],
    ["Standard Shoe (O/S Front)", "435"],
    ["Standard Shoes (both Hinds)", "430"],
    ["Standard Shoes all-round", "437"],
    ["synthetic hoof filler (both front)", "387"],
    ["Synthetic Hoof Filler (Front)", "320"],
    ["Synthetic Hoof Filler (near fore)", "319"],
    ["Synthetic Hoof Filler (Near Hind)", "428"],
    ["Synthetic Hoof Filler (Off Fore)", "407"],
    ["Synthetic Hoof Filler (Off Hind)", "345"],
    ["Synthetic Hoof Filler (Right Fore)", "404"],
    ["Synthetic Hoof Filler", "194"],
    ["Synthetic hoof repair - EQ (fronts)", "289"],
    ["Synthetic hoof repair - Equithane - Fronts", "348"],
    ["Synthetic Hoof Repair (Near Side Fore)", "325"],
    ["Synthetic Hoof Repair (Near Side Hind)", "312"],
    ["Synthetic hoof repair: EQ (fronts and hinds)", "359"],
    ["Synthetic hoof repair: EQ (near side front)", "363"],
    ["Synthetic Hoof Repair: EQ (Off Side Front)", "374"],
    ["Synthetic hoof repair: EQ (off side hind)", "364"],
    ["Tail Chain", "58"],
    ["Tendon Boots (Behind)", "441"],
    ["Tendon Boots (Near Fore)", "433"],
    ["Tendon Boots (Off Fore)", "434"],
    ["Tendon Boots Front", "162"],
    ["Tendon Boots (Front)", "162"],
    ["Tips (all round)", "344"],
    ["Tips (Fronts)", "286"],
    ["Tips (Hind)", "322"],
    ["Tips (Near Fore)", "225"],
    ["Tips (Off Side Front)", "396"],
    ["Tips", "90"],
    ["Toe Clips", "423"],
    ["Tongue Bit", "73"],
    ["Tongue Clip", "442"],
    ["Tongue Control Bit (Lugging)", "151"],
    ["Tongue Control and Lugging Bit", "151"],
    ["Tongue Control Bit (Straight bar rubber)", "275"],
    ["Tongue Control Bit (Victor)", "209"],
    ["Tongue Control Bit (w)", "220"],
    ["Tongue Control Bit", "68"],
    ["Tongue Control", "109"],
    ["Tongue Tie", "60"],
    ["Tongue Tie (variant)", "358"],
    ["Unshod (behind)", "426"],
    ["Unshod (front)", "438"],
    ["Unshod (Behind)", "426"],
    ["Unshod (Front)", "438"],
    ["Visor (Near Side)", "305"],
    ["Visor (Off Side)", "384"],
    ["Visor with one cowl", "443"],
    ["Visor", "61"],
    ["Visors", "267"],
    ["Wedge Pads (Front)", "229"],
    ["Wedge Plates (Front)", "119"],
    ["Wedge Plates (Hind)", "150"],
    ["Wedge Shoe (near hind)", "181"],
    ["Wedge Shoe (Off Side Front)", "270"],
    ["Wedge Shoes (Fore)", "96"],
    ["Wedge Shoes (Hind)", "95"],
    ["Wedge Shoes", "121"],
    ["Wedges (Hind)", "245"],
    ["Weighted shoes (hind)", "252"],
    ["Winker (Near Side)", "63"],
    ["Winker Near Side", "63"],
    ["Winker Off Side", "75"],
    ["Winker (Off Side)", "75"],
    ["Winkers", "62"],
    ["World Racing Plates (Front)", "255"],
    ["World Racing Plates (off fore)", "266"],
    ["World Racing Plates", "168"],
    ["WWR Plates (Fronts)", "244"],
    ["Standard Bit", "1"],
  ]);
  let result = map.get(Id);
  if (result == undefined) {
    console.log(`((ERROR)): Unable to map gear type ${Id}`);
    result = 0;
  }
  return result;
};

const checkIfNZResultFile = async (data) => {
  // Check if data is an object and has a Race property
  if (typeof data !== "object" || data === null || !("Race" in data)) {
    return false;
  }

  // Check if Race is an object and has a Result property
  if (
    typeof data.Race !== "object" ||
    data.Race === null ||
    !("Result" in data.Race)
  ) {
    return false;
  }

  // Check if Result is an array
  if (!Array.isArray(data.Race.Result)) {
    return false;
  }

  // All checks passed, return true
  return true;
};
const extractRating = (ratingString) => {
  if (!ratingString) return '';
  if (Array.isArray(ratingString)) {
    ratingString = ratingString[0];
  } 
  ratingString = ratingString.toString();
  const match = ratingString.match(/\d+/);
  return match ? match[0] : '';
};
const enterMarket = (horses, confidence) => {
  console.log(horses, confidence);
  let ratings = [];
  let tips = [];
  for (let the_horse of horses) {
    if (the_horse.rating)
      ratings.push({
        id: the_horse.id,
        rating: the_horse.rating,
        rawPercent: 0,
        finalPercent: 0,
        price: 0,
      });
    if (!the_horse.scratched && the_horse.tip)
      tips.push({ id: the_horse.id, tip: the_horse.tip });
  }
  ratings.sort((a, b) => a.rating - b.rating);
  let rawPercent = 0;
  let curve = 1;
  let lastrating = 0;
  let lastratingAdj = 0;
  for (let horse of ratings) {
    if (horse.rating <= lastrating + 0.5) {
      lastratingAdj += 0.1;
    } else {
      lastratingAdj = 0;
    }
    lastrating = horse.rating;
    let ratingDiff = horse.rating - ratings[0].rating;
    let ratingadj = 1;
    let furtheradj = 1;
    if (
      ratingDiff >
      ratings[ratings.length - 1].rating - (ratings[0].rating + 2)
    )
      ratingadj = 3.5;
    else if (
      ratingDiff >
      ratings[ratings.length - 1].rating - (ratings[0].rating + 4)
    )
      ratingadj = 2.5;
    else if (
      ratingDiff >
      ratings[ratings.length - 1].rating - (ratings[0].rating + 7)
    )
      ratingadj = 1.5;
    else if (
      ratingDiff <
      ratings[ratings.length - 1].rating - (ratings[0].rating + 10)
    )
      furtheradj = 2;
    else if (
      ratingDiff <
      ratings[ratings.length - 1].rating - (ratings[0].rating + 12)
    )
      furtheradj = 2.5;
    else if (
      ratingDiff <
      ratings[ratings.length - 1].rating - (ratings[0].rating + 15)
    )
      furtheradj = 3;
    else if (
      ratingDiff <
      ratings[ratings.length - 1].rating - (ratings[0].rating + 20)
    )
      furtheradj = 5;
    curve = ratingDiff * (ratingadj + lastratingAdj) + confidence / furtheradj;
    horse.rawPercent = confidence * curve;
    rawPercent += horse.rawPercent;
  }
  const percentadj = (1.1 + ratings.length / 100) / rawPercent;
  let tipPct = 0;
  let tipHorses = [];
  let tipNum =
    ratings.length / 2 < 4
      ? 4
      : ratings.length / 2 > 8
      ? 8
      : Math.round(ratings.length / 2);
  FinalRating: for (let i = 0; i < ratings.length; i++) {
    let horse = ratings[i];
    horse.finalPercent = horse.rawPercent * percentadj * 100;
    horse.price = rationalizeMarket(
      Math.round((100 / horse.finalPercent) * 10) / 10
    );
    horse.finalPercent = Math.round((100 / horse.price) * 2) / 2;
    if (i + 1 > ratings.length - tipNum) {
      if (tips.length > 0)
        for (const tip of tips) if (tip.id === horse.id) continue FinalRating;
      horse.tipPercent = horse.finalPercent * horse.finalPercent;
      tipHorses.unshift(horse);
      tipPct += horse.tipPercent;
    }
  }
  if (tips.length < 4 && tips.length > 0) {
    // reorder tips to move everything up
    tips.sort((a, b) => parseInt(a.tip) - parseInt(b.tip));
    let i = 0;
    for (let tip of tips) {
      i++;
      if (parseInt(tip.tip) !== i) tip.tip = i;
    }
    let j = 0;
    while (i < 4 && i < horses.length - 1) {
      i++;
      tips.push({ id: tipHorses[j].id, tip: i });
      j++;
    }
  } else if (tips.length === 0) {
    if (tipHorses.length === 0) {
      console.warn("No tipHorses available to assign tips.");
    } else {
      let i = 0;
      let maxIterations = 100; // Set a reasonable limit
      while (i < 4 && i < horses.length - 1 && maxIterations > 0) {
        maxIterations--;
        let totalTip = 0;
        let thisTip = Math.random() * tipPct;
        let foundHorse = false;
        tipHorseLoop: for (const horse of tipHorses) {
          for (let tip of tips) if (tip.id === horse.id) continue tipHorseLoop;
          totalTip += horse.tipPercent;
          if (thisTip <= totalTip) {
            i++;
            tips.push({ id: horse.id, tip: i });
            tipPct -= horse.tipPercent;
            foundHorse = true;
            break;
          }
        }
        if (!foundHorse) {
          // No horse found to tip, prevent infinite loop
          console.warn("No eligible horses found to assign tips.");
          break;
        }
      }
    }
  }

  for (let the_horse of horses) {
    for (const horse of ratings) {
      if (the_horse.id == horse.id) {
        the_horse.betting = horse.price;
      }
    }
    for (const horse of tips) {
      if (the_horse.id === horse.id) {
        the_horse.tip = horse.tip;
      }
    }
    if (the_horse.scratched) {
      the_horse.betting = "";
      the_horse.tip = "";
    }
  }
  return horses;
};
const rationalizeMarket = (num) => {
  if (num < 1) num = 501;
  else if (num < 1.1) num = Math.round(num * 100) / 100;
  else if (num < 1.3) num = Math.round(num * 50) / 50;
  else if (num < 2.5) num = Math.round(num * 20) / 20;
  else if (num < 4) num = Math.round(num * 10) / 10;
  else if (num < 5) num = Math.round(num * 5) / 5;
  else if (num < 10) num = Math.round(num * 2) / 2;
  else if (num < 21) num = Math.round(num);
  else if (num < 31) num = Math.round(num / 5) * 5 + 1;
  else if (num < 101) num = Math.round(num / 10) * 10 + 1;
  else if (num < 201) num = Math.round(num / 50) * 50 + 1;
  else if (num < 501) num = Math.round(num / 100) * 100 + 1;
  else if (num > 501) num = 501;
  return num;
};
/**
 * Checks if all races in a meeting are abandoned based on XML data
 * Works with both pre-parsed XML string or post-parsed JavaScript object
 * 
 * @param {string|object} data - Either XML string or parsed JavaScript object
 * @returns {Promise<boolean>} - True if all races are abandoned, false otherwise
 */
const checkIfAllRacesAbandoned = (rawData) => {
  try {
    // Check if there are races in the data
    if (!rawData || !rawData.Races || !rawData.Races.Race) {
      console.log("No races found in the rawData");
      return false;
    }
    
    // Ensure Race is an array (handle single race case)
    const races = Array.isArray(rawData.Races.Race) 
      ? rawData.Races.Race 
      : [rawData.Races.Race];
    
    // If there are no races, we can't determine if all are abandoned
    if (races.length === 0) {
      console.log("Empty races array in rawData");
      return false;
    }
    
    // Check if all races have CurrentRaceStage as "Abandoned"
    const allAbandoned = races.every(race => 
      race["@_CurrentRaceStage"] === "Abandoned"
    );
    
    if (allAbandoned) {
      console.log(`All ${races.length} races are marked as Abandoned`);
    } else {
      // Log how many races are abandoned for debugging
      const abandonedCount = races.filter(race => 
        race["@_CurrentRaceStage"] === "Abandoned"
      ).length;
      
      if (abandonedCount > 0) {
        console.log(`${abandonedCount} out of ${races.length} races are marked as Abandoned`);
      }
    }
    
    return allAbandoned;
    
  } catch (error) {
    console.error("Error in checkIfAllRacesAbandoned:", error);
    return false;
  }
};
const convertExtendedMargin = (margin) => {
  const num = parseFloat(margin);
  if (isNaN(num)) return null;

  const whole = Math.floor(num);
  const fraction = num - whole;

  let adjustedFraction = 0;
  if (fraction < 0.1) {
    adjustedFraction = 0.0;
  } else if (fraction <= 0.35) {
    adjustedFraction = 0.3;
  } else if (fraction <= 0.65) {
    adjustedFraction = 0.5;
  } else if (fraction <= 0.9) {
    adjustedFraction = 0.8;
  } else {
    // round up to next whole number
    return parseFloat((whole + 1).toFixed(1));
  }

  return parseFloat((whole + adjustedFraction).toFixed(1));
};
module.exports = {
  inspectObject,
  readS3File,
  uploadFileS3,
  getSecrets,
  jsonToXml,
  xmlToJs,
  convertDate,
  sDate,
  getDayMonth,
  convertToUTC,
  convertToUTC_TISDB,
  processTrackRating,
  processPrizes,
  processPrizes_NZ,
  processPrizes_HK,
  extractDamName_NZ,
  numberSuffix,
  getPastDateByDay,
  capFstLtr,
  cleverCapFstLtr,
  upperCase,
  processStarts,
  getHorseColor,
  getHorseColorCode,
  getHorseColorAbbr,
  getTrackCondition,
  getTrackGrading,
  getGroup,
  getAgeRestriction,
  getDurationMinutes,
  getHorseAge,
  getHorseSex,
  getOffMargin,
  getWeightType,
  openDBConnection,
  closeDBConnection,
  getCapitalFirstLetterOnly,
  getDualTrack,
  fixDuration,
  checkResultsStage,
  readFile,
  writeFile,
  convertOdds,
  validateXml,
  checkFileTypeCountry,
  checkHongKongFile,
  fixHongKongFile,
  splitNameBySpace,
  getMeetingDate_SGP,
  extractDamName_SGP,
  extractDamName_HK,
  extractHorseName_HK,
  poundsToHalfKilos,
  getStateName,
  generateUniqueRaceId,
  getGroupType,
  getSexRestriction,
  processGearElement,
  sleep,
  isEmptyObject,
  cleanseBarrierTrials,
  calculatePercentage,
  getPace,
  convertNzTrackRating,
  generateRating,
  getMR,
  generateBasicRating,
  logError,
  marginSelect,
  getAgeRestrictionByCode,
  getSexRestrictionByCode,
  setAgeRestriction,
  setSexRestriction,
  formatJockeyRestrictionsByCode,
  getWeightTypeByCode,
  getNzRaceType,
  formatWeightType,
  getShortName,
  getSexShortname,
  getReverseSexShortname,
  cleanupTextForXml,
  runningGears,
  checkMissingGrading,
  compareOldFormTIS,
  mailAlert,
  isolateMissingRuns,
  isNumeric,
  convertCSVtoJSON,
  getBaseRatingByClass,
  hkGearMatch,
  getGearID,
  checkIfNZResultFile,
  decodeHTMLEntities,
  extractRating,
  enterMarket,
  checkIfAllRacesAbandoned,
  convertExtendedMargin
};
