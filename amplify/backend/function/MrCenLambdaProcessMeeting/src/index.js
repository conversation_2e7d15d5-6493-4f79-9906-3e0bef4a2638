const mongoose = require("mongoose");
const moment = require("moment");

const centaur = require("@mediality/centaur");

const getUuid = require("uuid-by-string");
const helper = require("./library/helper");
const AWSXRay = require("aws-xray-sdk-core");
const engine = require("./library/engine");
const normalize = require("./library/normalize");
const wrap = require("@dazn/lambda-powertools-pattern-basic");

AWSXRay.enableAutomaticMode();
mongoose.set("debug", false);

// registering the database connector
var conn_env = "";
var conn = mongoose.connection;

conn.on("connected", function () {
  console.log("Database connection connected successfully");
});
conn.on("disconnected", function () {
  console.log("Database connection close successfully");
});
conn.on("error", console.error.bind(console, "Connection error:"));

exports.handler = wrap(async (event) => {
  // handle the intital event
  // console.log('Initial Event', JSON.stringify(event))
  try {
    if (process.env.AWS_SAM_LOCAL) {
      conn_env = "docker";
    } else if (process.env.LOCAL) {
      conn_env = "localhost";
    }
    // get the registered secret information, for ftp and database login details
    var secretDetails = await centaur.getSecrets(process.env.centaurSecrets);
    //connect to the database
    var connDetails = await centaur.generateConnectionString(
      secretDetails,
      conn_env
    );
    await mongoose.connect(
      connDetails.CONNECTION_STRING,
      connDetails.PARAMETERS
    );
    // determine which stage of the meeting load we are at, and proceed to the correct point of the step function, or further into the meeting load
    switch (event.stage) {
      case "notProcessing":
        // dont process the file if we get to this stage
        event = "";
        return { msg: "Step Function Completed - file not processed" };
      case "genMeetingData":
        // all files get here, genMeetingData determines how to proceed with each file type
        event = await genMeetingData(event);
        if (event == "GearChangesProcessed") {
          return "Gear Changes Completed";
        }
        if (event == "RegistrationAUProcessed") {
          return "Registration AU Files Completed";
        }
        if (event == "RatingsProcessed") {
          return "Ratings AU Files Completed";
        }
        if (event == "SectionalsProcessed") {
          return "Sectionals AU Files Completed";
        }
        if (event == "ScratchingsProcessed") {
          return "Scratchings Completed";
        }
        if (event == "ResultsProcessed") {
          return "Results Completed";
        }
        event.stage = "genRaceData";
        break;
      case "genRaceData":
        // standard meeting file, this is the individual race stage
        // console.log("Entering in Generating Race Data via Handler")
        event = await genRaceData(event);
        return { msg: "Step Function Completed" };
      default:
        // assess a received file and gather information about it
        // console.log("Entering in Create Meeting via Handler")
        event = await createMeeting(event);
        event.stage =
          event.stage == "notProcessing" ? "notProcessing" : "genMeetingData";
    }

    try {
      if ("stage" in event) {
        return event;
      } else {
        event.stage = "notProcessing";
        return event;
      }
    } catch (err) {
      console.log(err);
      event.stage = "notProcessing";
      return event;
    }
  } catch (err) {
    console.log(err);
    const response = {
      statusCode: 500,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "*",
      },
      body: JSON.stringify("Internal Server Error"),
    };
    return response;
  } finally {
    // await conn.close()
    console.log(`Mongoose Connection State: ${conn.readyState}`);
  }
});

const createMeeting = wrap(async (event) => {
  console.log("Entering createMeeting method!");
  console.log(JSON.stringify(event));
  // With a brand new file, we parse the file and determine how to proceed. creating temp meeting if the file is to be loaded.
  try {
    var segment = new AWSXRay.Segment("createMeeting");
    let xmlData;
    let decodedKey = decodeURIComponent(event["s3"]["object"]["key"]);
    decodedKey = decodedKey.replace(/\+/g, " ");
    try {
      console.log("decoded key is", decodedKey);
      xmlData = await helper.readS3File(
        decodedKey,
        event["s3"]["bucket"]["name"]
      );
    } catch (err) {
      console.error("S3 Error:", err);
      if (err.code === "NoSuchKey") {
        console.error(`S3 Key not found: ${event["s3"]["object"]["key"]}`);
      }
      throw err;
    }
    var isJSON = false;
    var isCSV = false;
    var csvData = {};
    //Validate XML
    var valid = await helper.validateXml(xmlData);
    if (valid == true) {
      console.log("XML is valid");
    } else {
      try {
        if (JSON.parse(xmlData)) {
          isJSON = true;
          console.log("its JSON!");
        }
      } catch (err) {
        console.log("not json either", err);
      }
      if (!isJSON) {
        if (event["s3"]["object"]["key"].includes(".csv")) {
          csvData = helper.convertCSVtoJSON(xmlData);
          isCSV = true;
        }
        if (!isCSV) {
          console.log("Invalid XML received");
          process.exit(1);
        }
      }
    }

    // Validate and Fix HongKong File (Raw HK file can't be converted into JSON)
    if (helper.checkHongKongFile(xmlData)) {
      xmlData = helper.fixHongKongFile(xmlData);
    }
    // determine if file is JSON or XML -
    var jsData = "";
    if (isJSON) {
      jsData = JSON.parse(xmlData);
    } else if (isCSV) {
      jsData = csvData;
    } else {
      jsData = await helper.xmlToJs(xmlData);
    }
    if (await helper.checkIfNZResultFile(jsData)) {
      console.log("yes for NZ results");

      return await engine.processNZResults(
        jsData,
        decodedKey,
        event["s3"]["bucket"]["name"]
      );
    }
    // Identify RA, SG, HK, SGP files
    var fileTypeCountry = await helper.checkFileTypeCountry(jsData);
    console.log(fileTypeCountry);
    if (fileTypeCountry.meetingStage == "Sectionals") {
      const sectTrack = await centaur.tracks
        .findOne({ TRK_TRACK_NAME: fileTypeCountry.trackName })
        .lean();
      if (sectTrack) {
        fileTypeCountry.venueAbbr = sectTrack.TRK_RSB_TRACK_ABBREV;
      } else {
        // create trigger for alert email
        var sendMail = await helper.mailAlert(
          `Probelem with sectional CSV for ${fileTypeCountry.trackName}`,
          `Track couldnt be matched to our database based on the name`
        );
        console.log(sendMail);
        event.stage = "notProcessing";
        return event;
      }
    }
    const meetingStage = fileTypeCountry.meetingStage;
    const meetingDate = fileTypeCountry.meetingDate;
    const venueAbbr = fileTypeCountry.venueAbbr;
    const trackName = fileTypeCountry.trackName;
    const country = fileTypeCountry.country;
    const category = fileTypeCountry.category;
    const env = process.env.ENV;
    console.log("meeting Date ", meetingDate);
    // Define the date range
    const startDate = moment().subtract(2, "days").startOf("day");
    const endDate = moment().add(7, "days").endOf("day");
    const dateFormats = ["YYYY-MM-DD", "DD MMM YYYY", "DD/MM/YYYY"];
    const meetingMoment = moment(meetingDate, dateFormats, true);

    if (!meetingMoment.isBetween(startDate, endDate, undefined, "[]")) {
      const fileKey = event["s3"]["object"]["key"];
      let fileName = fileKey.split("/").pop().split(".")[0];
      const startsWithRatingsOrRatingOrContainsRego =
        /^(ratings?|.*rego|.*jumpout|.*gear|.*scratchings?)/i.test(fileName);
      if (!startsWithRatingsOrRatingOrContainsRego) {
        fileName = fileName.replace(
          /s3___mr-cen-file-storage-prd_unprocessed_/,
          ""
        );
        console.log(
          `Meeting Date Out of Range: ${fileName} is out of the specified date range (${startDate.format(
            "YYYY-MM-DD"
          )} to ${endDate.format("YYYY-MM-DD")}). Please check...`
        );
        await helper.mailAlert(
          "Meeting Date Out of Range",
          `${fileName} is out of the specified date range (${startDate.format(
            "YYYY-MM-DD"
          )} to ${endDate.format("YYYY-MM-DD")}). Please check...`,
          "prod"
        );
      }
    }
    var theUUID = `${meetingDate}-${venueAbbr}`;
    // Generate Unique UUID for the meeting
    if (category == "trial") {
      theUUID = `${meetingDate}-${venueAbbr}-trial`;
    }
    var meetingId = await getUuid(theUUID);
    // generate the history item for the meeting load history
    var historyitem = {
      time: moment().toISOString(),
      bucket: event["s3"]["bucket"]["name"],
      file_path: event["s3"]["object"]["key"],
      trigger:
        event["eventName"] == "UserTrigger" ? "User Triggered" : "New File",
    };
    // check if meeting exists in TEMP
    var checkIfMeetTemp = await centaur.temp_meetings
      .findOne({ _id: meetingId })
      .lean();

    // check if meeting exists in DB
    var checkIfMeet = await centaur.processed_meetings
      .findOne({ _id: meetingId })
      .lean();
    if (checkIfMeetTemp && !checkIfMeet) {
      console.log("This happened because of failure of meeting load");
      await helper.mailAlert(
        `Meeting Failed to Load because of temp meeting exist`,
        `meeting date ${checkIfMeetTemp?.meetingDate} id ${checkIfMeetTemp._id}`,
        "prod"
      );
    }
    // console.log(meetingId,checkIfMeet)
    if (!checkIfMeet && category != "trial") {
      // check if its a meeting created when stg was primary env, then do it all again
      theUUID = `${meetingDate}-${venueAbbr}-stg`;
      var meetingIdAgain = await getUuid(theUUID);
      checkIfMeet = await centaur.processed_meetings
        .findOne({ _id: meetingIdAgain })
        .lean();
      if (checkIfMeet) {
        meetingId = meetingIdAgain;
      }
    }
    // IF Meeting exists, check if its unlocked, and if not, abandon and notify ((ALERT))
    if (
      checkIfMeetTemp ||
      (checkIfMeet &&
        checkIfMeet.meetingLocked &&
        checkIfMeet.meetingLocked != "unlocked")
    ) {
      // create NOT LOADED history item
      var history = checkIfMeet?.meetingLoadHistory ?? [];
      historyitem.trigger = "NOT LOADED";
      history.unshift(historyitem);
      var dataToUpdate = {};
      dataToUpdate["meetingLoadHistory"] = history;
      // generate frontend error notifications
      var errors = checkIfMeet.processedMeetingData.meeting.errors;
      if (checkIfMeet.meetingLocked != "unlocked") {
        errors.push(
          `${historyitem.file_path} didn't load because meeting was locked.`
        );
      } else {
        errors.push(
          `${historyitem.file_path} didn't load because a temp meeting already exists.`
        );
      }
      var meetingErrorCount = checkIfMeet.meetingErrorCount + 1;
      dataToUpdate["meetingErrorCount"] = meetingErrorCount;
      dataToUpdate["processedMeetingData.meeting.errors"] = errors;
      // update meeting for frontend errors and not loaded history item
      let theUpdate = await centaur.processed_meetings.updateOne(
        { _id: meetingId },
        {
          $set: dataToUpdate,
        }
      );
      // create trigger for alert email
      // var sendMail = await helper.mailAlert('meeting not loaded',`Error Updating meeting file ${historyitem.file_path} ${meetingId}`,(process.env.ENV == 'prd' ? 'alert' : 'error'))
      // console.log(sendMail)
      console.log(
        `((ALERT)): ABANDONING MEETING LOAD - file in progress ${historyitem.file_path}`
      );
      event.stage = "notProcessing";
      return event;
    } else if (checkIfMeet) {
      // meeting is good to load, add the history item to meeting
      var history = checkIfMeet?.meetingLoadHistory ?? [];
      history.unshift(historyitem);
      let theUpdate = await centaur.processed_meetings.updateOne(
        { _id: meetingId },
        {
          $set: { meetingLoadHistory: history, meetingLocked: "load" },
        }
      );
    }
    console.log("Meeting Id: " + meetingId);
    // Check meeting Lock status AGAIN
    // if (checkIfMeet && checkIfMeet.meetingLocked && checkIfMeet.meetingLocked != "unlocked"){
    //     console.log(`ABANDONING MEETING LOAD - edit in progress`)
    //     event.stage = 'notProcessing'
    //     return event
    // }
    // Verify if old meeting stage file received
    let status = await engine.checkMeetingStage(meetingId, meetingStage);
    console.log(meetingStage, status);
    if (!status) {
      // if meeting stage is old, abort the step function gracefully, and log
      console.log(`OLD MEETING STAGE - ${meetingStage} FILE RECEIVED`);
      let theUpdate = await centaur.processed_meetings.updateOne(
        { _id: meetingId },
        {
          $set: { meetingLocked: "unlocked" },
        }
      );
      event.stage = "notProcessing";
      return event;
    }
    event.meetingStage = meetingStage;
    if (checkIfMeet) {
      if (
        (["Nominations", "Weights", "DELETED"].includes(
          checkIfMeet.meetingStage
        ) ||
          category == "trial") &&
        ["GearChanges", "Scratchings", "RiderUpdates"].includes(meetingStage)
      ) {
        // we dont need gears prior to accepts, abort gracefully
        console.log(
          `Update only file not loaded. meetStage: ${checkIfMeet.meetingStage} filetype: ${meetingStage} category: ${category}`
        );
        let theUpdate = await centaur.processed_meetings.updateOne(
          { _id: meetingId },
          {
            $set: { meetingLocked: "unlocked" },
          }
        );
        event.stage = "notProcessing";
        return event;
      }
      // create the temp meeting if everythings been good so far
      var updateRecord = new centaur.temp_meetings(checkIfMeet);
      var d = await updateRecord.save();
      if (category == "trial") {
        // we dont get trial accepts from TIS, so we need to manually set the stage
        if (checkIfMeet.meetingStage == "Acceptances") {
          event.meetingStage = "Results";
        } else if (checkIfMeet.meetingStage == "DELETED") {
          event.meetingStage = "Acceptances";
        }
      }
    } else if (
      [
        "Nominations",
        "Weights",
        "Acceptances",
        "Finalfields",
        "FinalFields",
      ].includes(meetingStage) &&
      category != "trial"
    ) {
      // brand spanking new meeting, we have the right files to create it too, lets make the temp meeting
      var newRecord = new centaur.temp_meetings({ _id: meetingId });
      var d = await newRecord.save();
      // } else if (category == 'trial' && country == 'TISDB'){
    } else if (category == "trial") {
      console.log(country);
      // brand spanking new trial, lets create the temp meeting from the results (but as acceptances)
      if (
        country != "HK" &&
        meetingStage != "Results" &&
        (!fileTypeCountry.phase || !fileTypeCountry.phase.includes("Results"))
      ) {
        // we arent processing trials before results, we can change this later
        console.log(
          "not processing trial file" +
            meetingDate +
            " " +
            venueAbbr +
            " at the " +
            meetingStage +
            " stage."
        );
        event.stage = "notProcessing";
        return event;
      }
      event.meetingStage = "Acceptances";
      var newRecord = new centaur.temp_meetings({ _id: meetingId });
      var d = await newRecord.save();
    } else if (!["RegistrationAU", "Ratings"].includes(meetingStage)) {
      // this is not a file type we can create a meeting with, or one for DB updates, so lets exit gracefully
      console.log(
        `MEETING STAGE ${meetingStage} RECEIVED AS NEW MEETING, ABORTING`
      );
      event.stage = "notProcessing";
      return event;
    }

    if (d) {
      console.log(
        `created temp meeting for ${event["s3"]["object"]["key"]} ${meetingId}`
      );
    }

    // Uploads the JSON representation to the S3, helps with large objects
    const bucketName = `mr-cen-file-storage-${process.env.ENV}`;
    const filename = `processed/${meetingId}.json`;
    await helper.uploadFileS3(bucketName, filename, jsData);
    // if (category == 'trial' && (meetingStage != 'Results' && (!fileTypeCountry.phase || !fileTypeCountry.phase.includes('Results')))) {
    //     // we arent processing trials before results, we can change this later
    //     console.log('not processing trial file' + meetingDate + ' ' + venueAbbr + ' at the ' + meetingStage + ' stage.')
    //     event.stage = 'notProcessing'
    //     return event
    // }

    // create the params for the processed_meeting load
    event.stage = "genMeetingData";
    event.country = country; // AUS SGP HK NZ ((TISDB))
    event.meetingDate = meetingDate;
    event.category = category;
    event.meetingData = {
      meetingStage: meetingStage,
      meetingId: meetingId,
      body: "File Processed and Received data converted into json and saved",
      historyitem: historyitem,
    };
    // move to the next phase of the step function
    return event;
  } catch (err) {
    console.log("Create Meeting Error : " + err);
    segment.addError(err);
  } finally {
    segment.close();
  }
});

const genMeetingData = wrap(async (event) => {
  // Determine how to process the file based on params
  try {
    console.log("genMeetingData", JSON.stringify(event));
    var segment = new AWSXRay.Segment("genMeetingData");
    const meetingId = event.meetingData.meetingId;
    const meetingStage = event.meetingData.meetingStage;
    const country = event.country;
    const category = event.category;
    const bucketName = `mr-cen-file-storage-${process.env.ENV}`;
    const fileName = `processed/${meetingId}.json`;
    console.log("Reading Processed file: " + bucketName + "/" + fileName);
    var rawData = JSON.parse(await helper.readS3File(fileName, bucketName));

    if ("meetingId" in event["meetingData"]) {
      // Normalise Meeting Level Data as per country
      // normalized files (Noms, weights, accepts, finalfields) need to be rationalized
      // other files process through the engine
      switch (event.country) {
        case "AUS":
          if (event.meetingStage == "GearChanges") {
            processGearChanges = await engine.processGearChanges(
              rawData,
              meetingId
            );
            return "GearChangesProcessed";
          } else if (event.meetingStage == "Scratchings") {
            processScratchings = await engine.processScratchings(
              rawData,
              meetingId
            );
            return "ScratchingsProcessed";
          } else if (event.meetingStage == "RegistrationAU") {
            processRegistration = await engine.processRegistrationAU(rawData);
            return "RegistrationAUProcessed";
          } else if (event.meetingStage == "Ratings") {
            processRatings = await engine.processRatings(rawData);
            return "RatingsProcessed";
          } else if (
            event.meetingStage == "Results" ||
            event.meetingStage == "InterimResults"
          ) {
            processResults = await engine.processResults(
              rawData,
              meetingId,
              category
            );
            return "ResultsProcessed";
          } else if (event.meetingStage == "Sectionals") {
            processResults = await engine.processSectionals(
              rawData,
              meetingId,
              category
            );
            return "SectionalsProcessed";
          } else {
            if (category == "trial") {
              normalizedData = await normalize.normalizeMeetingData_AUS(
                rawData,
                meetingId,
                "Acceptances",
                event.country,
                "trial"
              );
            } else {
              normalizedData = await normalize.normalizeMeetingData_AUS(
                rawData,
                meetingId,
                meetingStage,
                event.country,
                category
              );
            }
          }
          break;
        case "NZ":
          console.log("Normalizing Meeting Level Data for New Zealand");
          var existingmeeting = await centaur.processed_meetings
            .findOne({ _id: meetingId })
            .lean();
          if (
            existingmeeting &&
            meetingStage == "Acceptances" &&
            existingmeeting.meetingStage != "DELETED"
          ) {
            processScratchings = await engine.processNZUpdates(
              rawData,
              meetingId
            );
            return "ScratchingsProcessed";
          } else if (existingmeeting && meetingStage == "Results") {
            processScratchings = await engine.processNZResults(
              rawData,
              meetingId
            );
            return "ResultsProcessed";
          } else {
            normalizedData = await normalize.normalizeMeetingData_NZ(
              rawData,
              meetingId,
              meetingStage,
              event.country
            );
          }
          break;
        case "SGP":
          console.log("Normalizing Meeting Level Data for Singapore");
          normalizedData = await normalize.normalizeMeetingData_SGP(
            rawData,
            meetingId,
            meetingStage,
            event.country
          );
          break;
        case "HK":
          console.log("Normalizing Meeting Level Data for HongKong");
          normalizedData = await normalize.normalizeMeetingData_HK(
            rawData,
            meetingId,
            meetingStage,
            event.country,
            category
          );
          break;
        case "TISDB":
          // TIS files were a fun special case as they cover all countries, and have a few of thier own rules
          if (event.meetingStage == "Results") {
            // processResults = await engine.processResults_TISDB(rawData, meetingId, category)
            processResults = await engine.processResults_TISDB(
              rawData,
              meetingId,
              category
            );
            return "ResultsProcessed";
          } else if (category == "trial") {
            normalizedData = await normalize.normalizeMeetingData_TISDB(
              rawData,
              meetingId,
              "Acceptances",
              event.country,
              "trial"
            );
          } else {
            normalizedData = await normalize.normalizeMeetingData_TISDB(
              rawData,
              meetingId,
              meetingStage,
              event.country,
              category
            );
          }
          break;
        default:
          // if it doesnt match a country, we cant load the file, exiting gracefully
          console.log(`((ERROR)): missing country in meeting ${meetingId}`);
          await exitGracefully(meetingId);
          event.stage = "notProcessing";
          return event;
      }
      if (
        normalizedData.eventStatus &&
        normalizedData.eventStatus == "notProcessing"
      ) {
        await exitGracefully(meetingId);
        event.stage = "notProcessing";
        return event;
      }
      // Now that the meeting is normalized, we can process the meeting level data and prep the races
      var raceArray = await engine.genMeetingLevelData(
        normalizedData,
        meetingId,
        meetingStage,
        country,
        event.meetingData.historyitem
      );
      event.stage = "genRaceData";
      event.country = country;
      event.meetingStage = meetingStage;
      // determine wether to add silks urls to horses
      if (raceArray.silksReady) {
        for (race of raceArray["races"]) {
          race.silksReady = true;
        }
      }
      event.meetingData.races = raceArray["races"];
      event.category = category;
      event.silksReady = raceArray.silksReady ?? false;
    } else {
      var body = "Malformed Event Received, Check input event data";
      console.log(event);
      event = { status: 500, body: body };
    }
    // meeting level done, race normalization next
    // moving to the races part of the step function
    return event;
  } catch (err) {
    console.log(err);
    segment.addError(err);
  } finally {
    segment.close();
  }
});

const genRaceData = wrap(async (event) => {
  // this fires simultaneously for each race from the step function
  console.log("genRaceData event", JSON.stringify(event));
  var normalizedRaceData = "";
  try {
    var segment = new AWSXRay.Segment("genRaceData");
    var meetingId = event.meetingId;
    var country = event.country;

    var bucketName = `mr-cen-file-storage-${process.env.ENV}`;
    var fileName = `processed/${meetingId}.json`;
    var rawData = JSON.parse(await helper.readS3File(fileName, bucketName));

    if (
      ("raceId" in event && "eventId" in event && event["eventId"] == "") ||
      ("index" in event && "ra_id" in event && "NominationNumber" in event)
    ) {
      // Normalise Race Level Data as per country
      switch (event.country) {
        case "AUS":
          normalizedRaceData = await normalize.normalizeRaceData_AUS(
            rawData,
            event.meetingStage,
            event.meetingId,
            event.category,
            event.silksReady,
            event.index,
            event.ra_id,
            event.NominationNumber
          );
          break;
        case "NZ":
          normalizedRaceData = await normalize.normalizeRaceData_NZ(
            rawData,
            event.meetingStage,
            event.meetingId,
            event.raceId
          );
          break;
        case "SGP":
          normalizedRaceData = await normalize.normalizeRaceData_SGP(
            rawData,
            event.meetingStage,
            event.meetingId,
            event.raceId
          );
          break;
        case "HK":
          normalizedRaceData = await normalize.normalizeRaceData_HK(
            rawData,
            event.meetingStage,
            event.meetingId,
            event.raceId
          );
          break;
        case "TISDB":
          normalizedRaceData = await normalize.normalizeRaceData_TISDB(
            rawData,
            event.meetingStage,
            event.meetingId,
            event.raceId,
            event.fileraceno,
            event.category,
            event.silksReady
          );
          break;
        default:
          // country cannot be identified for a mracem, this is epically problematic
          console.log(`((ERROR)): missing country in meeting ${meetingId}`);
          await exitGracefully(meetingId);
          event.stage = "notProcessing";
          return event;
      }
      if (
        normalizedRaceData.eventStatus &&
        normalizedRaceData.eventStatus == "notProcessing"
      ) {
        await exitGracefully(meetingId);
        event.stage = "notProcessing";
        return event;
      }
      // Race data having been normalised, now enter race data into meeting
      var result = await engine.genRaceLevelData(
        normalizedRaceData,
        event.meetingId,
        event.ra_id
      );
      response = result;
    } else {
      var body = "Malformed Event Received, Check input event data";
      response = { status: 500, body: body };
    }
    // send confirmation back to step function
    return response;
  } catch (err) {
    console.log(`Race ${event.raceId} Generation Error: ${err}`);
    segment.addError(err);
  } finally {
    segment.close();
  }
});

const exitGracefully = async (meetingId) => {
  let theUpdate = await centaur.processed_meetings
    .updateOne(
      { _id: meetingId },
      {
        $set: { meetingLocked: "unlocked" },
      }
    )
    .lean();
  let theDelete = await centaur.temp_meetings
    .deleteOne({ _id: meetingId })
    .lean();
  return [theUpdate, theDelete];
};
