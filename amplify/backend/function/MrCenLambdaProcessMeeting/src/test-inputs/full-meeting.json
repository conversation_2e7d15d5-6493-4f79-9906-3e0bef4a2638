{"eventVersion": "2.1", "eventSource": "aws:s3", "awsRegion": "ap-southeast-2", "eventTime": "2022-04-06T23:28:56.712Z", "eventName": "ObjectCreated:Put", "userIdentity": {"principalId": "AWS:AROAQ26BXZGMOUH276ZSO:Mr<PERSON>enLambdaPollerFunction-puck"}, "requestParameters": {"sourceIPAddress": "*************"}, "responseElements": {"x-amz-request-id": "RFFCF1HW716WR7CQ", "x-amz-id-2": "/L6HVlMuJ7S1mYKMMT9PYi0oJCDAGswNoSAOB8TXhMqEs9woMP9q94kdgXW4wqBPuEkF3ztP4FvMjxeYtTvpJ5v71/Ju0dtt"}, "s3": {"s3SchemaVersion": "1.0", "configurationId": "a5911392-db5e-4e78-9ceb-2a19bf311d40", "bucket": {"name": "mr-cen-file-storage-puck", "ownerIdentity": {"principalId": "A3923B8EIH46M9"}, "arn": "arn:aws:s3:::mr-cen-file-storage-puck"}, "object": {"key": "unprocessed/20220323_Ascot_WA_Professional_Results.xml", "size": 303712, "eTag": "6d183d445dbc16dbb683d78bfb04bbc2", "sequencer": "00624E2238988EF2E8"}, "fileType": "results"}, "messageId": "c0e54e7e-7ad8-44b7-b7cf-ae0bf5815643", "stage": "genRaceData", "country": "AUS", "meetingDate": "2022-03-23", "meetingStage": "Results", "category": "", "meetingData": {"meetingStage": "Results", "meetingId": "ac2f77f9-8234-5c4e-93c6-539f2823470d", "body": "File Processed and Received data converted into json and saved", "races": [{"raceId": "1", "meetingId": "ac2f77f9-8234-5c4e-93c6-539f2823470d", "stage": "genRaceData", "meetingStage": "Results", "country": "AUS"}, {"raceId": "2", "meetingId": "ac2f77f9-8234-5c4e-93c6-539f2823470d", "stage": "genRaceData", "meetingStage": "Results", "country": "AUS"}, {"raceId": "3", "meetingId": "ac2f77f9-8234-5c4e-93c6-539f2823470d", "stage": "genRaceData", "meetingStage": "Results", "country": "AUS"}, {"raceId": "4", "meetingId": "ac2f77f9-8234-5c4e-93c6-539f2823470d", "stage": "genRaceData", "meetingStage": "Results", "country": "AUS"}, {"raceId": "5", "meetingId": "ac2f77f9-8234-5c4e-93c6-539f2823470d", "stage": "genRaceData", "meetingStage": "Results", "country": "AUS"}, {"raceId": "6", "meetingId": "ac2f77f9-8234-5c4e-93c6-539f2823470d", "stage": "genRaceData", "meetingStage": "Results", "country": "AUS"}, {"raceId": "7", "meetingId": "ac2f77f9-8234-5c4e-93c6-539f2823470d", "stage": "genRaceData", "meetingStage": "Results", "country": "AUS"}, {"raceId": "8", "meetingId": "ac2f77f9-8234-5c4e-93c6-539f2823470d", "stage": "genRaceData", "meetingStage": "Results", "country": "AUS"}, {"raceId": "9", "meetingId": "ac2f77f9-8234-5c4e-93c6-539f2823470d", "stage": "genRaceData", "meetingStage": "Results", "country": "AUS"}]}}