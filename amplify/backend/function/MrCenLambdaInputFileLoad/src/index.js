const AWS = require('aws-sdk');
const s3 = new AWS.S3();

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */


exports.handler = async (event) => {
    console.log(`EVENT: ${JSON.stringify(event)}`);

    const key = event.Records[0].s3.object.key;
    const sourceBucket = event.Records[0].s3.bucket.name;
    const env = process.env.ENV;
    const destinationBucket = `mr-cen-file-storage-${env}`;

    const keyname = replaceKeyValues(key)
    console.log("keyname",keyname)
    const suffix = keyname.replace(/^.+?(\..{3})$/, '$1')
    var s3filename = keyname.replace(/^(.+?)\..{3}$/, '$1')
    s3filename = s3filename.replace(/\)|\(|\s|,|\./g, '_')
    s3filename = s3filename.replace(/_+/g, '_')

    const destinationKey = `unprocessed/${s3filename}${suffix}`;
    const copySource = encodeURIComponent(sourceBucket + '/' + keyname);

    try {
        const response = await s3.copyObject({
            CopySource: copySource,
            Bucket: destinationBucket,
            Key: destinationKey
        }).promise();
        console.log(`File copied successfully. Destination key: ${destinationKey} ${JSON.stringify(response, null, 2)}`);
        
    } catch (error) {
        console.log("Error copying the file to the destination bucket:", error.message);
    }

    return {
        statusCode: 200,
        body: JSON.stringify('Hello from Lambda!'),
    };
};


const replaceKeyValues = (keyname) =>{
    var newKey = decodeURIComponent(keyname);
    console.log(newKey)
    newKey = newKey.replace(/\+/g," ")
    return newKey
}



// {
//     "Records": [
//         {
//             "eventVersion": "2.1",
//             "eventSource": "aws:s3",
//             "awsRegion": "ap-southeast-2",
//             "eventTime": "2024-05-09T06:53:23.370Z",
//             "eventName": "ObjectCreated:Put",
//             "userIdentity": {
//                 "principalId": "AWS:AIDA2F6JZA3B54IAOZIR3"
//             },
//             "requestParameters": {
//                 "sourceIPAddress": "**************"
//             },
//             "responseElements": {
//                 "x-amz-request-id": "0BCFQJDV5NM21MBZ",
//                 "x-amz-id-2": "Ud95khSoQ3T6BUYUuivjipHO6F7JXI3i9QXpnuysM3EAyaZBccdm52PjrVD47UBZDiFd5qUy5U8ecR7x8u+bXUntZ9D6rLLC0M8wYoVKEt8="
//             },
//             "s3": {
//                 "s3SchemaVersion": "1.0",
//                 "configurationId": "s3-file-test",
//                 "bucket": {
//                     "name": "mr-cen-test-bucket",
//                     "ownerIdentity": {
//                         "principalId": "A2VS8IMAKDPYIV"
//                     },
//                     "arn": "arn:aws:s3:::mr-cen-test-bucket"
//                 },
//                 "object": {
//                     "key": "Ver4_2024_05_11_Raci_M2_Rotorua.xml",
//                     "size": 3367829,
//                     "eTag": "4048aa4933633871e6a57beede25b851",
//                     "sequencer": "00663C72E1B43CE44C"
//                 }
//             }
//         }
//     ]
// }