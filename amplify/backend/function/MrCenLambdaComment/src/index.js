const mongoose = require('mongoose');
const getUuid = require('uuid-by-string')
const helper = require("./library/helper")
const run_rules = require("./library/run_rules")
const AWSXRay = require("aws-xray-sdk-core")
const centaur = require('@mediality/centaur');

var con


AWSXRay.enableAutomaticMode();


exports.handler = async (event) => {
    var meeting_id = ''

    // if (process.env.ENV === 'prd'){
    //   await helper.closeDBConnection(con)
    //   const response = {
    //       statusCode: 200,
    //       headers: { "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Headers": "*" },
    //       body: JSON.stringify("Not on production yet please")
    //   }
    //   return response
    // }
    
    try {
        con = await helper.openDBConnection()
        var req = {
            path: event.path,
            method: event.httpMethod,
            params: event.queryStringParameters,
            body: event.body
        }
        console.log(req)
        if (req.params && req.params.meeting_id){
          meeting_id = req.params.meeting_id
        } else {
          return { statusCode: 400, body: 'No meeting ID found' }
        }
        // var meeting = await centaur.processed_meetings.findOne({_id: req.params.meeting_id}).lean();
        var meeting = await centaur.processed_meetings.findOne({_id: meeting_id}).lean();
        
        var processedMeetingData = meeting.processedMeetingData.meeting;
        var inputMeetingData = meeting.inputMeetingData;
        var races = processedMeetingData.races.race;
        console.log('races')
        for (var i=0;i<races.length;i++){
          var marketArray = []
          // i = 2
          var horses = races[i].horses.horse;
          var starters = helper.getRaceStarters(horses)
          for (const horse of horses){
            if (!horse.scratched && horse.betting)
              marketArray.push({ "horse_id" : horse['@_id'], "betting" : parseFloat(horse['betting']) })
          }
          marketArray.sort((a,b) => a.betting - b.betting)
          const raceInputIndex = meeting.inputMeetingData.races.race.findIndex((element) => element["@_id"] === races[i]["@_id"]);
          try{
            for (var j=0;j<horses.length;j++){
              if (!horses[j].scratched && horses[j].betting){
                const inputIndex = meeting.inputMeetingData.races.race[raceInputIndex].horses.horse.findIndex((element) => element["@_id"] === horses[j]["@_id"]);
                var writeComment = await generateCommentForHorse(meeting,races[i],i,marketArray,horses[j],j,inputIndex,raceInputIndex,starters)
                console.log(`comment for horse ${horses[j]['@_name']}, race ${races[i]} written: ${writeComment}` )
              }
            }

          } catch(err){
            console.log(err)
          }
        }



        // console.log(meeting)


        const response = {
            statusCode: 200,
            headers: { "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Headers": "*" },
            body: JSON.stringify(writeComment)
        }
        return response

    } catch (err) {
        console.log("Error in Handler: " + err)
        await helper.closeDBConnection(con)
        return { statusCode: 500, body: JSON.stringify(err.message) };
    } finally {
        await helper.closeDBConnection(con)
        const response = {
            statusCode: 200,
            headers: { "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Headers": "*" },
            body: JSON.stringify(writeComment)
        }
        return response
    }
};

const generateCommentForHorse = async (meeting,race,raceIndex,marketlist,horse,horseIndex,inputIndex,raceInputIndex,starters) => {
  var horseForm = await centaur.form.findOne({ horse_id: horse['@_id'] }).lean();
  console.log(`generating comment for horse: ${horse['@_name']}`)

  //run form intro rules first
  var formIntroRule = await centaur.comments.findOne({Id:27}).lean();
  var formIntro = run_rules.runFormIntroRules(horse,horseForm,formIntroRule,meeting)
  formIntro = formIntro.reverse()

  //compiled form with intros
  var compiledFormComment = formIntro.join(" ")
  var compiledComment = ''


  // run market 'predictive' rules
  var marketRule = await centaur.comments.findOne({Id:25}).lean();
  var predictiveComment = run_rules.runPredictiveRules(horse,race,marketlist,marketRule)
  
  // run summary rules
  var sentenceArray = []
  var summaryRules = await centaur.comments.find({ Id:{$lt:25} }).lean();
  var rulegroupsused = []
  summaryFor: for (ruleGroup of summaryRules){
    if (ruleGroup.Exclusive){
      for (grp of ruleGroup.Exclusive) if (rulegroupsused.includes(grp)) continue summaryFor
    } 
    
    var sentencesFromGroup = await run_rules.runSummaryRules(meeting,race,horse,horseForm,ruleGroup,starters)
    if (sentencesFromGroup != ''){
      if (predictiveComment.direction === sentencesFromGroup.direction || sentencesFromGroup.direction === 0){
        sentenceArray.push({"ruleGroup":ruleGroup.Id,"rank":ruleGroup.Order, "sentence":sentencesFromGroup.sentence})
        rulegroupsused.push(ruleGroup.Id)
      }
      
    }
  }

  // sort comment into 'ranks'
  sentenceArray.sort((a,b) => a.rank - b.rank)
  

  for (returned_sentence of sentenceArray){
    compiledComment = compiledComment.trim() + " " + returned_sentence.sentence.trim()
  }
  console.log(compiledComment)

  var theComment = compiledComment.trim() + " " + predictiveComment.sentence.trim()
  theComment = theComment.replace(/ +/g, " ")
  var addCommentToHorse = await updateDbHorseComment(meeting._id,raceIndex,horseIndex,inputIndex,raceInputIndex,theComment,compiledFormComment)

 return addCommentToHorse
};



const updateDbHorseComment = async (meeting_id,raceIndex,horseIndex,inputIndex,raceInputIndex,comment,formcomment) =>{
  console.log(`updating comment for horse ${raceIndex} in race ${horseIndex} input ${inputIndex}`)



  var horseToUpdate = "processedMeetingData.meeting.races.race." + raceIndex + ".horses.horse." + horseIndex + ".comment"
  var inputToUpdate = "inputMeetingData.races.race." + raceInputIndex + ".horses.horse." + inputIndex + ".comment"
  var horseToUpdateForm = "processedMeetingData.meeting.races.race." + raceIndex + ".horses.horse." + horseIndex + ".form_comments"
  var inputToUpdateForm = "inputMeetingData.races.race." + raceInputIndex + ".horses.horse." + inputIndex + ".form_comment"

  var dataToUpdate = {}
  dataToUpdate[horseToUpdate] = comment
  dataToUpdate[inputToUpdate] = comment
  dataToUpdate[horseToUpdateForm] = formcomment
  dataToUpdate[inputToUpdateForm] = formcomment


  var status = await centaur.processed_meetings.updateOne({ _id: meeting_id }, {
    "$set": dataToUpdate
  }).lean()
            
  return true
}