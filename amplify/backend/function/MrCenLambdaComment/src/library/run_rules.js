const util = require('util');
const moment = require('moment')
const helper = require('./helper')
const form_intro = require('./rules/form_intro')
const markets = require('./rules/markets');
const trials = require('./rules/trials');
const weight = require('./rules/weight');
const jumps = require('./rules/jumps');
const rest = require('./rules/rest');
const importedhorse = require('./rules/importedhorse');
const trainerchange = require('./rules/trainerchange');
const horseprofile = require('./rules/horseprofile');
const firststart = require('./rules/firststart');
const secondstart = require('./rules/secondstart');
const lastform = require('./rules/lastform');
const firstup = require('./rules/firstup');
const secondup = require('./rules/secondup');
const thirdup = require('./rules/thirdup');
const wet = require('./rules/wet');
const jockey = require('./rules/jockey');
const trainer = require('./rules/trainer');
const classes = require('./rules/class');
const hometrack = require('./rules/hometrack');
const twostartsback = require('./rules/twostartsback');
const trackdistance = require('./rules/trackdistance');
const pace = require('./rules/pace');
const maiden = require('./rules/maiden');
const barrier = require('./rules/barrier');
const spell = 84

const runFormIntroRules = (horse,form,rules,meeting) => {
  var formtowrite = []
  if (!form.form || form.form.length < 1){return [`First Start. By ${horse.sire['@_name']}${horse.sire['@_country'] != 'AUS' ? ' ('+horse.sire['@_country'].toUpperCase()+')' : ''} - ${horse.dam['@_name']}${horse.dam['@_country'] != 'AUS' ? ' ('+horse.dam['@_country'].toUpperCase()+')' : ''}.`]}
  if (form.form[0] && form.form[0].classes.class_id == 90){
    var theSentence = ''
    var j = 0
    var breakdown = form_intro.genFormBreakdown(form.form[0])
    if (form.form[0].stewards_report && form.form[0].stewards_report[0]){
      var steward = ''
      if (form.form[0].stewards_report[0].length == 1) {
        steward = form.form[0].stewards_report.replace(/\./g,',')
      } else {
        for (report of form.form[0].stewards_report){
          if (report) steward = steward + helper.formatStewards(report) + ", "
        }
      }
      steward = steward.replace(/, $/, " when")
      formtowrite.push((steward + " " + breakdown + ".").replace(/ +/g, " "))
    } else {
      while (rules.CommentRules[j]){
        var rule = rules.CommentRules[j]
        if (!rule.InUse) {j++; continue}
        var isRule = form_intro.formIntroTrialRules(rule,horse,form,form.form[0],meeting)
        if (isRule){
          theSentence = getSentence(rule)
          theSentence.sentence = form_intro.addAddiblesFormIntro(horse,form,form.form[0],theSentence.sentence)
          formtowrite.push((theSentence.sentence + " " + breakdown + ".").replace(/ +/g, " "))
          break
        }
        j++
      }
    }
  }
  var formCount = 0
  for (var i=0;i<form.form.length;i++){
    if (formCount > 1){ break }
    if (form.form[i].classes.class_id == 90){ continue }
    var breakdown = form_intro.genFormBreakdown(form.form[i]) 
    if (form.form[i].stewards_report && form.form[i].stewards_report[0]){
      var steward = ''
      if (form.form[i].stewards_report[0].length == 1) {
        steward = form.form[i].stewards_report.replace(/\./g,',')
      } else {
        for (report of form.form[i].stewards_report){
          if (report) steward = steward + helper.formatStewards(report) + ", "
        }
      }
      steward = steward.replace(/, *$/, " when")
      formtowrite.push((steward + " " + breakdown + ".").replace(/ +/g, " "))
      formCount++
    } else {
      var k = 0
      var theSentence = ''
      if (horse['@_name'].toLowerCase() == 'scarlet oak'){
        console.log('scarlet oak form comment matamata:')
      }
      while (rules.CommentRules[k]){
        var rule = rules.CommentRules[k]
        if (!rule.InUse) {j++; continue}
        var isRule = form_intro.formIntroRules(rule,horse,form,form.form[i],meeting)
        if (horse['@_name'].toLowerCase() == 'scarlet oak'){
          console.log(rule.Name,isRule)
        }
        if (isRule){
          theSentence = getSentence(rule)
          theSentence.sentence = form_intro.addAddiblesFormIntro(horse,form,form.form[i],theSentence.sentence)
          formtowrite.push((theSentence.sentence + " " + breakdown + ".").replace(/ +/g, " "))
          formCount++
          break
        }
        k++
      }
    }
  }
  return formtowrite
}

const runSummaryRules = async (meeting,race,horse,form,rules,starters) => {
  var theSentence = ''
  rules.CommentRules.sort((a,b) => a.Order - b.Order)
  // console.log('NewRuleGroup',rules.Name)
  switch (rules.Name)
  {
    case "TrialStatistics":
      if (horse.last_four_starts != '' || form.length == 0) return ''
      var k = 0
      while (rules.CommentRules[k]){
        var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
        if (!rule.InUse) {k++; continue}
        var isRule = trials.trialRules(rule,horse,form)
        if (isRule){
          theSentence = getSentence(rule)
          theSentence.sentence = trials.addAddiblesTrials(horse,form,theSentence.sentence)
          break
        }
        k++
      }
      break
    case "NoTrials":
      if (horse.last_four_starts != '' && form.length == 0) return ''
      var k = 0
      while (rules.CommentRules[k]){
        var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
        if (!rule.InUse) {k++; continue}
        var isRule = trials.trialRules(rule,horse,form)
        if (isRule){
          theSentence = getSentence(rule)
          break
        }
        k++
      }
      break
    case "Weight":
      // console.log('weight RG')
      var k = 0
      while (rules.CommentRules[k]){
        var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
        if (!rule.InUse) {k++; continue}
        var isRule = weight.weightRules(rule,horse,form,race,starters)
        if (isRule){
          theSentence = getSentence(rule)
          theSentence.sentence = weight.addAddiblesWeight(horse,theSentence.sentence)
          break
        }
        k++
      }
      break
    case "Jumps":
      // console.log('jumps RG')
      if (!['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return ''
      var k = 0
      while (rules.CommentRules[k]){
        var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
        if (!rule.InUse) {k++; continue}
        var isRule = jumps.jumpsRules(rule,horse,form,race)
        if (isRule){
          theSentence = getSentence(rule)
          theSentence.sentence = jumps.addAddiblesJumps(horse,form,theSentence.sentence)
          break
        }
        k++
      }
      break
    case "Rest":
      // console.log('rest RG')
      var lastrun = -1
      for (item of form.form){
        // console.log(item)
        if (item.classes.class_id && item.classes.class_id != 90){
          var lastrun = moment(meeting.meetingDate).diff(moment(item.meeting_date),'days')
          break
        }
      }
      // console.log(horse['@_name'],'lastrun',lastrun)
      if (lastrun && lastrun > 0){
        var k = 0
        while (rules.CommentRules[k]){
          var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
          var isRule = rest.restRules(rule,lastrun,spell)
          if (isRule){
            theSentence = getSentence(rule)
            break
          }
          k++
        }
      }
      break
    case "ImportedHorse":
      // console.log('ImportedHorse RG')
      var k = 0
      while (rules.CommentRules[k]){
        var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
        if (!rule.InUse) {k++; continue}
        var isRule = await importedhorse.importedhorseRules(rule,horse,form,race)
        if (isRule){
          theSentence = getSentence(rule)
          break
        }
        k++
      }
      break
    case "TrainerChange":
      var k = 0
      while (rules.CommentRules[k]){
        var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
        if (!rule.InUse) {k++; continue}
        var isRule = trainerchange.trainerchangeRules(rule,horse,form,race)
        if (isRule){
          theSentence = getSentence(rule)
          theSentence.sentence = await trainerchange.addAddiblesTrainerChange(horse,form,theSentence.sentence)
          break
        }
        k++
      }
      break
    case "HorseProfile":
      var k = 0
      var form_runs = helper.cleanseBarrierTrials(form.form)
      while (rules.CommentRules[k]){
        var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
        if (!rule.InUse) {k++; continue}
        var isRule = horseprofile.horseprofileRules(rule,horse,form_runs,race,meeting.meetDate)
        if (isRule){
          theSentence = getSentence(rule)
          theSentence.sentence = await horseprofile.addAddiblesHorseProfile(horse,race,form_runs,theSentence.sentence,rule)
          break
        }
        k++
      }
      break
    case "FirstStart":
      var k = 0
      var form_runs = helper.cleanseBarrierTrials(form.form)
      if (form_runs.length || !form.form.length) return ''
      var trial_form = form.form
      while (rules.CommentRules[k]){
        var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
        if (!rule.InUse) {k++; continue}
       var isRule = firststart.firststartRules(rule,horse,trial_form,race)
        if (isRule){
          theSentence = getSentence(rule)
          break
        }
        k++
      }
      break
    case "SecondStart":
      var k = 0
      var form_runs = helper.cleanseBarrierTrials(form.form)
      if (form_runs.length != 1) return ''
      while (rules.CommentRules[k]){
        var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
        if (!rule.InUse) {k++; continue}
        var isRule = secondstart.secondstartRules(rule,horse,form_runs,form)
        if (isRule){
          theSentence = getSentence(rule)
          theSentence.sentence = await horseprofile.addAddiblesHorseProfile(horse,race,form_runs,theSentence.sentence,rule)
          break
        }
        k++
      }
      break
    case "LastForm":
      var k = 0
      var form_runs = helper.cleanseBarrierTrials(form.form)
      if (form_runs.length < 1) return ''
      var lastrun = moment(meeting.meetingDate).diff(moment(form_runs[0].meeting_date),'days')
      
      while (rules.CommentRules[k]){
        var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
        if (!rule.InUse) {k++; continue}
        var isRule = lastform.lastformRules(rule,horse,form_runs,form.form,race,lastrun)
        if (isRule){
          theSentence = getSentence(rule)
          theSentence.sentence = await horseprofile.addAddiblesHorseProfile(horse,race,form_runs,theSentence.sentence,rule)
          break
        }
        k++
      }
      break
    case "FirstUp":
      var form_runs = helper.cleanseBarrierTrials(form.form)
      if (form_runs < 1) return ''
      // console.log('first up',horse['@_name'])
      if (form_runs[0]) console.log('diff',moment(meeting.meetingDate).diff(moment(form_runs[0].meeting_date),'days'))
      if (form_runs[0] && moment(meeting.meetingDate).diff(moment(form_runs[0].meeting_date),'days') < spell) return ''
      // console.log('firstupobj')
      var firstupobj = firstup.getfirstUpObj(form_runs,spell)
      // console.log('firstupobj')
      // console.log(firstupobj)
      var k = 0
      while (rules.CommentRules[k]){
        var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
        if (!rule.InUse) {k++; continue}
        var isRule = firstup.firstUpRules(rule,horse,form_runs,firstupobj,(form.form[0] ?? {}),meeting.meetingDate)
        if (isRule){
          theSentence = getSentence(rule)
          theSentence.sentence = firstup.addAddiblesFirstUp(firstupobj,race,theSentence.sentence)
          break
        }
        k++
      }
      
      break
    case "SecondUp":
      var form_runs = helper.cleanseBarrierTrials(form.form)
      if (!form_runs[0] || !form_runs[1]) return ''
      if (moment(form_runs[0].meeting_date).diff(moment(form_runs[1].meeting_date),'days') < spell) return ''
      if (moment(meeting.meetingDate).diff(moment(form_runs[0].meeting_date),'days') > spell) return ''
      var secondupobj = secondup.getSecondUpObj(form_runs,spell)
      var k = 0
      while (rules.CommentRules[k]){
        var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
        if (!rule.InUse) {k++; continue}
        var isRule = secondup.secondUpRules(rule,horse,form_runs,secondupobj,(form.form[0] ?? {}),meeting.meetingDate)
        if (isRule){
          theSentence = getSentence(rule)
          theSentence.sentence = secondup.addAddiblesSecondUp(secondupobj,theSentence.sentence)
          break
        }
        k++
      }
      
      break
    case "ThirdUp":
      var form_runs = helper.cleanseBarrierTrials(form.form)
      if (!form_runs[0] || !form_runs[1] || !form_runs[2]) return ''
      if (moment(form_runs[1].meeting_date).diff(moment(form_runs[2].meeting_date),'days') < spell) return ''
      if (moment(meeting.meetingDate).diff(moment(form_runs[0].meeting_date),'days') > spell) return ''
      if (moment(form_runs[0].meeting_date).diff(moment(form_runs[1].meeting_date),'days') > spell) return ''
      var thirdupobj = thirdup.getThirdUpObj(form_runs,spell)
      var k = 0
      while (rules.CommentRules[k]){
        var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
        if (!rule.InUse) {k++; continue}
        var isRule = thirdup.thirdUpRules(rule,horse,form_runs,thirdupobj,(form.form[0] ?? {}),meeting.meetingDate)
        if (isRule){
          theSentence = getSentence(rule)
          theSentence.sentence = thirdup.addAddiblesThirdUp(thirdupobj,theSentence.sentence)
          break
        }
        k++
      }
    case "Wet":
      var form_runs = helper.cleanseBarrierTrials(form.form)
      if (!meeting.processedMeetingData.meeting.track['@_expected_condition'] || !['H','S'].includes(meeting.processedMeetingData.meeting.track['@_expected_condition'].charAt(0)) || (meeting.processedMeetingData.meeting.track['@_expected_condition'].charAt(1) == '5')) return ''
      if (race.track['@_name'] == 'Singapore') return ''
      var wetobj = wet.getWetObj(form_runs,spell)
      var k = 0
      while (rules.CommentRules[k]){
        var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
        if (!rule.InUse) {k++; continue}
        var isRule = wet.wetRules(rule,horse,form_runs,wetobj,race)
        if (isRule){
          theSentence = getSentence(rule)
          break
        }
        k++
      }
      
      break
    case "Jockey":
      var form_runs = helper.cleanseBarrierTrials(form.form)
      if (form_runs.length > 0) return ''
      if (!horse.jockey || !horse.jockey['@_id']) return ''
      var meeting_class = meeting.processedMeetingData.meeting.track['@_location']
      var meeting_state = helper.getStateByNumber(meeting.meetingState)
      var k = 0
      while (rules.CommentRules[k]){
        var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
        if (!rule.InUse) {k++; continue}
        var isRule = await jockey.jockeyRules(rule,horse,meeting_class,meeting_state)
        if (isRule){
          theSentence = getSentence(rule)
          break
        }
        k++
      }
      
        break
      case "Trainer":
        var form_runs = helper.cleanseBarrierTrials(form.form)
        if (form_runs.length > 0) return ''
        if (!horse.trainer['@_id']) return ''
        var meeting_class = meeting.processedMeetingData.meeting.track['@_location']
        var meeting_state = helper.getStateByNumber(meeting.meetingState)
        var k = 0
        while (rules.CommentRules[k]){
          var rule = rules.CommentRules[k]
          // console.log(horse['@_name'],rule.Name)
          if (!rule.InUse) {k++; continue}
          var isRule = await trainer.trainerRules(rule,horse,meeting_class,meeting_state)
          if (isRule){
            theSentence = getSentence(rule)
            break
          }
          k++
        }
        
          break
        case "Class":
          var form_runs = helper.cleanseBarrierTrials(form.form)
          if (form_runs.length < 1) return ''
          var updown = await classes.calculateUpDown(race.classes.class_id,form_runs[0].classes.class_id,meeting.processedMeetingData.meeting.track,form_runs[0].track)
          var k = 0
          while (rules.CommentRules[k]){
            var rule = rules.CommentRules[k]
            // console.log(horse['@_name'],rule.Name)
            if (!rule.InUse) {k++; continue}
            var isRule = classes.classRules(rule,horse,updown,form_runs[0])
            if (isRule){
              theSentence = getSentence(rule)
              break
            }
            k++
          }
          
            break
        case "HomeTrack":
          var form_runs = helper.cleanseBarrierTrials(form.form)
          if (form_runs.length > 0) return ''
          if (!horse.training_location) return ''
          var k = 0
          while (rules.CommentRules[k]){
            var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)

            if (!rule.InUse) {k++; continue}
            var isRule = await hometrack.homeTrackRules(rule,horse,race.track['@_name'])
            if (isRule){
              theSentence = getSentence(rule)
              break
            }
            k++
          }
            break

        case "TwoStartsBack":
          var form_runs = helper.cleanseBarrierTrials(form.form)
          if (form_runs.length < 5) return ''
          var twostartsbackobj = twostartsback.getTwoStartsBackObj(form_runs,spell)
          var updown = await classes.calculateUpDown(race.classes.class_id,form_runs[1].classes.class_id,meeting.processedMeetingData.meeting.track,form_runs[1].track)
          while (rules.CommentRules[k]){
            var rule = rules.CommentRules[k]
            // console.log(horse['@_name'],rule.Name)
            if (!rule.InUse) {k++; continue}
            var isRule = twostartsback.twoStartsBackRules(rule,horse,form_runs,race,twostartsbackobj,updown)
            if (isRule){
              theSentence = getSentence(rule)
              theSentence.sentence = twostartsback.addAddiblesTwoStartsBack(twostartsbackobj,theSentence.sentence)
              break
            }
            k++
          }
            break
          case "TrackDistance":
            var form_runs = helper.cleanseBarrierTrials(form.form)
            if (form_runs.length < 1) return ''
            var k = 0
            while (rules.CommentRules[k]){
              var rule = rules.CommentRules[k]
              // console.log(horse['@_name'],rule.Name)
              if (!rule.InUse) {k++; continue}
              var isRule = trackdistance.trackDistanceRules(rule,horse,form_runs,race,spell)
              if (isRule){
                theSentence = getSentence(rule)
                theSentence.sentence = twostartsback.addAddiblesTwoStartsBack(horse,theSentence.sentence,form_runs)
                break
              }
              k++
            }
              break
            case "Pace":
              var form_runs = helper.cleanseBarrierTrials(form.form)
              if (form_runs.length < 1) return ''
              var k = 0
              while (rules.CommentRules[k]){
                var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
                if (!rule.InUse) {k++; continue}
                var isRule = pace.paceRules(rule,horse,form_runs,race,starters)
                if (isRule){
                  theSentence = getSentence(rule)
                  break
                }
                k++
              }
                break
            case "Maiden":
              var form_runs = helper.cleanseBarrierTrials(form.form)
              if (form_runs.length < 1) return ''
              var updown = await classes.calculateUpDown(race.classes.class_id,form_runs[0].classes.class_id,meeting.processedMeetingData.meeting.track,form_runs[0].track)
              var k = 0
              while (rules.CommentRules[k]){
                var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
                if (!rule.InUse) {k++; continue}
                var isRule = maiden.maidenRules(rule,horse,form_runs,meeting,updown)
                if (isRule){
                  theSentence = getSentence(rule)
                  break
                }
                k++
              }
                break
            case "Barriers":
              var form_runs = helper.cleanseBarrierTrials(form.form)
              if (form_runs.length < 2) return ''
              if (!race.distance || !race.distance['@_metres'] || parseInt(race.distance['@_metres']) > 1601 ) return ''
              if (meeting.processedMeetingData.meeting.track['@_name'] == 'Flemington' && parseInt(race.distance['@_metres']) < 1201 ) return ''
              var k = 0
              while (rules.CommentRules[k]){
                var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)
                if (!rule.InUse) {k++; continue}
                var isRule = barrier.barrierRules(rule,horse,form_runs,race,starters)
                if (isRule){
                  theSentence = getSentence(rule)
                  break
                }
                k++
              }
                break
  }
  return theSentence
}



const runPredictiveRules = (horse,race,marketarray,rules) => {
  var k = 0
  var theSentence = ''
  while (rules.CommentRules[k]){
    var rule = rules.CommentRules[k]
                // console.log(horse['@_name'],rule.Name)

    if (!rule.InUse) {k++; continue}
    var isRule = markets.marketRules(rule,horse,marketarray)
    if (isRule){
      theSentence = getSentence(rule)
      break
    }
    k++
  }
  return theSentence
}

const getSentence = (rule) => {
  var senCount = rule.CommentSentences.length - 1
  var pickSentence = Math.round(Math.random() * senCount)
  if (!senCount) pickSentence = 0
  // console.log(rule.Name,senCount,pickSentence)
  var returnSentence = rule.CommentSentences[pickSentence].Sentence
  return {"direction":rule.direction, "sentence":returnSentence}
}





module.exports = {
  runFormIntroRules,
  runSummaryRules,
  runPredictiveRules
};
