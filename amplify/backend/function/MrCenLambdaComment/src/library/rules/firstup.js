const moment = require('moment')
const helper = require("../helper")

const firstUpRules = (rule,horse,form,fuobj,last_run,meetdate) => {
  var ruleParams = rule.jsonParams
  
  switch (rule.Name)
  {
    case "NoFirstUpWins":
      if (horse.betting < ruleParams.odds_or_above) return false
      if (fuobj.count < 2) return false
      if ((fuobj.place / fuobj.count) > 0.19) return false
      return true
    case "GoodFirstUp":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (fuobj.count < 3) return false
      if (fuobj.win < 1) return false
      if ((fuobj.place / fuobj.count) < 0.2 || (fuobj.place / fuobj.count) > 0.69) return false
      return true
    case "GreatFirstUp":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (fuobj.win < 2) return false
      if ((fuobj.place / fuobj.count) < 0.7) return false
      return true
    case "WonLastFirstUp":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (!fuobj.won_last) return false
      if (form.count < 3) return false
      return true
    case "PlacedLastFirstUp":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (!fuobj.placed_last) return false
      if (form.count < 3) return false
      return true
    case "AfterTrialWin":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (!last_run || last_run.classes.class_id != 90 || last_run.finish_position != 1) return false
      if (moment(meetdate).diff(moment(last_run.meeting_date),'days') > 21) return false
      return true
  }
}

const getfirstUpObj = (form,spell) => {
  var firstupobj = {
    "count": 0,
    "win": 0,
    "place": 0,
    "won_last": false,
    "placed_last": false
  }
  var j = 0
  for (item of form){
    j++
    if (form.length == j || (form[j] && (moment(item.meeting_date).diff(moment(form[j].meeting_date),'days') > spell))){
      
      
      if (!(form.length == j)) {
        console.log('firstup diff')
        console.log(form[j].meeting_date)
        console.log(item.meeting_date)
        console.log(moment(item.meeting_date).diff(moment(form[j].meeting_date),'days'))
        
      }
      if (item.finish_position == 1){
        if (firstupobj.count == 0) {
          firstupobj.won_last = true
          firstupobj.placed_last = true
          firstupobj.last_class = item.classes.class ?? ''
          firstupobj.last_track = helper.cleverCapFstLtr(item.track['@_name'])
        }
        firstupobj.win++
        firstupobj.place++
      } else if (['2','3',2,3].includes(item.finish_position)){
        firstupobj.place++
        if (firstupobj.count == 0) {
          firstupobj.placed_last = true
          firstupobj.last_class = item.classes.class ?? ''
          firstupobj.last_track = helper.cleverCapFstLtr(item.track['@_name'])
        }
      }
      firstupobj.count++
    }
  }

  return firstupobj
}

const addAddiblesFirstUp = (firstupobj,race,sentence) => {
  if (sentence.includes("{")){
    sentence = sentence.replace('{last-first-up-class}',firstupobj.last_class) 
    sentence = sentence.replace('{last-first-up-track}',(race.track['@_name'] == firstupobj.last_track) ? 'here': firstupobj.last_track) 
  }
  return sentence
}



module.exports = {
  firstUpRules,
  getfirstUpObj,
  addAddiblesFirstUp
};
