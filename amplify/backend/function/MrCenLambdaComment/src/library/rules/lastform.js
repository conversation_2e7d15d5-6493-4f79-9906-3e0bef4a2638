const util = require('util');
const centaur = require('@mediality/centaur');
const moment = require('moment')
const helper = require('../helper.js');

const lastformRules = (rule,horse,starts_form,form,race,lastrun) => {
  var ruleParams = rule.jsonParams
  var last_margin = 0
  if (starts_form[0].beaten_margin) last_margin = starts_form[0].beaten_margin
  else last_margin = starts_form[0].margin

  switch (rule.Name)
  {
    case "WinnerMediumMarginBackmarker":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position != 1) return false
      if (!starts_form[0].positions || starts_form[0].positions['@_settling_down'] < Math.floor(starts_form[0].starters * 0.75)) return false
      if (last_margin < 0.4 || last_margin > 1.4) return false
      if (lastrun > 86) return false
      return true
    case "ImprovingTrialMaiden":
      if (lastrun < 84) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (!form[0]) return false
      if (!form[0].classes || !form[0].classes.class_id != 90) return false
      if (![1,2,3].includes(form[0].finish_position)) return false
      if (form[0].starters < 6) return false
      for (start of starts_form){
        if ([1,2,3].includes(start.finish_position)) return false
        if (start.days_since_last_run > 86) return false
      }
      return true
    case "WinnerSmallMarginClearLeader":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position != 1) return false
      if (!starts_form[0].positions) return false
      if (starts_form[0].positions['@_m1200'] && starts_form[0].positions['@_m1200'] != 1) return false
      if (starts_form[0].positions['@_m800'] && starts_form[0].positions['@_m800'] != 1) return false
      if (starts_form[0].positions['@_m400'] && starts_form[0].positions['@_m400'] != 1) return false
      if (starts_form[0].positions['@_settling_down'] && starts_form[0].positions['@_settling_down'] != 1) return false
      if (last_margin > 0.39) return false
      return true
    case "WinnerDeadHeat":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (lastrun > 86) return false
      if (starts_form[0].finish_position != 1) return false
      if (starts_form[0].official_margin_1 != 'DH') return false
      return true
    case "WinnerLargeMarginHandy":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position != 1) return false
      if (last_margin < 1.5) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] > Math.ceil(starts_form[0].starters * 0.25)) return false
      return true
    case "WinnerLargeMarginOffPace":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (starts_form[0].finish_position != 1) return false
      if (last_margin < 1.5) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] < Math.ceil(starts_form[0].starters * 0.25) || starts_form[0].positions['@_settling_down'] > Math.floor(starts_form[0].starters * 0.75)) return false
      return true
    case "WinnerMediumMarginHandy":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position != 1) return false
      if (last_margin < 0.4 || last_margin > 1.4) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] > Math.ceil(starts_form[0].starters * 0.25)) return false
      return true
    case "WinnerMediumMarginOffPace":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position != 1) return false
      if (last_margin < 0.4 || last_margin > 1.4) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] < Math.ceil(starts_form[0].starters * 0.25) || starts_form[0].positions['@_settling_down'] > Math.floor(starts_form[0].starters * 0.75)) return false
      return true
    case "WinnerSmallMarginHandy":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position != 1) return false
      if (last_margin > 0.39 ) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] > Math.ceil(starts_form[0].starters * 0.25)) return false
      return true
    case "WinnerSmallMarginOffPace":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (starts_form[0].finish_position != 1) return false
      if (last_margin > 0.39 ) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] < Math.ceil(starts_form[0].starters * 0.25) || starts_form[0].positions['@_settling_down'] > Math.floor(starts_form[0].starters * 0.75)) return false
      return true
    case "NotPlacedLargeMarginOffPace":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position <  Math.ceil(starts_form[0].starters * 0.5)) return false
      if (last_margin < 7.1 ) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] < Math.ceil(starts_form[0].starters * 0.5)) return false
      return true
    case "NotPlacedLargeMarginOffPace":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position <  Math.ceil(starts_form[0].starters * 0.5)) return false
      if (last_margin < 7.1 ) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] > 3) return false
      return true
    case "NotPlacedLargeMarginOnPace":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position < 4) return false
      if (starts_form[0].finish_position < Math.floor(starts_form[0].starters * 0.5)) return false
      if (last_margin < 7.1 ) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] > 3) return false
      return true
    case "UnplacedLast2ShortOdds":
      if (starts_form.length != 2) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      for (item of starts_form){
        if (item.finish_position < 4) return false
        if (!item.decimalprices || parseFloat(item.decimalprices['@_starting']) > 5) return false
      }
      return true
    case "PlacedSmallMarginHandy":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (last_margin > 0.5 ) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] > Math.ceil(starts_form[0].starters * 0.25)) return false
      return true
    case "PlacedMediumMarginHandy":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (last_margin < 0.51 || last_margin > 2) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] > Math.ceil(starts_form[0].starters * 0.25)) return false
      return true
    case "PlacedSmallMediumMarginOffPace":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (last_margin < 2.01 || last_margin > 4) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] < Math.floor(starts_form[0].starters * 0.25) || starts_form[0].positions['@_settling_down'] > Math.ceil(starts_form[0].starters * 0.75)) return false
      return true
    case "RacedPoorlyAllRuns":
      if (starts_form.length < 4) return false
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      for (item of starts_form){
        if (item.finish_position <  Math.floor(item.starters * 0.67)) return false
      }
      return true
    case "RacedPoorlyLast3":
      if (starts_form.length < 3) return false
      if (lastrun > 21) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      var formcount = 0
      for (item of starts_form){
        if (item.finish_position <  Math.floor(item.starters * 0.67)) return false
        if ((item.beaten_margin && item.beaten_margin < 5) || (item.margin && item.margin < 5)) return false
        formcount = formcount + 1
        if (formcount > 2) break
      }
      return true
    case "NotPlacedSmallMargin":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position < 4) return false
      if (last_margin > 2 ) return false
      return true
    case "NotPlacedMedium1Margin":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position < 4) return false
      if (last_margin < 2.01 || last_margin > 4) return false
      return true
    case "NotPlacedMedium2Margin":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position < 4) return false
      if (last_margin < 4.01 || last_margin > 7) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] < Math.floor(starts_form[0].starters * 0.25) || starts_form[0].positions['@_settling_down'] > Math.ceil(starts_form[0].starters * 0.75)) return false
      return true
    case "NotPlacedLargeMarginJustOffPace":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (starts_form[0].starters < 9) return false
      if (starts_form[0].finish_position < Math.floor(starts_form[0].starters * 0.5)) return false
      if (last_margin < 7) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || ![4,5].includes(starts_form[0].positions['@_settling_down'])) return false
      return true
    case "WellBackLargeMargin":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position < 4) return false
      if (last_margin < 7) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] < Math.floor(starts_form[0].starters * 0.75)) return false
      return true
    case "RacedPoorlyLast2":
      if (starts_form.length != 2) return false
      if (horse.betting < ruleParams.odds_or_above) return false
      var formcount = 0
      for (item of starts_form){
        if (item.finish_position <  Math.floor(item.starters * 0.67)) return false
        if ((item.beaten_margin && item.beaten_margin < 5) || (item.margin && item.margin < 5)) return false
        formcount = formcount + 1
        if (formcount > 1) break
      }
      return true
    case "ImprovingCampaignMaiden":
      if (lastrun > 86) return false
      if (starts_form.length < 3) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (starts_form[0].days_since_last_run < 84) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      var formcount = 0
      for (item of starts_form){
        if (formcount != 0){
          if (item.days_since_last_run > 86) return false
          if (item.finish_position < 6) return false
          if ((item.beaten_margin && item.beaten_margin < 5) || (item.margin && item.margin < 5)) return false
        }
        formcount = formcount + 1
        if (formcount > 1) break
      }
      return true
    case "ImprovingMaiden":
      if (lastrun > 86) return false
      if (starts_form.length < 3) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      // if (starts_form[0].days_since_last_run < 84) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      var formcount = 0
      for (item of starts_form){
        if (formcount != 0){
          if (item.finish_position < 5) return false
        }
        formcount = formcount + 1
        if (formcount > 1) break
      }
      return true
    case "WinnerLargeMarginClearLeader":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position != 1) return false
      if (!starts_form[0].positions) return false
      if (starts_form[0].positions['@_m1200'] && starts_form[0].positions['@_m1200'] != 1) return false
      if (starts_form[0].positions['@_m800'] && starts_form[0].positions['@_m800'] != 1) return false
      if (starts_form[0].positions['@_m400'] && starts_form[0].positions['@_m400'] != 1) return false
      if (starts_form[0].positions['@_settling_down'] && starts_form[0].positions['@_settling_down'] != 1) return false
      if (last_margin < 1.5) return false
      return true
    case "WinnerLargeMarginBackmarker":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position != 1) return false
      if (!starts_form[0].positions) return false
      if (starts_form[0].positions['@_settling_down'] && starts_form[0].positions['@_settling_down'] < Math.floor(starts_form[0].starters * 0.75)) return false
      if (last_margin < 1.5) return false
      return true
    case "WinnerMediumMarginClearLeader":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position != 1) return false
      if (!starts_form[0].positions) return false
      if (starts_form[0].positions['@_m1200'] && starts_form[0].positions['@_m1200'] != 1) return false
      if (starts_form[0].positions['@_m800'] && starts_form[0].positions['@_m800'] != 1) return false
      if (starts_form[0].positions['@_m400'] && starts_form[0].positions['@_m400'] != 1) return false
      if (starts_form[0].positions['@_settling_down'] && starts_form[0].positions['@_settling_down'] != 1) return false
      if (last_margin < 0.4 || last_margin > 1.5) return false
      return true
    case "WinnerSmallMarginBackmarker":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (starts_form[0].finish_position != 1) return false
      if (!starts_form[0].positions) return false
      if (starts_form[0].positions['@_settling_down'] && starts_form[0].positions['@_settling_down'] < Math.floor(starts_form[0].starters * 0.75)) return false
      if (last_margin > 0.39) return false
      return true
    case "PlacedSmallMarginClearLeader":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (!starts_form[0].positions) return false
      if (starts_form[0].positions['@_m1200'] && starts_form[0].positions['@_m1200'] != 1) return false
      if (starts_form[0].positions['@_m800'] && starts_form[0].positions['@_m800'] != 1) return false
      if (starts_form[0].positions['@_m400'] && starts_form[0].positions['@_m400'] != 1) return false
      if (starts_form[0].positions['@_settling_down'] && starts_form[0].positions['@_settling_down'] != 1) return false
      if (last_margin > 0.5) return false
      return true
    case "PlacedSmallMarginOffPace":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (last_margin > 0.5) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] < Math.floor(starts_form[0].starters * 0.25) || starts_form[0].positions['@_settling_down'] > Math.ceil(starts_form[0].starters * 0.75)) return false
      return true
    case "PlacedSmallMarginBackmarker":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (last_margin > 0.5) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] < Math.floor(starts_form[0].starters * 0.75)) return false
      return true
    case "PlacedMediumMarginClearLeader":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (!starts_form[0].positions) return false
      if (starts_form[0].positions['@_m1200'] && starts_form[0].positions['@_m1200'] != 1) return false
      if (starts_form[0].positions['@_m800'] && starts_form[0].positions['@_m800'] != 1) return false
      if (starts_form[0].positions['@_m400'] && starts_form[0].positions['@_m400'] != 1) return false
      if (starts_form[0].positions['@_settling_down'] && starts_form[0].positions['@_settling_down'] != 1) return false
      if (last_margin < 0.51 || last_margin > 2) return false
      return true
    case "PlacedMediumMarginOffPace":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (last_margin < 0.51 || last_margin > 2) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] < Math.floor(starts_form[0].starters * 0.25) || starts_form[0].positions['@_settling_down'] > Math.ceil(starts_form[0].starters * 0.75)) return false
      return true
    case "PlacedMediumMarginBackmarker":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (last_margin < 0.51 || last_margin > 2) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] < Math.floor(starts_form[0].starters * 0.75)) return false
      return true
    case "PlacedSmallMediumMarginClearLeader":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (!starts_form[0].positions) return false
      if (starts_form[0].positions['@_m1200'] && starts_form[0].positions['@_m1200'] != 1) return false
      if (starts_form[0].positions['@_m800'] && starts_form[0].positions['@_m800'] != 1) return false
      if (starts_form[0].positions['@_m400'] && starts_form[0].positions['@_m400'] != 1) return false
      if (starts_form[0].positions['@_settling_down'] && starts_form[0].positions['@_settling_down'] != 1) return false
      if (last_margin < 2.01 || last_margin > 4) return false
      return true
    case "PlacedSmallMediumMarginHandy":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (last_margin < 2.01 || last_margin > 4) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] < Math.floor(starts_form[0].starters * 0.25)) return false
      return true
    case "PlacedSmallMediumMarginBackmarker":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (last_margin < 2.01 || last_margin > 4) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] < Math.floor(starts_form[0].starters * 0.75)) return false
      return true
    case "PlacedBigMarginClearLeader":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (!starts_form[0].positions) return false
      if (starts_form[0].positions['@_m1200'] && starts_form[0].positions['@_m1200'] != 1) return false
      if (starts_form[0].positions['@_m800'] && starts_form[0].positions['@_m800'] != 1) return false
      if (starts_form[0].positions['@_m400'] && starts_form[0].positions['@_m400'] != 1) return false
      if (starts_form[0].positions['@_settling_down'] && starts_form[0].positions['@_settling_down'] != 1) return false
      if (last_margin < 4) return false
      return true
    case "PlacedBigMarginHandy":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (last_margin < 4.01) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] > Math.ceil(starts_form[0].starters * 0.25)) return false
      return true
    case "PlacedBigMarginOffPace":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (last_margin < 4.01) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] < Math.floor(starts_form[0].starters * 0.25) || starts_form[0].positions['@_settling_down'] > Math.ceil(starts_form[0].starters * 0.75)) return false
      return true
    case "PlacedBigMarginBackmarker":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (helper.runIsJumps(starts_form[0])) return false
      if (lastrun > 86) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (![2,3].includes(starts_form[0].finish_position)) return false
      if (last_margin < 4.01) return false
      if (!starts_form[0].positions) return false
      if (!starts_form[0].positions['@_settling_down'] || starts_form[0].positions['@_settling_down'] < Math.floor(starts_form[0].starters * 0.75)) return false
      return true
  }

}



module.exports = {
  lastformRules  
};
