const util = require('util');
const helper = require('../helper.js')
const centaur = require('@mediality/centaur');


const importedhorseRules = async (rule,horse,form,race) => {
  switch (rule.Name)
  {
    case "FirstStartInAus":
      if (!form.form || form.form.length == 0) return false
      if (horse['@_country'] == 'AUS') return false
      var actualform = helper.cleanseBarrierTrials(form.form)
      var hasform = false
      for (item of actualform){
        if (item.track['@_country']) {
          if (item.track['@_country'] == 'AUS') return false
        } else {
          var trackcountry = await centaur.tracks.findOne({TRK_TRACK_DB_ID:item.track['@_id']}).select('TRK_COUNTRY_OF_TRACK').lean()
          if (trackcountry == 'AUS' || trackcountry.TRK_COUNTRY_OF_TRACK == 'AUS') return false
        }
        hasform = true
      }
      if (hasform)
        return true
  }
}

module.exports = {
  importedhorseRules
};
