const util = require('util');
const moment = require('moment')
const helper = require('../helper.js')

const weightRules = (rule,horse,form,race,starters) => {
  var ruleParams = rule.jsonParams
  switch (rule.Name)
  {
    case "Lightweight":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (race.weight_type == 'Handicap' && starters < 10 && parseInt(horse.weight['@_total']) < 54.5)
        return true
      return false
    case "DownInWeight":
      if (!form.form || form.form.length == 0) return false 
      if (horse.betting > ruleParams.odds_or_below) return false
      var actualform = helper.cleanseBarrierTrials(form.form)
      if (actualform.length == 0) return false 
      if ((race.weight_type == 'Handicap' && actualform[0].weight_type == 'H') && (actualform[0].weight_carried - 6) >= horse.weight['@_total'])
        return true
      return false
    case "ClaimRider":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (horse.jockey && horse.jockey['@_allowance_weight'] && parseInt(horse.jockey['@_allowance_weight']) > 0)
        return true
      return false
  }
}

const addAddiblesWeight = (horse,sentence) => {
  if (horse.jockey && horse.jockey['@_allowance_weight']) sentence = sentence.replace('{allowance-weight}', horse.jockey['@_allowance_weight'])
  return sentence
}


module.exports = {
  weightRules,
  addAddiblesWeight
};
