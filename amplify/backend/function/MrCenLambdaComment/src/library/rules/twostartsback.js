const moment = require('moment')
const helper = require("../helper")

const twoStartsBackRules = (rule,horse,form,race,twostartsbackobj,updown) => {
  var ruleParams = rule.jsonParams
  
  switch (rule.Name)
  {
    case "StrongFormTwoStartsBack":
      if (['1','2','3',1,2,3].includes(form[0].finish_position)) return false
      if (!twostartsbackobj.won) return false
      if (['U','N'].includes(updown)) return false
      return true
  }
}

const getTwoStartsBackObj = (form,spell) => {
  var twostartsbackobj = {
    "win": false,
    "track": "",
    "class": 0
    
  }
  var meet_date_old_1 = false
  var meet_date_old_2 = false
  var meet_date_old_3 = false
  for (item of form){
    if (meet_date_old_1 && meet_date_old_2 && meet_date_old_3) {
      if ((moment(meet_date_old_2).diff(moment(meet_date_old_3),'days') < spell) && 
      (moment(meet_date_old_1).diff(moment(meet_date_old_2),'days') < spell) &&
      (moment(item.meeting_date).diff(moment(meet_date_old_1),'days') < spell)){
        if (item.finish_position == 1){
          twostartsbackobj.win = true
          twostartsbackobj.class = item.classes.class_id ?? ''
          twostartsbackobj.track = helper.cleverCapFstLtr(item.track['@_name'])
          return twostartsbackobj
        } else {
          return twostartsbackobj
        }
        
      } else {
        return twostartsbackobj
      }
    }
    meet_date_old_3 = meet_date_old_2
    meet_date_old_2 = meet_date_old_1
    meet_date_old_1 = item.meeting_date
  }
  return twostartsbackobj
}

const addAddiblesTwoStartsBack = (twostartsbackobj,sentence) => {
  if (sentence.includes("{")){
    sentence = sentence.replace('{second-last-track}',twostartsbackobj.track) 
  }
  return sentence
}



module.exports = {
  twoStartsBackRules,
  getTwoStartsBackObj,
  addAddiblesTwoStartsBack
};
