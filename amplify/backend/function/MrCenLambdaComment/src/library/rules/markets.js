const util = require('util');
const moment = require('moment')
const helper = require('../helper.js')

const marketRules = (rule,horse,marketarray) => {
  
  switch (rule.Name)
  {
    case "RagingFavourite":
      if (horse.last_four_starts != '' && horse.betting > 1 && horse.betting < 1.61)
        return true
      return false
    case "StrongFavourite":
      if (horse.last_four_starts != '' && horse.betting > 1.6 && horse.betting < 2.16)
        return true
      return false
    case "CouldBeFavourite":
      if (horse.last_four_starts != '' && horse.betting > 2.15 && (horse['@_id'] == marketarray[0].horse_id))
        return true
      return false
    case "IsAttractive":
      if (horse.last_four_starts != '' && (horse['@_id'] != marketarray[0].horse_id) && 
          horse.betting > 2.15 && horse.betting < 3)
        return true
      return false
    case "IsMajorPlayer":
      if (horse.last_four_starts != '' && (horse['@_id'] != marketarray[0].horse_id) && 
          horse.betting > 2.99 && horse.betting < 5.01)
        return true
      return false
    case "IsCompetetive":
      if (horse.last_four_starts != '' && horse.betting > 5 && horse.betting < 8.01)
        return true
      return false
    case "EachWayAppeal":
      if (horse.last_four_starts != '' && marketarray.length > 7 && horse.betting > 8 && horse.betting < 12.01)
        return true
      return false
    case "PlaceChanceBet":
      if (horse.last_four_starts != '' && horse.betting > 12 && horse.betting < 18.01)
        return true
      return false
    case "HasPoorOdds":
      if (horse.last_four_starts != '' && horse.betting > 18 && horse.betting < 31.01)
        return true
      return false
    case "HasSomeClaims":
      if (horse.last_four_starts != '' && horse.betting > 8 && horse.betting < 12.01)
        return true
      return false
    case "FirstStartGoodOdds":
      // console.log("FirstStartGoodOdds",horse.last_four_starts,checkIndex(horse['@_id'],marketarray),(race.starters * 0.33))
      if (horse.last_four_starts == '' && (marketarray.length * 0.33 > checkIndex(horse['@_id'],marketarray)))
        return true
      return false
    case "FirstStartMiddleOdds":
      // console.log("FirstStartMiddleOdds",horse.last_four_starts,checkIndex(horse['@_id'],marketarray),(race.starters * 0.33),(race.starters * 0.8))
      if (horse.last_four_starts == '' &&  (marketarray.length * 0.33 < checkIndex(horse['@_id'],marketarray)) && (marketarray.length * 0.8 >= checkIndex(horse['@_id'],marketarray)))
        return true
      return false
    case "FirstStartBadOdds":
      // console.log("FirstStartBadOdds",horse.last_four_starts,checkIndex(horse['@_id'],marketarray),(race.starters * 0.8))
      if (horse.last_four_starts == '' && (marketarray.length * 0.8 < checkIndex(horse['@_id'],marketarray)))
        return true
      return false
    case "HasLowestOdds":
      if (horse.last_four_starts != '' && horse.betting > 31.01)
        return true
      return false
    default:
      return false
  }

  
}

const checkIndex = (horse_id,marketarray) =>{

  for (var i=0;i<marketarray.length;i++){
    if (horse_id == marketarray[i].horse_id)
      return i+1
  }
  return marketarray.length
}


module.exports = {
  marketRules
};
