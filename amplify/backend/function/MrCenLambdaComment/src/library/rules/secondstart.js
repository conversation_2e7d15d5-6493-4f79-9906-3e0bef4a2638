const secondstartRules = (rule,horse,form) => {
  var ruleParams = rule.jsonParams
  switch (rule.Name)
  {
    case "BeatenSmallMargin":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (form[0].beaten_margin < 2) return true
      return false
    case "BeatenMedium1Margin":
      if (horse.betting > ruleParams.odds_or_below) return false
      for (item of form) if (item.finish_position == 1) return true
      return false
    case "BeatenMedium2Margin":
      if (horse.betting < ruleParams.odds_or_above) return false
      if (form[0].beaten_margin > 4 && form[0].beaten_margin < 7.1) return true
      return false
    case "BeatenLargeMargin":
      if (horse.betting < ruleParams.odds_or_above) return false
      if (form[0].beaten_margin < 7.1) return false
      return true
    case "WonSmallMargin":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (form.finish_position != 1 || form[0].beaten_margin > 2) return false
      return true
    case "WonMediumMargin":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (form.finish_position != 1 || form[0].beaten_margin < 2 || form[0].beaten_margin > 4) return false
      return true
    case "WonLargeMargin":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (form.finish_position != 1 || form[0].beaten_margin < 4.1) return false
      return true
  }
}



module.exports = {
  secondstartRules  
};
