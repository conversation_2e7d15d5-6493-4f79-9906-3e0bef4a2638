const util = require('util');
const centaur = require('@mediality/centaur');


const trainerRules = async (rule,horse,meeting_class,meeting_state) => {
  switch (rule.Name)
  {
    case "LeadingTrainer":
      var trainerIsMetro = await centaur.top_trainers.findOne({$and:[{TrainerId:horse.trainer['@_id']},{Location:meeting_class},{StateId:meeting_state}]}).lean()
      if (!trainerIsMetro) return false
      return true
  }
}

module.exports = {
  trainerRules
};
