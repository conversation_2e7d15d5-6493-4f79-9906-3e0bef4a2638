const firststartRules = (rule,horse,form,race) => {
  var ruleParams = rule.jsonParams
  switch (rule.Name)
  {
    case "CameFirstInLastTrial":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (form[0].finish_position == 1) return true
      return false
    case "CameFirstInTrials":
      if (horse.betting > ruleParams.odds_or_below) return false
      for (item of form) if (item.finish_position == 1) return true
      return false
    case "CameSecondInLastTrial":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (form[0].finish_position == 2 && race.starters > 5) return true
      return false
    case "CameThirdInLastTrial":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (form[0].finish_position == 3 && race.starters > 5) return true
      return false
    case "CameSecondInTrials":
      if (horse.betting > ruleParams.odds_or_below) return false
      for (item of form) if (item.finish_position == 2 && race.starters > 5) return true
      return false
    case "CameThirdInTrials":
      if (horse.betting > ruleParams.odds_or_below) return false
      for (item of form) if (item.finish_position == 3 && race.starters > 5) return true
      return false
    case "UnplacedInOnlyTrial":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (form.length == 1 && form[0].finish_position > 3 && race.starters > 5) return true
      return false
    case "UnplacedInTrials":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (form.length == 1) return false
      if (race.starters > 5) return false
      for (item of form) if (item.finish_position < 4) return false
      return true
    case "CameCloseInOnlyTrial":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (form.length == 1 && form[0].finish_position > 3 && (form[0].margin !== 'DS' && parseFloat(form[0].margin) < 2)) return true
      return false
    case "BackOfFieldInOnlyTrial":
      if (horse.betting < ruleParams.odds_or_above) return false
      if (form.length == 1 && form[0].finish_position > Math.floor(item.starters * 0.8) && (form[0].margin == 'DS' || parseFloat(form[0].margin) > 6.9)) return true
      return false
    case "BackOfFieldInAllTrials":
      if (horse.betting < ruleParams.odds_or_above) return false
      for (item of form) if (form.length == 1 || item.finish_position < Math.floor(item.starters * 0.8) || form[0].margin !== 'DS' || parseFloat(form[0].margin) < 7) return false
      return true
  }
}



module.exports = {
  firststartRules
};
