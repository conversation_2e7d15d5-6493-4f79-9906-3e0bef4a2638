const util = require('util');
const moment = require('moment')
const helper = require('../helper.js')
const jumpsRules = (rule,horse,form,race) => {
  switch (rule.Name)
  {
    case "IsJumps":
      return true
  }
}

const addAddiblesJumps = (horse,form,sentence) => {
  var starts = 0, firsts = 0, seconds = 0, thirds = 0
  if (form.form && form.form[0]){
    var form_runs = helper.cleanseBarrierTrials(form.form)
    for (item of form_runs){
      if (helper.runIsJumps(item)){
        starts++
        if (item.finish_position == 1){ firsts++ } 
        if (item.finish_position == 2){ seconds++ } 
        if (item.finish_position == 3){ thirds++ } 
      }
    }
  }
  sentence = sentence.replace('{jumps-starts}', starts.toString());
  sentence = sentence.replace('{jumps-firsts}', firsts.toString());
  sentence = sentence.replace('{jumps-seconds}', seconds.toString());
  sentence = sentence.replace('{jumps-thirds}', thirds.toString());
  return sentence
}

module.exports = {
  jumpsRules,
  addAddiblesJumps
};
