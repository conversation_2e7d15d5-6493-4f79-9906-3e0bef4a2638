const util = require('util');
const centaur = require('@mediality/centaur');


const classRules = (rule,horse,updown,last_form) => {
  var ruleParams = rule.jsonParams
  switch (rule.Name)
  {
    case "StrongerOpposition":
      // console.log("StrongerOpposition",updown,horse.betting,ruleParams.odds_or_above)
      if (horse.betting < ruleParams.odds_or_above) return false
      if (updown != 'U') return false
      return true
    case "WeakerOpposition":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (updown != 'D') return false
      if (!last_form.group && horse.betting > 7.01) return false
      return true
    case "DropInStartingPrice":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (!last_form.decimalprices || !last_form.decimalprices['@_starting']) return false
      if ((parseFloat(last_form.decimalprices['@_starting']) - 10) < horse.betting) return false
      return true
    case "RiseInStartingPrice":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (!last_form.decimalprices || !last_form.decimalprices['@_starting']) return false
      if ((parseFloat(last_form.decimalprices['@_starting']) + 10) < horse.betting) return false
      if (parseFloat(last_form.decimalprices['@_starting']) < 15) return false
      return true
  }
}

const calculateUpDown = async (nextClass,prevClass,nt,pt) => {
  var diffnum = 2
  var rankPrev = await centaur.race_classes.findOne({CLA_CLASS_ID:prevClass}).lean()
  var rankNext = await centaur.race_classes.findOne({CLA_CLASS_ID:nextClass}).lean()
  var prevTrk = await centaur.tracks.findOne({TRK_TRACK_ID:pt['@_id']}).lean()
  var nextTrk = await centaur.tracks.findOne({TRK_TRACK_ID:nt['@_id']}).lean()

  
  if ((rankPrev.CLA_RANK > 1000 && rankNext.CLA_RANK < 1000) ||(rankPrev.CLA_RANK < 1000 && rankNext.CLA_RANK > 1000)) return 'N'
  if (prevTrk.TRK_COUNTRY_OF_TRACK != nextTrk.TRK_COUNTRY_OF_TRACK) return 'S'
  if ((!rankPrev.CLA_RANK > 174 && !rankNext.CLA_RANK > 174) && (pt['@_location'] == "M" && prevTrk.TRK_STATE_OF_TRACK == 4) && (nt['@_location'] == "M" && [2,3].includes(nextTrk.TRK_STATE_OF_TRACK))) return 'N'

  if ((rankPrev.CLA_CLASS_LONG_DISP.includes('Class') && rankNext.CLA_CLASS_LONG_DISP.includes('Benchmark')) || (rankPrev.CLA_CLASS_LONG_DISP.includes('Benchmark') && rankNext.CLA_CLASS_LONG_DISP.includes('Class'))) return 'N'
  if ((['Restricted','Rating'].includes(rankPrev.CLA_CLASS_LONG_DISP) && rankNext.CLA_CLASS_LONG_DISP.includes('Benchmark')) || (rankPrev.CLA_CLASS_LONG_DISP.includes('Benchmark') && ['Restricted','Rating'].includes(rankNext.CLA_CLASS_LONG_DISP))) diffnum = 5

  if (rankPrev.CLA_RANK > 170 || rankNext.CLA_RANK > 170) diffnum = 1
  if ((rankPrev.CLA_RANK - diffnum) > rankNext.CLA_RANK) return 'D'
  if ((rankNext.CLA_RANK - diffnum) > rankPrev.CLA_RANK) return 'U'
  return 'S'
}

module.exports = {
  classRules,
  calculateUpDown
};
