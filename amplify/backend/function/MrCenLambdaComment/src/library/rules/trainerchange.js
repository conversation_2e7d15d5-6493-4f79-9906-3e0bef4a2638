const util = require('util');
const centaur = require('@mediality/centaur');


const trainerchangeRules = (rule,horse,form,race) => {
  switch (rule.Name)
  {
    case "TrainerChange":
      if (form.form && form.form.length == 0) return false
      var horsetrainer = horse.trainer['@_id']
      for (item of form.form){
        if (item.classes.class_id != 90){
          if (item.trainer_id != horsetrainer){
            return true
          }
          return false
        }
      }
      return false
  }
}

const addAddiblesTrainerChange = async (horse,form,sentence) => {
  var trainer_name = "another trainer"
  PrevTrainerLoop: for (item of form.form){
    if (item.classes.class_id != 90){
      var old_trainer = await centaur.trainers.findOne({TRN_TRAINER_ID:item.trainer_id}).lean()
      trainer_name = old_trainer.TRN_TRAINER_DISPLAYNAME
      break PrevTrainerLoop
    }
  }
  sentence = sentence.replace('{last-trainer}', trainer_name)
  return sentence
}

module.exports = {
  trainerchangeRules,
  addAddiblesTrainerChange
};
