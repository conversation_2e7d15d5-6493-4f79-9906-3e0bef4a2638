const util = require('util');
const centaur = require('@mediality/centaur');


const jockeyRules = async (rule,horse,meeting_class,meeting_state) => {
  switch (rule.Name)
  {
    case "LeadingMetroJockey":
      if (meeting_class != 'M') return false
      var jockeyIsMetro = await centaur.top_jockeys.findOne({$and:[{JockeyId:horse.jockey['@_id']},{Location:'M'},{StateId:meeting_state}]}).lean()
      if (!jockeyIsMetro) return false
      return true
    case "LeadingProvincialJockey":
      if (meeting_class != 'P') return false
      var jockeyIsMetro = await centaur.top_jockeys.findOne({$and:[{JockeyId:horse.jockey['@_id']},{Location:'P'},{StateId:meeting_state}]}).lean()
      if (!jockeyIsMetro) return false
      return true
    case "LeadingCountryJockey":
      if (meeting_class != 'C') return false
      var jockeyIsMetro = await centaur.top_jockeys.findOne({$and:[{JockeyId:horse.jockey['@_id']},{Location:'C'},{StateId:meeting_state}]}).lean()
      if (!jockeyIsMetro) return false
      return true
  }
}

module.exports = {
  jockeyRules
};
