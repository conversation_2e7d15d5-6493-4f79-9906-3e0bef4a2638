const util = require('util');
const moment = require('moment')
const helper = require('../helper.js')

const formIntroRules = (rule,horse,form,formitem) => {
  var finish_pos = setFinishPosition(formitem.finish_position)
  switch (rule.Name)
  {
    case "CameFromLastToWin":
      if ([1].includes(finish_pos) && formitem.starters >= 8 && (formitem.positions['@_m400'] > (formitem.starters * 0.8)))
        return true
      return false
    case "CameFromLastToPlace":
      if ([2,3].includes(finish_pos) && formitem.starters >= 8 && (formitem.positions['@_m400'] > (formitem.starters * 0.8)))
        return true
      return false
    case "CameFromSecondHalfToWin":
      if ([1].includes(finish_pos) && formitem.starters >= 6 && (formitem.positions['@_m400'] > (formitem.starters * 0.5)))
        return true
      return false
    case "CameFromSecondHalfToPlace":
      if ([2,3].includes(finish_pos) && formitem.starters >= 8 && (formitem.positions['@_m400'] > (formitem.starters * 0.5)))
        return true
      return false
    case "Leader":
      if ((finish_pos > 0 && finish_pos < 25) && formitem.starters >= 8 &&
          ([0,1].includes(formitem.positions['@_m1200'])) && ([1].includes(formitem.positions['@_m800'])) &&
          ([1].includes(formitem.positions['@_m400'])))
        return true
      return false
    case "Handy":
      if ((finish_pos > 0 && finish_pos < 25) && ([0,1,2,3,4].includes(formitem.positions['@_m1200'])) && 
          ([1,2,3,4].includes(formitem.positions['@_m800'])) && ([3,4].includes(formitem.positions['@_m400'])))
        return true
      return false
    case "Midfield":
      if (formitem.starters >= 8 && formitem.positions['@_settling_down'] > (formitem.starters * 0.4) && formitem.positions['@_settling_down'] < (formitem.starters * 0.6))
        return true
      return false
    case "Resumed":
      var formWithoutTrials = helper.cleanseBarrierTrials(form.form);
      for (var i=0;i < formWithoutTrials.length; i++){
        if (formWithoutTrials[i] === formitem){
          if (formWithoutTrials[i+1]){
            var formMoment = moment(formWithoutTrials[i].meeting_date)
            var prevMoment = moment(formWithoutTrials[i+1].meeting_date)
            var gap = formMoment.diff(prevMoment, 'days')
            if (gap > 119)
              return true
          } else {
            return false
          }
          break
        }
      }
      return false
    case "Won":
      if ([1].includes(finish_pos))
        return true
      return false
    case "PlacedSmallMargin":
      if ([2,3].includes(finish_pos) && getMarginDecimalValue(form.margin) < 1)
        return true
      return false
    case "PlacedMediumMargin":
      if ([2,3].includes(finish_pos) && getMarginDecimalValue(form.margin) >= 1 && getMarginDecimalValue(form.margin) < 1.5)
        return true
      return false
    case "UnplacedSmallMargin":
      if (finish_pos > 3 && finish_pos < 25 && getMarginDecimalValue(form.margin) < 2)
        return true
      return false
    case "UnplacedLargeMargin":
      if (finish_pos > 3 && finish_pos < 25 && getMarginDecimalValue(form.margin) > 2)
        return true
      return false
    case "OnPace":
      if ((finish_pos > 0 && finish_pos < 25) && ([0,1,2].includes(formitem.positions['@_m1200'])) && 
          ([1,2].includes(formitem.positions['@_m800'])) && ([1,2].includes(formitem.positions['@_m400'])))
        return true
      return false
  }

  return false
}

const formIntroTrialRules = (rule,horse,form,formitem,meeting) => {

  switch (rule.Name)
  {
    case "BarrierTrialNoRuns":
      if (horse.last_four_starts == '')
        return true
      return false
    case "BarrierTrialNoRecentRun":
      var formWithoutTrials = helper.cleanseBarrierTrials(form.form);
      if (!formWithoutTrials[0]){ return false }
      var meetDate = moment(meeting.date)
      var formDate = moment(formWithoutTrials[0].meeting_date)
      var gap = meetDate.diff(formDate, 'days')
      if (gap > 59)
        return true
      return false
    case "BarrierTrialRecentRun":
      var formWithoutTrials = helper.cleanseBarrierTrials(form.form);
      if (!formWithoutTrials[0]){ return false }
      var meetDate = moment(meeting.date)
      var formDate = moment(formWithoutTrials[0].meeting_date)
      var gap = meetDate.diff(formDate, 'days')
      if (gap <= 59)
        return true
      return false
    default:
      return false
  }
  
}

const addAddiblesFormIntro = (horse,form,formitem,sentence) => {
  
  if (sentence.includes("{")){
    var formWithoutTrials = helper.cleanseBarrierTrials(form.form);
    var i = 0
    for (formsnippet of formWithoutTrials){
      if (formsnippet === formitem){break}
      i++
    }
    
    if (formWithoutTrials.length > 0 && sentence.includes("{form-stage}")){
      
      if (form.form[i] && form.form[i+1]){
        var ups = helper.calculateUPs(
          (formWithoutTrials[i]?formWithoutTrials[i].meeting_date:0),
          (formWithoutTrials[i+1]?formWithoutTrials[i+1].meeting_date:0),
          (formWithoutTrials[i+2]?formWithoutTrials[i+2].meeting_date:0),
          (formWithoutTrials[i+3]?formWithoutTrials[i+3].meeting_date:0),
          (formWithoutTrials[i+4]?formWithoutTrials[i+4].meeting_date:0)
        )
        if (!formWithoutTrials[i+1]) {
            sentence = sentence.replace('{form-stage}', 'debut');
        } else if (ups == 1) {
            sentence = sentence.replace('{form-stage}', 'first-up');
        } else {
            sentence = sentence.replace('{form-stage}', '');
        }

      } else {
        sentence = sentence.replace('{form-stage}', '');
      }

    }
  }

  return sentence
}

const genFormBreakdown = (formitem) =>{
    var finish_pos = setFinishPositionStr(formitem.finish_position)
    try{
      var breakdown = ''
      var finishPos = finish_pos.toString();
      switch (finishPos)
      {
          case "1":
              var positions = formatMargin(formitem.official_margin_1)
              breakdown += positions + " win";
              break;
          case "2":
              var positions = formatMargin(formitem.official_margin_1)
              breakdown += positions + " 2nd";
              break;
          case "3":
              var positions = formatMargin(formitem.official_margin_1)
              var positions_2 = formatMargin(formitem.official_margin_2)
              breakdown += positions + ", " + positions_2 + " 3rd";
              break;
          case "21":
              var positions = formatMargin(formitem.margin)
              breakdown += positions + " 21st";
              break;
          case "22":
              var positions = formatMargin(formitem.margin)
              breakdown += positions + " 22nd";
              break;
          case "23":
              var positions = formatMargin(formitem.margin)
              breakdown += positions + " 23rd";
              break;
          default:
              if (!(parseInt(finishPos) > 0)){
                breakdown += finishPos
              } else {
                var positions = formatMargin(formitem.margin)
                breakdown += positions + " " + finishPos + "th";
              }
              break;
      }
      
      var starters = formitem.starters.toString();
      breakdown += " of " + starters;
      var barrier = formitem.barrier.toString();
      breakdown += " (" + barrier + ")";
      var decimalPrice = '0' 
      if (formitem.decimalprices['@_starting'])
        decimalPrice = parseFloat(formitem.decimalprices['@_starting']).toFixed(2).toString();
      if (formitem.decimalprices['@_starting']) breakdown += " $" + decimalPrice;
      var weight = parseFloat(formitem.weight_carried).toFixed(1).toString();
      if (parseFloat(formitem.weight_carried)) breakdown += " " + weight;
      try{
        var winner = helper.capFstLtr(((formitem.other_runners.other_runner[0]['@_position']) != finish_pos) ? formitem.other_runners.other_runner[0]['@_horse'] : formitem.other_runners.other_runner[1]['@_horse']);
        breakdown += " " + winner;
      }catch(err){ }
      var distance = formitem.distance['@_metres'].toString();
      breakdown += " " + distance + "m";
      var track = helper.capFstLtr(formitem.track['@_name']);
      breakdown += " " + track;
      // if (formitem.restrictions['@_age'])
      // {
      //     breakdown += " " + formitem.restrictions['@_age'].toString();
      // }
      // if (formitem.restrictions['@_sex'])
      // {
      //     breakdown += " " + formitem.restrictions['@_sex'].toString();
      // }
      // var theClass = formitem.classes.class;
      var shName = formitem.race['@_name']
      breakdown += " " + shName;
      var trackCond = formitem.track['@_grading'];
      
      switch (trackCond)
      {
          case "0":
              trackCond = "Synthetic(0)";
              break;
          case "1":
              trackCond = "Firm(1)";
              break;
          case "2":
              trackCond = "Firm(2)";
              break;
          case "3":
              trackCond = "Good(3)";
              break;
          case "4":
              trackCond = "Good(4)";
              break;
          case "5":
              trackCond = "Soft(5)";
              break;
          case "6":
              trackCond = "Soft(6)";
              break;
          case "7":
              trackCond = "Soft(7)";
              break;
          case "8":
              trackCond = "Heavy(8)";
              break;
          case "9":
              trackCond = "Heavy(9)";
              break;
          case "10":
              trackCond = "Heavy(10)";
              break;
          case "11":
              trackCond = "Heavy(11)";
              break;
          default:
              trackCond = formitem.track['@_condition'];
              break;
      }
      //convert trackcond to "Heavy(10)
      trackCond = trackCond.replace('(0)', '')
      breakdown += " " + trackCond;
      var meetingDate = moment(formitem.meeting_date);
      var localDate =  moment();
      if ((meetingDate.year() == localDate.year()) || (meetingDate.diff(localDate, 'months') < 6))
      {
          breakdown += " " + meetingDate.format("MMM D");
      }
      else
      {
          breakdown += " " + meetingDate.format("MMM D (YYYY)");
      }

      return breakdown
    } catch(err){
      console.log(`Error with form breakdown ${err}`)
      return ''
    }
        
}

const formatMargin = (margin) =>{
  var length = margin;
  switch (length)
  {
    case "DH": 
        length = 'dead heat';
        break;
      case "NS": 
        length = 'ns';
        break;
      case "SHH": 
        length = 'sht 1/2 hd';
        break;
      case "HH": 
        length = '1/2 hd';
        break;
      case "SH": 
        length = 'sht hd';
        break;
      case "H":  
        length = 'hd';
        break;
      case "HD":  
        length = 'hd';
        break;
      case "LH": 
        length = 'lng hd';
        break;
      case "HN": 
        length = '1/2 nk';
        break;
      case "SN": 
        length = 'sht nk';
        break;
      case "NK": 
        length = 'nk';
        break;
      case "LN": 
        length = 'lg nk';
        break;
      case "DS": 
        length = 'dist';
        break;
      case "Dead Heat":
          length = 'dead heat';
          break;
      case "Nose":
          length = 'ns';
          break;
      case "Short Half Head":
          length = 'sht 1/2 hd';
          break;
      case "Short 1/2 Head":
          length = "SHH";
          break;
      case "Half Head":
          length = '1/2 hd';
          break;
      case "1/2 Head":
          length = "HH";
          break;
      case "Short Head":
          length = 'sht hd';
          break;
      case "Head":
          length = 'hd';
          break;
      case "Long Head":
          length = 'lng hd';
          break;
      case "Half Neck":
          length = '1/2 nk';
          break;
      case "1/2 Neck":
          length = '1/2 nk';
          break;
      case "Short Neck":
          length = 'sht nk';
          break;
      case "Neck":
          length = 'nk';
          break;
      case "Long Neck":
          length = 'lg nk';
          break;
      case "Distanced":
          length = 'dist';
          break;
      default:
          length = getMarginDecimalValue(length).toString();
          var lenSplit = length.split(".");
          length = lenSplit[0];
          if (lenSplit[1])
          {
            switch (lenSplit[1].split()[0])
              {
                  case "1":
                      length += "-1/4";
                      break;
                  case "2":
                      length += "-1/4";
                      break;
                  case "3":
                      length += "-1/4";
                      break;
                  case "4":
                      length += "-1/2";
                      break;
                  case "5":
                      length += "-1/2";
                      break;
                  case "6":
                      length += "-1/2";
                      break;
                  case "7":
                      length += "-3/4";
                      break;
                  case "8":
                      length += "-3/4";
                      break;
                  case "9":
                      length += "-3/4";
                      break;
                  default:
                      break;
              }
          }
          length += " len";
          const regexLength = /^0-/g;
          length = length.replace(regexLength,"")
          break;
  }
  return length;
}


const getMarginDecimalValue = (value) => {
  var decValue = parseFloat(0.0);
  if (!value) return 0;
  if (value[0] === "0" || (parseFloat(value) > 100)) {
      decValue = parseFloat(value) / 10;
      return decValue;
  }
  return parseFloat(value); 
}

const setFinishPosition = (Id) => {
  var map = new Map([
      ["FF", 25],
      ["PU", 26],
      ["FL", 27],
      ["RO", 28],
      ["DQ", 29],
      ["NP", 30],
      ["LS", 31],
      ["LR", 32],
      ["SB", 33],
      ["SC", 34],
      ["BD", 35],
      ["UN", 36]
  ])
  let result = map.get(Id)
  if (result == undefined) result = parseInt(Id)
  return result
}

const setFinishPositionStr = (Id) => {
  var map = new Map([
    [25, "failed to finish"],
    [26, "pulled up"],
    [27, "fell"],
    [28, "RO"],
    [29, "disqualified"],
    [30, "took no part"],
    [31, "LS"],
    [32, "lost rider"],
    [33, "scratched at barrier"],
    [34, "SC"],
    [35, "BD"],
    [36, "unplaced"]
  ])
  let result = map.get(Id)
  if (result == undefined) result = parseInt(Id)
  return result
}

module.exports = {
  formIntroRules,
  formIntroTrialRules,
  addAddiblesFormIntro,
  genFormBreakdown
};
