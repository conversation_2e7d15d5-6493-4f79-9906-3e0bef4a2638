const moment = require('moment')
const helper = require("../helper")

const wetRules = (rule,horse,form,wetobj,race) => {
  var ruleParams = rule.jsonParams
  
  switch (rule.Name)
  {
    case "NoWetStarts":
      if (form.length < 3) return false
      if (wetobj.count > 0) return false
      if (horse.betting > ruleParams.odds_or_above) return false
      return true
    case "NoWetWinsOrPlaces":
      if (wetobj.count < 3) return false
      if (wetobj.place > 0) return false
      if (horse.place_percentage < 1) return false
      if (race.classes && race.classes.class && race.classes.class == 'Maiden') return false
      if (horse.betting > ruleParams.odds_or_above) return false
      return true
    case "WonOftenOnWet":
      if (wetobj.count < 3) return false
      if (wetobj.win < 1) return false
      if (wetobj.place / wetobj.count < 0.5) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      return true
    case "WonFrequentlyInWet":
      if (wetobj.count < 3) return false
      if (wetobj.win < 2) return false
      if (wetobj.place / wetobj.count < 0.67) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      return true
    case "WonMoreThanOnceInWet":
      if (wetobj.count < 3) return false
      if (wetobj.win < 2) return false
      if (wetobj.place / wetobj.count > 0.49) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      return true
    case "AllWinsWet":
      if (wetobj.count < 7) return false
      if (wetobj.win < 3) return false
      if (horse.statistics.statistic[0]['@_firsts'] != wetobj.win) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      return true
  }
}

const getWetObj = (form,spell) => {
  var wetobj = {
    "count": 0,
    "win": 0,
    "place": 0,
    "won_last": false,
    "placed_last": false
  }
  for (item of form){
    if (['H','S','Heavy','Slow','Wet'].includes(item.track['@_condition']) && item.track['@_grading'] != 5){
      if (item.finish_position == 1){
        if (wetobj.count == 0) {
          wetobj.won_last = true
          wetobj.placed_last = true
          wetobj.last_class = item.classes.class ?? ''
          wetobj.last_track = helper.cleverCapFstLtr(item.track['@_name'])
        }
        wetobj.win++
        wetobj.place++
      } else if (['2','3',2,3].includes(item.finish_position)){
        wetobj.place++
        if (wetobj.count == 0) {
          wetobj.placed_last = true
          wetobj.last_class = item.classes.class ?? ''
          wetobj.last_track = helper.cleverCapFstLtr(item.track['@_name'])
        }
      }
      wetobj.count++
    }
  }
  return wetobj
}


module.exports = {
  wetRules,
  getWetObj
};
