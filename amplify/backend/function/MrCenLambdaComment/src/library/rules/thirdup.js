const moment = require('moment')
const helper = require("../helper")

const thirdUpRules = (rule,horse,form,tuobj,last_run,meetdate) => {
  var ruleParams = rule.jsonParams
  
  switch (rule.Name)
  {
    // case "NoThirdUpWinsOrPlaces":
    //   if (moment(meetdate).diff(moment(last_run.meeting_date),'days') > 21) return false
    //   if (horse.betting < ruleParams.odds_or_above) return false
    //   if (tuobj.count < 3) return false
    //   if (tuobj.place > 0) return false
    //   return true
    // case "GoodThirdUp":
    //   if (moment(meetdate).diff(moment(last_run.meeting_date),'days') > 21) return false
    //   if (horse.betting > ruleParams.odds_or_below) return false
    //   if (tuobj.count < 3) return false
    //   if (tuobj.win < 1) return false
    //   if ((tuobj.place / tuobj.count) < 0.5 || (tuobj.place / tuobj.count) > 0.69) return false
    //   return true
    // case "GreatThirdUp":
    //   if (moment(meetdate).diff(moment(last_run.meeting_date),'days') > 21) return false
    //   if (horse.betting > ruleParams.odds_or_below) return false
    //   if (tuobj.win < 2) return false
    //   if ((tuobj.place / tuobj.count) < 0.7) return false
    //   return true
    case "ThirdUpAndGoodOdds":
      if (moment(meetdate).diff(moment(last_run.meeting_date),'days') > 21) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      return true
    // case "ThirdUpAndWonLastPrep":
    //   if (horse.betting > ruleParams.odds_or_below) return false
    //   if (!tuobj.won_last) return false
    //   return true
    // case "ThirdUpAndPlacedLastPrep":
    //   if (horse.betting > ruleParams.odds_or_below) return false
    //   if (!tuobj.placed_last) return false
    //   return true
  }
}

const getThirdUpObj = (form,spell) => {
  var thirdupobj = {
    "count": 0,
    "win": 0,
    "place": 0,
    "won_last": false,
    "placed_last": false
  }
  var meet_date_old_1 = false
  var meet_date_old_2 = false
  var meet_date_old_3 = false
  for (item of form){
    if ((meet_date_old_1 && meet_date_old_2 && meet_date_old_3) && 
    (moment(meet_date_old_2).diff(moment(meet_date_old_3),'days') > spell) && 
    (moment(meet_date_old_1).diff(moment(meet_date_old_2),'days') < spell) &&
    (moment(item.meeting_date).diff(moment(meet_date_old_1),'days') < spell)){
      if (item.finish_position == 1){
        if (thirdupobj.count == 0) {
          thirdupobj.won_last = true
          thirdupobj.placed_last = true
          thirdupobj.last_class = item.classes.class ?? ''
          thirdupobj.last_track = helper.cleverCapFstLtr(item.track['@_name'])
        }
        thirdupobj.win++
        thirdupobj.place++
      } else if (['2','3',2,3].includes(item.finish_position)){
        thirdupobj.place++
        if (thirdupobj.count == 0) {
          thirdupobj.placed_last = true
          thirdupobj.last_class = item.classes.class ?? ''
          thirdupobj.last_track = helper.cleverCapFstLtr(item.track['@_name'])
        }
      }
      thirdupobj.count++
    }
    meet_date_old_3 = meet_date_old_2
    meet_date_old_2 = meet_date_old_1
    meet_date_old_1 = item.meeting_date
  }
  return thirdupobj
}

const addAddiblesThirdUp = (thirdupobj,sentence) => {
  if (sentence.includes("{")){
    

    sentence = sentence.replace('{last-third-up-class}',thirdupobj.last_class) 
    sentence = sentence.replace('{last-third-up-track}',thirdupobj.last_track) 
  }
  return sentence
}



module.exports = {
  thirdUpRules,
  getThirdUpObj,
  addAddiblesThirdUp
};
