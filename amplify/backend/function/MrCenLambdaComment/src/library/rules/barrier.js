const moment = require('moment')
const helper = require("../helper")

const barrierRules = (rule,horse,form,race,starters) => {
  var ruleParams = rule.jsonParams
  
  switch (rule.Name)
  {
    case "InsideBarrier":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (horse.barrier > 4) return false
      return true
    case "OuterBarrier":
      if (horse.betting < ruleParams.odds_or_above) return false
      if (starters < 10) return false
      if (horse.barrier < (parseInt(starters) * 0.8)) return false
      return true
    case "InsideBarrierGoodSettling":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (form.length < 2) return false
      if (!form[0].positions['@_settling_down'] || form[0].positions['@_settling_down'] > 4) return false
      if (!form[1].positions['@_settling_down'] || form[1].positions['@_settling_down'] > 4) return false
      if (starters < 10) return false
      if (horse.barrier > (parseInt(starters) * 0.34)) return false
      return true
    case "OuterBarrierGoodSettling":
      if (horse.betting < ruleParams.odds_or_above) return false
      if (form.length < 2) return false
      if (!form[0].positions['@_settling_down'] || form[0].positions['@_settling_down'] > 4) return false
      if (!form[1].positions['@_settling_down'] || form[1].positions['@_settling_down'] > 4) return false
      if (starters < 10) return false
      if (horse.barrier < (parseInt(starters) * 0.8)) return false
      return true
  }
}





module.exports = {
  barrierRules
};
