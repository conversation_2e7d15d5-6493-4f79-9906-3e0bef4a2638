const util = require('util');


const restRules = (rule,lastrun,spell) => {
  switch (rule.Name)
  {
    case "Spell":
      if (lastrun >= spell)
        return true
      return false
    case "LetUp":
      if (lastrun > 42 && lastrun < spell)
        return true
      return false
    case "Freshened":
      if (lastrun > 21 && lastrun < 43)
        return true
      return false
    case "QuickBackup":
      if (lastrun < 8)
        return true
      return false
    case "LongSpell":
      if (lastrun > 364)
        return true
      return false
  }
}




module.exports = {
  restRules
};
