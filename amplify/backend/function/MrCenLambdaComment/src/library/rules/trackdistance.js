const util = require('util');
const moment = require('moment')
const helper = require('../helper.js')

const trackDistanceRules = (rule,horse,form,race,spell) => {
  var trkDist = {}
  var allStat = {}
  var trkStat = {}
  var distStat = {}
  var ruleParams = rule.jsonParams
  for (stat of horse.statistics.statistic){
    if (stat['@_type'] == 'all') allStat = stat
    if (stat['@_type'] == 'distance_track') trkDist = stat
    if (stat['@_type'] == 'track') trkStat = stat
    if (stat['@_type'] == 'distance') distStat = stat
  } 
  switch (rule.Name)
  {
    case "WonAtThisTrackAndDistance":
      if (ruleParams.ignore_tracks.includes(race.track['@_name'])) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (trkDist['@_total'] < 3) return false
      if (trkDist['@_firsts'] < 2) return false
      if (((trkDist['@_firsts']+trkDist['@_seconds']+trkDist['@_thirds']) / trkDist['@_total']) < 0.4) return false
      return true
    case "WonAtThisTrack":
      if (ruleParams.ignore_tracks.includes(race.track['@_name'])) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (trkStat['@_firsts'] < 2) return false
      // if ((trkStat['@_firsts'] / allStat['@_firsts']) >= 0.5) return false
      tracksWins = {}
      for (run of form){
        if (run.finish_position == 1 && run.track['@_name'].toLowerCase() != race.track['@_name'].toLowerCase()){
          if (!tracksWins[run.track['@_name']]) tracksWins[run.track['@_name']] = 0
          tracksWins[run.track['@_name']] =  tracksWins[run.track['@_name']] + 1
          // console.log('WonAtThisTrack',tracksWins)
          if (tracksWins[run.track['@_name']] >= trkStat['@_firsts']) return false
        }
      }
      return true
    case "LovesThisDistance":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (distStat['@_firsts'] < 2) return false
      if (Math.round(parseFloat(form[0].distance['@_metres']) / 50) == Math.round(parseFloat(race.distance['@_metres']) / 50)) return false
      if (((distStat['@_firsts']+distStat['@_seconds']+distStat['@_thirds']) / distStat['@_total']) < 0.5) return false
      return true
    case "YetToPlacePast":
      if (horse.betting < ruleParams.odds_or_above) return false
      if (parseInt(horse['@_age']) < 4) return false
      if ((allStat['@_firsts']) < 1) return false
      if (parseFloat(race.distance['@_metres']) < 2000) return false
      var checkDist = parseFloat(race.distance['@_metres']) - 400
      var longruncounter = 0
      var hasrunthisdistance = false
      for (run of form){
        if (run.finish_position < 4 && parseFloat(run.distance['@_metres']) > checkDist) return false
        if (parseFloat(run.distance['@_metres']) > checkDist) longruncounter++
        if ((Math.round(parseFloat(run.distance['@_metres']) / 50) == Math.round(parseFloat(race.distance['@_metres']) / 50))) hasrunthisdistance = true
      } 
      if (!hasrunthisdistance) return false
      if (longruncounter < 5) return false
      return true
    case "UpInDistance":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (form.length < 2) return false
      if (horse.last_four_starts.slice(-2).includes('x')) return false
      var checkDist = parseFloat(race.distance['@_metres']) - 200
      if ((parseFloat(form[0].distance['@_metres']) > checkDist) || (parseFloat(form[1].distance['@_metres']) > checkDist)) return false
      return true
  }
}

const addAddiblesTrackDistance = (horse,form,sentence) => {
  if (sentence.includes("{")){
    var place_dist = 0
    for (item of form){
      if ([1,2,3,'1','2','3'].includes(item.finish_position)){
        if (parseInt(item.distance['@_metres']) > place_dist) place_dist = parseInt(item.distance['@_metres'])
      }
    }
    sentence = sentence.replace('{max-place-dist}',place_dist.toString() +'m') 
  }
  return sentence
}

module.exports = {
  trackDistanceRules,
  addAddiblesTrackDistance
};
