const util = require('util');
const centaur = require('@mediality/centaur');


const paceRules = (rule,horse,form,race,starters) => {
  var ruleParams = rule.jsonParams
  switch (rule.Name)
  {
    case "Backmarker":
      if (form.length < 5) return false
      if (starters < 6) return false
      var kount = 0
      for (run of form){
        kount++
        if (!run.positions || !run.positions['@_m400']) return false
        if ((parseFloat(run.positions['@_m400']) / parseFloat(run.starters)) < 0.67) return false
        if (kount > 4) break
      }
      return true
    case "Pacy":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (form.length < 3) return false
      if (parseFloat(race.distance['@_metres']) > 1600) return false
      var kount = 0
      for (run of form){
        kount++
        if (parseFloat(run.distance['@_metres']) > 1600) return false
        if (!run.positions) return false
        if (!run.positions['@_m1200'] == 0 && parseFloat(run.positions['@_m1200']) > 3) return false
        if (!run.positions['@_m800'] == 0 && parseFloat(run.positions['@_m800']) > 3) return false
        if (!run.positions['@_m400'] == 0 && parseFloat(run.positions['@_m400']) > 3) return false
        if (!run.positions['@_settling_down'] == 0 && parseFloat(run.positions['@_settling_down']) > 3) return false
       if (kount > 2) break
      }
      return true
  }
}

module.exports = {
  paceRules
};
