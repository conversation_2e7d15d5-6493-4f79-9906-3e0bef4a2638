const util = require('util');
const centaur = require('@mediality/centaur');
const moment = require('moment')
const helper = require('../helper.js')
const classes = require('./class.js')

const horseprofileRules = (rule,horse,form,race,meetDate) => {
  var ruleParams = rule.jsonParams
  // console.log('horseprofile')
  switch (rule.Name)
  {
    case "ShootingFor":
      if (form.length == 0) return false
      var wincount = 0
      for (item of form){
        if (item.finish_position == 1) wincount = wincount + 1
        else if (wincount < 3 ) return false
        if (wincount > 2) return true
      }
    case "PromisingMaiden":
      if (form.length == 0) return false
      if (form.length < 2 || form.length > 5) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      for (item of form){
          if (item.finish_position == 1) return false
      }
      return true
    case "UnbeatenHorse":
      if (form.length == 0) return false
      if (form.length < ruleParams.min_starts) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      for (item of form){
          if (item.finish_position != 1) return false
      }
      return true
    case "TalentedHorse":
      if (form.length == 0) return false
      if (horse.place_percentage < ruleParams.min_place_percent) return false
      if (form.length < ruleParams.min_starts) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      var wincount = 0
      for (item of form){
          if (item.finish_position == 1) wincount = wincount + 1
      }
      if (wincount >= ruleParams.min_wins) return true
      return false
    case "ConsistentHorse":
      if (form.length == 0) return false
      if (horse.place_percentage < ruleParams.min_place_percent) return false
      if (horse.win_percentage < ruleParams.min_win_percent) return false
      if (form.length < ruleParams.min_starts) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      var formcount = 0
      for (item of form){
          if (![1,2,3,4].includes(item.finish_position)) return false
          formcount = formcount + 1
          if (formcount > 2) return true
      }
      return false
    case "HandyHorse":
      if (form.length == 0) return false
      if (horse.place_percentage < ruleParams.min_place_percent) return false
      if (horse.win_percentage < ruleParams.min_win_percent) return false
      if (form.length < ruleParams.min_starts) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      var formcount = 0
      for (item of form){
        if (![1,2,3,4].includes(item.finish_position)) return false
        formcount = formcount + 1
        if (formcount > 1) return true
      }
      return false  
    case "VeteranBadOdds":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (horse['@_age'] < ruleParams.min_age) return false
      if (horse['@_age'] > ruleParams.max_age) return false
      if (form.length == 0) return false
      if (form.length < ruleParams.min_starts) return false
      if (horse.betting < ruleParams.odds_or_above) return false
      var wincount = 0
      for (item of form){
          if (item.finish_position == 1){
            wincount = wincount + 1
            if (wincount >= ruleParams.min_wins) return true
          } 
      }
      return false  
    case "VeteranGoodOdds":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (horse['@_age'] < ruleParams.min_age) return false
      if (horse['@_age'] > ruleParams.max_age) return false
      if (form.length == 0) return false
      if (form.length < ruleParams.min_starts) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      var wincount = 0
      for (item of form){
          if (item.finish_position == 1){
            wincount = wincount + 1
            if (wincount >= ruleParams.min_wins) return true
          } 
      }
      return false  
    case "OldTimerBadOdds":
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (horse['@_age'] < ruleParams.min_age) return false
      if (horse.betting < ruleParams.odds_or_above) return false
      return true  
    case "OldTimerGoodOdds":
      if (form.length == 0) return false
      if (['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (horse['@_age'] < ruleParams.min_age) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (horse.win_percentage < ruleParams.min_win_percent) return false
      if (form.length < ruleParams.min_starts) return false
      return true
    case "ReadyToWin":
      if (form.length == 0) return false
      if (form.length < 2) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      var formcount = 0
      var theDate = moment(meetDate)
      for (item of form){
        var formdate =  moment(item.meeting_date)
        var datediff = theDate.diff(formdate,'days')
        theDate = formdate
        if ([0,1].includes(formcount)){
          if (datediff > 28) return false
          if (![2,3,4].includes(item.finish_position)) return false
        } else if ([2,3].includes(formcount)){
          if (datediff > 28) return true
          if (![2,3,4,5,6].includes(item.finish_position)) return false
        } else return true
        formcount = formcount + 1
      }
      return true
    case "ChasingHatTrick":
      if (form.length == 0) return false
      if (form.length < 2) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      var formcount = 0
      var theDate = moment(meetDate)
      for (item of form){
        var formdate =  moment(item.meeting_date)
        var datediff = theDate.diff(formdate,'days')
        theDate = formdate
        if ([0,1].includes(formcount)){
          if (datediff > 28) return false
          if (item.finish_position != 1) return false
        } else if (formcount == 2){
          if (datediff > 28) return true
          if (item.finish_position == 1) return false
        } else return true
        formcount = formcount + 1
      }
      return true
    case "InForm":
      if (form.length == 0) return false
      if (form.length < 7) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      var formcount = 0
      var theDate = moment(meetDate)
      for (item of form){
        var formdate =  moment(item.meeting_date)
        var datediff = theDate.diff(formdate,'days')
        theDate = formdate
        if ([0].includes(formcount)){
          if (datediff > 28)return false
          if (item.finish_position != 1)return false
        }  else if ([1].includes(formcount)){
          if (datediff > 28)return false
          if ([2,3,4].includes(item.finish_position))return false
        } else if ([2].includes(formcount)){
          if (datediff > 28)return false
          if (item.finish_position != 1)return false
        } else return true
        formcount = formcount + 1
      }
      return false
    case "RacingWell":
      if (form.length == 0) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      var formcount = 0
      var theDate = moment(meetDate)
      for (item of form){
        var formdate =  moment(item.meeting_date)
        var datediff = theDate.diff(formdate,'days')
        if ([0,1,2].includes(formcount)){
          if (datediff > 61) return false
          if (![2,3,4].includes(item.finish_position))return false
          if (item.starters < 8) return false
        } else {
          return true
        }
        formcount = formcount + 1
      }
      return false
    case "JumpsDebut":
      if (!['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (horse.betting < ruleParams.odds_or_above) return false
      for (item of form){
        if (helper.runIsJumps(item)) return false
      }
      return true
    case "WonOnlyJumps":
      if (form.length == 0) return false
      if (!['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      var jumpscount = 0
      var wonjumps = false
      for (item of form){
        if (helper.runIsJumps(item)){
          jumpscount = jumpscount + 1
          if (item.finish_position == 1) wonjumps = true
        }
      }
      if (jumpscount == 1 && wonjumps) return true
      return false
    case "PlacedOnlyJumps":
      if (form.length == 0) return false
      if (!['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      var jumpscount = 0
      var placedjumps = false
      for (item of form){
        if (helper.runIsJumps(item)){
          jumpscount = jumpscount + 1
          if ([2,3].includes(item.finish_position)) placedjumps = true
        }
      }
      if (jumpscount == 1 && placedjumps) return true
      return false
    case "WonLatestJumps":
      if (form.length == 0) return false
      if (!['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      var jumpscount = 0
      var wonjumps = false
      for (item of form){
        if (helper.runIsJumps(item)){
          if (jumpscount == 0 && item.finish_position == 1) wonjumps = true
          jumpscount = jumpscount + 1
        }
      }
      if (jumpscount > 1 && wonjumps) return true
      return false
    case "PlacedLatestJumps":
      if (form.length == 0) return false
      if (!['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      var jumpscount = 0
      var placedjumps = false
      for (item of form){
        if (helper.runIsJumps(item)){
          if (jumpscount == 0 && [2,3].includes(item.finish_position)) placedjumps = true
          jumpscount = jumpscount + 1
        }
      }
      if (jumpscount > 1 && placedjumps) return true
      return false
    case "WasntDisgracedLatestJumps":
      if (form.length == 0) return false
      if (!['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (horse.betting < ruleParams.odds_or_above) return false
      var jumpscount = 0
      var competedjumps = false
      for (item of form){
        if (helper.runIsJumps(item)){
          if (jumpscount == 0 && item.finish_position > 3 && item.finish_position <= Math.floor(item.starters / 2) ) competedjumps = true
          jumpscount = jumpscount + 1
        }
      }
      if (jumpscount > 1 && competedjumps) return true
      return false
    case "BattledHomeLatestJumps":
      if (form.length == 0) return false
      if (!['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (horse.betting < ruleParams.odds_or_above) return false
      var jumpscount = 0
      var battledjumps = false
      for (item of form){
        if (helper.runIsJumps(item)){
          if (jumpscount == 0 && item.finish_position > Math.floor(item.starters / 2) && item.finish_position <= Math.floor(item.starters * 0.8) ) battledjumps = true
          jumpscount = jumpscount + 1
        }
      }
      if (jumpscount > 1 && battledjumps) return true
      return false
    case "DidLittleLatestJumps":
      if (form.length == 0) return false
      if (!['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (horse.betting < ruleParams.odds_or_above) return false
      var jumpscount = 0
      var flounderedjumps = false
      for (item of form){
        if (helper.runIsJumps(item)){
          if (jumpscount == 0 && item.finish_position > Math.floor(item.starters * 0.8)) flounderedjumps = true
          jumpscount = jumpscount + 1
        }
      }
      if (jumpscount > 1 && flounderedjumps) return true
      return false
    case "FellLatestJumps":
      if (form.length == 0) return false
      if (!['Jumps','Hurdle','Steeple','Steeplechase'].includes(race.race_type)) return false
      if (horse.betting < ruleParams.odds_or_above) return false
      var jumpscount = 0
      var felljumps = false
      for (item of form){
        if (helper.runIsJumps(item)){
          if (jumpscount == 0 && (item.finish_position == 27 || item.finish_position == 'FL')) felljumps = true
          jumpscount = jumpscount + 1
        }
      }
      if (jumpscount > 1 && felljumps) return true
      return false
  }
}

const addAddiblesHorseProfile = async (horse,race,form,sentence,rule) => {
  if (sentence.includes("{")){
    if (sentence.includes("{wins-in-row}") ){
      var wincount = 1
      for (item of form){
        
        if (item.finish_position == 1) wincount = wincount + 1
        else break 
        
      }
      sentence = sentence.replace('{wins-in-row}',wincount.toString()+"th") 
    }

    sentence = sentence.replace('{age}',horse['@_age'].toString()+"yo") 
    sentence = sentence.replace('{sex}',helper.getSexName(horse['@_sex'])) 
    sentence = sentence.replace('{gender}',(['Filly','Mare'].includes(helper.getSexName(horse['@_sex'])) ? 'her' : 'his'))
    // {last-at-odds} {last-track} {form-stage}
    if (sentence.includes("{last-at-odds}") && form[0].decimalprices && form[0].decimalprices['@_starting']){
      if (parseFloat(form[0].decimalprices['@_starting']) > 30) sentence = sentence.replace('{last-at-odds}',"at long odds")
      if (parseFloat(form[0].decimalprices['@_starting']) < 2.51) sentence = sentence.replace('{last-at-odds}',"at short odds")
      sentence = sentence.replace('{last-at-odds}',"")
    } else {
      sentence = sentence.replace('{last-at-odds}',"")
    }
    if (sentence.includes("{last-track}") && form[0].track && form[0].track['@_name']){
      if (form[0].track['@_id'] == race.track['@_id']){
        sentence = sentence.replace('{last-track}','here')
      } else {
        sentence = sentence.replace('{last-track}','at '+form[0].track['@_name'])
      }
      
    } else {
      sentence = sentence.replace('{last-track}',"")
    }
    if (sentence.includes("{form-stage}") && form.length > 0){
      if (form.length == 1){
        sentence = sentence.replace('{form-stage}','on debut')
      } else if (form[0].days_since_last_run > 86){
        sentence = sentence.replace('{form-stage}','first-up')
      } else {
        sentence = sentence.replace('{form-stage}',getFormStageItem())
      }
    } else {
      sentence = sentence.replace('{form-stage}',"")
    }
    if (sentence.includes("{last-under-weight}") && form.length > 0){
      if (form[0].weight_carried > 59.5){
        sentence = sentence.replace('{last-under-weight}','under '+form[0].weight_carried.toString()+"kg")
      } else {
        sentence = sentence.replace('{last-under-weight}','')
      }
    } else {
      sentence = sentence.replace('{last-under-weight}',"")
    }
    if (sentence.includes("{last-in-wet}") && form.length > 0){
      if (form[0].track && form[0].track['@_grading'] && form[0].track['@_grading'] > 7){
        sentence = sentence.replace('{last-in-wet}','in the wet')
      } else {
        sentence = sentence.replace('{last-in-wet}','')
      }
    } else {
      sentence = sentence.replace('{last-in-wet}',"")
    }
    if (sentence.includes("{class-direction}") && (race.classes.class_id != 30 && (!race.classes.second_class_id || race.classes.second_class_id != 30)) && form.length > 0){
      if (form[0].weight_carried > 0){
        var classdir = await classes.calculateUpDown(race.classes.class_id,form[0].classes.class_id,race.track,form[0].track)
        var dirsentence = helper.classDirectionSynonyms(classdir)
        sentence = sentence.replace('{class-direction}',dirsentence)
      } else {
        sentence = sentence.replace('{class-direction}','')
      }
    } else {
      sentence = sentence.replace('{class-direction}',"")
    }
    if (sentence.includes("{starts}")) sentence = sentence.replace('{starts}',form.length.toString())
  }

  return sentence
}


const getFormStageItem = () =>{

  var formstagearray = ['at latest','last start','last outing','last time out']

  return formstagearray[Math.floor(Math.random()*formstagearray.length)];

}

 

module.exports = {
  horseprofileRules,
  addAddiblesHorseProfile
};
