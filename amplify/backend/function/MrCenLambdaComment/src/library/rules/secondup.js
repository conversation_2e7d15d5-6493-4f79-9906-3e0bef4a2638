const moment = require('moment')
const helper = require("../helper")

const secondUpRules = (rule,horse,form,suobj,last_run,meetdate) => {
  var ruleParams = rule.jsonParams
  
  switch (rule.Name)
  {
    case "NoSecondUpWinsOrPlaces":
      if (moment(meetdate).diff(moment(last_run.meeting_date),'days') > 21) return false
      if (horse.betting < ruleParams.odds_or_above) return false
      if (suobj.count < 3) return false
      if (suobj.place > 0) return false
      return true
    case "GoodSecondUp":
      if (moment(meetdate).diff(moment(last_run.meeting_date),'days') > 21) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (suobj.count < 3) return false
      if (suobj.win < 1) return false
      if ((suobj.place / suobj.count) < 0.5 || (suobj.place / suobj.count) > 0.69) return false
      return true
    case "GreatSecondUp":
      if (moment(meetdate).diff(moment(last_run.meeting_date),'days') > 21) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      if (suobj.win < 2) return false
      if ((suobj.place / suobj.count) < 0.7) return false
      return true
    case "SecondUpAndGoodOdds":
      if (moment(meetdate).diff(moment(last_run.meeting_date),'days') > 21) return false
      if (horse.betting > ruleParams.odds_or_below) return false
      return true
    case "SecondUpAndWonLastPrep":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (!suobj.won_last) return false
      return true
    case "SecondUpAndPlacedLastPrep":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (!suobj.placed_last) return false
      return true
  }
}

const getSecondUpObj = (form,spell) => {
  var secondupobj = {
    "count": 0,
    "win": 0,
    "place": 0,
    "won_last": false,
    "placed_last": false
  }
  var meet_date_old_1 = false
  var meet_date_old_2 = false
  for (item of form){
    if ((meet_date_old_1 && meet_date_old_2) && (moment(meet_date_old_2).diff(moment(meet_date_old_1),'days') > spell) && (moment(item.meeting_date).diff(moment(meet_date_old_1),'days') < spell)){
      if (item.finish_position == 1){
        if (secondupobj.count == 0) {
          secondupobj.won_last = true
          secondupobj.placed_last = true
          secondupobj.last_class = item.classes.class ?? ''
          secondupobj.last_track = helper.cleverCapFstLtr(item.track['@_name'])
        }
        secondupobj.win++
        secondupobj.place++
      } else if (['2','3',2,3].includes(item.finish_position)){
        secondupobj.place++
        if (secondupobj.count == 0) {
          secondupobj.placed_last = true
          secondupobj.last_class = item.classes.class ?? ''
          secondupobj.last_track = helper.cleverCapFstLtr(item.track['@_name'])
        }
      }
      secondupobj.count++
    }
    meet_date_old_2 = meet_date_old_1
    meet_date_old_1 = item.meeting_date
  }
  return secondupobj
}

const addAddiblesSecondUp = (secondupobj,sentence) => {
  if (sentence.includes("{")){
    

    sentence = sentence.replace('{last-second-up-class}',secondupobj.last_class) 
    sentence = sentence.replace('{last-second-up-track}',secondupobj.last_track) 
  }
  return sentence
}



module.exports = {
  secondUpRules,
  getSecondUpObj,
  addAddiblesSecondUp
};
