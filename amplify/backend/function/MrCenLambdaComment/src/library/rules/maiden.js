const moment = require('moment')
const helper = require("../helper")

const maidenRules = (rule,horse,form,meeting,updown) => {
  var ruleParams = rule.jsonParams
  
  switch (rule.Name)
  {
    case "EasierRaceCloseMargin":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (horse.win_percentage > 0) return false
      if (form.length < ruleParams.min_starts) return false
      if ((form[0].beaten_margin && form[0].beaten_margin > ruleParams.max_margin) || (form[0].margin && form[0].margin > ruleParams.max_margin)) return false
      if (!form[0].track['@_location']) return false
      if (form[0].track['@_location'] == 'C') return false
      if (form[0].track['@_location'] == 'M' && meeting.processedMeetingData.meeting.track['@_location'] == 'M') return false
      if (form[0].track['@_location'] == 'P' && ['M','P'].includes(meeting.processedMeetingData.meeting.track['@_location'])) return false
      return true
    case "LongTimeMaiden":
      if (horse.betting < ruleParams.odds_or_above) return false
      if (form.length < ruleParams.min_starts) return false
      if (horse.win_percentage > 0) return false
      return true
    case "EasierRaceMediumMargin":
      if (horse.betting > ruleParams.odds_or_below) return false
      if (horse.win_percentage > 0) return false
      if (form.length < ruleParams.min_starts) return false
      if ((form[0].beaten_margin && form[0].beaten_margin > ruleParams.max_margin) || (form[0].margin && form[0].margin > ruleParams.max_margin)) return false
      if ((form[0].beaten_margin && form[0].beaten_margin < ruleParams.min_margin) || (form[0].margin && form[0].margin < ruleParams.min_margin)) return false
      if (!form[0].track['@_location']) return false
      if (form[0].track['@_location'] == 'C') return false
      if (form[0].track['@_location'] == 'M' && meeting.processedMeetingData.meeting.track['@_location'] == 'M') return false
      if (form[0].track['@_location'] == 'P' && ['M','P'].includes(meeting.processedMeetingData.meeting.track['@_location'])) return false
      return true
    case "EasierRaceLongMargin":
      if (horse.betting < ruleParams.odds_or_above) return false
      if (horse.win_percentage > 0) return false
      if (form.length < ruleParams.min_starts) return false
      if ((form[0].beaten_margin && form[0].beaten_margin < ruleParams.min_margin) || (form[0].margin && form[0].margin < ruleParams.min_margin)) return false
      if (!form[0].track['@_location']) return false
      if (form[0].track['@_location'] == 'C') return false
      if (form[0].track['@_location'] == 'M' && meeting.processedMeetingData.meeting.track['@_location'] == 'M') return false
      if (form[0].track['@_location'] == 'P' && ['M','P'].includes(meeting.processedMeetingData.meeting.track['@_location'])) return false
      return true
    case "BackToCountry":
      console.log("BackToCountry",horse.betting,ruleParams.odds_or_above,meeting.processedMeetingData.meeting.track['@_location'],form[0].track['@_location'],updown)
      if (horse.betting > ruleParams.odds_or_below) return false
      if (meeting.processedMeetingData.meeting.track['@_location'] !== 'C') return false
      if (!form[0].track['@_location'] || form[0].track['@_location'] === 'C') return false
      if (['U','N'].includes(updown)) return false
      return true
    case "BackToProvincial":
      if (horse.betting < ruleParams.odds_or_above) return false
      if (meeting.processedMeetingData.meeting.track['@_location'] !== 'P') return false
      if (!form[0].track['@_location'] || form[0].track['@_location'] !== 'M') return false
      if (['U','N'].includes(updown)) return false
      return true
  }
}





module.exports = {
  maidenRules
};
