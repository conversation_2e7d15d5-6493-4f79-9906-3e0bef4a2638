const util = require('util');
const moment = require('moment')
const helper = require('../helper.js')

const trialRules = (rule,horse,form) => {
  switch (rule.Name)
  {
    case "ShowTrialStatistics":
      if (form.form && form.form[0])
        return true
      return false
    case "HasNoTrials":
      if (!form.form)
        return true
      if (!form.form[0])
        return true
      return false
  }
  

  
}

const addAddiblesTrials = (horse,form,sentence) => {
  var starts = 0, firsts = 0, seconds = 0, thirds = 0
  if (form.form && form.form[0]){
    for (item of form.form){
      starts++
      if (item.finish_position == 1){ firsts++ } 
      if (item.finish_position == 2){ seconds++ } 
      if (item.finish_position == 3){ thirds++ } 
    }
  }
  sentence = sentence.replace('{trials-starts}', starts.toString());
  sentence = sentence.replace('{trials-firsts}', firsts.toString());
  sentence = sentence.replace('{trials-seconds}', seconds.toString());
  sentence = sentence.replace('{trials-thirds}', thirds.toString());
  return sentence
}


module.exports = {
  trialRules,
  addAddiblesTrials
};
