const moment = require("moment");
const util = require("util");
const AWS = require("aws-sdk");

const getSecrets = async (secretName) => {
  const AWS = require("aws-sdk");
  var secretsmanager = new AWS.SecretsManager();
  var params = {
    SecretId: secretName,
  };
  const fetchSecretString = await secretsmanager
    .getSecretValue(params)
    .promise();
  aws_secrets = JSON.parse(fetchSecretString.SecretString);
  return aws_secrets;
};
const getHorseColorAbbr = (color) => {
  var result = "";
  switch (color) {
    case "CHESTNUT":
      result = "ch";
      break;
    case "BAY":
      result = "b";
      break;
    case "BROWN":
      result = "br";
      break;
    case "BLACK":
      result = "bl";
      break;
    case "GREY":
      result = "gr";
      break;
    case "WHITE":
      result = "wh";
      break;
    case "BAY OR BROWN":
      result = "b/br";
      break;
    case "BROWN OR BLACK":
      result = "br/bl";
      break;
    case "GREY-CHESTNUT":
      result = "gr/ch";
      break;
    case "GREY-BAY":
      result = "gr/b";
      break;
    case "GREY-BROWN":
      result = "gr/br";
      break;
    case "GREY-BLACK":
      result = "gr/bl";
      break;
    case "GREY-ROAN":
      result = "gr/ro";
      break;
    case "ROAN":
      result = "ro";
      break;
    default:
      result = "b";
  }
  return result;
};

const openDBConnection = async (env) => {
  console.log("open connection", env);
  const local = require("./local");
  const mongoose = require("mongoose");

  if (env == "local") {
    //Local environment

    var cert_path = "./rds-cert/global-bundle.pem";
    console.log("Running Locally");
    DB_USERNAME = local.DB_USERNAME;
    DB_PASSWORD = local.DB_PASSWORD;
    DB_URL = local.DB_URL;
    DB_NAME = local.DB_NAME;
    CONNECTION_STRING =
      "mongodb://" +
      DB_USERNAME +
      ":" +
      DB_PASSWORD +
      "@" +
      DB_URL +
      "/" +
      DB_NAME;
    CONNECTION_STRING =
      "mongodb://root:Test.123!@127.0.0.1:27000/dev-centaur?directConnection=true&ssl=true&retrywrites=false&tls=true";
    await mongoose
      .connect(CONNECTION_STRING, {
        connectTimeoutMS: 1000,
        tlsCAFile: cert_path,
        directConnection: true,
        ssl: true,
        sslValidate: false,
        maxPoolSize: 10,
      })
      .then(
        () => {
          console.log("Connected to Local");
          response = "Connect to Local";
        },
        (err) => {
          console.log("Not connected:" + err);
          response = "Not Connected to Local";
        }
      );
  } else {
    console.log("Running Online");
    var aws_secrets = await getSecrets(process.env.centaurSecrets);
    DB_USERNAME = aws_secrets.DB_USERNAME;
    DB_PASSWORD = aws_secrets.DB_PASSWORD;
    DB_URL = aws_secrets.DB_URL;
    DB_NAME = aws_secrets.DB_NAME;
    CONNECTION_STRING =
      "mongodb://" +
      DB_USERNAME +
      ":" +
      DB_PASSWORD +
      "@" +
      DB_URL +
      "/" +
      DB_NAME +
      "?tls=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false";
    await mongoose
      .connect(CONNECTION_STRING, {
        connectTimeoutMS: 1000,
        tlsCAFile: cert_path,
        directConnection: true,
        ssl: true,
        sslValidate: false,
        maxPoolSize: 10,
      })
      .then(
        () => {
          console.log("Connected to Online");
          response = "Connect Online";
        },
        (err) => {
          console.log("Not connected:" + err);
          response = "Not Connected Online";
        }
      );
  }
  return mongoose;
};

const closeDBConnection = async (con) => {
  con.connection.close();
  console.log("Connection Closed");
};
const upperCase = (str = "") => {
  if (!str) return "";
  return str.toUpperCase();
};
const logError = (error_log, errors, newError) => {
  // console.log(error_log, errors, newError)
  if (
    !((error_log ?? []).includes(newError)) && 
    !((errors ?? []).includes(newError))
  ) {
    errors.push(newError);
    error_log.push(newError)
  }
  // console.log('finished error')
  return [error_log, errors];
};

const sDate = (dt) => {
  let floatingDate = moment(dt, "DD/MM/YYYY").toDate();
  return floatingDate;
  //return moment(dt).format().toString();
};
const convertDate = (dt) => {
  //  dd/mm/yyyy

  return moment(dt).format("DD/MM/YYYY");
};

const getHorseSex = (sex, age) => {
  if (["F", "C"].includes(sex) && parseInt(age) > 3) {
    if (sex == "F") sex = "M";
    if (sex == "C") sex = "H";
  }
  return sex;
};

const getHorseAge = (foalingDate) => {
  var a = moment(foalingDate);
  if (a.month() < 6) {
    a.subtract(1, "years");
  }
  a.set({ month: 0, date: 1 });
  var b = moment();
  if (b.get("month") < 7) {
    b.subtract(1, "years");
  }
  b.set({ month: 0, date: 1 });
  let result = b.diff(a, "years");
  return result;
};
const capFstLtr = (str = "") => {
  if (!str) return "";
  return str
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};
const getPastDateByDay = (days = 0) => {
  var date = moment();
  var result = date.subtract(days, "day").format("YYYY-MM-DD"); //+ "T00:00:00.000+00.00";
  return result;
};

const cleanseBarrierTrials = (data) => {
  var cleanseddata = [];
  for (raceEvent of data) {
    // if (raceEvent.weight_carried > 0){
    //     cleanseddata.push(raceEvent)
    // }
    if (
      (raceEvent.classes &&
        raceEvent.classes.class_id &&
        raceEvent.classes.class_id != 90 &&
        (!raceEvent.classes.second_class_id ||
          raceEvent.classes.second_class_id != 90)) ||
      (raceEvent.classes &&
        raceEvent.classes.class &&
        raceEvent.classes.class == "Open" &&
        (!raceEvent.classes.second_class_id ||
          raceEvent.classes.second_class_id != 90))
    ) {
      cleanseddata.push(raceEvent);
    }
  }
  return cleanseddata;
};
const convertFP = (finish) =>{
  var fp_map = finish.toString()
  var map = new Map([
      ["25", "0"],
      ["26", "0"],
      ["27", "F"],
      ["28", "0"],
      ["29", "Q"],
      ["30", "0"],
      ["31", "0"],
      ["32", "L"],
      ["33", "0"],
      ["34", "0"],
      ["35", "0"],
      ["36", "0"],
      ["FF", "0"],
      ["PU", "0"],
      ["FL", "F"],
      ["RO", "0"],
      ["DQ", "Q"],
      ["NP", "0"],
      ["LS", "0"],
      ["LR", "L"],
      ["SB", "0"],
      ["SC", "0"],
      ["BD", "0"],
      ["UN", "0"],
      ["failed to finish", "0"],
  ])
  let result = map.get(fp_map)
  if (result == undefined) {
      if (finish > 9) {result = "0"}
      else {result = fp_map}
  }

  return result
};
const processStarts = (num, data, meetingDate) => {
  try {
    var result = "";
    var spell = 84;
    if (data.length == 0) return result;
    var x = moment(meetingDate);
    var y = moment(data[0].meeting_date);
    var gap = x.diff(y, "days");
    result = gap >= spell ? "x" : "";
    if (data.length == 1) {
      result = convertFP(data[0].finish_position) + result;
    }
    if (data.length >= 2) {
      for (i = 0; i < num; i++) {
        if (data[i] && data[i + 1]) {
          var a = moment(data[i].meeting_date);
          var b = moment(data[i + 1].meeting_date);
          var gaptwo = a.diff(b, "days");
          result =
            (gaptwo >= spell ? "x" : "") +
            convertFP(data[i].finish_position) +
            result;
        } else if (data[i]) {
          result = convertFP(data[i].finish_position) + result;
        } else {
          continue;
        }
      }
      if (num >= 4) {
        var final_value = result.substring(result.length - num, result.length);
        return final_value;
      }
    }
  } catch (err) {
    console.log("Error Generating process Starts " + err);
    return result;
  }

  return result;
};

const calculatePercentage = (position, trackRating, data) => {
  var starts = 0;
  var targets = 0;
  for (form of data) {
    starts++;
    if (
      form.track["@_grading"] in trackRating &&
      form.finish_position in position
    ) {
      targets++;
    }
  }
  if (targets == 0) {
    return 0;
  } else {
    return Math.round((targets / starts) * 100);
  }
};

const getPace = (formItems, distance, starts) => {
  var startsSplit = starts.split("");
  var inRunningCount = 0;
  var inRunningArray = [];
  for (var i = 0; i < startsSplit.length; i++) {
    var numberInRunning = 0;
    var sumInRunning = 0;
    if (startsSplit[i] == "x") {
      continue;
    } else if (startsSplit[i + 1] && startsSplit[i + 1] == "x") {
      continue;
    } else if (!startsSplit[i + 1]) {
      break;
    } else if (inRunningCount > 7) {
      break;
    }
    if (formItems[i]) {
      if (
        formItems[i].distance["@_metres"] < distance - 400 ||
        formItems[i].distance["@_metres"] > distance + 200
      ) {
        continue;
      }
      if (formItems[i].positions["@_m400"] > 0) {
        numberInRunning++;
        sumInRunning =
          sumInRunning + parseInt(formItems[i].positions["@_m400"]);
      }
      if (formItems[i].positions["@_m800"] > 0) {
        numberInRunning++;
        sumInRunning =
          sumInRunning + parseInt(formItems[i].positions["@_m800"]);
      }
      if (formItems[i].positions["@_m1200"] > 0) {
        numberInRunning++;
        sumInRunning =
          sumInRunning + parseInt(formItems[i].positions["@_m1200"]);
      }
      if (formItems[i].positions["@_settling_down"] > 0) {
        numberInRunning++;
        sumInRunning =
          sumInRunning + parseInt(formItems[i].positions["@_settling_down"]);
      }
      if (numberInRunning > 0) {
        inRunningCount++;
        inRunningArray.push(sumInRunning / numberInRunning);
      }
    }
  }
  var finalFigure = 0;
  if (inRunningArray.length > 0) {
    for (inRun of inRunningArray) {
      finalFigure = finalFigure + inRun;
    }
    finalFigure = finalFigure / inRunningArray.length;
  }
  var paceRank = [0, ""];
  if (finalFigure > 10.05) {
    paceRank = [6, "Rear"];
  } else if (finalFigure > 7.15) {
    paceRank = [5, "Mid"];
  } else if (finalFigure > 6.05) {
    paceRank = [4, "Handy/Mid"];
  } else if (finalFigure > 3.95) {
    paceRank = [3, "Handy"];
  } else if (finalFigure > 2.55) {
    paceRank = [2, "Lead/Handy"];
  } else if (finalFigure > 0) {
    paceRank = [1, "Lead"];
  } else {
    paceRank = [7, "?"];
  }

  return paceRank;
};
const getMR = async (ratings, distance, firstUp) => {
  var ratingSelect = "";
  if (distance < 1251) {
    ratingSelect = "mr1";
  } else if (distance < 1500) {
    ratingSelect = "mr2";
  } else if (distance < 1800) {
    ratingSelect = "mr3";
  } else {
    ratingSelect = "mr4";
  }
  if (firstUp) {
    ratingSelect = ratingSelect + "_d";
  }
  if (ratingSelect != "") {
    return ratings[ratingSelect];
  }
};
const generateBasicRating = async (
  master_rating,
  age,
  sex,
  distance,
  weight,
  meeting_date
) => {
  var wfa = await getWfa(age, sex, distance, meeting_date);
  
  var wamr = master_rating + (wfa - weight);
  wamr = Math.round(wamr * 2) / 2;

  return wamr;
};
const generateRating = async (
  master_rating,
  age,
  sex,
  distance,
  weight,
  meeting_date
) => { 
  var wfa = await getWfa(age, sex, distance, meeting_date);
  var ld = Math.log(distance);
  var awfa = weight - wfa;
  var ld2 = ld * ld;
  var wamr = master_rating - (-0.184 * ld2 + (3.3981 * ld - 14.016)) * awfa;
  wamr = Math.round(wamr * 2) / 2;
  
  return wamr;
};
const runningGears = (changes, running_gear) => {
  var ran_gear = running_gear;
  for (gear_item of changes) {
    if (gear_item["@_option"].includes("off")) {
      const item_index = ran_gear.indexOf(gear_item["@_name"]);
      if (item_index > -1) {
        ran_gear.splice(item_index, 1);
      } else if (gear_item["@_name"].toLowerCase() == "blinkers") {
        for (item of ran_gear) {
          if (item.toLowerCase().includes("blinkers")) {
            ran_gear.splice(ran_gear.indexOf(item), 1);
            break;
          }
        }
      } else if (gear_item["@_name"].toLowerCase() == "winkers") {
        for (item of ran_gear) {
          if (item.toLowerCase().includes("winkers")) {
            ran_gear.splice(ran_gear.indexOf(item), 1);
            break;
          }
        }
      } else {
        var x = tryMatchGear(gear_item["@_name"], ran_gear);
        if (x > -1) {
          ran_gear.splice(x, 1);
        }
      }
    } else if (
      !ran_gear.includes(gear_item["@_name"]) &&
      gear_item["@_name"].toLowerCase() != "gelded"
    ) {
      ran_gear.push(gear_item["@_name"]);
    }
  }
  return ran_gear;
};

const getWfa = async (age,sex,distance,date) => {
  if (age > 4){
      age = "5+"
  } else {
      age = age.toString()
  }
  if (distance < 1201){
      distance = "1200"
  } else if (distance < 1401){
      distance = "1400"
  } else if (distance < 1601){
      distance = "1600"
  } else if (distance < 2001){
      distance = "2000"
  } else if (distance < 2401){
      distance = "2400"
  } else {
      distance = "3200"
  }
  var theDate = moment(date)
  var theMonth = theDate.month()+1

  if (theMonth < 10){
      theMonth = "0"+ theMonth.toString()
  }
  
  var sexAdjust = 0

  if (['M','F'].includes(sex)){
      sexAdjust = 2 
  }

  

  var wfa = {
      "1200":{
          "2":{
              "08": 42.5,
              "09": 42.5,
              "10": 42.5,
              "11": 42.5,
              "12": 42.5,
              "01": 45.0,
              "02": 46.0,
              "03": 47.0,
              "04": 48.0,
              "05": 49.0,
              "06": 50.0,
              "07": 51.0
          },
          "3":{
              "08": 51.5,
              "09": 52.0,
              "10": 53.0,
              "11": 53.5,
              "12": 54.5,
              "01": 55.0,
              "02": 55.5,
              "03": 56.0,
              "04": 56.5,
              "05": 57.0,
              "06": 57.5,
              "07": 58.0
          },
          "4":{
              "08": 58.5,
              "09": 58.5,
              "10": 58.5,
              "11": 58.5,
              "12": 58.5,
              "01": 58.5,
              "02": 58.5,
              "03": 58.5,
              "04": 58.5,
              "05": 58.5,
              "06": 58.5,
              "07": 58.5
          },
          "5+":{
              "08": 58.5,
              "09": 58.5,
              "10": 58.5,
              "11": 58.5,
              "12": 58.5,
              "01": 58.5,
              "02": 58.5,
              "03": 58.5,
              "04": 58.5,
              "05": 58.5,
              "06": 58.5,
              "07": 58.5
          }
      },
      "1400":{
          "2":{
              "08": 42.5,
              "09": 42.5,
              "10": 42.5,
              "11": 42.5,
              "12": 42.5,
              "01": 44.0,
              "02": 45.0,
              "03": 46.0,
              "04": 47.0,
              "05": 48.0,
              "06": 49.0,
              "07": 50.0
          },
          "3":{
              "08": 50.5,
              "09": 51.0,
              "10": 52.0,
              "11": 53.0,
              "12": 54.0,
              "01": 54.5,
              "02": 55.5,
              "03": 56.0,
              "04": 56.5,
              "05": 57.0,
              "06": 57.5,
              "07": 58.0
          },
          "4":{
              "08": 58.5,
              "09": 58.5,
              "10": 58.5,
              "11": 59.0,
              "12": 59.0,
              "01": 59.0,
              "02": 59.0,
              "03": 59.0,
              "04": 59.0,
              "05": 59.0,
              "06": 59.0,
              "07": 59.0
          },
          "5+":{
              "08": 59.0,
              "09": 59.0,
              "10": 59.0,
              "11": 59.0,
              "12": 59.0,
              "01": 59.0,
              "02": 59.0,
              "03": 59.0,
              "04": 59.0,
              "05": 59.0,
              "06": 59.0,
              "07": 59.0
          }
      },
      "1600":{
          "2":{
              "08": 42.5,
              "09": 42.5,
              "10": 42.5,
              "11": 42.5,
              "12": 42.5,
              "01": 43.5,
              "02": 44.5,
              "03": 45.5,
              "04": 46.5,
              "05": 47.5,
              "06": 48.5,
              "07": 49.5
          },
          "3":{
              "08": 50.0,
              "09": 50.5,
              "10": 51.0,
              "11": 52.0,
              "12": 53.0,
              "01": 54.0,
              "02": 55.0,
              "03": 56.0,
              "04": 56.5,
              "05": 57.0,
              "06": 57.5,
              "07": 58.0
          },
          "4":{
              "08": 58.5,
              "09": 58.5,
              "10": 58.5,
              "11": 59.0,
              "12": 59.0,
              "01": 59.0,
              "02": 59.0,
              "03": 59.0,
              "04": 59.0,
              "05": 59.0,
              "06": 59.0,
              "07": 59.0
          },
          "5+":{
              "08": 59.0,
              "09": 59.0,
              "10": 59.0,
              "11": 59.0,
              "12": 59.0,
              "01": 59.0,
              "02": 59.0,
              "03": 59.0,
              "04": 59.0,
              "05": 59.0,
              "06": 59.0,
              "07": 59.0
          }
      },
      "2000":{
          "2":{
              "08": 42.5,
              "09": 42.5,
              "10": 42.5,
              "11": 42.5,
              "12": 42.5,
              "01": 42.5,
              "02": 43.5,
              "03": 44.5,
              "04": 45.5,
              "05": 46.5,
              "06": 47.5,
              "07": 48.5
          },
          "3":{
              "08": 49.0,
              "09": 49.5,
              "10": 50.0,
              "11": 51.0,
              "12": 52.0,
              "01": 53.0,
              "02": 54.0,
              "03": 54.5,
              "04": 55.5,
              "05": 56.5,
              "06": 57.0,
              "07": 57.5
          },
          "4":{
              "08": 58.0,
              "09": 58.0,
              "10": 58.0,
              "11": 58.5,
              "12": 58.5,
              "01": 58.5,
              "02": 59.0,
              "03": 59.0,
              "04": 59.0,
              "05": 59.0,
              "06": 59.0,
              "07": 59.0
          },
          "5+":{
              "08": 59.0,
              "09": 59.0,
              "10": 59.0,
              "11": 59.0,
              "12": 59.0,
              "01": 59.0,
              "02": 59.0,
              "03": 59.0,
              "04": 59.0,
              "05": 59.0,
              "06": 59.0,
              "07": 59.0
          }
      },
      "2400":{
          "2":{
              "08": 42.5,
              "09": 42.5,
              "10": 42.5,
              "11": 42.5,
              "12": 42.5,
              "01": 42.5,
              "02": 42.5,
              "03": 42.5,
              "04": 42.5,
              "05": 42.5,
              "06": 42.5,
              "07": 42.5
          },
          "3":{
              "08": 48.5,
              "09": 49.0,
              "10": 49.5,
              "11": 50.5,
              "12": 51.0,
              "01": 52.0,
              "02": 53.0,
              "03": 54.0,
              "04": 54.5,
              "05": 55.5,
              "06": 56.0,
              "07": 57.0
          },
          "4":{
              "08": 57.5,
              "09": 57.5,
              "10": 57.5,
              "11": 58.0,
              "12": 58.0,
              "01": 58.0,
              "02": 58.5,
              "03": 58.5,
              "04": 58.5,
              "05": 59.0,
              "06": 59.0,
              "07": 59.0
          },
          "5+":{
              "08": 59.0,
              "09": 59.0,
              "10": 59.0,
              "11": 59.0,
              "12": 59.0,
              "01": 59.0,
              "02": 59.0,
              "03": 59.0,
              "04": 59.0,
              "05": 59.0,
              "06": 59.0,
              "07": 59.0
          }
      },
      "3200":{
          "2":{
              "08": 42.5,
              "09": 42.5,
              "10": 42.5,
              "11": 42.5,
              "12": 42.5,
              "01": 42.5,
              "02": 42.5,
              "03": 42.5,
              "04": 42.5,
              "05": 42.5,
              "06": 42.5,
              "07": 42.5
          },
          "3":{
              "08": 48.0,
              "09": 48.5,
              "10": 49.0,
              "11": 50.0,
              "12": 50.5,
              "01": 51.5,
              "02": 52.5,
              "03": 53.5,
              "04": 54.0,
              "05": 55.0,
              "06": 55.5,
              "07": 56.0
          },
          "4":{
              "08": 57.5,
              "09": 57.5,
              "10": 57.5,
              "11": 58.0,
              "12": 58.0,
              "01": 58.0,
              "02": 58.5,
              "03": 58.5,
              "04": 58.5,
              "05": 59.0,
              "06": 59.0,
              "07": 59.0
          },
          "5+":{
              "08": 59.5,
              "09": 59.5,
              "10": 59.5,
              "11": 59.5,
              "12": 59.5,
              "01": 59.5,
              "02": 59.5,
              "03": 59.5,
              "04": 59.5,
              "05": 59.5,
              "06": 59.5,
              "07": 59.5
          }
      }
  }
  
  if (wfa[distance] && wfa[distance][age] && wfa[distance][age][theMonth]){
      return (wfa[distance][age][theMonth] - sexAdjust)
  } else {
      console.log('Error with ratings: wfa not found')
  }

}



module.exports = {
  getSecrets,
  openDBConnection,
  closeDBConnection,
  upperCase,
  getHorseColorAbbr,
  sDate,
  logError,
  getHorseAge,
  getHorseSex,
  convertDate,
  capFstLtr,
  getPastDateByDay,
  cleanseBarrierTrials,
  processStarts,
  calculatePercentage,
  getPace,
  getMR,
  generateBasicRating,
  generateRating,
  runningGears
};
