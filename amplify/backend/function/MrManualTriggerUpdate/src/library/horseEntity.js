const helper = require("./helper");
const stats = require("./stats");
// Import Models
const centaur = require("@mediality/centaur");
const genHorseLevelData_AUS = async (
  Horse,
  Race,
  meetingDate,
  meetingStage
) => {
  try {
    let name = "",
      country = "",
      age = "",
      sex = "",
      horse_id = "";
    let trainer_stats = "";
    let prizmoney_won = 0,
      last4s = "",
      last10s = "",
      last15s = "",
      last20s = "";
    let win_percentage = "",
      place_percentage = "",
      win_distances = "",
      pace = [0, ""];
    let acceptances = "";
    let class_quality = "F";
    let rating = 0,
      ratingWfa = 0,
      firstUp = true;
    let currTrack = await centaur.tracks.findOne({
      TRK_TRACK_DB_ID: Race.track["@_id"],
    });
    // Fetch Horse data from horse_names table
    const horseData = await centaur.horses
      .findOne({ HRN_HORSE_ID: Horse["@_id"] })
      .lean();

    if (horseData) {
      const horseId = horseData.HRN_HORSE_ID;
      name =
        horseData.HRN_DISPLAY_NAME ??
        helper.capFstLtr(horseData.HRN_HORSE_NAME);
      country = horseData.HOR_COUNTRY_OF_ORIGIN;
      age = helper.getHorseAge(horseData.HOR_FOALING_DATE);
      sex = helper.getHorseSex(horseData.HOR_SEX, age);
      horse_id = horseData.HRN_HORSE_ID;
      let error_log = Horse.error_log ?? [];
      let errors = Horse.errors;
      if (!Horse.trainer || !Horse.trainer["@_id"]) {
        helper.logError(error_log, errors, `Trainer not found for horse.`);
      } else {
        const trainer_data = await centaur.trainers
          .findOne({ TRN_TRAINER_ID: Horse.trainer["@_id"] })
          .lean();
        if (trainer_data) {
          trainer_stats = await stats.getTrainerStats(
            trainer_data.TRN_TRAINER_ID
          );
        } else {
          helper.logError(
            error_log,
            errors,
            `Trainer ${Horse.trainer["@_name"]} with id ${Horse.trainer["@_id"]} not found in database, perhaps its changed?`
          );
        }
      }

      prizmoney_won = parseInt(horseData.HOR_TOTAL_PRIZEMONEY);

      const formData = await centaur.form.find({ horse_id: horseId }).lean();
      var formItems = [];
      if (formData[0] && formData[0].form) {
        try {
          formItems = helper.cleanseBarrierTrials(formData[0].form);
          if (Array.isArray(formItems)) {
            last4s = helper.processStarts(4, formItems, meetingDate).toString();
            last10s = helper
              .processStarts(10, formItems, meetingDate)
              .toString();
            last15s = helper
              .processStarts(15, formItems, meetingDate)
              .toString();
            last20s = helper
              .processStarts(20, formItems, meetingDate)
              .toString();
            var alltrack = [
              "0",
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "7",
              "8",
              "9",
              "10",
              "11",
            ];
            win_percentage = helper.calculatePercentage(
              [1, 101],
              alltrack,
              formItems
            );
            place_percentage = helper.calculatePercentage(
              [1, 2, 3, 101],
              alltrack,
              formItems
            );
            pace = helper.getPace(
              formItems,
              Race.distance["@_metres"],
              last20s
            );
            win_distances = stats.getWinDistance(formItems);
            let classID = Race.classes.class_id;
            if (Race.group) {
              classID = "group " + Race.group;
            }
            if (formItems[0] && formItems[0].group) {
              class_quality = upDownClass(
                classID,
                "group " + formItems[0].group.toString()
              );
            } else if (
              formItems[0] &&
              formItems[0].classes &&
              formItems[0].classes.class_id
            ) {
              class_quality = upDownClass(
                classID,
                formItems[0].classes.class_id
              );
            } else {
              class_quality = "F";
            }

            if (
              formItems[0] &&
              formItems[0].track &&
              formItems[0].track["@_id"] &&
              (!formItems[0].track["@_country"] ||
                formItems[0].track["@_country"] == "")
            ) {
              console.log("curr1: ", currTrack);
              var lasttrack = await centaur.tracks.findOne({
                TRK_TRACK_DB_ID: formItems[0].track["@_id"],
              });
              console.log("curr1: ", lasttrack);
              console.log("Check for track register");
              console.log(lasttrack.TRK_COUNTRY_OF_TRACK);
              console.log(currTrack.TRK_COUNTRY_OF_TRACK);
              if (
                lasttrack &&
                currTrack &&
                currTrack.TRK_COUNTRY_OF_TRACK != ""
              ) {
                if (
                  lasttrack.TRK_COUNTRY_OF_TRACK !=
                  currTrack.TRK_COUNTRY_OF_TRACK
                ) {
                  if (lasttrack.TRK_COUNTRY_OF_TRACK === "") {
                    helper.logError(
                      error_log,
                      errors,
                      `${name} ${horse_id} - has its last registered form item on a track with no country set: ${lasttrack.TRK_TRACK_NAME} ${lasttrack.TRK_TRACK_DB_ID}`
                    );
                  } else {
                    helper.logError(
                      error_log,
                      errors,
                      `${name} ${horse_id} - has its last registered form item in ${lasttrack.TRK_COUNTRY_OF_TRACK}`
                    );
                    console.log("error_log:", error_log);
                    console.log("errors:", errors);
                  }
                }
              } else {
                helper.logError(
                  error_log,
                  errors,
                  `${name} ${horse_id} - cant find track ${formItems[0].track["@_name"]} for last form item. The track seems to not be in the database.`
                );
              }
            } else if (
              currTrack &&
              currTrack.TRK_COUNTRY_OF_TRACK != "" &&
              formItems[0] &&
              formItems[0].track &&
              formItems[0].track["@_country"] &&
              formItems[0].track["@_country"] != currTrack.TRK_COUNTRY_OF_TRACK
            ) {
              console.log("curr2:", currTrack);
              console.log(formItems[0].track);
              // console.log(helper.logError(error_log,errors,`test`))
              helper.logError(
                error_log,
                errors,
                `${name} ${horse_id} - has its last registered form item in ${formItems[0].track["@_country"]}`
              );
            } else if (
              formItems[0] &&
              (!formItems[0].track || !formItems[0].track["@_id"])
            ) {
              console.log("track-not-found!", formItems[0]);
              helper.logError(
                error_log,
                errors,
                `${name} ${horse_id} - cant find track ${formItems[0].track["@_name"]} for last form item. The track seems to not be in the database.`
              );
            }
          }
        } catch (err) {
          console.log(err);
          console.log("error_log:", error_log);
          console.log("errors:", errors);
          helper.logError(
            error_log,
            errors,
            `${name} ${horse_id} - starts generation error (copy this for the IT folks): ${err}`
          );
          console.log(error_log);
          // console.log(`starts generation error with ${horse_id}: ${err}`);
          // throw new Error(`Error at genHorseLevelData_AUS ${horse_id}: ${err}`);
        }
      }
      var isweight = Horse.weight_carried ?? false;
      try {
        if (last4s != "" && last4s.slice(last4s.length - 1) != "x") {
          firstUp = false;
        }
          if (["NZ", "HK", "SGP"].includes(currTrack.TRK_COUNTRY_OF_TRACK) || Race.locked) {
            if (Horse.rating) rating = Horse.rating;
          } else if (
            horseData.HOR_RATINGS &&
            horseData.HOR_RATINGS.mr1 &&
            horseData.HOR_RATINGS.mr1 !== "0"
          ) {
            rating = await helper.getMR(
              horseData.HOR_RATINGS,
              Race.distance["@_metres"],
              firstUp
            ); //CHANGE THIS WHEN YOU FIND OUT
            ratingWfa = rating;

            // Apply Gear Change Rating Adjustments
            const gearChangeAdjustments = {
              Gelded: { adjustment: 3, id: "30" },
              Blinkers: {
                "first time": { adjustment: 3, id: "356" },
                again: { adjustment: 2, id: "356" },
              },
              "Tongue Tie": {
                "first time": { adjustment: 1, id: "60" },
              },
              "Ear Muffs": { "first time": { adjustment: 1, id: "29" } },
              "Ear Muffs (Pre-Race Only)": {
                "first time": { adjustment: 1, id: "413" },
              },
              "Bar Plates": {
                off: { adjustment: 1, id: "67" },
                "first time": { adjustment: -3, id: "67" },
              },
              "Nose Roll": { "first time": { adjustment: 1, id: "42" } },
              Winkers: { "first time": { adjustment: 1, id: "62" } },
              "Glue On Shoes": { "first time": { adjustment: -1, id: "78" } },
              Bandages: { adjustment: -2, id: "4" },
              "Concussion Plates": {
                "first time": { adjustment: -2, id: "26" },
              },
            };

            if (Horse.gear_changes && Horse.gear_changes.gear_change) {
              const gearChanges = Array.isArray(Horse.gear_changes.gear_change)
                ? Horse.gear_changes.gear_change
                : [Horse.gear_changes.gear_change];
              //console.log("Gear changes", JSON.stringify(gearChanges));
              gearChanges.forEach((gear) => {
                const gearName = gear["@_name"];
                const gearOption = gear["@_option"]
                  ? gear["@_option"].toLowerCase()
                  : "";
                //console.log("gearName", gearName);
                //console.log("Gear option", JSON.stringify(gearOption));
                if (gearChangeAdjustments[gearName]) {
                  let adjustment;

                  if (gearName === "Gelded") {
                    // For Gelded, apply adjustment without checking the option
                    adjustment = gearChangeAdjustments[gearName].adjustment;
                    //console.log("Gelded", gearName);
                    //console.log("Adjustment", adjustment);
                  } else if (gearName === "Bandages") {
                    // For Bandages, only apply adjustment if race type is 'flat'
                    if (
                      Race.race_type &&
                      Race.race_type.toLowerCase() === "flat"
                    ) {
                      adjustment = gearChangeAdjustments[gearName].adjustment;
                    }
                    //console.log("Bandages", gearName);
                    //console.log("Adjustment", adjustment);
                  } else if (
                    typeof gearChangeAdjustments[gearName] === "object" &&
                    gearChangeAdjustments[gearName][gearOption]
                  ) {
                    adjustment =
                      gearChangeAdjustments[gearName][gearOption].adjustment;
                    //console.log("adjustment", adjustment);
                  } else if (gearChangeAdjustments[gearName].adjustment) {
                    adjustment = gearChangeAdjustments[gearName].adjustment;
                    //console.log("adjustment", adjustment);
                  }

                  if (adjustment !== undefined) {
                    //console.log("Rating for Horse", Horse["@_name"], rating);
                    rating += adjustment;
                  }
                }
              });
            }
            // Apply Pace Rating Adjustments
            const paceRatingAdjustments = {
              1: 2,
              2: 1,
              3: 0,
              4: 0,
              5: -1,
              6: -2,
              7: 0,
            };

            rating += paceRatingAdjustments[pace[0]] ?? 0;
          } else {
            rating = 0;
            ratingWfa = 0;
          }
          if (rating > 0 && isweight) {
            ratingWfa = await helper.generateBasicRating(
              rating,
              age,
              sex,
              Race.distance["@_metres"],
              isweight,
              meetingDate
            );
            rating = await helper.generateRating(
              rating,
              age,
              sex,
              Race.distance["@_metres"],
              isweight,
              meetingDate
            );
          }
        
        
      } catch (err) {
        console.log(
          `((ERROR)): Couldnt generate rating for ${Horse["@_name"]} ${err}`
        );
        console.log(horseData);
        rating = 0;
        throw new Error(
          `Error generating ratings at genHorseLevelData_AUS: ${err}`
        );
      }
      //Acceptance data - to be updated at Acceptance stage
      if (meetingStage == "Acceptances" || meetingStage == "FinalFields") {
        // Jockey
        if (
          !Horse.scratched &&
          Horse.jockey &&
          Horse.jockey["@_firstname"] &&
          Horse.jockey["@_surname"]
        ) {
          var jockey_data = await centaur.jockeys
            .findOne({
              JOC_JOCKEY_ID: Horse.jockey["@_id"],
            })
            .lean();
          if (!jockey_data && Horse.jockey["@_name"]) {
            jockey_data = await centaur.jockeys
              .findOne({ JOC_JOCKEY_RANAME: Horse.jockey["@_name"] })
              .lean();
          }
          if (jockey_data) {
            let jockey_id = jockey_data.JOC_JOCKEY_ID;
            let jockey_record = {
              jockey: {
                statistics: await stats.getJockeyStats(jockey_id),
              },
            };
            acceptances = Object.assign(acceptances, jockey_record);
          }
        }
      }
      var horse = {
        "@_name": name,
        "@_country": country,
        "@_age": age,
        "@_sex": sex,
        "@_id": horse_id,
        trainer: {
          statistics: trainer_stats,
        },
        prizemoney_won: prizmoney_won,
        last_four_starts: last4s,
        last_ten_starts: last10s,
        last_fifteen_starts: last15s,
        last_twenty_starts: last20s,
        FF5_dry: "0",
        FF5_wet: "0",
        FF_Dry_Rating_100: "100",
        FF_Wet_Rating_100: "100",
        win_percentage: win_percentage,
        place_percentage: place_percentage,
        pace_value: pace[0],
        pace: pace[1],
        win_distances: win_distances,
        class_quality: class_quality,
        rating: rating,
        rating_wfa: ratingWfa,
        errors: errors,
        error_log: error_log,
      };
      console.log(Horse['@_name'],'rating',horse.rating)
      //Updating data for Acceptance and Final Fields Stage
      if (meetingStage == "Acceptances" || meetingStage == "FinalFields") {
        horse = Object.assign(horse, acceptances);
      }
      // Horse for InputDataMeeting
    }
    const horse_data = {
      horse_processedMeetingData: horse,
    };
    return horse_data;
  } catch (error) {
    console.log(`Error at genHorseLevelData_AUS: ${Horse["@_name"]} ${error}`);
    throw new Error(`Error at genHorseLevelData_AUS: ${error}`);
  }
};

const upDownClass = (next_class, last_class) => {
  if (
    next_class.toString().includes("group") &&
    !last_class.toString().includes("group")
  )
    return "U";
  if (
    !next_class.toString().includes("group") &&
    last_class.toString().includes("group")
  )
    return "D";
  if (
    next_class.toString().includes("group") &&
    last_class.toString().includes("group")
  ) {
    if (next_class === last_class) return "S";
    if (next_class === "group 1") return "U";
    if (next_class === "group 2" && last_class !== "group 1") return "U";
    if (next_class === "group 3" && last_class === "group LR") return "U";
    return "D";
  }
  let last = centaur.race_classes
    .findOne({ CLA_CLASS_DB_ID: last_class })
    .select("CLA_RANK")
    .lean();
  let next = centaur.race_classes
    .findOne({ CLA_CLASS_DB_ID: next_class })
    .select("CLA_RANK")
    .lean();
  return last > next ? "D" : next > last ? "U" : "S";
};

module.exports = {
  genHorseLevelData_AUS,
};
