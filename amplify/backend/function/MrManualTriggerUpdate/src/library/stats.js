const helper = require("./helper");
// Import Models
const centaur = require("@mediality/centaur");
const getTrainerStats = async (trainerId = 0) => {
  try {
    var st = 0;
    var s1 = 0;
    var s2 = 0;
    var s3 = 0;

    const pastDate = new Date(helper.getPastDateByDay(365)); //Calculate past year date from today
    const statsDataTotal = await centaur.form_index
      .find()
      .where("trainer_id")
      .equals(trainerId)
      .where("meeting_date")
      .gt(pastDate)
      .lean();

    for (const race of statsDataTotal) {
      if (race.weight_carried == 0) {
        continue;
      }
      st++;
      if (race.finish_pos == 1) {
        s1++;
      } else if (race.finish_pos == 2) {
        s2++;
      } else if (race.finish_pos == 3) {
        s3++;
      }
    }

    const stats = {
      statistic: {
        "@_type": "one_year", //Fix value of 365 days
        "@_total": st.toString(),
        "@_firsts": s1.toString(),
        "@_seconds": s2.toString(),
        "@_thirds": s3.toString(),
      },
    };
    return stats;
  } catch (err) {
    console.log(`((ERROR)): Trainer Stats Error - ${trainerId}:` + err);
  }
};
const getJockeyStats = async (jockeyId = 0) => {
  try {
    var st = 0;
    var s1 = 0;
    var s2 = 0;
    var s3 = 0;

    const pastDate = new Date(helper.getPastDateByDay(365)); //Calculate past year date from today
    const statsDataTotal = await centaur.form_index
      .find()
      .where("jockey_id")
      .equals(jockeyId)
      .where("meeting_date")
      .gt(pastDate)
      .lean();

    for (const race of statsDataTotal) {
      if (race.weight_carried == 0) {
        continue;
      }
      if (race.weight_carried > 0) {
        st++;
        if (race.finish_pos == 1) {
          s1++;
        } else if (race.finish_pos == 2) {
          s2++;
        } else if (race.finish_pos == 3) {
          s3++;
        }
      }
    }

    const stats = {
      statistic: {
        "@_type": "one_year", //Fix value of 365 days
        "@_total": st.toString(),
        "@_firsts": s1.toString(),
        "@_seconds": s2.toString(),
        "@_thirds": s3.toString(),
      },
    };
    return stats;
  } catch (err) {
    console.log("((ERROR)): Jockey Stats Error - " + err);
    throw new Error(err);
  }
};
const getWinDistance = (formItems) => {
    try {
      var win_distance = {};
      for (run of formItems) {
        if (run.positions && run.finish_position == 1) {
          var run_distance = run.distance["@_metres"];
          if (win_distance[run_distance]) {
            win_distance[run_distance] = win_distance[run_distance] + 1;
          } else {
            win_distance[run_distance] = 1;
          }
        }
      }
      var dataToUpdate = [];
      for (const win in win_distance) {
        let temp = {
          "@_distance": win,
          "@_wins": win_distance[win],
        };
        dataToUpdate.push(temp);
      }
      var winDistances = {
        win_distance: dataToUpdate,
      };
  
      return winDistances;
    } catch (err) {
      // var sendMail = await helper.mailAlert('Trainer Stats error',`Trainer Stats - ${err}`,'error')
      // console.log(sendMail)
      console.log("((ERROR)): Win Distances Error - " + err);
    }
  };
  
module.exports = {
  getTrainerStats,
  getJockeyStats,
  getWinDistance
};
