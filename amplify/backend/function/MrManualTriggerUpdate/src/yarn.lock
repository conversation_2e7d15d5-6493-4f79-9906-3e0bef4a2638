# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@aws-crypto/crc32@3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/crc32/-/crc32-3.0.0.tgz#07300eca214409c33e3ff769cd5697b57fdd38fa"
  integrity sha512-IzSgsrxUcsrejQbPVilIKy16kAT52EwB6zSaI+M3xxIhKh5+aldEyvI+z6erM7TCLB2BJsFrtHjp6/4/sr+3dA==
  dependencies:
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^1.11.1"

"@aws-crypto/crc32c@3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/crc32c/-/crc32c-3.0.0.tgz#016c92da559ef638a84a245eecb75c3e97cb664f"
  integrity sha512-ENNPPManmnVJ4BTXlOjAgD7URidbAznURqD0KvfREyc4o20DPYdEldU1f5cQ7Jbj0CJJSPaMIk/9ZshdB3210w==
  dependencies:
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^1.11.1"

"@aws-crypto/ie11-detection@^3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/ie11-detection/-/ie11-detection-3.0.0.tgz#640ae66b4ec3395cee6a8e94ebcd9f80c24cd688"
  integrity sha512-341lBBkiY1DfDNKai/wXM3aujNBkXR7tq1URPQDL9wi3AUbI80NR74uF1TXHMm7po1AcnFk8iu2S2IeU/+/A+Q==
  dependencies:
    tslib "^1.11.1"

"@aws-crypto/sha1-browser@3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/sha1-browser/-/sha1-browser-3.0.0.tgz#f9083c00782b24714f528b1a1fef2174002266a3"
  integrity sha512-NJth5c997GLHs6nOYTzFKTbYdMNA6/1XlKVgnZoaZcQ7z7UJlOgj2JdbHE8tiYLS3fzXNCguct77SPGat2raSw==
  dependencies:
    "@aws-crypto/ie11-detection" "^3.0.0"
    "@aws-crypto/supports-web-crypto" "^3.0.0"
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-locate-window" "^3.0.0"
    "@aws-sdk/util-utf8-browser" "^3.0.0"
    tslib "^1.11.1"

"@aws-crypto/sha256-browser@3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/sha256-browser/-/sha256-browser-3.0.0.tgz#05f160138ab893f1c6ba5be57cfd108f05827766"
  integrity sha512-8VLmW2B+gjFbU5uMeqtQM6Nj0/F1bro80xQXCW6CQBWgosFWXTx77aeOF5CAIAmbOK64SdMBJdNr6J41yP5mvQ==
  dependencies:
    "@aws-crypto/ie11-detection" "^3.0.0"
    "@aws-crypto/sha256-js" "^3.0.0"
    "@aws-crypto/supports-web-crypto" "^3.0.0"
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-locate-window" "^3.0.0"
    "@aws-sdk/util-utf8-browser" "^3.0.0"
    tslib "^1.11.1"

"@aws-crypto/sha256-js@3.0.0", "@aws-crypto/sha256-js@^3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/sha256-js/-/sha256-js-3.0.0.tgz#f06b84d550d25521e60d2a0e2a90139341e007c2"
  integrity sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==
  dependencies:
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^1.11.1"

"@aws-crypto/supports-web-crypto@^3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/supports-web-crypto/-/supports-web-crypto-3.0.0.tgz#5d1bf825afa8072af2717c3e455f35cda0103ec2"
  integrity sha512-06hBdMwUAb2WFTuGG73LSC0wfPu93xWwo5vL2et9eymgmu3Id5vFAHBbajVWiGhPO37qcsdCap/FqXvJGJWPIg==
  dependencies:
    tslib "^1.11.1"

"@aws-crypto/util@^3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/util/-/util-3.0.0.tgz#1c7ca90c29293f0883468ad48117937f0fe5bfb0"
  integrity sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==
  dependencies:
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-utf8-browser" "^3.0.0"
    tslib "^1.11.1"

"@aws-lambda-powertools/commons@^1.18.1":
  version "1.18.1"
  resolved "https://registry.yarnpkg.com/@aws-lambda-powertools/commons/-/commons-1.18.1.tgz#fcfdef39639105a7b2b5363e4bcade9d277f5468"
  integrity sha512-gFRgQ2GJDghKvf+fXvT0kQVftgOT05W+hCa7RkfZj6HSjVAO+9DZZeJL3JK1HcsLAjWRj7W9ra0/MqB3Abf+PQ==

"@aws-lambda-powertools/logger@^1.5.1":
  version "1.18.1"
  resolved "https://registry.yarnpkg.com/@aws-lambda-powertools/logger/-/logger-1.18.1.tgz#6388ddbafca6b3f65277b7a364df45f426f5c592"
  integrity sha512-GsSMqaFXCSz+llSOn2CVNMoN+j/jNsS6JP2Opy9myU0tvg7PeuU3+rN24vKyibUwpxM466IzWFBSJkYdm0bqVw==
  dependencies:
    "@aws-lambda-powertools/commons" "^1.18.1"
    lodash.merge "^4.6.2"

"@aws-lambda-powertools/tracer@^1.5.1":
  version "1.18.1"
  resolved "https://registry.yarnpkg.com/@aws-lambda-powertools/tracer/-/tracer-1.18.1.tgz#9a6c618abb195d0e2cc25b2cd36469f9d63317bd"
  integrity sha512-bMLBtdEFNmLUR9RJvBULR6XJD0XopUhhS1mlpeQlm2BCPIN3gLbqAlJK8dMXyAw8GCpLpHaziCo2+7a/AIh7lA==
  dependencies:
    "@aws-lambda-powertools/commons" "^1.18.1"
    aws-xray-sdk-core "^3.5.3"

"@aws-sdk/client-api-gateway@^3.54.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-api-gateway/-/client-api-gateway-3.572.0.tgz#8cb616a601dc32e239f9b30833968c8920cd8086"
  integrity sha512-/XBS1qA/MFyJZ4GQ+276zGPBjecp2zeX5+fa8bRK9wj0Df3+OTtDyAsodBx9MRHwoC0R2zNbuUo8cOcb3tH/UQ==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/client-sso-oidc" "3.572.0"
    "@aws-sdk/client-sts" "3.572.0"
    "@aws-sdk/core" "3.572.0"
    "@aws-sdk/credential-provider-node" "3.572.0"
    "@aws-sdk/middleware-host-header" "3.567.0"
    "@aws-sdk/middleware-logger" "3.568.0"
    "@aws-sdk/middleware-recursion-detection" "3.567.0"
    "@aws-sdk/middleware-sdk-api-gateway" "3.567.0"
    "@aws-sdk/middleware-user-agent" "3.572.0"
    "@aws-sdk/region-config-resolver" "3.572.0"
    "@aws-sdk/types" "3.567.0"
    "@aws-sdk/util-endpoints" "3.572.0"
    "@aws-sdk/util-user-agent-browser" "3.567.0"
    "@aws-sdk/util-user-agent-node" "3.568.0"
    "@smithy/config-resolver" "^2.2.0"
    "@smithy/core" "^1.4.2"
    "@smithy/fetch-http-handler" "^2.5.0"
    "@smithy/hash-node" "^2.2.0"
    "@smithy/invalid-dependency" "^2.2.0"
    "@smithy/middleware-content-length" "^2.2.0"
    "@smithy/middleware-endpoint" "^2.5.1"
    "@smithy/middleware-retry" "^2.3.1"
    "@smithy/middleware-serde" "^2.3.0"
    "@smithy/middleware-stack" "^2.2.0"
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/node-http-handler" "^2.5.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/smithy-client" "^2.5.1"
    "@smithy/types" "^2.12.0"
    "@smithy/url-parser" "^2.2.0"
    "@smithy/util-base64" "^2.3.0"
    "@smithy/util-body-length-browser" "^2.2.0"
    "@smithy/util-body-length-node" "^2.3.0"
    "@smithy/util-defaults-mode-browser" "^2.2.1"
    "@smithy/util-defaults-mode-node" "^2.3.1"
    "@smithy/util-endpoints" "^1.2.0"
    "@smithy/util-middleware" "^2.2.0"
    "@smithy/util-retry" "^2.2.0"
    "@smithy/util-stream" "^2.2.0"
    "@smithy/util-utf8" "^2.3.0"
    tslib "^2.6.2"

"@aws-sdk/client-cognito-identity@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-cognito-identity/-/client-cognito-identity-3.572.0.tgz#1bcc8263a01d255c28b47911494a10ce2bab2ffd"
  integrity sha512-YratomQtGVkQakvBZjtAl10H1D8y8XKnrB6TfbY17SQn4BR8FleGHL6pXDCNw8guvMCoZUfWn8k2B22RpwHKSw==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/client-sso-oidc" "3.572.0"
    "@aws-sdk/client-sts" "3.572.0"
    "@aws-sdk/core" "3.572.0"
    "@aws-sdk/credential-provider-node" "3.572.0"
    "@aws-sdk/middleware-host-header" "3.567.0"
    "@aws-sdk/middleware-logger" "3.568.0"
    "@aws-sdk/middleware-recursion-detection" "3.567.0"
    "@aws-sdk/middleware-user-agent" "3.572.0"
    "@aws-sdk/region-config-resolver" "3.572.0"
    "@aws-sdk/types" "3.567.0"
    "@aws-sdk/util-endpoints" "3.572.0"
    "@aws-sdk/util-user-agent-browser" "3.567.0"
    "@aws-sdk/util-user-agent-node" "3.568.0"
    "@smithy/config-resolver" "^2.2.0"
    "@smithy/core" "^1.4.2"
    "@smithy/fetch-http-handler" "^2.5.0"
    "@smithy/hash-node" "^2.2.0"
    "@smithy/invalid-dependency" "^2.2.0"
    "@smithy/middleware-content-length" "^2.2.0"
    "@smithy/middleware-endpoint" "^2.5.1"
    "@smithy/middleware-retry" "^2.3.1"
    "@smithy/middleware-serde" "^2.3.0"
    "@smithy/middleware-stack" "^2.2.0"
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/node-http-handler" "^2.5.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/smithy-client" "^2.5.1"
    "@smithy/types" "^2.12.0"
    "@smithy/url-parser" "^2.2.0"
    "@smithy/util-base64" "^2.3.0"
    "@smithy/util-body-length-browser" "^2.2.0"
    "@smithy/util-body-length-node" "^2.3.0"
    "@smithy/util-defaults-mode-browser" "^2.2.1"
    "@smithy/util-defaults-mode-node" "^2.3.1"
    "@smithy/util-endpoints" "^1.2.0"
    "@smithy/util-middleware" "^2.2.0"
    "@smithy/util-retry" "^2.2.0"
    "@smithy/util-utf8" "^2.3.0"
    tslib "^2.6.2"

"@aws-sdk/client-s3@^3.282.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-s3/-/client-s3-3.572.0.tgz#2ef0882029a5f621e11e2d3ce685381f0781bf1b"
  integrity sha512-YLtJRVZN+ktOaseWeTtthmimRQoWxygdzRPFlb1HpDPX+akBrGkL7Mz69onpXKfqm9Loz3diUXHqKfpxRX9Pog==
  dependencies:
    "@aws-crypto/sha1-browser" "3.0.0"
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/client-sso-oidc" "3.572.0"
    "@aws-sdk/client-sts" "3.572.0"
    "@aws-sdk/core" "3.572.0"
    "@aws-sdk/credential-provider-node" "3.572.0"
    "@aws-sdk/middleware-bucket-endpoint" "3.568.0"
    "@aws-sdk/middleware-expect-continue" "3.572.0"
    "@aws-sdk/middleware-flexible-checksums" "3.572.0"
    "@aws-sdk/middleware-host-header" "3.567.0"
    "@aws-sdk/middleware-location-constraint" "3.567.0"
    "@aws-sdk/middleware-logger" "3.568.0"
    "@aws-sdk/middleware-recursion-detection" "3.567.0"
    "@aws-sdk/middleware-sdk-s3" "3.572.0"
    "@aws-sdk/middleware-signing" "3.572.0"
    "@aws-sdk/middleware-ssec" "3.567.0"
    "@aws-sdk/middleware-user-agent" "3.572.0"
    "@aws-sdk/region-config-resolver" "3.572.0"
    "@aws-sdk/signature-v4-multi-region" "3.572.0"
    "@aws-sdk/types" "3.567.0"
    "@aws-sdk/util-endpoints" "3.572.0"
    "@aws-sdk/util-user-agent-browser" "3.567.0"
    "@aws-sdk/util-user-agent-node" "3.568.0"
    "@aws-sdk/xml-builder" "3.567.0"
    "@smithy/config-resolver" "^2.2.0"
    "@smithy/core" "^1.4.2"
    "@smithy/eventstream-serde-browser" "^2.2.0"
    "@smithy/eventstream-serde-config-resolver" "^2.2.0"
    "@smithy/eventstream-serde-node" "^2.2.0"
    "@smithy/fetch-http-handler" "^2.5.0"
    "@smithy/hash-blob-browser" "^2.2.0"
    "@smithy/hash-node" "^2.2.0"
    "@smithy/hash-stream-node" "^2.2.0"
    "@smithy/invalid-dependency" "^2.2.0"
    "@smithy/md5-js" "^2.2.0"
    "@smithy/middleware-content-length" "^2.2.0"
    "@smithy/middleware-endpoint" "^2.5.1"
    "@smithy/middleware-retry" "^2.3.1"
    "@smithy/middleware-serde" "^2.3.0"
    "@smithy/middleware-stack" "^2.2.0"
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/node-http-handler" "^2.5.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/smithy-client" "^2.5.1"
    "@smithy/types" "^2.12.0"
    "@smithy/url-parser" "^2.2.0"
    "@smithy/util-base64" "^2.3.0"
    "@smithy/util-body-length-browser" "^2.2.0"
    "@smithy/util-body-length-node" "^2.3.0"
    "@smithy/util-defaults-mode-browser" "^2.2.1"
    "@smithy/util-defaults-mode-node" "^2.3.1"
    "@smithy/util-endpoints" "^1.2.0"
    "@smithy/util-retry" "^2.2.0"
    "@smithy/util-stream" "^2.2.0"
    "@smithy/util-utf8" "^2.3.0"
    "@smithy/util-waiter" "^2.2.0"
    tslib "^2.6.2"

"@aws-sdk/client-secrets-manager@^3.282.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-secrets-manager/-/client-secrets-manager-3.572.0.tgz#0accf4d2f15173f30ebbb9c2826ff6a00aea3165"
  integrity sha512-253zfIN1JhkWf+JvzXbFZwCzUNFH3dsx15o4FRPP1KV5/SpYJso7O4KPOK7PPUbcTvbjrQNJo5K30XIRVON1ow==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/client-sso-oidc" "3.572.0"
    "@aws-sdk/client-sts" "3.572.0"
    "@aws-sdk/core" "3.572.0"
    "@aws-sdk/credential-provider-node" "3.572.0"
    "@aws-sdk/middleware-host-header" "3.567.0"
    "@aws-sdk/middleware-logger" "3.568.0"
    "@aws-sdk/middleware-recursion-detection" "3.567.0"
    "@aws-sdk/middleware-user-agent" "3.572.0"
    "@aws-sdk/region-config-resolver" "3.572.0"
    "@aws-sdk/types" "3.567.0"
    "@aws-sdk/util-endpoints" "3.572.0"
    "@aws-sdk/util-user-agent-browser" "3.567.0"
    "@aws-sdk/util-user-agent-node" "3.568.0"
    "@smithy/config-resolver" "^2.2.0"
    "@smithy/core" "^1.4.2"
    "@smithy/fetch-http-handler" "^2.5.0"
    "@smithy/hash-node" "^2.2.0"
    "@smithy/invalid-dependency" "^2.2.0"
    "@smithy/middleware-content-length" "^2.2.0"
    "@smithy/middleware-endpoint" "^2.5.1"
    "@smithy/middleware-retry" "^2.3.1"
    "@smithy/middleware-serde" "^2.3.0"
    "@smithy/middleware-stack" "^2.2.0"
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/node-http-handler" "^2.5.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/smithy-client" "^2.5.1"
    "@smithy/types" "^2.12.0"
    "@smithy/url-parser" "^2.2.0"
    "@smithy/util-base64" "^2.3.0"
    "@smithy/util-body-length-browser" "^2.2.0"
    "@smithy/util-body-length-node" "^2.3.0"
    "@smithy/util-defaults-mode-browser" "^2.2.1"
    "@smithy/util-defaults-mode-node" "^2.3.1"
    "@smithy/util-endpoints" "^1.2.0"
    "@smithy/util-middleware" "^2.2.0"
    "@smithy/util-retry" "^2.2.0"
    "@smithy/util-utf8" "^2.3.0"
    tslib "^2.6.2"
    uuid "^9.0.1"

"@aws-sdk/client-sso-oidc@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-sso-oidc/-/client-sso-oidc-3.572.0.tgz#0abe3282c0900f0641770a928d31221a06df494d"
  integrity sha512-S6C/S6xYesDakEuzYvlY1DMMKLtKQxdbbygq3hfeG2R0jUt9KpRLsQXK8qrBuVCKa3WcnjN/30hp4g/iUWFU/w==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/client-sts" "3.572.0"
    "@aws-sdk/core" "3.572.0"
    "@aws-sdk/credential-provider-node" "3.572.0"
    "@aws-sdk/middleware-host-header" "3.567.0"
    "@aws-sdk/middleware-logger" "3.568.0"
    "@aws-sdk/middleware-recursion-detection" "3.567.0"
    "@aws-sdk/middleware-user-agent" "3.572.0"
    "@aws-sdk/region-config-resolver" "3.572.0"
    "@aws-sdk/types" "3.567.0"
    "@aws-sdk/util-endpoints" "3.572.0"
    "@aws-sdk/util-user-agent-browser" "3.567.0"
    "@aws-sdk/util-user-agent-node" "3.568.0"
    "@smithy/config-resolver" "^2.2.0"
    "@smithy/core" "^1.4.2"
    "@smithy/fetch-http-handler" "^2.5.0"
    "@smithy/hash-node" "^2.2.0"
    "@smithy/invalid-dependency" "^2.2.0"
    "@smithy/middleware-content-length" "^2.2.0"
    "@smithy/middleware-endpoint" "^2.5.1"
    "@smithy/middleware-retry" "^2.3.1"
    "@smithy/middleware-serde" "^2.3.0"
    "@smithy/middleware-stack" "^2.2.0"
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/node-http-handler" "^2.5.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/smithy-client" "^2.5.1"
    "@smithy/types" "^2.12.0"
    "@smithy/url-parser" "^2.2.0"
    "@smithy/util-base64" "^2.3.0"
    "@smithy/util-body-length-browser" "^2.2.0"
    "@smithy/util-body-length-node" "^2.3.0"
    "@smithy/util-defaults-mode-browser" "^2.2.1"
    "@smithy/util-defaults-mode-node" "^2.3.1"
    "@smithy/util-endpoints" "^1.2.0"
    "@smithy/util-middleware" "^2.2.0"
    "@smithy/util-retry" "^2.2.0"
    "@smithy/util-utf8" "^2.3.0"
    tslib "^2.6.2"

"@aws-sdk/client-sso@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-sso/-/client-sso-3.572.0.tgz#d686db985b4c430dbfa6854c8fa1c17de2c3d7ac"
  integrity sha512-S+xhScao5MD79AkrcHmFpEDk+CgoiuB/31WFcTcnrTio5TOUONAaT0QyscOIwRp7BZ7Aez7TBM+loTteJ+TQvg==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/core" "3.572.0"
    "@aws-sdk/middleware-host-header" "3.567.0"
    "@aws-sdk/middleware-logger" "3.568.0"
    "@aws-sdk/middleware-recursion-detection" "3.567.0"
    "@aws-sdk/middleware-user-agent" "3.572.0"
    "@aws-sdk/region-config-resolver" "3.572.0"
    "@aws-sdk/types" "3.567.0"
    "@aws-sdk/util-endpoints" "3.572.0"
    "@aws-sdk/util-user-agent-browser" "3.567.0"
    "@aws-sdk/util-user-agent-node" "3.568.0"
    "@smithy/config-resolver" "^2.2.0"
    "@smithy/core" "^1.4.2"
    "@smithy/fetch-http-handler" "^2.5.0"
    "@smithy/hash-node" "^2.2.0"
    "@smithy/invalid-dependency" "^2.2.0"
    "@smithy/middleware-content-length" "^2.2.0"
    "@smithy/middleware-endpoint" "^2.5.1"
    "@smithy/middleware-retry" "^2.3.1"
    "@smithy/middleware-serde" "^2.3.0"
    "@smithy/middleware-stack" "^2.2.0"
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/node-http-handler" "^2.5.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/smithy-client" "^2.5.1"
    "@smithy/types" "^2.12.0"
    "@smithy/url-parser" "^2.2.0"
    "@smithy/util-base64" "^2.3.0"
    "@smithy/util-body-length-browser" "^2.2.0"
    "@smithy/util-body-length-node" "^2.3.0"
    "@smithy/util-defaults-mode-browser" "^2.2.1"
    "@smithy/util-defaults-mode-node" "^2.3.1"
    "@smithy/util-endpoints" "^1.2.0"
    "@smithy/util-middleware" "^2.2.0"
    "@smithy/util-retry" "^2.2.0"
    "@smithy/util-utf8" "^2.3.0"
    tslib "^2.6.2"

"@aws-sdk/client-sts@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-sts/-/client-sts-3.572.0.tgz#75437254cb314a0a0cdb7b28887d502442167408"
  integrity sha512-jCQuH2qkbWoSY4wckLSfzf3OPh7zc7ZckEbIGGVUQar/JVff6EIbpQ+uNG29DDEOpdPPd8rrJsVuUlA/nvJdXA==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/client-sso-oidc" "3.572.0"
    "@aws-sdk/core" "3.572.0"
    "@aws-sdk/credential-provider-node" "3.572.0"
    "@aws-sdk/middleware-host-header" "3.567.0"
    "@aws-sdk/middleware-logger" "3.568.0"
    "@aws-sdk/middleware-recursion-detection" "3.567.0"
    "@aws-sdk/middleware-user-agent" "3.572.0"
    "@aws-sdk/region-config-resolver" "3.572.0"
    "@aws-sdk/types" "3.567.0"
    "@aws-sdk/util-endpoints" "3.572.0"
    "@aws-sdk/util-user-agent-browser" "3.567.0"
    "@aws-sdk/util-user-agent-node" "3.568.0"
    "@smithy/config-resolver" "^2.2.0"
    "@smithy/core" "^1.4.2"
    "@smithy/fetch-http-handler" "^2.5.0"
    "@smithy/hash-node" "^2.2.0"
    "@smithy/invalid-dependency" "^2.2.0"
    "@smithy/middleware-content-length" "^2.2.0"
    "@smithy/middleware-endpoint" "^2.5.1"
    "@smithy/middleware-retry" "^2.3.1"
    "@smithy/middleware-serde" "^2.3.0"
    "@smithy/middleware-stack" "^2.2.0"
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/node-http-handler" "^2.5.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/smithy-client" "^2.5.1"
    "@smithy/types" "^2.12.0"
    "@smithy/url-parser" "^2.2.0"
    "@smithy/util-base64" "^2.3.0"
    "@smithy/util-body-length-browser" "^2.2.0"
    "@smithy/util-body-length-node" "^2.3.0"
    "@smithy/util-defaults-mode-browser" "^2.2.1"
    "@smithy/util-defaults-mode-node" "^2.3.1"
    "@smithy/util-endpoints" "^1.2.0"
    "@smithy/util-middleware" "^2.2.0"
    "@smithy/util-retry" "^2.2.0"
    "@smithy/util-utf8" "^2.3.0"
    tslib "^2.6.2"

"@aws-sdk/core@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/core/-/core-3.572.0.tgz#875cbd9e2ca6b78a3c2663cf67aed24e6b143667"
  integrity sha512-DBmf94qfN0dfaLl5EnNcq6TakWfOtVXYifHoTbX+VBwESj3rlY4W+W4mAnvBgAqDjlLFy7bBljmx+vnjnV73lg==
  dependencies:
    "@smithy/core" "^1.4.2"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/signature-v4" "^2.3.0"
    "@smithy/smithy-client" "^2.5.1"
    "@smithy/types" "^2.12.0"
    fast-xml-parser "4.2.5"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-cognito-identity@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-cognito-identity/-/credential-provider-cognito-identity-3.572.0.tgz#99f48ba1483b309a6dd314280f2a9c28e5f550a6"
  integrity sha512-fd7uln2vkmvZBRrvmUjfGQFXbSVuVEk3sbhQJxs2cdTM7fcQwCBjo4XTR+fckgOY6aD4wyZsy1+5+ODbx8x1Vg==
  dependencies:
    "@aws-sdk/client-cognito-identity" "3.572.0"
    "@aws-sdk/types" "3.567.0"
    "@smithy/property-provider" "^2.2.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-env@3.568.0":
  version "3.568.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-env/-/credential-provider-env-3.568.0.tgz#fc7fda0bc48bbc75065a9084e41d429037e0e1c5"
  integrity sha512-MVTQoZwPnP1Ev5A7LG+KzeU6sCB8BcGkZeDT1z1V5Wt7GPq0MgFQTSSjhImnB9jqRSZkl1079Bt3PbO6lfIS8g==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/property-provider" "^2.2.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-http@3.568.0":
  version "3.568.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-http/-/credential-provider-http-3.568.0.tgz#7f7239bed7c23db7356ebeae5f3b3bda9f751b08"
  integrity sha512-gL0NlyI2eW17hnCrh45hZV+qjtBquB+Bckiip9R6DIVRKqYcoILyiFhuOgf2bXeF23gVh6j18pvUvIoTaFWs5w==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/fetch-http-handler" "^2.5.0"
    "@smithy/node-http-handler" "^2.5.0"
    "@smithy/property-provider" "^2.2.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/smithy-client" "^2.5.1"
    "@smithy/types" "^2.12.0"
    "@smithy/util-stream" "^2.2.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-ini@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-ini/-/credential-provider-ini-3.572.0.tgz#b6a447e85a10938a1f038bd7e1096c73229e6699"
  integrity sha512-05KzbAp77fEiQXqMeodXeMbT83FOqSyBrfSEMz6U8uBXeJCy8zPUjN3acqcbG55/HNJHUvg1cftqzy+fUz71gA==
  dependencies:
    "@aws-sdk/credential-provider-env" "3.568.0"
    "@aws-sdk/credential-provider-process" "3.572.0"
    "@aws-sdk/credential-provider-sso" "3.572.0"
    "@aws-sdk/credential-provider-web-identity" "3.568.0"
    "@aws-sdk/types" "3.567.0"
    "@smithy/credential-provider-imds" "^2.3.0"
    "@smithy/property-provider" "^2.2.0"
    "@smithy/shared-ini-file-loader" "^2.4.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-node@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-node/-/credential-provider-node-3.572.0.tgz#dbb1d26a8a2c18c52f8067f6c6371ee850c1fb05"
  integrity sha512-anlYZnpmVkfp9Gan+LcEkQvmRf/m0KcbR11th8sBEyI5lxMaHKXhnAtC/hEGT7e3L6rgNOrFYTPuSvllITD/Pg==
  dependencies:
    "@aws-sdk/credential-provider-env" "3.568.0"
    "@aws-sdk/credential-provider-http" "3.568.0"
    "@aws-sdk/credential-provider-ini" "3.572.0"
    "@aws-sdk/credential-provider-process" "3.572.0"
    "@aws-sdk/credential-provider-sso" "3.572.0"
    "@aws-sdk/credential-provider-web-identity" "3.568.0"
    "@aws-sdk/types" "3.567.0"
    "@smithy/credential-provider-imds" "^2.3.0"
    "@smithy/property-provider" "^2.2.0"
    "@smithy/shared-ini-file-loader" "^2.4.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-process@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-process/-/credential-provider-process-3.572.0.tgz#6054c37721d44b3e855b41f4ad8e3dd73f84e6cf"
  integrity sha512-hXcOytf0BadSm/MMy7MV8mmY0+Jv3mkavsHNBx0R82hw5ollD0I3JyOAaCtdUpztF0I72F8K+q8SpJQZ+EwArw==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/property-provider" "^2.2.0"
    "@smithy/shared-ini-file-loader" "^2.4.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-sso@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-sso/-/credential-provider-sso-3.572.0.tgz#d0fe8122538fc498e9d4f797dfe99eed5bfc7443"
  integrity sha512-iIlnpJiDXFp3XC4hJNSiNurnU24mr3iLB3HoNa9efr944bo6XBl9FQdk3NssIkqzSmgyoB2CEUx/daBHz4XSow==
  dependencies:
    "@aws-sdk/client-sso" "3.572.0"
    "@aws-sdk/token-providers" "3.572.0"
    "@aws-sdk/types" "3.567.0"
    "@smithy/property-provider" "^2.2.0"
    "@smithy/shared-ini-file-loader" "^2.4.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-web-identity@3.568.0":
  version "3.568.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-web-identity/-/credential-provider-web-identity-3.568.0.tgz#b4e7958dc92a6cbbf5e9fd065cecd76573d4b70f"
  integrity sha512-ZJSmTmoIdg6WqAULjYzaJ3XcbgBzVy36lir6Y0UBMRGaxDgos1AARuX6EcYzXOl+ksLvxt/xMQ+3aYh1LWfKSw==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/property-provider" "^2.2.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/credential-providers@^3.186.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-providers/-/credential-providers-3.572.0.tgz#8b02056927cbd1e64c84264892709969439fa84e"
  integrity sha512-4Juh49ycOL90/ZJnkROLzH/mi2x1ylTiFw2/Q5RbhVHDuVDdgbtA4gO7rviHnFj1VigucnF21Jr5KUNf6wU91w==
  dependencies:
    "@aws-sdk/client-cognito-identity" "3.572.0"
    "@aws-sdk/client-sso" "3.572.0"
    "@aws-sdk/client-sts" "3.572.0"
    "@aws-sdk/credential-provider-cognito-identity" "3.572.0"
    "@aws-sdk/credential-provider-env" "3.568.0"
    "@aws-sdk/credential-provider-http" "3.568.0"
    "@aws-sdk/credential-provider-ini" "3.572.0"
    "@aws-sdk/credential-provider-node" "3.572.0"
    "@aws-sdk/credential-provider-process" "3.572.0"
    "@aws-sdk/credential-provider-sso" "3.572.0"
    "@aws-sdk/credential-provider-web-identity" "3.568.0"
    "@aws-sdk/types" "3.567.0"
    "@smithy/credential-provider-imds" "^2.3.0"
    "@smithy/property-provider" "^2.2.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-bucket-endpoint@3.568.0":
  version "3.568.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-bucket-endpoint/-/middleware-bucket-endpoint-3.568.0.tgz#790c0943cc097d3a83665131bc9e0743598cc6ca"
  integrity sha512-uc/nbSpXv64ct/wV3Ksz0/bXAsEtXuoZu5J9FTcFnM7c2MSofa0YQrtrJ8cG65uGbdeiFoJwPA048BTG/ilhCA==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@aws-sdk/util-arn-parser" "3.568.0"
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/types" "^2.12.0"
    "@smithy/util-config-provider" "^2.3.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-expect-continue@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-expect-continue/-/middleware-expect-continue-3.572.0.tgz#91df3b88a0a109450db84577609ed19520dfff38"
  integrity sha512-+NKWVK295rOEANU/ohqEfNjkcEdZao7z6HxkMXX4gu4mDpSsVU8WhYr5hp5k3PUhtaiPU8M1rdfQBrZQc4uttw==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-flexible-checksums@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-flexible-checksums/-/middleware-flexible-checksums-3.572.0.tgz#639ee54f838a5382a69f07351cd783488b6ad89b"
  integrity sha512-ysblGDRn1yy8TlKUrwhnFbl3RuMfbVW1rbtePClEYpC/1u9MsqPmm/fmWJJGKat7NclnsgpQyfSQ64DCuaEedg==
  dependencies:
    "@aws-crypto/crc32" "3.0.0"
    "@aws-crypto/crc32c" "3.0.0"
    "@aws-sdk/types" "3.567.0"
    "@smithy/is-array-buffer" "^2.2.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/types" "^2.12.0"
    "@smithy/util-utf8" "^2.3.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-host-header@3.567.0":
  version "3.567.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-host-header/-/middleware-host-header-3.567.0.tgz#52f278234458ec3035e9534fee582c95a8fec4f7"
  integrity sha512-zQHHj2N3in9duKghH7AuRNrOMLnKhW6lnmb7dznou068DJtDr76w475sHp2TF0XELsOGENbbBsOlN/S5QBFBVQ==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-location-constraint@3.567.0":
  version "3.567.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-location-constraint/-/middleware-location-constraint-3.567.0.tgz#c469e745a3fa146dd29d0024a9f4d2a498985822"
  integrity sha512-XiGTH4VxrJ5fj6zeF6UL5U5EuJwLqj9bHW5pB+EKfw0pmbnyqfRdYNt46v4GsQql2iVOq1Z/Fiv754nIItBI/A==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-logger@3.568.0":
  version "3.568.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-logger/-/middleware-logger-3.568.0.tgz#aeb85cc8f7da431442d0f5914f3a3e262eb55a09"
  integrity sha512-BinH72RG7K3DHHC1/tCulocFv+ZlQ9SrPF9zYT0T1OT95JXuHhB7fH8gEABrc6DAtOdJJh2fgxQjPy5tzPtsrA==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-recursion-detection@3.567.0":
  version "3.567.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.567.0.tgz#95d91f071b57fb5245d522db70df1652275f06ac"
  integrity sha512-rFk3QhdT4IL6O/UWHmNdjJiURutBCy+ogGqaNHf/RELxgXH3KmYorLwCe0eFb5hq8f6vr3zl4/iH7YtsUOuo1w==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-sdk-api-gateway@3.567.0":
  version "3.567.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-sdk-api-gateway/-/middleware-sdk-api-gateway-3.567.0.tgz#51b24c78c731700d04dedb291fc1d8ce566f9761"
  integrity sha512-iKvm25N8mivDIp/L4bdxMykmsFkVvIS75700vS1dLcEWpA8xYTItElangGjYn/D6siFhs+8xRtqAplb7usYdNA==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-sdk-s3@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-sdk-s3/-/middleware-sdk-s3-3.572.0.tgz#62534ecbfc55d91fcb768b97bb14f73577c3b00e"
  integrity sha512-ygQL1G2hWoJXkUGL/Xr5q9ojXCH8hgt/oKsxJtc5U8ZXw3SRlL6pCVE7+aiD0l8mgIGbW0vrL08Oc/jYWlakdw==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@aws-sdk/util-arn-parser" "3.568.0"
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/signature-v4" "^2.3.0"
    "@smithy/smithy-client" "^2.5.1"
    "@smithy/types" "^2.12.0"
    "@smithy/util-config-provider" "^2.3.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-signing@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-signing/-/middleware-signing-3.572.0.tgz#d3c648e3a280774115003d7ea07860f80f79a19d"
  integrity sha512-/pEVgHnf8LsTG0hu9yqqvmLMknlKO5c19NM3J9qTWGLPfySi8tWrFuREAFKAxqJFgDw1IdFWd+dXIkodpbGwew==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/property-provider" "^2.2.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/signature-v4" "^2.3.0"
    "@smithy/types" "^2.12.0"
    "@smithy/util-middleware" "^2.2.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-ssec@3.567.0":
  version "3.567.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-ssec/-/middleware-ssec-3.567.0.tgz#0a425182d940f963b34146b22dc2872cb21c41e4"
  integrity sha512-lhpBwFi3Tcw+jlOdaCsg3lCAg4oOSJB00bW/aLTFeZWutwi9VexMmsddZllx99lN+LDeCjryNyVd2TCRCKwYhQ==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-user-agent@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.572.0.tgz#e629083356e3ea1303544240be82e2784d768984"
  integrity sha512-R4bBbLp1ywtF1kJoOX1juDMztKPWeQHNj6XuTvtruFDn1RdfnBlbM3+9rguRfH5s4V+xfl8SSWchnyo2cI00xg==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@aws-sdk/util-endpoints" "3.572.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/region-config-resolver@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/region-config-resolver/-/region-config-resolver-3.572.0.tgz#51a4485bf1b1c3a154fe96e8b9fc0a1e80240fef"
  integrity sha512-xkZMIxek44F4YW5r9otD1O5Y/kDkgAb6JNJePkP1qPVojrkCmin3OFYAOZgGm+T4DZAQ5rWhpaqTAWmnRumYfw==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/types" "^2.12.0"
    "@smithy/util-config-provider" "^2.3.0"
    "@smithy/util-middleware" "^2.2.0"
    tslib "^2.6.2"

"@aws-sdk/signature-v4-multi-region@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/signature-v4-multi-region/-/signature-v4-multi-region-3.572.0.tgz#0d50b39bbe715ae65dd3954a14df09d9c22fb04d"
  integrity sha512-FD6FIi8py1ZAR53NjD2VXKDvvQUhhZu7CDUfC9gjAa7JDtv+rJvM9ZuoiQjaDnzzqYxTr4pKqqjLsd6+8BCSWA==
  dependencies:
    "@aws-sdk/middleware-sdk-s3" "3.572.0"
    "@aws-sdk/types" "3.567.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/signature-v4" "^2.3.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/token-providers@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/token-providers/-/token-providers-3.572.0.tgz#b63ef02f1700057e9f4532365cd098699b0f8328"
  integrity sha512-IkSu8p32tQZhKqwmfLZLGfYwNhsS/HUQBLnDMHJlr9VifmDqlTurcr+DwMCaMimuFhcLeb45vqTymKf/ro/OBw==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/property-provider" "^2.2.0"
    "@smithy/shared-ini-file-loader" "^2.4.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/types@3.567.0", "@aws-sdk/types@^3.222.0", "@aws-sdk/types@^3.4.1":
  version "3.567.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/types/-/types-3.567.0.tgz#b2dc88e154140b1ff87e94f63c97447bdb1c1738"
  integrity sha512-JBznu45cdgQb8+T/Zab7WpBmfEAh77gsk99xuF4biIb2Sw1mdseONdoGDjEJX57a25TzIv/WUJ2oABWumckz1A==
  dependencies:
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/util-arn-parser@3.568.0":
  version "3.568.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-arn-parser/-/util-arn-parser-3.568.0.tgz#6a19a8c6bbaa520b6be1c278b2b8c17875b91527"
  integrity sha512-XUKJWWo+KOB7fbnPP0+g/o5Ulku/X53t7i/h+sPHr5xxYTJJ9CYnbToo95mzxe7xWvkLrsNtJ8L+MnNn9INs2w==
  dependencies:
    tslib "^2.6.2"

"@aws-sdk/util-endpoints@3.572.0":
  version "3.572.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-endpoints/-/util-endpoints-3.572.0.tgz#41a42cbeb6744f5cf0b983c1b9dd958cb0bd66e0"
  integrity sha512-AIEC7ItIWBqkJLtqcSd0HG8tvdh3zVwqnKPHNrcfFay0Xonqx3p/qTCDwGosh5CM5hDGzyOSRA5PkacEDBTz9w==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/types" "^2.12.0"
    "@smithy/util-endpoints" "^1.2.0"
    tslib "^2.6.2"

"@aws-sdk/util-locate-window@^3.0.0":
  version "3.568.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-locate-window/-/util-locate-window-3.568.0.tgz#2acc4b2236af0d7494f7e517401ba6b3c4af11ff"
  integrity sha512-3nh4TINkXYr+H41QaPelCceEB2FXP3fxp93YZXB/kqJvX0U9j0N0Uk45gvsjmEPzG8XxkPEeLIfT2I1M7A6Lig==
  dependencies:
    tslib "^2.6.2"

"@aws-sdk/util-user-agent-browser@3.567.0":
  version "3.567.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.567.0.tgz#1ef37a87b28155274d62e31c1ac5c1c043dcd0b3"
  integrity sha512-cqP0uXtZ7m7hRysf3fRyJwcY1jCgQTpJy7BHB5VpsE7DXlXHD5+Ur5L42CY7UrRPrB6lc6YGFqaAOs5ghMcLyA==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/types" "^2.12.0"
    bowser "^2.11.0"
    tslib "^2.6.2"

"@aws-sdk/util-user-agent-node@3.568.0":
  version "3.568.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.568.0.tgz#8bfb81b23d4947462f1e49c70187b85e7cd3837a"
  integrity sha512-NVoZoLnKF+eXPBvXg+KqixgJkPSrerR6Gqmbjwqbv14Ini+0KNKB0/MXas1mDGvvEgtNkHI/Cb9zlJ3KXpti2A==
  dependencies:
    "@aws-sdk/types" "3.567.0"
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@aws-sdk/util-utf8-browser@^3.0.0":
  version "3.259.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-utf8-browser/-/util-utf8-browser-3.259.0.tgz#3275a6f5eb334f96ca76635b961d3c50259fd9ff"
  integrity sha512-UvFa/vR+e19XookZF8RzFZBrw2EUkQWxiBW0yYQAhvk3C+QVGl0H3ouca8LDBlBfQKXwmW3huo/59H8rwb1wJw==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/xml-builder@3.567.0":
  version "3.567.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/xml-builder/-/xml-builder-3.567.0.tgz#8dad7461955a8f8458593973b31b3457ea5ad887"
  integrity sha512-Db25jK9sZdGa7PEQTdm60YauUVbeYGsSEMQOHGP6ifbXfCknqgkPgWV16DqAKJUsbII0xgkJ9LpppkmYal3K/g==
  dependencies:
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@dazn/lambda-powertools-correlation-ids@^1.28.1":
  version "1.28.1"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-correlation-ids/-/lambda-powertools-correlation-ids-1.28.1.tgz#ff0b94de104154cbdf5825e9f2a5a789c4cb6e92"
  integrity sha512-/RZUT5ZlVcQxsWi+OolEwXUIsXHqffNeZ+eY4Je23s9VcztuqHuHeyrlJh1m5Kg76EsvQTq+5b1xvjf3J/6A1Q==

"@dazn/lambda-powertools-logger@^1.28.1":
  version "1.28.1"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-logger/-/lambda-powertools-logger-1.28.1.tgz#ac638e6e13552ac4e3a35613436f8a132e9bfe05"
  integrity sha512-vfnKgEwW/jv4PSkqRsEWPWLY5fkcjCnLrSZpca2Exh7pIUWZQN3FqLdpqs23caa+qtOCJ2JM8toa21uwSzYMLw==
  dependencies:
    "@dazn/lambda-powertools-correlation-ids" "^1.28.1"

"@dazn/lambda-powertools-middleware-correlation-ids@^1.29.0":
  version "1.29.0"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-middleware-correlation-ids/-/lambda-powertools-middleware-correlation-ids-1.29.0.tgz#e03e8184e2a3673a77d18d02576b44646caa060c"
  integrity sha512-kXOOKzEMKz6nYHUQo2GUvTqnQeXo1U6/RI87xUjSeztcjHaDZ0Jw6plUepZD+YawjfsVIrHKfnZrlN909utttA==
  dependencies:
    "@dazn/lambda-powertools-correlation-ids" "^1.28.1"
    "@dazn/lambda-powertools-logger" "^1.28.1"

"@dazn/lambda-powertools-middleware-log-timeout@^1.29.0":
  version "1.29.0"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-middleware-log-timeout/-/lambda-powertools-middleware-log-timeout-1.29.0.tgz#027b9fcaa0413b5d0e0261f16721be31079f9e9c"
  integrity sha512-BJv3DQdcuOCBfp93cFv3LgCcCBhwh4s8COmw4x+c3cEdkY6zajo9tHAikFea8Fv9ShDXAcUgnPpkv8EFMbAH+w==
  dependencies:
    "@dazn/lambda-powertools-logger" "^1.28.1"

"@dazn/lambda-powertools-middleware-sample-logging@^1.29.0":
  version "1.29.0"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-middleware-sample-logging/-/lambda-powertools-middleware-sample-logging-1.29.0.tgz#a0b403f7387e202b47df1bdc975c5e0ba09fd46f"
  integrity sha512-VHe3bSw0ch5Ql5tA3XvCta8db1Nr6NaSJ0Oj2oqQU+F15WJfqPD+reeKMgj3F1z8lJqXWAea3aD4nQT0PCTt6Q==
  dependencies:
    "@dazn/lambda-powertools-correlation-ids" "^1.28.1"
    "@dazn/lambda-powertools-logger" "^1.28.1"

"@dazn/lambda-powertools-pattern-basic@^1.29.0":
  version "1.29.0"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-pattern-basic/-/lambda-powertools-pattern-basic-1.29.0.tgz#d97d47730588cb93dc115402fbab12e4492c6948"
  integrity sha512-HYmu9eKVRYNu5Q2CYuOl3UmBMAfpHzvNJFRdR8f8F5DJLktsexapk1sDjZZq4bP1ZmduuSbG/mUN9nmtkCRWYw==
  dependencies:
    "@dazn/lambda-powertools-middleware-correlation-ids" "^1.29.0"
    "@dazn/lambda-powertools-middleware-log-timeout" "^1.29.0"
    "@dazn/lambda-powertools-middleware-sample-logging" "^1.29.0"
    "@middy/core" "^2.1.0"

"@mediality/centaur@../../centaurappCentaurAppCommonLayer/lib/nodejs":
  version "1.0.31"
  dependencies:
    "@aws-lambda-powertools/logger" "^1.5.1"
    "@aws-lambda-powertools/tracer" "^1.5.1"
    "@aws-sdk/client-api-gateway" "^3.54.0"
    "@aws-sdk/client-s3" "^3.282.0"
    "@aws-sdk/client-secrets-manager" "^3.282.0"
    "@dazn/lambda-powertools-logger" "^1.28.1"
    "@dazn/lambda-powertools-pattern-basic" "^1.29.0"
    "@mediality/centaur" "./"
    aws-sdk "^2.1324.0"
    aws-xray-sdk "^3.3.4"
    aws-xray-sdk-core "^3.3.4"
    axios "^1.6.7"
    basic-ftp "^5.0.1"
    fast-xml-parser "^4.0.1"
    fs "^0.0.1-security"
    fs-extra "^10.0.0"
    install "^0.13.0"
    moment "^2.29.1"
    mongoose "^6.1.3"
    pify "^5.0.0"
    uuid "^8.3.2"
    uuid-by-string "^3.0.4"
    validator "^13.7.0"
    xml2js "^0.4.23"
    xmlbuilder2 "^3.0.2"

"@mediality/centaur@./":
  version "2.0.0"

"@middy/core@^2.1.0":
  version "2.5.7"
  resolved "https://registry.yarnpkg.com/@middy/core/-/core-2.5.7.tgz#a1b3eff68881ff66b14b5051255791f7cbd3b471"
  integrity sha512-KX5Ud0SP+pol6PGkYtMCH4goHobs1XJo3OvEUwdiZUIjZgo56Q08nLu5N7Bs6P+FwGTQHA+hlQ3I5SZbfpO/jg==

"@mongodb-js/saslprep@^1.1.0":
  version "1.1.6"
  resolved "https://registry.yarnpkg.com/@mongodb-js/saslprep/-/saslprep-1.1.6.tgz#54da3f794c71a17445740fe2b74882e0c76a3058"
  integrity sha512-jqTTXQ46H8cAxmXBu8wm1HTSIMBMrIcoVrsjdQkKdMBj3il/fSCgWyya4P2I1xjPBl69mw+nRphrPlcIqBd20Q==
  dependencies:
    sparse-bitfield "^3.0.3"

"@oozcitak/dom@1.15.10":
  version "1.15.10"
  resolved "https://registry.yarnpkg.com/@oozcitak/dom/-/dom-1.15.10.tgz#dca7289f2b292cff2a901ea4fbbcc0a1ab0b05c2"
  integrity sha512-0JT29/LaxVgRcGKvHmSrUTEvZ8BXvZhGl2LASRUgHqDTC1M5g1pLmVv56IYNyt3bG2CUjDkc67wnyZC14pbQrQ==
  dependencies:
    "@oozcitak/infra" "1.0.8"
    "@oozcitak/url" "1.0.4"
    "@oozcitak/util" "8.3.8"

"@oozcitak/infra@1.0.8":
  version "1.0.8"
  resolved "https://registry.yarnpkg.com/@oozcitak/infra/-/infra-1.0.8.tgz#b0b089421f7d0f6878687608301fbaba837a7d17"
  integrity sha512-JRAUc9VR6IGHOL7OGF+yrvs0LO8SlqGnPAMqyzOuFZPSZSXI7Xf2O9+awQPSMXgIWGtgUf/dA6Hs6X6ySEaWTg==
  dependencies:
    "@oozcitak/util" "8.3.8"

"@oozcitak/url@1.0.4":
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/@oozcitak/url/-/url-1.0.4.tgz#ca8b1c876319cf5a648dfa1123600a6aa5cda6ba"
  integrity sha512-kDcD8y+y3FCSOvnBI6HJgl00viO/nGbQoCINmQ0h98OhnGITrWR3bOGfwYCthgcrV8AnTJz8MzslTQbC3SOAmw==
  dependencies:
    "@oozcitak/infra" "1.0.8"
    "@oozcitak/util" "8.3.8"

"@oozcitak/util@8.3.8":
  version "8.3.8"
  resolved "https://registry.yarnpkg.com/@oozcitak/util/-/util-8.3.8.tgz#10f65fe1891fd8cde4957360835e78fd1936bfdd"
  integrity sha512-T8TbSnGsxo6TDBJx/Sgv/BlVJL3tshxZP7Aq5R1mSnM5OcHY2dQaxLMu2+E8u3gN0MLOzdjurqN4ZRVuzQycOQ==

"@smithy/abort-controller@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/abort-controller/-/abort-controller-2.2.0.tgz#18983401a5e2154b5c94057730024a7d14cbcd35"
  integrity sha512-wRlta7GuLWpTqtFfGo+nZyOO1vEvewdNR1R4rTxpC8XU6vG/NDyrFBhwLZsqg1NUoR1noVaXJPC/7ZK47QCySw==
  dependencies:
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/chunked-blob-reader-native@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/chunked-blob-reader-native/-/chunked-blob-reader-native-2.2.0.tgz#aff8bddf9fdc1052f885e1b15aa81e4d274e541e"
  integrity sha512-VNB5+1oCgX3Fzs072yuRsUoC2N4Zg/LJ11DTxX3+Qu+Paa6AmbIF0E9sc2wthz9Psrk/zcOlTCyuposlIhPjZQ==
  dependencies:
    "@smithy/util-base64" "^2.3.0"
    tslib "^2.6.2"

"@smithy/chunked-blob-reader@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/chunked-blob-reader/-/chunked-blob-reader-2.2.0.tgz#192c1787bf3f4f87e2763803425f418e6e613e09"
  integrity sha512-3GJNvRwXBGdkDZZOGiziVYzDpn4j6zfyULHMDKAGIUo72yHALpE9CbhfQp/XcLNVoc1byfMpn6uW5H2BqPjgaQ==
  dependencies:
    tslib "^2.6.2"

"@smithy/config-resolver@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/config-resolver/-/config-resolver-2.2.0.tgz#54f40478bb61709b396960a3535866dba5422757"
  integrity sha512-fsiMgd8toyUba6n1WRmr+qACzXltpdDkPTAaDqc8QqPBUzO+/JKwL6bUBseHVi8tu9l+3JOK+tSf7cay+4B3LA==
  dependencies:
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/types" "^2.12.0"
    "@smithy/util-config-provider" "^2.3.0"
    "@smithy/util-middleware" "^2.2.0"
    tslib "^2.6.2"

"@smithy/core@^1.4.2":
  version "1.4.2"
  resolved "https://registry.yarnpkg.com/@smithy/core/-/core-1.4.2.tgz#1c3ed886d403041ce5bd2d816448420c57baa19c"
  integrity sha512-2fek3I0KZHWJlRLvRTqxTEri+qV0GRHrJIoLFuBMZB4EMg4WgeBGfF0X6abnrNYpq55KJ6R4D6x4f0vLnhzinA==
  dependencies:
    "@smithy/middleware-endpoint" "^2.5.1"
    "@smithy/middleware-retry" "^2.3.1"
    "@smithy/middleware-serde" "^2.3.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/smithy-client" "^2.5.1"
    "@smithy/types" "^2.12.0"
    "@smithy/util-middleware" "^2.2.0"
    tslib "^2.6.2"

"@smithy/credential-provider-imds@^2.3.0":
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/@smithy/credential-provider-imds/-/credential-provider-imds-2.3.0.tgz#326ce401b82e53f3c7ee4862a066136959a06166"
  integrity sha512-BWB9mIukO1wjEOo1Ojgl6LrG4avcaC7T/ZP6ptmAaW4xluhSIPZhY+/PI5YKzlk+jsm+4sQZB45Bt1OfMeQa3w==
  dependencies:
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/property-provider" "^2.2.0"
    "@smithy/types" "^2.12.0"
    "@smithy/url-parser" "^2.2.0"
    tslib "^2.6.2"

"@smithy/eventstream-codec@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-codec/-/eventstream-codec-2.2.0.tgz#63d74fa817188995eb55e792a38060b0ede98dc4"
  integrity sha512-8janZoJw85nJmQZc4L8TuePp2pk1nxLgkxIR0TUjKJ5Dkj5oelB9WtiSSGXCQvNsJl0VSTvK/2ueMXxvpa9GVw==
  dependencies:
    "@aws-crypto/crc32" "3.0.0"
    "@smithy/types" "^2.12.0"
    "@smithy/util-hex-encoding" "^2.2.0"
    tslib "^2.6.2"

"@smithy/eventstream-serde-browser@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-browser/-/eventstream-serde-browser-2.2.0.tgz#69c93cc0210f04caeb0770856ef88c9a82564e11"
  integrity sha512-UaPf8jKbcP71BGiO0CdeLmlg+RhWnlN8ipsMSdwvqBFigl5nil3rHOI/5GE3tfiuX8LvY5Z9N0meuU7Rab7jWw==
  dependencies:
    "@smithy/eventstream-serde-universal" "^2.2.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/eventstream-serde-config-resolver@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-config-resolver/-/eventstream-serde-config-resolver-2.2.0.tgz#23c8698ce594a128bcc556153efb7fecf6d04f87"
  integrity sha512-RHhbTw/JW3+r8QQH7PrganjNCiuiEZmpi6fYUAetFfPLfZ6EkiA08uN3EFfcyKubXQxOwTeJRZSQmDDCdUshaA==
  dependencies:
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/eventstream-serde-node@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-node/-/eventstream-serde-node-2.2.0.tgz#b82870a838b1bd32ad6e0cf33a520191a325508e"
  integrity sha512-zpQMtJVqCUMn+pCSFcl9K/RPNtQE0NuMh8sKpCdEHafhwRsjP50Oq/4kMmvxSRy6d8Jslqd8BLvDngrUtmN9iA==
  dependencies:
    "@smithy/eventstream-serde-universal" "^2.2.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/eventstream-serde-universal@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-universal/-/eventstream-serde-universal-2.2.0.tgz#a75e330040d5e2ca2ac0d8bccde3e390ac5afd38"
  integrity sha512-pvoe/vvJY0mOpuF84BEtyZoYfbehiFj8KKWk1ds2AT0mTLYFVs+7sBJZmioOFdBXKd48lfrx1vumdPdmGlCLxA==
  dependencies:
    "@smithy/eventstream-codec" "^2.2.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/fetch-http-handler@^2.5.0":
  version "2.5.0"
  resolved "https://registry.yarnpkg.com/@smithy/fetch-http-handler/-/fetch-http-handler-2.5.0.tgz#0b8e1562807fdf91fe7dd5cde620d7a03ddc10ac"
  integrity sha512-BOWEBeppWhLn/no/JxUL/ghTfANTjT7kg3Ww2rPqTUY9R4yHPXxJ9JhMe3Z03LN3aPwiwlpDIUcVw1xDyHqEhw==
  dependencies:
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/querystring-builder" "^2.2.0"
    "@smithy/types" "^2.12.0"
    "@smithy/util-base64" "^2.3.0"
    tslib "^2.6.2"

"@smithy/hash-blob-browser@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/hash-blob-browser/-/hash-blob-browser-2.2.0.tgz#d26db0e88b8fc4b59ee487bd026363ea9b48cf3a"
  integrity sha512-SGPoVH8mdXBqrkVCJ1Hd1X7vh1zDXojNN1yZyZTZsCno99hVue9+IYzWDjq/EQDDXxmITB0gBmuyPh8oAZSTcg==
  dependencies:
    "@smithy/chunked-blob-reader" "^2.2.0"
    "@smithy/chunked-blob-reader-native" "^2.2.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/hash-node@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/hash-node/-/hash-node-2.2.0.tgz#df29e1e64811be905cb3577703b0e2d0b07fc5cc"
  integrity sha512-zLWaC/5aWpMrHKpoDF6nqpNtBhlAYKF/7+9yMN7GpdR8CzohnWfGtMznPybnwSS8saaXBMxIGwJqR4HmRp6b3g==
  dependencies:
    "@smithy/types" "^2.12.0"
    "@smithy/util-buffer-from" "^2.2.0"
    "@smithy/util-utf8" "^2.3.0"
    tslib "^2.6.2"

"@smithy/hash-stream-node@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/hash-stream-node/-/hash-stream-node-2.2.0.tgz#7b341fdc89851af6b98d8c01e47185caf0a4b2d9"
  integrity sha512-aT+HCATOSRMGpPI7bi7NSsTNVZE/La9IaxLXWoVAYMxHT5hGO3ZOGEMZQg8A6nNL+pdFGtZQtND1eoY084HgHQ==
  dependencies:
    "@smithy/types" "^2.12.0"
    "@smithy/util-utf8" "^2.3.0"
    tslib "^2.6.2"

"@smithy/invalid-dependency@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/invalid-dependency/-/invalid-dependency-2.2.0.tgz#ee3d8980022cb5edb514ac187d159b3e773640f0"
  integrity sha512-nEDASdbKFKPXN2O6lOlTgrEEOO9NHIeO+HVvZnkqc8h5U9g3BIhWsvzFo+UcUbliMHvKNPD/zVxDrkP1Sbgp8Q==
  dependencies:
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/is-array-buffer@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/is-array-buffer/-/is-array-buffer-2.2.0.tgz#f84f0d9f9a36601a9ca9381688bd1b726fd39111"
  integrity sha512-GGP3O9QFD24uGeAXYUjwSTXARoqpZykHadOmA8G5vfJPK0/DC67qa//0qvqrJzL1xc8WQWX7/yc7fwudjPHPhA==
  dependencies:
    tslib "^2.6.2"

"@smithy/md5-js@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/md5-js/-/md5-js-2.2.0.tgz#033c4c89fe0cbb3f7e99cca3b7b63a2824c98c6d"
  integrity sha512-M26XTtt9IIusVMOWEAhIvFIr9jYj4ISPPGJROqw6vXngO3IYJCnVVSMFn4Tx1rUTG5BiKJNg9u2nxmBiZC5IlQ==
  dependencies:
    "@smithy/types" "^2.12.0"
    "@smithy/util-utf8" "^2.3.0"
    tslib "^2.6.2"

"@smithy/middleware-content-length@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-content-length/-/middleware-content-length-2.2.0.tgz#a82e97bd83d8deab69e07fea4512563bedb9461a"
  integrity sha512-5bl2LG1Ah/7E5cMSC+q+h3IpVHMeOkG0yLRyQT1p2aMJkSrZG7RlXHPuAgb7EyaFeidKEnnd/fNaLLaKlHGzDQ==
  dependencies:
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/middleware-endpoint@^2.5.1":
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-endpoint/-/middleware-endpoint-2.5.1.tgz#1333c58304aff4d843e8ef4b85c8cb88975dd5ad"
  integrity sha512-1/8kFp6Fl4OsSIVTWHnNjLnTL8IqpIb/D3sTSczrKFnrE9VMNWxnrRKNvpUHOJ6zpGD5f62TPm7+17ilTJpiCQ==
  dependencies:
    "@smithy/middleware-serde" "^2.3.0"
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/shared-ini-file-loader" "^2.4.0"
    "@smithy/types" "^2.12.0"
    "@smithy/url-parser" "^2.2.0"
    "@smithy/util-middleware" "^2.2.0"
    tslib "^2.6.2"

"@smithy/middleware-retry@^2.3.1":
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-retry/-/middleware-retry-2.3.1.tgz#d6fdce94f2f826642c01b4448e97a509c4556ede"
  integrity sha512-P2bGufFpFdYcWvqpyqqmalRtwFUNUA8vHjJR5iGqbfR6mp65qKOLcUd6lTr4S9Gn/enynSrSf3p3FVgVAf6bXA==
  dependencies:
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/service-error-classification" "^2.1.5"
    "@smithy/smithy-client" "^2.5.1"
    "@smithy/types" "^2.12.0"
    "@smithy/util-middleware" "^2.2.0"
    "@smithy/util-retry" "^2.2.0"
    tslib "^2.6.2"
    uuid "^9.0.1"

"@smithy/middleware-serde@^2.3.0":
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-serde/-/middleware-serde-2.3.0.tgz#a7615ba646a88b6f695f2d55de13d8158181dd13"
  integrity sha512-sIADe7ojwqTyvEQBe1nc/GXB9wdHhi9UwyX0lTyttmUWDJLP655ZYE1WngnNyXREme8I27KCaUhyhZWRXL0q7Q==
  dependencies:
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/middleware-stack@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-stack/-/middleware-stack-2.2.0.tgz#3fb49eae6313f16f6f30fdaf28e11a7321f34d9f"
  integrity sha512-Qntc3jrtwwrsAC+X8wms8zhrTr0sFXnyEGhZd9sLtsJ/6gGQKFzNB+wWbOcpJd7BR8ThNCoKt76BuQahfMvpeA==
  dependencies:
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/node-config-provider@^2.3.0":
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/@smithy/node-config-provider/-/node-config-provider-2.3.0.tgz#9fac0c94a14c5b5b8b8fa37f20c310a844ab9922"
  integrity sha512-0elK5/03a1JPWMDPaS726Iw6LpQg80gFut1tNpPfxFuChEEklo2yL823V94SpTZTxmKlXFtFgsP55uh3dErnIg==
  dependencies:
    "@smithy/property-provider" "^2.2.0"
    "@smithy/shared-ini-file-loader" "^2.4.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/node-http-handler@^2.5.0":
  version "2.5.0"
  resolved "https://registry.yarnpkg.com/@smithy/node-http-handler/-/node-http-handler-2.5.0.tgz#7b5e0565dd23d340380489bd5fe4316d2bed32de"
  integrity sha512-mVGyPBzkkGQsPoxQUbxlEfRjrj6FPyA3u3u2VXGr9hT8wilsoQdZdvKpMBFMB8Crfhv5dNkKHIW0Yyuc7eABqA==
  dependencies:
    "@smithy/abort-controller" "^2.2.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/querystring-builder" "^2.2.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/property-provider@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/property-provider/-/property-provider-2.2.0.tgz#37e3525a3fa3e11749f86a4f89f0fd7765a6edb0"
  integrity sha512-+xiil2lFhtTRzXkx8F053AV46QnIw6e7MV8od5Mi68E1ICOjCeCHw2XfLnDEUHnT9WGUIkwcqavXjfwuJbGlpg==
  dependencies:
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/protocol-http@^3.3.0":
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/@smithy/protocol-http/-/protocol-http-3.3.0.tgz#a37df7b4bb4960cdda560ce49acfd64c455e4090"
  integrity sha512-Xy5XK1AFWW2nlY/biWZXu6/krgbaf2dg0q492D8M5qthsnU2H+UgFeZLbM76FnH7s6RO/xhQRkj+T6KBO3JzgQ==
  dependencies:
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/querystring-builder@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/querystring-builder/-/querystring-builder-2.2.0.tgz#22937e19fcd0aaa1a3e614ef8cb6f8e86756a4ef"
  integrity sha512-L1kSeviUWL+emq3CUVSgdogoM/D9QMFaqxL/dd0X7PCNWmPXqt+ExtrBjqT0V7HLN03Vs9SuiLrG3zy3JGnE5A==
  dependencies:
    "@smithy/types" "^2.12.0"
    "@smithy/util-uri-escape" "^2.2.0"
    tslib "^2.6.2"

"@smithy/querystring-parser@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/querystring-parser/-/querystring-parser-2.2.0.tgz#24a5633f4b3806ff2888d4c2f4169e105fdffd79"
  integrity sha512-BvHCDrKfbG5Yhbpj4vsbuPV2GgcpHiAkLeIlcA1LtfpMz3jrqizP1+OguSNSj1MwBHEiN+jwNisXLGdajGDQJA==
  dependencies:
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/service-error-classification@^2.0.4", "@smithy/service-error-classification@^2.1.5":
  version "2.1.5"
  resolved "https://registry.yarnpkg.com/@smithy/service-error-classification/-/service-error-classification-2.1.5.tgz#0568a977cc0db36299d8703a5d8609c1f600c005"
  integrity sha512-uBDTIBBEdAQryvHdc5W8sS5YX7RQzF683XrHePVdFmAgKiMofU15FLSM0/HU03hKTnazdNRFa0YHS7+ArwoUSQ==
  dependencies:
    "@smithy/types" "^2.12.0"

"@smithy/shared-ini-file-loader@^2.4.0":
  version "2.4.0"
  resolved "https://registry.yarnpkg.com/@smithy/shared-ini-file-loader/-/shared-ini-file-loader-2.4.0.tgz#1636d6eb9bff41e36ac9c60364a37fd2ffcb9947"
  integrity sha512-WyujUJL8e1B6Z4PBfAqC/aGY1+C7T0w20Gih3yrvJSk97gpiVfB+y7c46T4Nunk+ZngLq0rOIdeVeIklk0R3OA==
  dependencies:
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/signature-v4@^2.3.0":
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/@smithy/signature-v4/-/signature-v4-2.3.0.tgz#c30dd4028ae50c607db99459981cce8cdab7a3fd"
  integrity sha512-ui/NlpILU+6HAQBfJX8BBsDXuKSNrjTSuOYArRblcrErwKFutjrCNb/OExfVRyj9+26F9J+ZmfWT+fKWuDrH3Q==
  dependencies:
    "@smithy/is-array-buffer" "^2.2.0"
    "@smithy/types" "^2.12.0"
    "@smithy/util-hex-encoding" "^2.2.0"
    "@smithy/util-middleware" "^2.2.0"
    "@smithy/util-uri-escape" "^2.2.0"
    "@smithy/util-utf8" "^2.3.0"
    tslib "^2.6.2"

"@smithy/smithy-client@^2.5.1":
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/@smithy/smithy-client/-/smithy-client-2.5.1.tgz#0fd2efff09dc65500d260e590f7541f8a387eae3"
  integrity sha512-jrbSQrYCho0yDaaf92qWgd+7nAeap5LtHTI51KXqmpIFCceKU3K9+vIVTUH72bOJngBMqa4kyu1VJhRcSrk/CQ==
  dependencies:
    "@smithy/middleware-endpoint" "^2.5.1"
    "@smithy/middleware-stack" "^2.2.0"
    "@smithy/protocol-http" "^3.3.0"
    "@smithy/types" "^2.12.0"
    "@smithy/util-stream" "^2.2.0"
    tslib "^2.6.2"

"@smithy/types@^2.12.0":
  version "2.12.0"
  resolved "https://registry.yarnpkg.com/@smithy/types/-/types-2.12.0.tgz#c44845f8ba07e5e8c88eda5aed7e6a0c462da041"
  integrity sha512-QwYgloJ0sVNBeBuBs65cIkTbfzV/Q6ZNPCJ99EICFEdJYG50nGIY/uYXp+TbsdJReIuPr0a0kXmCvren3MbRRw==
  dependencies:
    tslib "^2.6.2"

"@smithy/url-parser@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/url-parser/-/url-parser-2.2.0.tgz#6fcda6116391a4f61fef5580eb540e128359b3c0"
  integrity sha512-hoA4zm61q1mNTpksiSWp2nEl1dt3j726HdRhiNgVJQMj7mLp7dprtF57mOB6JvEk/x9d2bsuL5hlqZbBuHQylQ==
  dependencies:
    "@smithy/querystring-parser" "^2.2.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/util-base64@^2.3.0":
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-base64/-/util-base64-2.3.0.tgz#312dbb4d73fb94249c7261aee52de4195c2dd8e2"
  integrity sha512-s3+eVwNeJuXUwuMbusncZNViuhv2LjVJ1nMwTqSA0XAC7gjKhqqxRdJPhR8+YrkoZ9IiIbFk/yK6ACe/xlF+hw==
  dependencies:
    "@smithy/util-buffer-from" "^2.2.0"
    "@smithy/util-utf8" "^2.3.0"
    tslib "^2.6.2"

"@smithy/util-body-length-browser@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-body-length-browser/-/util-body-length-browser-2.2.0.tgz#25620645c6b62b42594ef4a93b66e6ab70e27d2c"
  integrity sha512-dtpw9uQP7W+n3vOtx0CfBD5EWd7EPdIdsQnWTDoFf77e3VUf05uA7R7TGipIo8e4WL2kuPdnsr3hMQn9ziYj5w==
  dependencies:
    tslib "^2.6.2"

"@smithy/util-body-length-node@^2.3.0":
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-body-length-node/-/util-body-length-node-2.3.0.tgz#d065a9b5e305ff899536777bbfe075cdc980136f"
  integrity sha512-ITWT1Wqjubf2CJthb0BuT9+bpzBfXeMokH/AAa5EJQgbv9aPMVfnM76iFIZVFf50hYXGbtiV71BHAthNWd6+dw==
  dependencies:
    tslib "^2.6.2"

"@smithy/util-buffer-from@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-buffer-from/-/util-buffer-from-2.2.0.tgz#6fc88585165ec73f8681d426d96de5d402021e4b"
  integrity sha512-IJdWBbTcMQ6DA0gdNhh/BwrLkDR+ADW5Kr1aZmd4k3DIF6ezMV4R2NIAmT08wQJ3yUK82thHWmC/TnK/wpMMIA==
  dependencies:
    "@smithy/is-array-buffer" "^2.2.0"
    tslib "^2.6.2"

"@smithy/util-config-provider@^2.3.0":
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-config-provider/-/util-config-provider-2.3.0.tgz#bc79f99562d12a1f8423100ca662a6fb07cde943"
  integrity sha512-HZkzrRcuFN1k70RLqlNK4FnPXKOpkik1+4JaBoHNJn+RnJGYqaa3c5/+XtLOXhlKzlRgNvyaLieHTW2VwGN0VQ==
  dependencies:
    tslib "^2.6.2"

"@smithy/util-defaults-mode-browser@^2.2.1":
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-2.2.1.tgz#9db31416daf575d2963c502e0528cfe8055f0c4e"
  integrity sha512-RtKW+8j8skk17SYowucwRUjeh4mCtnm5odCL0Lm2NtHQBsYKrNW0od9Rhopu9wF1gHMfHeWF7i90NwBz/U22Kw==
  dependencies:
    "@smithy/property-provider" "^2.2.0"
    "@smithy/smithy-client" "^2.5.1"
    "@smithy/types" "^2.12.0"
    bowser "^2.11.0"
    tslib "^2.6.2"

"@smithy/util-defaults-mode-node@^2.3.1":
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-2.3.1.tgz#4613210a3d107aadb3f85bd80cb71c796dd8bf0a"
  integrity sha512-vkMXHQ0BcLFysBMWgSBLSk3+leMpFSyyFj8zQtv5ZyUBx8/owVh1/pPEkzmW/DR/Gy/5c8vjLDD9gZjXNKbrpA==
  dependencies:
    "@smithy/config-resolver" "^2.2.0"
    "@smithy/credential-provider-imds" "^2.3.0"
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/property-provider" "^2.2.0"
    "@smithy/smithy-client" "^2.5.1"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/util-endpoints@^1.2.0":
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-endpoints/-/util-endpoints-1.2.0.tgz#b8b805f47e8044c158372f69b88337703117665d"
  integrity sha512-BuDHv8zRjsE5zXd3PxFXFknzBG3owCpjq8G3FcsXW3CykYXuEqM3nTSsmLzw5q+T12ZYuDlVUZKBdpNbhVtlrQ==
  dependencies:
    "@smithy/node-config-provider" "^2.3.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/util-hex-encoding@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-hex-encoding/-/util-hex-encoding-2.2.0.tgz#87edb7c88c2f422cfca4bb21f1394ae9602c5085"
  integrity sha512-7iKXR+/4TpLK194pVjKiasIyqMtTYJsgKgM242Y9uzt5dhHnUDvMNb+3xIhRJ9QhvqGii/5cRUt4fJn3dtXNHQ==
  dependencies:
    tslib "^2.6.2"

"@smithy/util-middleware@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-middleware/-/util-middleware-2.2.0.tgz#80cfad40f6cca9ffe42a5899b5cb6abd53a50006"
  integrity sha512-L1qpleXf9QD6LwLCJ5jddGkgWyuSvWBkJwWAZ6kFkdifdso+sk3L3O1HdmPvCdnCK3IS4qWyPxev01QMnfHSBw==
  dependencies:
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/util-retry@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-retry/-/util-retry-2.2.0.tgz#e8e019537ab47ba6b2e87e723ec51ee223422d85"
  integrity sha512-q9+pAFPTfftHXRytmZ7GzLFFrEGavqapFc06XxzZFcSIGERXMerXxCitjOG1prVDR9QdjqotF40SWvbqcCpf8g==
  dependencies:
    "@smithy/service-error-classification" "^2.1.5"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@smithy/util-stream@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-stream/-/util-stream-2.2.0.tgz#b1279e417992a0f74afa78d7501658f174ed7370"
  integrity sha512-17faEXbYWIRst1aU9SvPZyMdWmqIrduZjVOqCPMIsWFNxs5yQQgFrJL6b2SdiCzyW9mJoDjFtgi53xx7EH+BXA==
  dependencies:
    "@smithy/fetch-http-handler" "^2.5.0"
    "@smithy/node-http-handler" "^2.5.0"
    "@smithy/types" "^2.12.0"
    "@smithy/util-base64" "^2.3.0"
    "@smithy/util-buffer-from" "^2.2.0"
    "@smithy/util-hex-encoding" "^2.2.0"
    "@smithy/util-utf8" "^2.3.0"
    tslib "^2.6.2"

"@smithy/util-uri-escape@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-uri-escape/-/util-uri-escape-2.2.0.tgz#56f5764051a33b67bc93fdd2a869f971b0635406"
  integrity sha512-jtmJMyt1xMD/d8OtbVJ2gFZOSKc+ueYJZPW20ULW1GOp/q/YIM0wNh+u8ZFao9UaIGz4WoPW8hC64qlWLIfoDA==
  dependencies:
    tslib "^2.6.2"

"@smithy/util-utf8@^2.3.0":
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-utf8/-/util-utf8-2.3.0.tgz#dd96d7640363259924a214313c3cf16e7dd329c5"
  integrity sha512-R8Rdn8Hy72KKcebgLiv8jQcQkXoLMOGGv5uI1/k0l+snqkOzQ1R0ChUBCxWMlBsFMekWjq0wRudIweFs7sKT5A==
  dependencies:
    "@smithy/util-buffer-from" "^2.2.0"
    tslib "^2.6.2"

"@smithy/util-waiter@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-waiter/-/util-waiter-2.2.0.tgz#d11baf50637bfaadb9641d6ca1619da413dd2612"
  integrity sha512-IHk53BVw6MPMi2Gsn+hCng8rFA3ZmR3Rk7GllxDUW9qFJl/hiSvskn7XldkECapQVkIg/1dHpMAxI9xSTaLLSA==
  dependencies:
    "@smithy/abort-controller" "^2.2.0"
    "@smithy/types" "^2.12.0"
    tslib "^2.6.2"

"@types/aws-lambda@^8.10.92":
  version "8.10.137"
  resolved "https://registry.yarnpkg.com/@types/aws-lambda/-/aws-lambda-8.10.137.tgz#c9998a944541afdd6df0d159e9ec9c23dfe5fb40"
  integrity sha512-YNFwzVarXAOXkjuFxONyDw1vgRNzyH8AuyN19s0bM+ChSu/bzxb5XPxYFLXoqoM+tvgzwR3k7fXcEOW125yJxg==

"@types/body-parser@*":
  version "1.19.5"
  resolved "https://registry.yarnpkg.com/@types/body-parser/-/body-parser-1.19.5.tgz#04ce9a3b677dc8bd681a17da1ab9835dc9d3ede4"
  integrity sha512-fB3Zu92ucau0iQ0JMCFQE7b/dv8Ot07NI3KaZIkIUNXq82k4eBAqUaneXfleGY9JWskeS9y+u0nXMyspcuQrCg==
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/cls-hooked@^4.3.3":
  version "4.3.8"
  resolved "https://registry.yarnpkg.com/@types/cls-hooked/-/cls-hooked-4.3.8.tgz#ece275711b34eca51b3bc3899b13add7d8aff250"
  integrity sha512-tf/7H883gFA6MPlWI15EQtfNZ+oPL0gLKkOlx9UHFrun1fC/FkuyNBpTKq1B5E3T4fbvjId6WifHUdSGsMMuPg==
  dependencies:
    "@types/node" "*"

"@types/connect@*":
  version "3.4.38"
  resolved "https://registry.yarnpkg.com/@types/connect/-/connect-3.4.38.tgz#5ba7f3bc4fbbdeaff8dded952e5ff2cc53f8d858"
  integrity sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==
  dependencies:
    "@types/node" "*"

"@types/express-serve-static-core@^4.17.33":
  version "4.19.0"
  resolved "https://registry.yarnpkg.com/@types/express-serve-static-core/-/express-serve-static-core-4.19.0.tgz#3ae8ab3767d98d0b682cda063c3339e1e86ccfaa"
  integrity sha512-bGyep3JqPCRry1wq+O5n7oiBgGWmeIJXPjXXCo8EK0u8duZGSYar7cGqd3ML2JUsLGeB7fmc06KYo9fLGWqPvQ==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*":
  version "4.17.21"
  resolved "https://registry.yarnpkg.com/@types/express/-/express-4.17.21.tgz#c26d4a151e60efe0084b23dc3369ebc631ed192d"
  integrity sha512-ejlPM315qwLpaQlQDTjPdsUFSc6ZsP4AN6AlWnogPjQ7CVi7PYF3YVz+CY3jE2pwYf7E/7HlDAN0rV2GxTG0HQ==
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/http-errors@*":
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/@types/http-errors/-/http-errors-2.0.4.tgz#7eb47726c391b7345a6ec35ad7f4de469cf5ba4f"
  integrity sha512-D0CFMMtydbJAegzOyHjtiKPLlvnm3iTZyZRSZoLq2mRhDdmLfIWOCYPfQJ4cu2erKghU++QvjcUjp/5h7hESpA==

"@types/mime@^1":
  version "1.3.5"
  resolved "https://registry.yarnpkg.com/@types/mime/-/mime-1.3.5.tgz#1ef302e01cf7d2b5a0fa526790c9123bf1d06690"
  integrity sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==

"@types/mysql@*":
  version "2.15.26"
  resolved "https://registry.yarnpkg.com/@types/mysql/-/mysql-2.15.26.tgz#f0de1484b9e2354d587e7d2bd17a873cc8300836"
  integrity sha512-DSLCOXhkvfS5WNNPbfn2KdICAmk8lLc+/PNvnPnF7gOdMZCxopXduqv0OQ13y/yA/zXTSikZZqVgybUxOEg6YQ==
  dependencies:
    "@types/node" "*"

"@types/node@*":
  version "20.12.11"
  resolved "https://registry.yarnpkg.com/@types/node/-/node-20.12.11.tgz#c4ef00d3507000d17690643278a60dc55a9dc9be"
  integrity sha512-vDg9PZ/zi+Nqp6boSOT7plNuthRugEKixDv5sFTIpkE89MmNtEArAShI4mxuX2+UrLEe9pxC1vm2cjm9YlWbJw==
  dependencies:
    undici-types "~5.26.4"

"@types/pg@*":
  version "8.11.6"
  resolved "https://registry.yarnpkg.com/@types/pg/-/pg-8.11.6.tgz#a2d0fb0a14b53951a17df5197401569fb9c0c54b"
  integrity sha512-/2WmmBXHLsfRqzfHW7BNZ8SbYzE8OSk7i3WjFYvfgRHj7S1xj+16Je5fUKv3lVdVzk/zn9TXOqf+avFCFIE0yQ==
  dependencies:
    "@types/node" "*"
    pg-protocol "*"
    pg-types "^4.0.1"

"@types/qs@*":
  version "6.9.15"
  resolved "https://registry.yarnpkg.com/@types/qs/-/qs-6.9.15.tgz#adde8a060ec9c305a82de1babc1056e73bd64dce"
  integrity sha512-uXHQKES6DQKKCLh441Xv/dwxOq1TVS3JPUMlEqoEglvlhR6Mxnlew/Xq/LRVHpLyk7iK3zODe1qYHIMltO7XGg==

"@types/range-parser@*":
  version "1.2.7"
  resolved "https://registry.yarnpkg.com/@types/range-parser/-/range-parser-1.2.7.tgz#50ae4353eaaddc04044279812f52c8c65857dbcb"
  integrity sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==

"@types/send@*":
  version "0.17.4"
  resolved "https://registry.yarnpkg.com/@types/send/-/send-0.17.4.tgz#6619cd24e7270793702e4e6a4b958a9010cfc57a"
  integrity sha512-x2EM6TJOybec7c52BX0ZspPodMsQUd5L6PRwOunVyVUhXiBSKf3AezDL8Dgvgt5o0UfKNfuA0eMLr2wLT4AiBA==
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-static@*":
  version "1.15.7"
  resolved "https://registry.yarnpkg.com/@types/serve-static/-/serve-static-1.15.7.tgz#22174bbd74fb97fe303109738e9b5c2f3064f714"
  integrity sha512-W8Ym+h8nhuRwaKPaDw34QUkwsGi6Rc4yYqvKFo5rm2FUEhCFbzVWrxXUxuKK8TASjWsysJY0nsmNCGhCOIsrOw==
  dependencies:
    "@types/http-errors" "*"
    "@types/node" "*"
    "@types/send" "*"

"@types/webidl-conversions@*":
  version "7.0.3"
  resolved "https://registry.yarnpkg.com/@types/webidl-conversions/-/webidl-conversions-7.0.3.tgz#1306dbfa53768bcbcfc95a1c8cde367975581859"
  integrity sha512-CiJJvcRtIgzadHCYXw7dqEnMNRjhGZlYK05Mj9OyktqV8uVT8fD2BFOB7S1uwBE3Kj2Z+4UyPmFw/Ixgw/LAlA==

"@types/whatwg-url@^8.2.1":
  version "8.2.2"
  resolved "https://registry.yarnpkg.com/@types/whatwg-url/-/whatwg-url-8.2.2.tgz#749d5b3873e845897ada99be4448041d4cc39e63"
  integrity sha512-FtQu10RWgn3D9U4aazdwIE2yzphmTJREDqNdODHrbrZmmMqI0vMheC/6NE/J1Yveaj8H+ela+YwWTjq5PGmuhA==
  dependencies:
    "@types/node" "*"
    "@types/webidl-conversions" "*"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.yarnpkg.com/argparse/-/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==
  dependencies:
    sprintf-js "~1.0.2"

async-hook-jl@^1.7.6:
  version "1.7.6"
  resolved "https://registry.yarnpkg.com/async-hook-jl/-/async-hook-jl-1.7.6.tgz#4fd25c2f864dbaf279c610d73bf97b1b28595e68"
  integrity sha512-gFaHkFfSxTjvoxDMYqDuGHlcRyUuamF8s+ZTtJdDzqjws4mCt7v0vuV79/E2Wr2/riMQgtG4/yUtXWs1gZ7JMg==
  dependencies:
    stack-chain "^1.3.7"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

atomic-batcher@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/atomic-batcher/-/atomic-batcher-1.0.2.tgz#d16901d10ccec59516c197b9ccd8930689b813b4"
  integrity sha512-EFGCRj4kLX1dHv1cDzTk+xbjBFj1GnJDpui52YmEcxxHHEWjYyT6l51U7n6WQ28osZH4S9gSybxe56Vm7vB61Q==

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz#a5cc375d6a03c2efc87a553f3e0b1522def14846"
  integrity sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==
  dependencies:
    possible-typed-array-names "^1.0.0"

aws-sdk@^2.1324.0:
  version "2.1616.0"
  resolved "https://registry.yarnpkg.com/aws-sdk/-/aws-sdk-2.1616.0.tgz#faeaa8eb252e48d5eb9145bb3dc7b58f98c86e6b"
  integrity sha512-Wes2FOJn/5Bo35ku+KYvn/H+xnuPuI97mQKxhU+d3TczAY56rFH/qw7Bff8HI0Gi6m6lDEhhq76qvG4gfdPexg==
  dependencies:
    buffer "4.9.2"
    events "1.1.1"
    ieee754 "1.1.13"
    jmespath "0.16.0"
    querystring "0.2.0"
    sax "1.2.1"
    url "0.10.3"
    util "^0.12.4"
    uuid "8.0.0"
    xml2js "0.6.2"

aws-xray-sdk-core@3.6.0, aws-xray-sdk-core@^3.3.4, aws-xray-sdk-core@^3.5.3:
  version "3.6.0"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk-core/-/aws-xray-sdk-core-3.6.0.tgz#a9144aeb327b1ffb826833bd542e966a374e96df"
  integrity sha512-+UnYmVEni9NNJvE6aFY1dbvMtFquXSYAj+HYfm+90icoGKYvvLD71R7PHyFFnYct5of4NFpEXJtUJrWMv8e4mQ==
  dependencies:
    "@aws-sdk/types" "^3.4.1"
    "@smithy/service-error-classification" "^2.0.4"
    "@types/cls-hooked" "^4.3.3"
    atomic-batcher "^1.0.2"
    cls-hooked "^4.2.2"
    semver "^7.5.3"

aws-xray-sdk-express@3.6.0:
  version "3.6.0"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk-express/-/aws-xray-sdk-express-3.6.0.tgz#89e1a865e4e7d792e782a331bc69ae7e072d66b6"
  integrity sha512-vIHk+qpmLhI2JWiqgjhHZYerNo9jbjifJ6qIb+l5CCAqe00CIjnlnLF9LKLi4UhVT9xyAp46aCpWMEl+XeXSYA==
  dependencies:
    "@types/express" "*"

aws-xray-sdk-mysql@3.6.0:
  version "3.6.0"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk-mysql/-/aws-xray-sdk-mysql-3.6.0.tgz#0711503f38ebdb6e9e7aea486992e6f9965a357d"
  integrity sha512-DNZEe8Q331tzqc0dLM1Zy0d2GNFoks7BIX3Lop/+lGLytO6a4sdZ/NIXZX6pngG1sBYlsoTSFCFi470UD6Q6AA==
  dependencies:
    "@types/mysql" "*"

aws-xray-sdk-postgres@3.6.0:
  version "3.6.0"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk-postgres/-/aws-xray-sdk-postgres-3.6.0.tgz#9f3b90cf75c03156fd450d71d7b67ba126565bd8"
  integrity sha512-jQ4pNWq0RdVVZJ8XVqD7Rc6YS6bYqxCV0IQW2SBAhnW6S5yCLRekYgxbj0D35VZgLyqZQX98kdOV7ZqrWRyAyw==
  dependencies:
    "@types/pg" "*"

aws-xray-sdk@^3.3.4:
  version "3.6.0"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk/-/aws-xray-sdk-3.6.0.tgz#7db599301d434a4b3e9f46ecd0dd841c106238bc"
  integrity sha512-ID2R0U94wEMPLAkmQ7+TZuzvMjp2fe0dqqUQb6BniIDWXdvV5bphrMhsHzp/0PDRjGqsVSrZ1V1qRmq8bhEl1A==
  dependencies:
    aws-xray-sdk-core "3.6.0"
    aws-xray-sdk-express "3.6.0"
    aws-xray-sdk-mysql "3.6.0"
    aws-xray-sdk-postgres "3.6.0"

axios@^1.6.7:
  version "1.6.8"
  resolved "https://registry.yarnpkg.com/axios/-/axios-1.6.8.tgz#66d294951f5d988a00e87a0ffb955316a619ea66"
  integrity sha512-v/ZHtJDU39mDpyBoFVkETcd/uNdxrWRrg3bKpOKzXFA6Bvqopts6ALSMU3y6ijYxbw2B+wPrIv46egTzJXCLGQ==
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

base64-js@^1.0.2, base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://registry.yarnpkg.com/base64-js/-/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

basic-ftp@^5.0.1:
  version "5.0.5"
  resolved "https://registry.yarnpkg.com/basic-ftp/-/basic-ftp-5.0.5.tgz#14a474f5fffecca1f4f406f1c26b18f800225ac0"
  integrity sha512-4Bcg1P8xhUuqcii/S0Z9wiHIrQVPMermM1any+MX5GeGD7faD3/msQUDGLol9wOcz4/jbg/WJnGqoJF6LiBdtg==

bowser@^2.11.0:
  version "2.11.0"
  resolved "https://registry.yarnpkg.com/bowser/-/bowser-2.11.0.tgz#5ca3c35757a7aa5771500c70a73a9f91ef420a8f"
  integrity sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==

bson@^4.7.2:
  version "4.7.2"
  resolved "https://registry.yarnpkg.com/bson/-/bson-4.7.2.tgz#320f4ad0eaf5312dd9b45dc369cc48945e2a5f2e"
  integrity sha512-Ry9wCtIZ5kGqkJoi6aD8KjxFZEx78guTQDnpXWiNthsxzrxAK/i8E6pCHAIZTbaEFWcOCvbecMukfK7XUvyLpQ==
  dependencies:
    buffer "^5.6.0"

buffer@4.9.2:
  version "4.9.2"
  resolved "https://registry.yarnpkg.com/buffer/-/buffer-4.9.2.tgz#230ead344002988644841ab0244af8c44bbe3ef8"
  integrity sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

buffer@^5.6.0:
  version "5.7.1"
  resolved "https://registry.yarnpkg.com/buffer/-/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

call-bind@^1.0.2, call-bind@^1.0.7:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/call-bind/-/call-bind-1.0.7.tgz#06016599c40c56498c18769d2730be242b6fa3b9"
  integrity sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

cls-hooked@^4.2.2:
  version "4.2.2"
  resolved "https://registry.yarnpkg.com/cls-hooked/-/cls-hooked-4.2.2.tgz#ad2e9a4092680cdaffeb2d3551da0e225eae1908"
  integrity sha512-J4Xj5f5wq/4jAvcdgoGsL3G103BtWpZrMo8NEinRltN+xpTZdI+M38pyQqhuFU/P792xkMFvnKSf+Lm81U1bxw==
  dependencies:
    async-hook-jl "^1.7.6"
    emitter-listener "^1.0.1"
    semver "^5.4.1"

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.yarnpkg.com/combined-stream/-/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

debug@4.x:
  version "4.3.4"
  resolved "https://registry.yarnpkg.com/debug/-/debug-4.3.4.tgz#1319f6579357f2338d3337d2cdd4914bb5dcc865"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/define-data-property/-/define-data-property-1.1.4.tgz#894dc141bb7d3060ae4366f6a0107e68fbe48c5e"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

emitter-listener@^1.0.1:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/emitter-listener/-/emitter-listener-1.1.2.tgz#56b140e8f6992375b3d7cb2cab1cc7432d9632e8"
  integrity sha512-Bt1sBAGFHY9DKY+4/2cV6izcKJUf5T7/gkdmkxzX/qv9CcGH8xSwVRW5mtX03SWJtRTWSOpzCuWN9rBFYZepZQ==
  dependencies:
    shimmer "^1.2.0"

es-define-property@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/es-define-property/-/es-define-property-1.0.0.tgz#c7faefbdff8b2696cf5f46921edfb77cc4ba3845"
  integrity sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==
  dependencies:
    get-intrinsic "^1.2.4"

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/es-errors/-/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/esprima/-/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==

events@1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/events/-/events-1.1.1.tgz#9ebdb7635ad099c70dcc4c2a1f5004288e8bd924"
  integrity sha512-kEcvvCBByWXGnZy6JUlgAp2gBIUjfCAV6P6TgT1/aaQKcmuAEC4OZTV1I4EWQLz2gxZw76atuVyvHhTxvi0Flw==

fast-xml-parser@4.2.5:
  version "4.2.5"
  resolved "https://registry.yarnpkg.com/fast-xml-parser/-/fast-xml-parser-4.2.5.tgz#a6747a09296a6cb34f2ae634019bf1738f3b421f"
  integrity sha512-B9/wizE4WngqQftFPmdaMYlXoJlJOYxGQOanC77fq9k8+Z0v5dDSVh+3glErdIROP//s/jgb7ZuxKfB8nVyo0g==
  dependencies:
    strnum "^1.0.5"

fast-xml-parser@^4.0.1:
  version "4.3.6"
  resolved "https://registry.yarnpkg.com/fast-xml-parser/-/fast-xml-parser-4.3.6.tgz#190f9d99097f0c8f2d3a0e681a10404afca052ff"
  integrity sha512-M2SovcRxD4+vC493Uc2GZVcZaj66CCJhWurC4viynVSTvrpErCShNcDz1lAho6n9REQKvL/ll4A4/fw6Y9z8nw==
  dependencies:
    strnum "^1.0.5"

follow-redirects@^1.15.6:
  version "1.15.6"
  resolved "https://registry.yarnpkg.com/follow-redirects/-/follow-redirects-1.15.6.tgz#7f815c0cda4249c74ff09e95ef97c23b5fd0399b"
  integrity sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==

for-each@^0.3.3:
  version "0.3.3"
  resolved "https://registry.yarnpkg.com/for-each/-/for-each-0.3.3.tgz#69b447e88a0a5d32c3e7084f3f1710034b21376e"
  integrity sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==
  dependencies:
    is-callable "^1.1.3"

form-data@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/form-data/-/form-data-4.0.0.tgz#93919daeaf361ee529584b9b31664dc12c9fa452"
  integrity sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

fs-extra@^10.0.0:
  version "10.1.0"
  resolved "https://registry.yarnpkg.com/fs-extra/-/fs-extra-10.1.0.tgz#02873cfbc4084dde127eaa5f9905eef2325d1abf"
  integrity sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs@^0.0.1-security:
  version "0.0.1-security"
  resolved "https://registry.yarnpkg.com/fs/-/fs-0.0.1-security.tgz#8a7bd37186b6dddf3813f23858b57ecaaf5e41d4"
  integrity sha512-3XY9e1pP0CVEUCdj5BmfIZxRBTSDycnbqhIOGec9QYtmVH2fbLpj86CFWkrNOkt/Fvty4KZG5lTglL9j/gJ87w==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/function-bind/-/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

get-intrinsic@^1.1.3, get-intrinsic@^1.2.4:
  version "1.2.4"
  resolved "https://registry.yarnpkg.com/get-intrinsic/-/get-intrinsic-1.2.4.tgz#e385f5a4b5227d449c3eabbad05494ef0abbeadd"
  integrity sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

gopd@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/gopd/-/gopd-1.0.1.tgz#29ff76de69dac7489b7c0918a5788e56477c332c"
  integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
  dependencies:
    get-intrinsic "^1.1.3"

graceful-fs@^4.1.6, graceful-fs@^4.2.0:
  version "4.2.11"
  resolved "https://registry.yarnpkg.com/graceful-fs/-/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz#963ed7d071dc7bf5f084c5bfbe0d1b6222586854"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/has-proto/-/has-proto-1.0.3.tgz#b31ddfe9b0e6e9914536a6ab286426d0214f77fd"
  integrity sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==

has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/has-symbols/-/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"
  integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==

has-tostringtag@^1.0.0, has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/has-tostringtag/-/has-tostringtag-1.0.2.tgz#2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc"
  integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.0:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/hasown/-/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

ieee754@1.1.13:
  version "1.1.13"
  resolved "https://registry.yarnpkg.com/ieee754/-/ieee754-1.1.13.tgz#ec168558e95aa181fd87d37f55c32bbcb6708b84"
  integrity sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg==

ieee754@^1.1.13, ieee754@^1.1.4:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/ieee754/-/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==

inherits@^2.0.3:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

install@^0.13.0:
  version "0.13.0"
  resolved "https://registry.yarnpkg.com/install/-/install-0.13.0.tgz#6af6e9da9dd0987de2ab420f78e60d9c17260776"
  integrity sha512-zDml/jzr2PKU9I8J/xyZBQn8rPCAY//UOYNmR01XwNwyfhEWObo2SWfSl1+0tm1u6PhxLwDnfsT/6jB7OUxqFA==

ip-address@^9.0.5:
  version "9.0.5"
  resolved "https://registry.yarnpkg.com/ip-address/-/ip-address-9.0.5.tgz#117a960819b08780c3bd1f14ef3c1cc1d3f3ea5a"
  integrity sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g==
  dependencies:
    jsbn "1.1.0"
    sprintf-js "^1.1.3"

is-arguments@^1.0.4:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/is-arguments/-/is-arguments-1.1.1.tgz#15b3f88fda01f2a97fec84ca761a560f123efa9b"
  integrity sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-callable@^1.1.3:
  version "1.2.7"
  resolved "https://registry.yarnpkg.com/is-callable/-/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==

is-generator-function@^1.0.7:
  version "1.0.10"
  resolved "https://registry.yarnpkg.com/is-generator-function/-/is-generator-function-1.0.10.tgz#f1558baf1ac17e0deea7c0415c438351ff2b3c72"
  integrity sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==
  dependencies:
    has-tostringtag "^1.0.0"

is-typed-array@^1.1.3:
  version "1.1.13"
  resolved "https://registry.yarnpkg.com/is-typed-array/-/is-typed-array-1.1.13.tgz#d6c5ca56df62334959322d7d7dd1cca50debe229"
  integrity sha512-uZ25/bUAlUY5fR4OKT4rZQEBrzQWYV9ZJYGGsUmEJ6thodVJ1HX64ePQ6Z0qPWP+m+Uq6e9UugrE38jeYsDSMw==
  dependencies:
    which-typed-array "^1.1.14"

isarray@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==

jmespath@0.16.0:
  version "0.16.0"
  resolved "https://registry.yarnpkg.com/jmespath/-/jmespath-0.16.0.tgz#b15b0a85dfd4d930d43e69ed605943c802785076"
  integrity sha512-9FzQjJ7MATs1tSpnco1K6ayiYE3figslrXA72G2HQ/n76RzvYlofyi5QM+iX4YRs/pu3yzxlVQSST23+dMDknw==

js-md5@^0.7.3:
  version "0.7.3"
  resolved "https://registry.yarnpkg.com/js-md5/-/js-md5-0.7.3.tgz#b4f2fbb0b327455f598d6727e38ec272cd09c3f2"
  integrity sha512-ZC41vPSTLKGwIRjqDh8DfXoCrdQIyBgspJVPXHBGu4nZlAEvG3nf+jO9avM9RmLiGakg7vz974ms99nEV0tmTQ==

js-sha1@^0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/js-sha1/-/js-sha1-0.6.0.tgz#adbee10f0e8e18aa07cdea807cf08e9183dbc7f9"
  integrity sha512-01gwBFreYydzmU9BmZxpVk6svJJHrVxEN3IOiGl6VO93bVKYETJ0sIth6DASI6mIFdt7NmfX9UiByRzsYHGU9w==

js-yaml@3.14.1:
  version "3.14.1"
  resolved "https://registry.yarnpkg.com/js-yaml/-/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/jsbn/-/jsbn-1.1.0.tgz#b01307cb29b618a1ed26ec79e911f803c4da0040"
  integrity sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A==

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.yarnpkg.com/jsonfile/-/jsonfile-6.1.0.tgz#bc55b2634793c679ec6403094eb13698a6ec0aae"
  integrity sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

kareem@2.5.1:
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/kareem/-/kareem-2.5.1.tgz#7b8203e11819a8e77a34b3517d3ead206764d15d"
  integrity sha512-7jFxRVm+jD+rkq3kY0iZDJfsO2/t4BBPeEb2qKn2lR/9KhuksYk5hxzfRYWMPV8P/x2d0kHD306YyWLzjjH+uA==

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.yarnpkg.com/lodash.merge/-/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==

memory-pager@^1.0.2:
  version "1.5.0"
  resolved "https://registry.yarnpkg.com/memory-pager/-/memory-pager-1.5.0.tgz#d8751655d22d384682741c972f2c3d6dfa3e66b5"
  integrity sha512-ZS4Bp4r/Zoeq6+NLJpP+0Zzm0pR8whtGPf1XExKLJBAczGMnSi3It14OiNCStjQjM6NU1okjQGSxgEZN8eBYKg==

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12:
  version "2.1.35"
  resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

moment@^2.29.1:
  version "2.30.1"
  resolved "https://registry.yarnpkg.com/moment/-/moment-2.30.1.tgz#f8c91c07b7a786e30c59926df530b4eac96974ae"
  integrity sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==

mongodb-connection-string-url@^2.6.0:
  version "2.6.0"
  resolved "https://registry.yarnpkg.com/mongodb-connection-string-url/-/mongodb-connection-string-url-2.6.0.tgz#57901bf352372abdde812c81be47b75c6b2ec5cf"
  integrity sha512-WvTZlI9ab0QYtTYnuMLgobULWhokRjtC7db9LtcVfJ+Hsnyr5eo6ZtNAt3Ly24XZScGMelOcGtm7lSn0332tPQ==
  dependencies:
    "@types/whatwg-url" "^8.2.1"
    whatwg-url "^11.0.0"

mongodb@4.17.2:
  version "4.17.2"
  resolved "https://registry.yarnpkg.com/mongodb/-/mongodb-4.17.2.tgz#237c0534e36a3449bd74c6bf6d32f87a1ca7200c"
  integrity sha512-mLV7SEiov2LHleRJPMPrK2PMyhXFZt2UQLC4VD4pnth3jMjYKHhtqfwwkkvS/NXuo/Fp3vbhaNcXrIDaLRb9Tg==
  dependencies:
    bson "^4.7.2"
    mongodb-connection-string-url "^2.6.0"
    socks "^2.7.1"
  optionalDependencies:
    "@aws-sdk/credential-providers" "^3.186.0"
    "@mongodb-js/saslprep" "^1.1.0"

mongoose@^6.1.3:
  version "6.12.8"
  resolved "https://registry.yarnpkg.com/mongoose/-/mongoose-6.12.8.tgz#9980fc2f0ce9a0cb2b4acad803a853eb802f278d"
  integrity sha512-/9KOOVq1a4XNzqcWiWoOckvCE9eJPQ4M6rA8BmGTeuyi/w8t7F7LZ+8Lv3yilcqV5JP78SfMmgzm4YqlGl3fOg==
  dependencies:
    bson "^4.7.2"
    kareem "2.5.1"
    mongodb "4.17.2"
    mpath "0.9.0"
    mquery "4.0.3"
    ms "2.1.3"
    sift "16.0.1"

mpath@0.9.0:
  version "0.9.0"
  resolved "https://registry.yarnpkg.com/mpath/-/mpath-0.9.0.tgz#0c122fe107846e31fc58c75b09c35514b3871904"
  integrity sha512-ikJRQTk8hw5DEoFVxHG1Gn9T/xcjtdnOKIU1JTmGjZZlg9LST2mBLmcX3/ICIbgJydT2GOc15RnNy5mHmzfSew==

mquery@4.0.3:
  version "4.0.3"
  resolved "https://registry.yarnpkg.com/mquery/-/mquery-4.0.3.tgz#4d15f938e6247d773a942c912d9748bd1965f89d"
  integrity sha512-J5heI+P08I6VJ2Ky3+33IpCdAvlYGTSUjwTPxkAr8i8EoduPMBX2OY/wa3IKZIQl7MU4SbFk8ndgSKyB/cl1zA==
  dependencies:
    debug "4.x"

ms@2.1.2:
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/ms/-/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

ms@2.1.3:
  version "2.1.3"
  resolved "https://registry.yarnpkg.com/ms/-/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

obuf@~1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/obuf/-/obuf-1.1.2.tgz#09bea3343d41859ebd446292d11c9d4db619084e"
  integrity sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==

pg-int8@1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/pg-int8/-/pg-int8-1.0.1.tgz#943bd463bf5b71b4170115f80f8efc9a0c0eb78c"
  integrity sha512-WCtabS6t3c8SkpDBUlb1kjOs7l66xsGdKpIPZsg4wR+B3+u9UAum2odSsF9tnvxg80h4ZxLWMy4pRjOsFIqQpw==

pg-numeric@1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/pg-numeric/-/pg-numeric-1.0.2.tgz#816d9a44026086ae8ae74839acd6a09b0636aa3a"
  integrity sha512-BM/Thnrw5jm2kKLE5uJkXqqExRUY/toLHda65XgFTBTFYZyopbKjBe29Ii3RbkvlsMoFwD+tHeGaCjjv0gHlyw==

pg-protocol@*:
  version "1.6.1"
  resolved "https://registry.yarnpkg.com/pg-protocol/-/pg-protocol-1.6.1.tgz#21333e6d83b01faaebfe7a33a7ad6bfd9ed38cb3"
  integrity sha512-jPIlvgoD63hrEuihvIg+tJhoGjUsLPn6poJY9N5CnlPd91c2T18T/9zBtLxZSb1EhYxBRoZJtzScCaWlYLtktg==

pg-types@^4.0.1:
  version "4.0.2"
  resolved "https://registry.yarnpkg.com/pg-types/-/pg-types-4.0.2.tgz#399209a57c326f162461faa870145bb0f918b76d"
  integrity sha512-cRL3JpS3lKMGsKaWndugWQoLOCoP+Cic8oseVcbr0qhPzYD5DWXK+RZ9LY9wxRf7RQia4SCwQlXk0q6FCPrVng==
  dependencies:
    pg-int8 "1.0.1"
    pg-numeric "1.0.2"
    postgres-array "~3.0.1"
    postgres-bytea "~3.0.0"
    postgres-date "~2.1.0"
    postgres-interval "^3.0.0"
    postgres-range "^1.1.1"

pify@^5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/pify/-/pify-5.0.0.tgz#1f5eca3f5e87ebec28cc6d54a0e4aaf00acc127f"
  integrity sha512-eW/gHNMlxdSP6dmG6uJip6FXN0EQBwm2clYYd8Wul42Cwu/DK8HEftzsapcNdYe2MfLiIwZqsDk2RDEsTE79hA==

possible-typed-array-names@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/possible-typed-array-names/-/possible-typed-array-names-1.0.0.tgz#89bb63c6fada2c3e90adc4a647beeeb39cc7bf8f"
  integrity sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==

postgres-array@~3.0.1:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/postgres-array/-/postgres-array-3.0.2.tgz#68d6182cb0f7f152a7e60dc6a6889ed74b0a5f98"
  integrity sha512-6faShkdFugNQCLwucjPcY5ARoW1SlbnrZjmGl0IrrqewpvxvhSLHimCVzqeuULCbG0fQv7Dtk1yDbG3xv7Veog==

postgres-bytea@~3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/postgres-bytea/-/postgres-bytea-3.0.0.tgz#9048dc461ac7ba70a6a42d109221619ecd1cb089"
  integrity sha512-CNd4jim9RFPkObHSjVHlVrxoVQXz7quwNFpz7RY1okNNme49+sVyiTvTRobiLV548Hx/hb1BG+iE7h9493WzFw==
  dependencies:
    obuf "~1.1.2"

postgres-date@~2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/postgres-date/-/postgres-date-2.1.0.tgz#b85d3c1fb6fb3c6c8db1e9942a13a3bf625189d0"
  integrity sha512-K7Juri8gtgXVcDfZttFKVmhglp7epKb1K4pgrkLxehjqkrgPhfG6OO8LHLkfaqkbpjNRnra018XwAr1yQFWGcA==

postgres-interval@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/postgres-interval/-/postgres-interval-3.0.0.tgz#baf7a8b3ebab19b7f38f07566c7aab0962f0c86a"
  integrity sha512-BSNDnbyZCXSxgA+1f5UU2GmwhoI0aU5yMxRGO8CdFEcY2BQF9xm/7MqKnYoM1nJDk8nONNWDk9WeSmePFhQdlw==

postgres-range@^1.1.1:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/postgres-range/-/postgres-range-1.1.4.tgz#a59c5f9520909bcec5e63e8cf913a92e4c952863"
  integrity sha512-i/hbxIE9803Alj/6ytL7UHQxRvZkI9O4Sy+J3HGc4F4oo/2eQAjTSNJ0bfxyse3bH0nuVesCk+3IRLaMtG3H6w==

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz#e102f16ca355424865755d2c9e8ea4f24d58c3e2"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"
  integrity sha512-RofWgt/7fL5wP1Y7fxE7/EmTLzQVnB0ycyibJ0OOHIlJqTNzglYFxVwETOcIoJqJmpDXJ9xImDv+Fq34F/d4Dw==

punycode@^2.1.1:
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==

querystring@0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/querystring/-/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"
  integrity sha512-X/xY82scca2tau62i9mDyU9K+I+djTMUsvwf7xnUX5GLvVzgJybOJf4Y6o9Zx3oJK/LSXg5tTZBjwzqVPaPO2g==

sax@1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/sax/-/sax-1.2.1.tgz#7b8e656190b228e81a66aea748480d828cd2d37a"
  integrity sha512-8I2a3LovHTOpm7NV5yOyO8IHqgVsfK4+UuySrXU8YXkSRX7k6hCV9b3HrkKCr3nMpgj+0bmocaJJWpvp1oc7ZA==

sax@>=0.6.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/sax/-/sax-1.3.0.tgz#a5dbe77db3be05c9d1ee7785dbd3ea9de51593d0"
  integrity sha512-0s+oAmw9zLl1V1cS9BtZN7JAd0cW5e0QH4W3LWEK6a4LaLEA2OTpGYWDY+6XasBLtz6wkm3u1xRw95mRuJ59WA==

semver@^5.4.1:
  version "5.7.2"
  resolved "https://registry.yarnpkg.com/semver/-/semver-5.7.2.tgz#48d55db737c3287cd4835e17fa13feace1c41ef8"
  integrity sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==

semver@^7.5.3:
  version "7.6.1"
  resolved "https://registry.yarnpkg.com/semver/-/semver-7.6.1.tgz#60bfe090bf907a25aa8119a72b9f90ef7ca281b2"
  integrity sha512-f/vbBsu+fOiYt+lmwZV0rVwJScl46HppnOA1ZvIuBWKOTlllpyJ3bfVax76/OrhCH38dyxoDIA8K7uB963IYgA==

set-function-length@^1.2.1:
  version "1.2.2"
  resolved "https://registry.yarnpkg.com/set-function-length/-/set-function-length-1.2.2.tgz#aac72314198eaed975cf77b2c3b6b880695e5449"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

shimmer@^1.2.0:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/shimmer/-/shimmer-1.2.1.tgz#610859f7de327b587efebf501fb43117f9aff337"
  integrity sha512-sQTKC1Re/rM6XyFM6fIAGHRPVGvyXfgzIDvzoq608vM+jeyVD0Tu1E6Np0Kc2zAIFWIj963V2800iF/9LPieQw==

sift@16.0.1:
  version "16.0.1"
  resolved "https://registry.yarnpkg.com/sift/-/sift-16.0.1.tgz#e9c2ccc72191585008cf3e36fc447b2d2633a053"
  integrity sha512-Wv6BjQ5zbhW7VFefWusVP33T/EM0vYikCaQ2qR8yULbsilAT8/wQaXvuQ3ptGLpoKx+lihJE3y2UTgKDyyNHZQ==

smart-buffer@^4.2.0:
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/smart-buffer/-/smart-buffer-4.2.0.tgz#6e1d71fa4f18c05f7d0ff216dd16a481d0e8d9ae"
  integrity sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==

socks@^2.7.1:
  version "2.8.3"
  resolved "https://registry.yarnpkg.com/socks/-/socks-2.8.3.tgz#1ebd0f09c52ba95a09750afe3f3f9f724a800cb5"
  integrity sha512-l5x7VUUWbjVFbafGLxPWkYsHIhEvmF85tbIeFZWc8ZPtoMyybuEhL7Jye/ooC4/d48FgOjSJXgsF/AJPYCW8Zw==
  dependencies:
    ip-address "^9.0.5"
    smart-buffer "^4.2.0"

sparse-bitfield@^3.0.3:
  version "3.0.3"
  resolved "https://registry.yarnpkg.com/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz#ff4ae6e68656056ba4b3e792ab3334d38273ca11"
  integrity sha512-kvzhi7vqKTfkh0PZU+2D2PIllw2ymqJKujUcyPMd9Y75Nv4nPbGJZXNhxsgdQab2BmlDct1YnfQCguEvHr7VsQ==
  dependencies:
    memory-pager "^1.0.2"

sprintf-js@^1.1.3:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/sprintf-js/-/sprintf-js-1.1.3.tgz#4914b903a2f8b685d17fdf78a70e917e872e444a"
  integrity sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==

stack-chain@^1.3.7:
  version "1.3.7"
  resolved "https://registry.yarnpkg.com/stack-chain/-/stack-chain-1.3.7.tgz#d192c9ff4ea6a22c94c4dd459171e3f00cea1285"
  integrity sha512-D8cWtWVdIe/jBA7v5p5Hwl5yOSOrmZPWDPe2KxQ5UAGD+nxbxU0lKXA4h85Ta6+qgdKVL3vUxsbIZjc1kBG7ug==

strnum@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/strnum/-/strnum-1.0.5.tgz#5c4e829fe15ad4ff0d20c3db5ac97b73c9b072db"
  integrity sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==

tr46@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/tr46/-/tr46-3.0.0.tgz#555c4e297a950617e8eeddef633c87d4d9d6cbf9"
  integrity sha512-l7FvfAHlcmulp8kr+flpQZmVwtu7nfRV7NZujtN0OqES8EL4O4e0qqzL0DC5gAvx/ZC/9lk6rhcUwYvkBnBnYA==
  dependencies:
    punycode "^2.1.1"

tslib@^1.11.1:
  version "1.14.1"
  resolved "https://registry.yarnpkg.com/tslib/-/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==

tslib@^2.3.1, tslib@^2.6.2:
  version "2.6.2"
  resolved "https://registry.yarnpkg.com/tslib/-/tslib-2.6.2.tgz#703ac29425e7b37cd6fd456e92404d46d1f3e4ae"
  integrity sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q==

undici-types@~5.26.4:
  version "5.26.5"
  resolved "https://registry.yarnpkg.com/undici-types/-/undici-types-5.26.5.tgz#bcd539893d00b56e964fd2657a4866b221a65617"
  integrity sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/universalify/-/universalify-2.0.1.tgz#168efc2180964e6386d061e094df61afe239b18d"
  integrity sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==

url@0.10.3:
  version "0.10.3"
  resolved "https://registry.yarnpkg.com/url/-/url-0.10.3.tgz#021e4d9c7705f21bbf37d03ceb58767402774c64"
  integrity sha512-hzSUW2q06EqL1gKM/a+obYHLIO6ct2hwPuviqTTOcfFVc61UbfJ2Q32+uGL/HCPxKqrdGB5QUwIe7UqlDgwsOQ==
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

util@^0.12.4:
  version "0.12.5"
  resolved "https://registry.yarnpkg.com/util/-/util-0.12.5.tgz#5f17a6059b73db61a875668781a1c2b136bd6fbc"
  integrity sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

uuid-by-string@^3.0.4:
  version "3.0.7"
  resolved "https://registry.yarnpkg.com/uuid-by-string/-/uuid-by-string-3.0.7.tgz#3c9b7e60c3d4a1bf5da5dfb2601721acc813d8fc"
  integrity sha512-9xf+GAcwzLLGL2Z2Vb7hmi7jWIAKSiuaI5cLFsKw1IIlm7S5VpqvdJ5S7N36hqdy0v7DAwnnENJVAeev57/H1A==
  dependencies:
    js-md5 "^0.7.3"
    js-sha1 "^0.6.0"

uuid@8.0.0:
  version "8.0.0"
  resolved "https://registry.yarnpkg.com/uuid/-/uuid-8.0.0.tgz#bc6ccf91b5ff0ac07bbcdbf1c7c4e150db4dbb6c"
  integrity sha512-jOXGuXZAWdsTH7eZLtyXMqUb9EcWMGZNbL9YcGBJl4MH4nrxHmZJhEHvyLFrkxo+28uLb/NYRcStH48fnD0Vzw==

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://registry.yarnpkg.com/uuid/-/uuid-8.3.2.tgz#80d5b5ced271bb9af6c445f21a1a04c606cefbe2"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

uuid@^9.0.1:
  version "9.0.1"
  resolved "https://registry.yarnpkg.com/uuid/-/uuid-9.0.1.tgz#e188d4c8853cc722220392c424cd637f32293f30"
  integrity sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==

validator@^13.7.0:
  version "13.12.0"
  resolved "https://registry.yarnpkg.com/validator/-/validator-13.12.0.tgz#7d78e76ba85504da3fee4fd1922b385914d4b35f"
  integrity sha512-c1Q0mCiPlgdTVVVIJIrBuxNicYE+t/7oKeI9MWLj3fh/uq2Pxh/3eeWbVZ4OcGW1TUf53At0njHw5SMdA3tmMg==

webidl-conversions@^7.0.0:
  version "7.0.0"
  resolved "https://registry.yarnpkg.com/webidl-conversions/-/webidl-conversions-7.0.0.tgz#256b4e1882be7debbf01d05f0aa2039778ea080a"
  integrity sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==

whatwg-url@^11.0.0:
  version "11.0.0"
  resolved "https://registry.yarnpkg.com/whatwg-url/-/whatwg-url-11.0.0.tgz#0a849eebb5faf2119b901bb76fd795c2848d4018"
  integrity sha512-RKT8HExMpoYx4igMiVMY83lN6UeITKJlBQ+vR/8ZJ8OCdSiN3RwCq+9gH0+Xzj0+5IrM6i4j/6LuvzbZIQgEcQ==
  dependencies:
    tr46 "^3.0.0"
    webidl-conversions "^7.0.0"

which-typed-array@^1.1.14, which-typed-array@^1.1.2:
  version "1.1.15"
  resolved "https://registry.yarnpkg.com/which-typed-array/-/which-typed-array-1.1.15.tgz#264859e9b11a649b388bfaaf4f767df1f779b38d"
  integrity sha512-oV0jmFtUky6CXfkqehVvBP/LSWJ2sy4vWMioiENyJLePrBO/yKyV9OyJySfAKosh+RYkIl5zJCNZ8/4JncrpdA==
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.2"

xml2js@0.6.2:
  version "0.6.2"
  resolved "https://registry.yarnpkg.com/xml2js/-/xml2js-0.6.2.tgz#dd0b630083aa09c161e25a4d0901e2b2a929b499"
  integrity sha512-T4rieHaC1EXcES0Kxxj4JWgaUQHDk+qwHcYOCFHfiwKz7tOVPLq7Hjq9dM1WCMhylqMEfP7hMcOIChvotiZegA==
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xml2js@^0.4.23:
  version "0.4.23"
  resolved "https://registry.yarnpkg.com/xml2js/-/xml2js-0.4.23.tgz#a0c69516752421eb2ac758ee4d4ccf58843eac66"
  integrity sha512-ySPiMjM0+pLDftHgXY4By0uswI3SPKLDw/i3UXbnO8M/p28zqexCUoPmQFrYD+/1BzhGJSs2i1ERWKJAtiLrug==
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder2@^3.0.2:
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/xmlbuilder2/-/xmlbuilder2-3.1.1.tgz#b977ef8a6fb27a1ea7ffa7d850d2c007ff343bc0"
  integrity sha512-WCSfbfZnQDdLQLiMdGUQpMxxckeQ4oZNMNhLVkcekTu7xhD4tuUDyAPoY8CwXvBYE6LwBHd6QW2WZXlOWr1vCw==
  dependencies:
    "@oozcitak/dom" "1.15.10"
    "@oozcitak/infra" "1.0.8"
    "@oozcitak/util" "8.3.8"
    js-yaml "3.14.1"

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "https://registry.yarnpkg.com/xmlbuilder/-/xmlbuilder-11.0.1.tgz#be9bae1c8a046e76b31127726347d0ad7002beb3"
  integrity sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==
