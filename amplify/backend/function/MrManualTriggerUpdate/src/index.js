const centaur = require("@mediality/centaur");
const helper = require("./library/helper");
const { genHorseLevelData_AUS } = require("./library/horseEntity");
const AWS = require("aws-sdk");
const lambda = new AWS.Lambda();
let con;

/**
 * Processes a meeting by generating updated stats anh horse info on the meeting data.
 *
 * @param {object} event - An event object containing the meeting ID and user information in the `body` property.
 * @returns {object} - An object containing the status code and a message indicating the result of the manual update process.
 *
 * @example
 * const event = {
 *   body: JSON.stringify({
 *     meetingId: "12345",
 *     user: "<PERSON>"
 *   })
 * };
 *
 * const result = await exports.handler(event);
 * console.log(result);
 *
 * Expected output:
 * {
 *   statusCode: 200,
 *   body: "Manual update finished successfully!"
 * }
 */
/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
  let meetingId;
  let user;
  let compareType;
  let files;
  try {
    console.log(`EVENT: ${JSON.stringify(event)}`);
    const eventParsed = event
    if (eventParsed.body) {
      const body = eventParsed.body;
      console.log(body)
      meetingId = body.id;
      user = body.user;
      compareType = body.compareType ?? "update";
      files = body.files ?? "";
      checkstats = body.checkstats ?? true;
      distribute = body.distribute ?? false;
    }
    // if (eventParsed.raceArray) {
    //   const raceArray = eventParsed.raceArray
    // }
    console.log(`Meeting ID: ${meetingId}`);

    con = await helper.openDBConnection(process.env.ENV);
    if (!meetingId) {
      return null;
    }
    const processed_meeting = await centaur.processed_meetings
      .findOne({ _id: meetingId })
      .lean();
    //step 1: check if the meeting is available in processed_meetings
    // if not fatal error return
    let temporary_meeting = await centaur.temp_meetings
      .findOne({ _id: meetingId })
      .lean();
    //step 2: check if the meeting is available in temp_meetings
    // if no, return error as this is expected to be there
    let savedMeeting = false
    if (temporary_meeting) {
      savedMeeting = temporary_meeting
    } else {
      return {
        statusCode: 401,
        body: JSON.stringify("Meeting isn't an update and temp meeting doesnt exist!"),
      };
    }
   
    
    if (savedMeeting) {
      if (Array.isArray(savedMeeting.processedMeetingData.meeting.races.race)) {
        const racePromises = savedMeeting.processedMeetingData.meeting.races.race.map(async (race, raceIndex) => {
        // for (const race of savedMeeting.processedMeetingData.meeting.races
        //   .race) {
          if (Array.isArray(race.horses.horse)) {
            const horsePromises = race.horses.horse.map(async (horse) => {
              if (horse["@_id"]){
                const updatedHorse = await genHorseLevelData_AUS(
                  horse,
                  race,
                  savedMeeting.meetingDate,
                  savedMeeting.meetingStage
                );
                horse.prizemoney_won =
                  updatedHorse.horse_processedMeetingData.prizemoney_won;
                horse.last_four_starts =
                  updatedHorse.horse_processedMeetingData.last_four_starts;
                horse.last_ten_starts =
                  updatedHorse.horse_processedMeetingData.last_ten_starts;
                horse.last_fifteen_starts =
                  updatedHorse.horse_processedMeetingData.last_fifteen_starts;
                horse.last_twenty_starts =
                  updatedHorse.horse_processedMeetingData.last_twenty_starts;
                horse.FF5_dry = updatedHorse.horse_processedMeetingData.FF5_dry;
                horse.FF5_wet = updatedHorse.horse_processedMeetingData.FF5_wet;
                horse.FF_Dry_Rating_100 =
                  updatedHorse.horse_processedMeetingData.FF_Dry_Rating_100;
                horse.FF_Wet_Rating_100 =
                  updatedHorse.horse_processedMeetingData.FF_Wet_Rating_100;
                horse.win_percentage =
                  updatedHorse.horse_processedMeetingData.win_percentage;
                horse.place_percentage =
                  updatedHorse.horse_processedMeetingData.place_percentage;
                horse.pace_value =
                  updatedHorse.horse_processedMeetingData.pace_value;
                horse.pace = updatedHorse.horse_processedMeetingData.pace;
                horse.win_distances =
                  updatedHorse.horse_processedMeetingData.win_distances;
                horse.class_quality =
                  updatedHorse.horse_processedMeetingData.class_quality;
                  if (race.locked) {
                    // Find corresponding race and horse from processed_meeting
                    if (processed_meeting && processed_meeting.processedMeetingData?.meeting?.races?.race) {
                        const processedRace = processed_meeting.processedMeetingData.meeting.races.race.find(
                            r => r["@_number"] === race["@_number"]
                        );
                        
                        if (processedRace && processedRace.horses?.horse) {
                            const processedHorse = processedRace.horses.horse.find(
                                h => h["@_id"] === horse["@_id"]
                            );
                            
                            if (processedHorse) {
                                // Use ratings from processed meeting
                                horse.rating = processedHorse.rating;
                                horse.rating_wfa = processedHorse.rating_wfa;
                            } else {
                                console.log(`Warning: Could not find processed horse ${horse["@_id"]} in race ${race["@_number"]} - using calculated ratings`);
                                horse.rating = updatedHorse.horse_processedMeetingData.rating;
                                horse.rating_wfa = updatedHorse.horse_processedMeetingData.rating_wfa;
                            }
                        }
                    }
                } else {
                    // Use newly calculated ratings for unlocked races
                    horse.rating = updatedHorse.horse_processedMeetingData.rating;
                    horse.rating_wfa = updatedHorse.horse_processedMeetingData.rating_wfa;
                }
                event.raceArray[raceIndex].pair_rating[horse["@_id"]].rating = horse.rating
                if (updatedHorse.horse_processedMeetingData?.jockey?.statistics) {
                  horse.jockey.statistics = updatedHorse.horse_processedMeetingData.jockey.statistics;
                }
                if (updatedHorse.horse_processedMeetingData?.trainer?.statistics) {
                  horse.trainer.statistics = updatedHorse.horse_processedMeetingData.trainer.statistics;
                }
                horse.statistics = ""
                horse.errors = updatedHorse.horse_processedMeetingData.errors
                horse.error_log = updatedHorse.horse_processedMeetingData.error_log
              }
            });
            await Promise.all(horsePromises);
          }
          console.log('finished race '+race['@_number'])
        });
        await Promise.all(racePromises);
      }
      console.log(savedMeeting.processedMeetingData.meeting.races.race[0].horses.horse[0]['@_name'],savedMeeting.processedMeetingData.meeting.races.race[0].horses.horse[0].rating)
      let dataToUpdate = {
        "processedMeetingData.meeting":
              savedMeeting.processedMeetingData.meeting,
      }
      let i = 0;
      for (const race of savedMeeting.inputMeetingData.races.race){
        let horseIdArray = [];
        let inputHorses = []
        for (const horse of race.horses.horse){
          if (horseIdArray.includes(horse["@_id"])) continue;
          horseIdArray.push(horse["@_id"]);
          inputHorses.push(horse)
        }
        dataToUpdate["inputMeetingData.races.race."+i+".horses.horse"] = inputHorses;
        i++;
      }
      // Save the updated meeting back to the temp meeting db
      let updateMeeting = await centaur.temp_meetings.updateOne(
        { _id: meetingId },
        {
          $set: dataToUpdate
        }
      );
      console.log(updateMeeting);
    }
    return event;
  } catch (error) {
    console.log(`Error: ${error}`);
    
    if (con) await helper.closeDBConnection(con);
  } finally {
    
    if (con) await helper.closeDBConnection(con);
  }
};
