{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.10.0\",\"stackType\":\"function-Lambda\",\"metadata\":{}}", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}, "stateMachineTargetName": {"Type": "String"}, "queueName": {"Type": "String"}, "functioncentaurappCentaurAppCommonLayerArn": {"Type": "String", "Default": "functioncentaurappCentaurAppCommonLayerArn"}, "awsLocal": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"sqsEventSource": {"Type": "AWS::Lambda::EventSourceMapping", "Properties": {"BatchSize": 1, "Enabled": "True", "EventSourceArn": {"Fn::Join": ["-", [{"Fn::Join": [":", ["arn:aws:sqs", {"Ref": "AWS::Region"}, {"Ref": "AWS::AccountId"}, {"Ref": "queueName"}]]}, {"Ref": "env"}]]}, "FunctionName": {"Ref": "LambdaFunction"}}}, "sqsExecuteLambdaPermission": {"Type": "AWS::Lambda::Permission", "Properties": {"FunctionName": {"Ref": "LambdaFunction"}, "Action": "lambda:InvokeFunction", "Principal": "sqs.amazonaws.com", "SourceAccount": {"Ref": "AWS::AccountId"}, "SourceArn": {"Fn::Join": ["-", [{"Fn::Join": [":", ["arn:aws:sqs", {"Ref": "AWS::Region"}, {"Ref": "AWS::AccountId"}, {"Ref": "queueName"}]]}, {"Ref": "env"}]]}}}, "LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.processSqsEvents", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "MrCenLambdaStepFunctionExecutor", {"Fn::Join": ["", ["MrCenLambdaStepFunctionExecutor", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "STATE_MACHINE_ARN": {"Fn::Join": [":", ["arn:aws:states", {"Ref": "AWS::Region"}, {"Ref": "AWS::AccountId"}, "stateMachine", {"Fn::Join": ["-", [{"Ref": "stateMachineTargetName"}, {"Ref": "env"}]]}]]}, "AWS_LOCAL": {"Ref": "awsLocal"}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "TracingConfig": {"Mode": "Active"}, "Runtime": "nodejs20.x", "Layers": [{"Ref": "functioncentaurappCentaurAppCommonLayerArn"}], "Timeout": 25}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "centaurappLambdaRoleed86b78b", {"Fn::Join": ["", ["centaurappLambdaRoleed86b78b", "-", {"Ref": "env"}]]}]}, "Path": "/", "ManagedPolicyArns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaSQSQueueExecutionRole", "arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess", "arn:aws:iam::aws:policy/AWSStepFunctionsFullAccess"], "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}, "InvocationErrorAlarm": {"Type": "AWS::CloudWatch::Alarm", "DependsOn": ["LambdaFunction"], "Properties": {"AlarmActions": [{"Fn::Sub": ["arn:aws:sns:${region}:${account}:MrCenAlarms-${environment}", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "environment": {"Ref": "env"}}]}], "AlarmName": {"Fn::Join": ["", ["LambdaError-", {"Ref": "LambdaFunction"}]]}, "ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "FunctionName", "Value": {"Ref": "LambdaFunction"}}], "EvaluationPeriods": 1, "MetricName": "Errors", "Namespace": "AWS/Lambda", "Period": 60, "Statistic": "Maximum", "Threshold": 1, "TreatMissingData": "notBreaching"}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}}}