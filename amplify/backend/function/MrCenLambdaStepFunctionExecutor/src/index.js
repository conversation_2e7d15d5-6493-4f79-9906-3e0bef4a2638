
const uuid = require('uuid');
const aws = require('aws-sdk')
const helper = require("./library/helper")
const AWSXRay = require("aws-xray-sdk-core")
const wrap = require('@dazn/lambda-powertools-pattern-basic')

AWSXRay.enableAutomaticMode();

var stepfunctions = new aws.StepFunctions()


exports.processSqsEvents = wrap(async (event) => {
  
  console.log(event)
  if (event.userTrigger){
    var exec_uuid = uuid.v4();
    var record = event.record
    console.log(record)
    var fileType = record['s3']['object']['key'].split(".")[0].split("_").slice(-1)[0].toLowerCase();
    var params = {
      stateMachineArn: process.env.STATE_MACHINE_ARN,
      name: exec_uuid,
      input: JSON.stringify(record),
    };

    console.log(
      `Starting Execution with Message Id: ${record[messageId]}, ` +
      `with Execution Id: ${exec_uuid}, and Mediality ` +
      `Racing File Type: ${fileType}`)

    try {
      var resp = await stepfunctions.startExecution(params).promise();
      console.log(resp)
    }
    catch (err) {
      console.log("Error: - " + err)
    }
  } else{
    var segment = new AWSXRay.Segment('processSqsEvents');
    console.log(event);
    var messageId = event['Records'][0]['messageId'];
    var messageBody = JSON.parse(event['Records'][0]['body']);
    
    // Check if the message is an SNS encapsulated message and extract the S3 event
    var s3event = messageBody.Type === 'Notification' ? JSON.parse(messageBody.Message) : messageBody;

    for (var index = 0; index < s3event['Records'].length; index++) {
      console.log(s3event);
      var exec_uuid = uuid.v4();
      
      if ('Records' in s3event) {
        var record = s3event['Records'][index];
        var fileType = record['s3']['object']['key'].split(".")[0].split("_").slice(-1)[0].toLowerCase();
        record['s3']['fileType'] = fileType;
        record['messageId'] = messageId; // Attach the SQS messageId to the record for tracking
        console.log(messageId);
        console.log(record);

        var params = {
          stateMachineArn: process.env.STATE_MACHINE_ARN,
          name: exec_uuid,
          input: JSON.stringify(record),
        };

        console.log(`Starting Execution with Message Id: ${messageId}, ` +
          `with Execution Id: ${exec_uuid}, and Mediality ` +
          `Racing File Type: ${fileType}`);

        try {
          var resp = await stepfunctions.startExecution(params).promise();
          console.log(resp);
        } catch (err) {
          console.log("Error: - " + err);
        }
      } else {
        console.log("Unknown Mediality Racing File Type");
        console.log(s3event);
        break; // Exit the loop if no 'Records' field is found
      }
    }
  }


  // return this.response
});

exports.index = wrap(async (event) => {
  console.log(`recevied event ${event}`)
  exports.processSqsEvents(event)
});

// Local Testing to get around limitations in Amplify Mock
if (process.env.AWS_LOCAL == "true") {
  const fs = require('fs');
  let rawdata = fs.readFileSync('event_weights.json');
  let event = JSON.parse(rawdata);
  exports.processSqsEvents(event)
}
