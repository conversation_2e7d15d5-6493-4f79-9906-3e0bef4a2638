const util = require('util')
const AWS = require('aws-sdk');
const SQS = new AWS.SQS({region: process.env.AWS_REGION || 'ap-southeast-2'});


const inspectObject = async(jsonObject) => {
    console.log(
        util.inspect(
            jsonObject,
            {showHidden: false, depth: null, colors: true}
        )
    )
}

const deleteSqsMessage = async (queueName) => {
}


module.exports = {
    inspectObject
};
