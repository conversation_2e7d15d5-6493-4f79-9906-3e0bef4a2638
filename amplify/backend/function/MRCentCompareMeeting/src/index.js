const moment = require("moment");
const helper = require("./helper");
const uuid = require("uuid");
const results = require("./results");
const centaur = require("@mediality/centaur");

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
  // console.log(`EVENT: ${JSON.stringify(event)}`);
  let meetingId;
  let files;
  let checkstats;
  let compareType;
  let distribute;
  if (event.body) {
    meetingId = event.body.id;
    files = event.body.files;
    checkstats = event.body.checkstats;
    compareType = event.body.compareType;
    distribute = event.body.distribute
  } else if (event.meetingId) {
    meetingId = event.meetingId;
    files = event.files ?? '';
    checkstats = event.checkstats ?? false;
    compareType = event.compareType ?? 'load';
    distribute = event.distribute ?? false;
  }
  // console.log(
  //   `meetingId 1: ${meetingId} files: ${files} checkstats: ${checkstats}`
  // );
  const payload = {
    meetingId: meetingId,
    files: files,
    checkstats: checkstats,
    compareType: compareType,
    distribute: distribute
  };
  console.log(`Payload: 2: ${JSON.stringify(payload)}`);
  if (["update","load"].includes(compareType)){
    await compareMeetingAPI(JSON.stringify(payload));
  } else if (compareType === "saveOnly"){
    await saveOnly(meetingId,event.user);
  } else if (compareType === "cancelEdit"){
    await cancelEdit(meetingId);
  } else {
    return {
      statusCode: 422,
      body: JSON.stringify("Compare meeting failed, missing syntax :("),
    };
  }
  return {
    statusCode: 200,
    body: JSON.stringify("Compare meeting done successfully!"),
  };
};
const compareMeetingAPI = async (payloadString) => {
  console.log(`payloadString: ${JSON.stringify(payloadString)}`);
  const payload = JSON.parse(payloadString);
  console.log(`Payload: ${JSON.stringify(payload)}`);
  const meetingId = payload.meetingId;
  const files = payload.files;
  const checkstats = payload.checkstats;
  const compareType = payload.compareType;
  let distribute = payload.distribute;
  let raceNo = 0;
  let dir = 1;
  let errors = [];
  let con = await helper.openDBConnection(process.env.ENV);
  // Generate the meeting error count and populate horse errors to races
  const genErrors = await genMeetingErrorCount(meetingId)
  try {
    
    if (checkstats) {
      const isValid = await checkStatsAndValidity(meetingId);
      if (!isValid) {
        return "Meeting compare failed, deleting temp";
      }
    }

    const pm = await centaur.processed_meetings
      .findOne({ _id: meetingId })
      .select("processedMeetingData.meeting")
      .lean();
    const tm = await centaur.temp_meetings
      .findOne({ _id: meetingId })
      .select("processedMeetingData.meeting meetingLoadHistory")
      .lean();

    if (!tm) {
      return "ERROR: temp meeting not found";
    }

    let diffObject = {};
    if (pm) {
      const { meeting: meet1 } = pm.processedMeetingData;
      const { meeting: meet2 } = tm.processedMeetingData;

      diffObject = compareTheMeeting(meet1, meet2, dir, diffObject);
      dir++;
      diffObject = compareTheMeeting(meet2, meet1, dir, diffObject);
      errors = [...new Set(diffObject.errors)]
      diffObject = genDiffSummary(diffObject);
      console.log('gendiff done')
      diffObject.summary = genMeetingCompleteness(meet2);

    } else {
      diffObject.notes = "New meeting load";
    }

    const checkChangeLog = await centaur.changelog
      .findOne({ meeting_id: meetingId })
      .lean();
    const upm = await centaur.processed_meetings
      .findOne({ _id: meetingId })
      .select("meetingLoadHistory")
      .lean();

    let i = 0;
    const skipFilesNoChanges = [
      "_Gear",
      "_FORM_",
      "_Scratchings",
      "_Nominations",
      "_Weights",
      "_Acceptances",
      "_FinalFields",
      "RATINGS",
    ];
    let writeChanges = true;

    if (diffObject.notes === "No changes.") {
      console.log("not adding log or delivering because NO CHANGES");
      for (const type of skipFilesNoChanges) {
        if (tm.meetingLoadHistory[0].file_path.includes(type)) {
          writeChanges = false;
        }
      }
    }

    if (writeChanges && checkChangeLog) {
      const logs = checkChangeLog.changelog;

      logs.unshift({
        time: 
        (compareType == "update" ?
        moment.utc().format() : 
        tm.meetingLoadHistory[0].time),
        bucket: 
        (compareType == "update" ? 
        "UPDATES" : 
        tm.meetingLoadHistory[0].bucket),
        file_path: 
        (compareType == "update" ? 
        "Meeting update process" : 
        tm.meetingLoadHistory[0].file_path),
        trigger: 
        (compareType == "update" ? 
        "UPDATES" : 
        tm.meetingLoadHistory[0].trigger),
        changes: diffObject,
      });

      if (upm?.meetingLoadHistory?.length > tm.meetingLoadHistory.length) {
        for (let i=0; i < upm.meetingLoadHistory.length; i++) {
          if (
            upm.meetingLoadHistory[i].time === tm.meetingLoadHistory[0].time
          ) {
            break;
          }
          logs.unshift(upm.meetingLoadHistory[i]);
          errors.push(`${upm.meetingLoadHistory[i].file_path} Failed to load`);
        }
      }

      const dataToUpdate = {
        changelog: logs,
      };

      const updateChangeLog = await centaur.changelog
        .updateOne(
          { _id: checkChangeLog._id },
          {
            $set: dataToUpdate,
          }
        )
        .lean();
      console.log(updateChangeLog);
    } else if (writeChanges) {
      let logs = [];
      if (tm.meetingLoadHistory.length > 0) {
        logs = tm.meetingLoadHistory;
        logs[0].changes = diffObject;

        if (upm?.meetingLoadHistory?.length > tm.meetingLoadHistory.length) {
          for (; i < upm.meetingLoadHistory.length; i++) {
            if (
              upm.meetingLoadHistory[i].time.toString() ===
              tm.meetingLoadHistory[0].time.toString()
            ) {
              break;
            }
            logs.unshift(upm.meetingLoadHistory[i]);
            errors.push(
              `${upm.meetingLoadHistory[i].file_path} Failed to load`
            );
          }
        }
      } else {
        if (upm?.meetingLoadHistory?.[0]) {
          logs = upm.meetingLoadHistory;
        }
        console.log("Error: couldnt find the load item for these changes!!");
      }

      const id = uuid.v4();
      const dataToCreate = {
        _id: id,
        meeting_id: meetingId,
        changelog: logs,
      };

      const newRecord = new centaur.changelog(dataToCreate);
      const d = await newRecord.save();
      console.log(d);
    } else if (checkChangeLog) {
      const logs = checkChangeLog.changelog;
      if (upm?.meetingLoadHistory?.length > tm.meetingLoadHistory.length - 1) {
        for (; i < upm.meetingLoadHistory.length; i++) {
          if (
            upm.meetingLoadHistory[i].time === tm.meetingLoadHistory[1].time
          ) {
            break;
          }
          if (upm.meetingLoadHistory[i].trigger === "NOT LOADED") {
            logs.unshift(upm.meetingLoadHistory[i]);
            errors.push(
              `${upm.meetingLoadHistory[i].file_path} Failed to load`
            );
          }
        }
      }

      const dataToUpdate = {
        changelog: logs,
      };
      const updateChangeLog = await centaur.changelog
        .updateOne(
          { _id: checkChangeLog._id },
          {
            $set: dataToUpdate,
          }
        )
        .lean();
    } else {
      let logs = [];
      if (upm?.meetingLoadHistory?.length > tm.meetingLoadHistory.length - 1) {
        for (; i < upm.meetingLoadHistory.length; i++) {
          if (
            upm.meetingLoadHistory[i].time === tm.meetingLoadHistory[1].time
          ) {
            break;
          }
          if (upm.meetingLoadHistory[i].trigger === "NOT LOADED") {
            logs.unshift(upm.meetingLoadHistory[i]);
            errors.push(
              `${upm.meetingLoadHistory[i].file_path} Failed to load`
            );
          }
        }
      }

      const id = uuid.v4();
      const dataToCreate = {
        _id: id,
        meeting_id: meetingId,
        changelog: logs,
      };

      const newRecord = new centaur.changelog(dataToCreate);
      const d = await newRecord.save();
      console.log(d);
    }

    let filesToSave = writeChanges ? files : "";
    const raceNoToSave = raceNo || 0;
    const distributeToSave = distribute && writeChanges;

    const snd = await saveAndDistribute(
      meetingId,
      errors,
      distributeToSave,
      filesToSave,
      raceNoToSave
    );
    console.log("meeting compared (and deleted)");
    return "meeting compared and finished";
  } catch (error) {
    if (con) await helper.closeDBConnection(con);
    console.error(`Error in compareMeetingAPI: ${error.message}`);
    throw error;
  } finally {
    if (con) await helper.closeDBConnection(con);
  }
};

const compareTheMeeting = (meet1, meet2, dir, diffObject) => {
  try {
    console.log("compare meeting level");
    if (!diffObject.errors) diffObject.errors = []
    for (const item in meet1) {
      if (["track", "races", "errors", "product"].includes(item)) continue;
      try {
        if (meet1[item] !== meet2[item]) {
          appendDiff(
            meet1[item],
            meet2[item],
            dir,
            diffObject,
            "meeting." + item
          );
        }
      } catch (err) {
        appendDiff(
          meet1[item],
          "ERROR DIFFING: " + err,
          dir,
          diffObject,
          "meeting." + item
        );
      }
    }
    console.log("compare meeting track");
    for (const item in meet1.track) {
      if (["weather"].includes(item)) continue;
      try {
        if (meet1.track[item] !== meet2.track[item]) {
          appendDiff(
            meet1.track[item],
            meet2.track[item],
            dir,
            diffObject,
            "meeting.track." + item
          );
        }
      } catch (err) {
        appendDiff(
          meet1.track[item],
          "ERROR DIFFING: " + err,
          dir,
          diffObject,
          "meeting.track." + item
        );
      }
    }
    for (const item in meet1.errors) {
      try {
        if (meet1.errors[item] !== meet2.errors[item]) {
          appendDiff(
            meet1.errors[item],
            meet2.errors[item],
            dir,
            diffObject,
            "meeting.errors." + item
          );
        }
      } catch (err) {
        appendDiff(
          meet1.errors[item],
          "ERROR DIFFING: " + err,
          dir,
          diffObject,
          "meeting.errors." + item
        );
      }
    }
    RaceLoop: for (var i = 0; i < meet1.races.race.length; i++) {
      race_from = meet1.races.race[i];
      race_to = meet2.races.race[i];
      if (dir == 1 && race_from["@_id"] !== race_to["@_id"]){
        for (let race of meet2.races.race){
          if (race["@_id"] == race_from["@_id"]) {
            race_to = race;
          }
        }
        if (race_from["@_id"] !== race_to["@_id"]){
          diffObject.errors.push(`Race noms number: ${race_from["@_nominations_number"]} (${race_from["@_name"]}) Racecode: ${race_from["@_ra_id"] ?? ""} has been removed from the meeting`);
          appendDiff(
            "",
            `Race noms number ${race_from["@_nominations_number"]} has been removed from the meeting`,
            dir,
            diffObject,
            "meeting.change"
          );
          continue RaceLoop;
        }
      } else if (race_from["@_id"] !== race_to["@_id"]){
        for (let race of meet2.races.race){
          if (race["@_id"] === race_from["@_id"]) {
            race_to = race;
          }
        }
        if (race_from["@_id"] !== race_to["@_id"]) continue RaceLoop;
      }
      var raceno = "Race " + (race_from["@_number"] !== race_to["@_number"] ? (dir === 1 ? race_to["@_number"] : race_from["@_number"] ) : race_from["@_number"]) ;
      for (const item in race_from) {
        if (["errors", "records","error_log"].includes(item)) continue;
        if (item == "horses") {
          if (race_from[item]["horse"][0]) {
            theHorseLoop: for (let x = 0; x < race_from[item]["horse"].length; x++) {
              const j = parseInt(x);
              var horse = race_from[item]["horse"][j];
              if (horse.scratched && (race_from.race_stage == 'Results' || race_from.race_stage == 'InterimResults') && (race_to.race_stage == 'Results' || race_to.race_stage =='InterimResults')) {
                continue theHorseLoop; // Skip this horse if it is scratched
              }
              var h_cname = horse["@_name"].replace(/[^A-z ]/g, "");
              console.log(raceno,h_cname)
              if (!race_to[item]["horse"][j]) {
                if (
                  ["Acceptances", "FinalFields", "Final Fields", "InterimResults", "Results"].includes(
                    meet1.stage
                  ) &&
                  !helper.meetStageChange(meet1.stage, meet2.stage)
                ) {
                  console.log('horse missing!')
                  console.log(` ${race_from['@_number']}: horse ${h_cname} ${
                      race_from[item]["horse"][j]["@_id"]
                    } appears to have been ${dir == 1 ? "removed" : "added"}`)
                  diffObject.errors.push(
                    ` ${race_from['@_number']}: horse ${h_cname} ${
                      race_from[item]["horse"][j]["@_id"]
                    } appears to have been ${dir == 1 ? "removed" : "added"}`
                  );
                  break theHorseLoop;
                } 
              } else if (horse["@_id"] != race_to[item]["horse"][j]["@_id"]) {
                if (
                  ["Acceptances", "FinalFields", "Final Fields", "InterimResults", "Results"].includes(
                    meet1.stage
                  ) &&
                  !helper.meetStageChange(meet1.stage, meet2.stage)
                ) {
                  console.log('id mismatch!')
                  console.log(`Race ${race_from['@_number']}: Field has changed, please check. horse ${
                      (dir == 1 ? race_from : race_to)[item]["horse"][j].tab_number
                    }. ${
                      (dir == 1 ? race_from : race_to)[item]["horse"][j]["@_name"]
                    } ${
                      (dir == 1 ? race_from : race_to)[item]["horse"][j]["@_id"]
                    } has been replaced with ${
                      (dir == 1 ? race_to : race_from)[item]["horse"][j].tab_number
                    }. ${
                      (dir == 1 ? race_to : race_from)[item]["horse"][j]["@_name"]
                    } ${
                      (dir == 1 ? race_to : race_from)[item]["horse"][j]["@_id"]
                    }, this means we cant compare the remaining horses. There were ${
                      (dir == 1 ? race_from : race_to)[item]["horse"].length
                    } horses, there are now ${
                      (dir == 1 ? race_to : race_from)[item]["horse"].length
                    } horses.`)
                  diffObject.errors.push(
                    `Race ${race_from['@_number']}: Field has changed, please check. horse ${
                      (dir == 1 ? race_from : race_to)[item]["horse"][j].tab_number
                    }. ${
                      (dir == 1 ? race_from : race_to)[item]["horse"][j]["@_name"]
                    } ${
                      (dir == 1 ? race_from : race_to)[item]["horse"][j]["@_id"]
                    } has been replaced with ${
                      (dir == 1 ? race_to : race_from)[item]["horse"][j].tab_number
                    }. ${
                      (dir == 1 ? race_to : race_from)[item]["horse"][j]["@_name"]
                    } ${
                      (dir == 1 ? race_to : race_from)[item]["horse"][j]["@_id"]
                    }, this means we cant compare the remaining horses. There were ${
                      (dir == 1 ? race_from : race_to)[item]["horse"].length
                    } horses, there are now ${
                      (dir == 1 ? race_to : race_from)[item]["horse"].length
                    } horses.`
                  );
                  
                  break theHorseLoop;
                }
                
              }
              let to_index = -1;
              if (!race_to[item]["horse"][j] || race_to[item]["horse"][j]["@_id"] !== race_from[item]["horse"][j]["@_id"]){
                for (let t = 0;t < race_to[item]["horse"].length; t++){
                  if (race_from[item]["horse"][j]["@_id"] === race_to[item]["horse"][t]["@_id"]) {
                    to_index = parseInt(t);
                    break;
                  }
                }
                console.log("horse index",j)
                console.log("to index",to_index)
                // console.log(race_to[item]["horse"][to_index]["@_id"],race_from[item]["horse"][j]["@_id"])

                if (to_index < 0){
                  if (dir == 1){
                    appendDiff(
                      "",
                      "horse removed from race.",
                      dir,
                      diffObject,
                      "races." +
                        raceno +
                        ".horses." +
                        h_cname + 
                        ".notes"
                    );
                  } else {
                    appendDiff(
                      "",
                      "horse added to race.",
                      dir,
                      diffObject,
                      "races." +
                        raceno +
                        ".horses." +
                        h_cname + 
                        ".notes"
                    );
                  }
                  continue theHorseLoop;
                }
              } else {
                to_index = parseInt(j);
              }

              for (const horse_in in horse) {
                if (typeof horse[horse_in] === "object") {
                  if (["statistics"].includes(horse_in)) {
                    // compareStats(race_from[item]['horse'][j][horse_in]['statistic'],race_to[item]['horse'][j][horse_in]['statistic'],dir,diffObject,'races.'+raceno+'.horses.'+h_cname+'.'+horse_in)
                    continue;
                  }
                  for (const horse_in_sub in horse[horse_in]) {
                    if (Array.isArray(horse[horse_in][horse_in_sub])) {
                      for (
                        var k = 0;
                        k < horse[horse_in][horse_in_sub].length;
                        k++
                      ) {
                        for (const horse_in_sub_bit in horse[horse_in][
                          horse_in_sub
                        ][k]) {
                          try {
                            if (
                              race_from[item]["horse"][j][horse_in][
                                horse_in_sub
                              ][k][horse_in_sub_bit] !==
                              race_to[item]["horse"][to_index][horse_in][horse_in_sub][
                                k
                              ][horse_in_sub_bit]
                            ) {
                              appendDiff(
                                race_from[item]["horse"][j][horse_in][
                                  horse_in_sub
                                ],
                                race_to[item]["horse"][to_index][horse_in][
                                  horse_in_sub
                                ],
                                dir,
                                diffObject,
                                "races." +
                                  raceno +
                                  ".horses." +
                                  h_cname +
                                  "." +
                                  horse_in +
                                  "." +
                                  horse_in_sub
                              );
                            }
                          } catch (err) {
                            appendDiff(
                              race_from[item]["horse"][j][horse_in][
                                horse_in_sub
                              ][k],
                              "ERROR DIFFING: " + err,
                              dir,
                              diffObject,
                              "races." +
                                raceno +
                                ".horses." +
                                h_cname +
                                "." +
                                horse_in +
                                "." +
                                horse_in_sub +
                                "." +
                                k.toString()
                            );
                          }
                        }
                      }
                    } else if (
                      typeof horse[horse_in][horse_in_sub] === "object"
                    ) {
                      if (
                        ["trainer", "jockey"].includes(horse_in) &&
                        horse_in_sub === "statistics"
                      ) {
                        // compareStats(race_from[item]['horse'][j][horse_in][horse_in_sub]['statistic'],race_to[item]['horse'][j][horse_in][horse_in_sub]['statistic'],dir,diffObject,'races.'+raceno+'.horses.'+h_cname+'.'+horse_in+'.'+horse_in_sub)
                        continue;
                      }
                      for (const horse_in_sub_bit in horse[horse_in][
                        horse_in_sub
                      ]) {
                        if (
                          Array.isArray(
                            horse[horse_in][horse_in_sub][horse_in_sub_bit]
                          )
                        ) {
                          for (
                            var k = 0;
                            k <
                            horse[horse_in][horse_in_sub][horse_in_sub_bit]
                              .length;
                            k++
                          ) {
                            for (const horse_in_sub_bit_pec in horse[horse_in][
                              horse_in_sub
                            ][horse_in_sub_bit][k]) {
                              try {
                                if (
                                  race_from[item]["horse"][j][horse_in][
                                    horse_in_sub
                                  ][horse_in_sub_bit][k][
                                    horse_in_sub_bit_pec
                                  ].includes("progeny")
                                )
                                  break;
                              } catch (err) {
                                console.log(
                                  race_from[item]["horse"][j][horse_in][
                                    horse_in_sub
                                  ][horse_in_sub_bit][k][horse_in_sub_bit_pec]
                                );
                              }
                              try {
                                if (
                                  race_from[item]["horse"][j][horse_in][
                                    horse_in_sub
                                  ][horse_in_sub_bit][k][
                                    horse_in_sub_bit_pec
                                  ] !=
                                  race_to[item]["horse"][to_index][horse_in][
                                    horse_in_sub
                                  ][horse_in_sub_bit][k][horse_in_sub_bit_pec]
                                ) {
                                  appendDiff(
                                    race_from[item]["horse"][j][horse_in][
                                      horse_in_sub
                                    ][horse_in_sub_bit][k],
                                    race_to[item]["horse"][to_index][horse_in][
                                      horse_in_sub
                                    ][horse_in_sub_bit][k],
                                    dir,
                                    diffObject,
                                    "races." +
                                      raceno +
                                      ".horses." +
                                      h_cname +
                                      "." +
                                      horse_in +
                                      "." +
                                      horse_in_sub +
                                      "." +
                                      horse_in_sub_bit +
                                      "." +
                                      k.toString()
                                  );
                                }
                              } catch (err) {
                                appendDiff(
                                  race_from[item]["horse"][j][horse_in][
                                    horse_in_sub
                                  ][horse_in_sub_bit][k],
                                  "ERROR DIFFING: " + err,
                                  dir,
                                  diffObject,
                                  "races." +
                                    raceno +
                                    ".horses." +
                                    h_cname +
                                    "." +
                                    horse_in +
                                    "." +
                                    horse_in_sub +
                                    "." +
                                    horse_in_sub_bit +
                                    "." +
                                    k.toString()
                                );
                              }
                            }
                          }
                        } else if (
                          typeof horse[horse_in][horse_in_sub] === "object"
                        ) {
                          for (const horse_in_sub_bit_pec in horse[horse_in][
                            horse_in_sub
                          ][horse_in_sub_bit]) {
                            try {
                              if (
                                race_from[item]["horse"][j][horse_in][
                                  horse_in_sub
                                ][horse_in_sub_bit][horse_in_sub_bit_pec] !=
                                race_to[item]["horse"][to_index][horse_in][
                                  horse_in_sub
                                ][horse_in_sub_bit][horse_in_sub_bit_pec]
                              ) {
                                appendDiff(
                                  race_from[item]["horse"][j][horse_in][
                                    horse_in_sub
                                  ][horse_in_sub_bit][horse_in_sub_bit_pec],
                                  race_to[item]["horse"][to_index][horse_in][
                                    horse_in_sub
                                  ][horse_in_sub_bit][horse_in_sub_bit_pec],
                                  dir,
                                  diffObject,
                                  "races." +
                                    raceno +
                                    ".horses." +
                                    h_cname +
                                    "." +
                                    horse_in +
                                    "." +
                                    horse_in_sub +
                                    "." +
                                    horse_in_sub_bit +
                                    "." +
                                    horse_in_sub_bit_pec
                                );
                              }
                            } catch (err) {
                              appendDiff(
                                race_from[item]["horse"][j][horse_in][
                                  horse_in_sub
                                ][horse_in_sub_bit][horse_in_sub_bit_pec],
                                "ERROR DIFFING: " + err,
                                dir,
                                diffObject,
                                "races." +
                                  raceno +
                                  ".horses." +
                                  h_cname +
                                  "." +
                                  horse_in +
                                  "." +
                                  horse_in_sub +
                                  "." +
                                  horse_in_sub_bit +
                                  "." +
                                  horse_in_sub_bit_pec
                              );
                            }
                          }
                        } else {
                          try {
                            if (
                              race_from[item]["horse"][j][horse_in][
                                horse_in_sub
                              ][horse_in_sub_bit] !=
                              race_to[item]["horse"][to_index][horse_in][horse_in_sub][
                                horse_in_sub_bit
                              ]
                            ) {
                              appendDiff(
                                race_from[item]["horse"][j][horse_in][
                                  horse_in_sub
                                ][horse_in_sub_bit],
                                race_to[item]["horse"][to_index][horse_in][
                                  horse_in_sub
                                ][horse_in_sub_bit],
                                dir,
                                diffObject,
                                "races." +
                                  raceno +
                                  ".horses." +
                                  h_cname +
                                  "." +
                                  horse_in +
                                  "." +
                                  horse_in_sub +
                                  "." +
                                  horse_in_sub_bit
                              );
                            }
                          } catch (err) {
                            appendDiff(
                              race_from[item]["horse"][j][horse_in][
                                horse_in_sub
                              ][horse_in_sub_bit],
                              "ERROR DIFFING: " + err,
                              dir,
                              diffObject,
                              "races." +
                                raceno +
                                ".horses." +
                                h_cname +
                                "." +
                                horse_in +
                                "." +
                                horse_in_sub +
                                "." +
                                horse_in_sub_bit
                            );
                          }
                        }
                      }
                    } else {
                      try {
                        if (
                          race_from[item]["horse"][j][horse_in][horse_in_sub] !=
                          race_to[item]["horse"][to_index][horse_in][horse_in_sub]
                        ) {
                          appendDiff(
                            race_from[item]["horse"][j][horse_in][horse_in_sub],
                            race_to[item]["horse"][to_index][horse_in][horse_in_sub],
                            dir,
                            diffObject,
                            "races." +
                              raceno +
                              ".horses." +
                              h_cname +
                              "." +
                              horse_in +
                              "." +
                              horse_in_sub
                          );
                        }
                      } catch (err) {
                        appendDiff(
                          race_from[item]["horse"][j][horse_in][horse_in_sub],
                          "ERROR DIFFING: " + err,
                          dir,
                          diffObject,
                          "races." +
                            raceno +
                            ".horses." +
                            h_cname +
                            "." +
                            horse_in +
                            "." +
                            horse_in_sub
                        );
                      }
                    }
                  }
                } else {
                  try {
                    if (
                      race_from[item]["horse"][j][horse_in] !=
                      race_to[item]["horse"][to_index][horse_in]
                    ) {
                      appendDiff(
                        race_from[item]["horse"][j][horse_in],
                        race_to[item]["horse"][to_index][horse_in],
                        dir,
                        diffObject,
                        "races." +
                          raceno +
                          ".horses." +
                          h_cname +
                          "." +
                          horse_in
                      );
                    }
                  } catch (err) {
                    appendDiff(
                      race_from[item]["horse"][j][horse_in],
                      "ERROR DIFFING: " + err,
                      dir,
                      diffObject,
                      "races." + raceno + ".horses." + h_cname + "." + horse_in
                    );
                  }
                }
              }
            }
          }
        } else if (item == "prizes") {
          if (race_from[item]["prize"]) {
            if (race_from[item]["prize"][0]) {
              for (var j = 0; j < race_from[item]["prize"].length; j++) {
                var prize = race_from[item]["prize"][j];
                for (const prize_in in prize) {
                  try {
                    if (
                      race_from[item]["prize"][j][prize_in] !=
                      race_to[item]["prize"][j][prize_in]
                    ) {
                      appendDiff(
                        race_from[item]["prize"][j],
                        race_to[item]["prize"][j],
                        dir,
                        diffObject,
                        "races." + raceno + "." + item + "." + j.toString()
                      );
                    }
                  } catch (err) {
                    appendDiff(
                      race_from[item]["prize"][j],
                      "ERROR DIFFING: " + err,
                      dir,
                      diffObject,
                      "races." + raceno + "." + item + "." + j.toString()
                    );
                  }
                }
              }
            } else {
              try {
                if (race_from[item]["prize"] != race_to[item]["prize"]) {
                  appendDiff(
                    race_from[item]["prize"],
                    race_to[item]["prize"],
                    dir,
                    diffObject,
                    "races." + raceno + "." + item
                  );
                }
              } catch (err) {
                appendDiff(
                  race_from[item]["prize"],
                  "ERROR DIFFING: " + err,
                  dir,
                  diffObject,
                  "races." + raceno + "." + item + "." + j.toString()
                );
              }
            }
          } else {
            if (race_to[item]["prize"]) {
              appendDiff(
                "",
                race_to[item]["prize"],
                dir,
                diffObject,
                "races." + raceno + "." + item
              );
            }
          }
        } else if (
          [
            "track",
            "distance",
            "restrictions",
            "classes",
            "prizes",
            "sectional",
          ].includes(item)
        ) {
          for (const initem in race_from[item]) {
            if (["errors", "records"].includes(item)) continue;
            try {
              if (race_from[item][initem] != race_to[item][initem]) {
                appendDiff(
                  race_from[item][initem],
                  race_to[item][initem],
                  dir,
                  diffObject,
                  "races." + raceno + "." + item + "." + initem
                );
              }
            } catch (err) {
              appendDiff(
                race_from[item][initem],
                "ERROR DIFFING: " + err,
                dir,
                diffObject,
                "races." + raceno + "." + item + "." + initem
              );
            }
          }
        } else {
          try {
            if (race_from[item] != race_to[item]) {
              appendDiff(
                race_from[item],
                race_to[item],
                dir,
                diffObject,
                "races." + raceno + "." + item
              );
            }
          } catch (err) {
            appendDiff(
              race_from[item],
              "ERROR DIFFING: " + err,
              dir,
              diffObject,
              "races." + raceno + "." + item
            );
          }
        }
      }
      var race_sort = {};
      if (diffObject.races) {
        Object.keys(diffObject.races)
          .sort()
          .forEach(function (v, i) {
            race_sort[v] = diffObject.races[v];
            // console.log(v, data[v]);
          });
        delete diffObject.races;
        diffObject.races = race_sort;
      }
    }
    console.log('compare done')
  } catch (err) {
    console.log("ERROR IN COMAPRE: " + err);
  }
  return diffObject;
};

const compareStats = (stats1, stats2, dir, diffObject, pathfull) => {
  if (!Array.isArray(stats1)) stats1 = [stats1];
  if (!Array.isArray(stats2)) stats2 = [stats2];
  for (var i = 0; i < stats1.length; i++) {
    if (stats1[i]["@_type"].includes("progeny")) continue;
    for (const sub in stats1[i]) {
      try {
        if (stats1[i][sub] !== stats2[i][sub]) {
          console.log(
            `${pathfull}.${stats1[i]["@_type"]}.${sub}`,
            stats1[i][sub],
            stats2[i][sub]
          );
          appendDiff(
            stats1[i][sub],
            stats2[i][sub],
            dir,
            diffObject,
            `${pathfull}.${stats1[i]["@_type"]}.${sub}`
          );
        }
      } catch (err) {
        console.log("error with statscompare:" + err);
        console.log(stats1[i][sub], stats2[i][sub]);
        appendDiff(
          stats1[i][sub],
          "ERROR DIFFING: " + err,
          dir,
          diffObject,
          `${pathfull}.${stats1[i]["@_type"]}.${sub}`
        );
      }
    }
  }
};

const appendDiff = (item1, item2, dir, diffObj, pathfull) => {
  if (
    item1 &&
    item1.toString().includes("ERROR DIFFING:")
  )
    item1 = "";
  if (
    item2 &&
    item2.toString().includes("ERROR DIFFING:")
  )
    item2 = "";
  if (!item1 && !item2) return diffObj;
  var path = pathfull.split(".");
  if (dir == 1) {
    i1 = item1;
    i2 = item2;
  } else {
    i1 = item2;
    i2 = item1;
  }
  if (path[0]) {
    if (!diffObj[path[0]]) {
      diffObj[path[0]] = {};
    }
    if (path[1]) {
      if (!diffObj[path[0]][path[1]]) {
        diffObj[path[0]][path[1]] = {};
      }
      if (path[2]) {
        if (!diffObj[path[0]][path[1]][path[2]]) {
          diffObj[path[0]][path[1]][path[2]] = {};
        }
        if (path[3]) {
          if (!diffObj[path[0]][path[1]][path[2]][path[3]]) {
            diffObj[path[0]][path[1]][path[2]][path[3]] = {};
          }
          if (path[4]) {
            if (!diffObj[path[0]][path[1]][path[2]][path[3]][path[4]]) {
              diffObj[path[0]][path[1]][path[2]][path[3]][path[4]] = {};
            }
            if (path[5]) {
              if (
                !diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]]
              ) {
                diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]] =
                  {};
              }
              if (path[6]) {
                if (
                  !diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][
                    path[5]
                  ][path[6]]
                ) {
                  diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]][
                    path[6]
                  ] = {};
                }
                if (path[7]) {
                  if (
                    !diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][
                      path[5]
                    ][path[6]][path[7]]
                  ) {
                    diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][
                      path[5]
                    ][path[6]][path[7]] = {};
                  }
                  if (path[8]) {
                    if (
                      !diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][
                        path[5]
                      ][path[6]][path[7]][path[8]]
                    ) {
                      diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][
                        path[5]
                      ][path[6]][path[7]][path[8]] = {};
                    }
                    diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][
                      path[5]
                    ][path[6]][path[7]][path[8]] = [i1, i2];
                  } else {
                    diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][
                      path[5]
                    ][path[6]][path[7]] = [i1, i2];
                  }
                } else {
                  diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]][
                    path[6]
                  ] = [i1, i2];
                }
              } else {
                diffObj[path[0]][path[1]][path[2]][path[3]][path[4]][path[5]] =
                  [i1, i2];
              }
            } else {
              diffObj[path[0]][path[1]][path[2]][path[3]][path[4]] = [i1, i2];
            }
          } else {
            diffObj[path[0]][path[1]][path[2]][path[3]] = [i1, i2];
          }
        } else {
          diffObj[path[0]][path[1]][path[2]] = [i1, i2];
        }
      } else {
        diffObj[path[0]][path[1]] = [i1, i2];
      }
    } else {
      diffObj[path[0]] = [i1, i2];
    }
  }
  return diffObj;
};

const genDiffSummary = (diffObj) => {
  diffObj.notes = "";
  if (!diffObj.meeting && !diffObj.races) {
    diffObj.notes = "No changes.";
    return diffObj;
  }
  if (diffObj.meeting) diffObj.notes += "MEETING ";
  if (diffObj.races) {
    var racenames = 0;
    var racetimes = 0;
    var jockeys = 0;
    var scratchings = 0;
    var markets = 0;
    var comment = 0;
    for (const the_race in diffObj.races) {
      race = diffObj.races[the_race];
      if (race["@_ra_name"]) racenames++;
      if (race.start_time) racetimes++;
      if (race.horses) {
        for (const the_horse in race.horses) {
          horse = race.horses[the_horse];
          if (horse.jockey && !horse.scratched) jockeys++;
          if (horse.scratched) scratchings++;
          if (horse.betting) markets++;
          if (horse.comment) comment++;
        }
      }
    }
    diffObj.count = {};
    if (racenames > 0) {
      diffObj.notes += "RACENAMES ";
      diffObj.count.racenames = racenames;
    }
    if (racetimes > 0) {
      diffObj.notes += "RACETIMES ";
      diffObj.count.racetimes = racetimes;
    }
    if (jockeys > 1) {
      diffObj.notes += "JOCKEYS ";
      diffObj.count.jockeys = jockeys;
    }
    if (scratchings > 1) {
      diffObj.notes += "SCRATCHINGS ";
      diffObj.count.scratchings = scratchings;
    }
    if (markets > 0) {
      diffObj.notes += "MARKETS ";
      diffObj.count.markets = markets;
    }
    if (comment > 0) {
      diffObj.notes += "COMMENT ";
      diffObj.count.comment = comment;
    }
  }
  return diffObj;
};

const genMeetingCompleteness = (meeting) => {
  var races = meeting.races.race;

  var horsetotal = 0,
    jockeyfill = 0,
    markets = 0,
    tips = 0,
    scratched = 0,
    comment = 0;
  var tipstotal = races.length * 4;

  for (var race of races) {
    for (var horse of race.horses.horse) {
      if (horse.scratched) {
        scratched++;
      } else {
        horsetotal++;
        if (horse.jockey && horse.jockey["@_id"]) {
          jockeyfill++;
        }
        if (horse.comment) {
          comment++;
        }
        if (horse.tip) {
          tips++;
        }
        if (horse.betting) {
          markets++;
        }
      }
    }
  }

  return (completionsummary = {
    J: [jockeyfill, horsetotal],
    M: [markets, horsetotal],
    C: [comment, horsetotal],
    T: [tips, tipstotal],
    S: scratched,
  });
};

const checkStatsAndValidity = async (meetingId) => {
  const MAX_ITERATIONS = 10;
  const SLEEP_DURATION = 20000;

  for (let count = 1; count <= MAX_ITERATIONS; count++) {
    console.log(count);
    await sleep(SLEEP_DURATION);

    try {
      const meetingData = await centaur.temp_meetings
        .findOne({ _id: meetingId })
        .lean();

      // Check if the number of races in processedMeetingData matches inputMeetingData
      // if (
      //   meetingData.processedMeetingData.meeting.races.race.length !==
      //   meetingData.inputMeetingData.races.race.length
      // ) {
      //   await errorMeeting(
      //     meetingId,
      //     "ERROR: The number of races in the inputData doesn't match this new file, load was aborted. Please investigate the received file."
      //   );
      //   return false;
      // }

      const eventIdSet = new Set();

      for (const raceItem of meetingData.processedMeetingData.meeting.races
        .race) {
        // Check for duplicate "@_id" values
        if (eventIdSet.has(raceItem["@_id"])) {
          await errorMeeting(
            meetingId,
            "ERROR: Major issues with nomination numbers, load was aborted. Please investigate the received file."
          );
          return false;
        }
        eventIdSet.add(raceItem["@_id"]);

        // Check if the last horse in the race has valid statistics
        // const lastHorse = raceItem.horses?.horse?.slice(-1)?.[0];
        // if (!lastHorse?.statistics?.statistic?.[0]) {
        //   return false;
        // }
      }

      return true;
    } catch (error) {
      console.error("An error occurred:", error);
      await errorMeeting(
        meetingId,
        "Something went wrong while processing the meeting data, load was aborted."
      );
      return false;
    }
  }

  await errorMeeting(
    meetingId,
    "Timed out while generating statistics, load was aborted. Try a reload if the meeting exists."
  );
  return false;
};

const errorMeeting = async (
  meetingId,
  error = "postprocess couldnt finish, perhaps stats never completed."
) => {
  console.log(`error and delete: ${meetingId} ${error}`);
  let errorarraypm = await centaur.temp_meetings
    .findOne({ _id: meetingId })
    .lean();
  errorarraypm.processedMeetingData.meeting.errors.unshift(error);
  errorarraypm.meetingLocked = "unlocked";
  errorarraypm.meetingErrorCount = errorarraypm.meetingErrorCount++;
  var lastfile = {
    time: moment.utc().format(),
    bucket: errorarraypm.meetingLoadHistory[0].bucket,
    file_path: errorarraypm.meetingLoadHistory[0].file_path,
    trigger: "POSTLOAD FAIL",
  };
  let checkChangeLog = await centaur.changelog
    .findOne({ meeting_id: meetingId })
    .lean();
  if (checkChangeLog) {
    const upm = await centaur.processed_meetings
      .findOne({ _id: meetingId })
      .select("meetingLoadHistory")
      .lean();
    var logs = checkChangeLog.changelog;
    logs.unshift(lastfile);
    if (
      upm &&
      upm.meetingLoadHistory &&
      upm.meetingLoadHistory.length > errorarraypm.meetingLoadHistory.length
    ) {
      for (var i = 0; i < upm.meetingLoadHistory.length; i++) {
        if (
          upm.meetingLoadHistory[i].time ===
          errorarraypm.meetingLoadHistory[0].time
        )
          break;
        logs.unshift(upm.meetingLoadHistory[i]);
        errors.push(`${upm.meetingLoadHistory[i].file_path} Failed to load`);
      }
    }
    var dataToUpdate = {
      changelog: logs,
    };
    let updateChangeLog = await centaur.changelog
      .updateOne(
        { _id: checkChangeLog._id },
        {
          $set: dataToUpdate,
        }
      )
      .lean();
    console.log(updateChangeLog);
  }

  var checkmeet = await centaur.processed_meetings
    .findOne({ _id: meetingId })
    .lean();
  if (checkmeet && checkmeet.meetingStage != "DELETED") {
    let errorArray = errorarraypm.processedMeetingData.meeting.errors;
    let me = await centaur.processed_meetings
      .updateOne(
        { _id: meetingId },
        {
          $set: {
            "processedMeetingData.meeting.errors": errorArray,
            meetingLocked: "unlocked",
            meetingErrorCount: errorarraypm.meetingErrorCount,
          },
        }
      )
      .lean();
    console.log;
  } else if (!checkmeet) {
    // var sendMail = await helper.mailAlert('Initial file not loaded!',`File ${errorarraypm.meetingLoadHistory[0].file_path} wasnt able to load, and failed in postprocess with error: ${error}`,'alert')
    // console.log(sendMail)
    var newRecord = new centaur.processed_meetings(errorarraypm);
    var d = await newRecord.save();
    console.log(d);
  } else {
    if (checkmeet && checkmeet.meetingStage == "DELETED")
      var dpm = await centaur.processed_meetings
        .deleteOne({ _id: meetingId })
        .lean();
    var newRecord = new centaur.processed_meetings(errorarraypm);
    var d = await newRecord.save();
    console.log(d);
  }

  let deletetemp = await centaur.temp_meetings
    .deleteOne({ _id: meetingId })
    .lean();
  console.log(deletetemp);
};

const sleep = async (miliseconds) => {
  var currentTime = new Date().getTime();

  while (currentTime + miliseconds >= new Date().getTime()) {}
};

const saveAndDistribute = async (
  meetingId,
  errors,
  distribute = false,
  files = "FIELDS,FORM",
  raceNo = 0
) => {
  // return "failure";
  var tm = await centaur.temp_meetings.findOne({ _id: meetingId }).lean();
  const dpm = await centaur.processed_meetings
    .deleteOne({ _id: meetingId })
    .lean();
  tm.meetingLocked = "unlocked";
  var i = 1;
  for (race of tm.processedMeetingData.meeting.races.race) {
    if (!race["@_id"]) {
      errors.push(`Race ${i} is missing`);
    }
    i++;
  }

  if (errors.length > 0) {
    tm.meetingErrorCount = tm.meetingErrorCount + errors.length;
    tm.processedMeetingData.meeting.errors = errors.concat(
      tm.processedMeetingData.meeting.errors
    );
    tm.validated = false;
  }
  if (tm.meetingErrorCount > 0) {
    distribute = false;
  }
  // console.log('saving and distributing mid')
  // TURNING OFF ALL AUTO DISTRIBUTION
  if (
    (!distribute && files != "") ||
    (tm.AcceptanceFileCounter == 1 && files == "FIELDS,FORM,SCRATCHINGS")
  ) {
    // if (!distribute && files != '') {
    tm.validated = false;
    distribute = false;
  }
  tm.updatedAt = moment.utc().format();
  tm.createdAt = moment.utc().format();
  console.log(tm);
  var newRecord = new centaur.processed_meetings(tm);
  var d = await newRecord.save();
  console.log(d);
  const dtm = await centaur.temp_meetings.deleteOne({ _id: meetingId }).lean();
  if (files === "RESULTS") {
    var theResult = await results.genResultsByMeetingId(meetingId);
    console.log(theResult);
    console.log(`form approved for ${meetingId} race ${raceNo}`);
  }

  if (distribute) {
    const AWS = require("aws-sdk");

    var lambda = new AWS.Lambda();
    var payload = {
      queryStringParameters: {
        id: meetingId,
        race_no: raceNo,
        if_validated: true,
        files: files,
      },
    };

    var params = {
      FunctionName: "MrCenLambdaDeliveryFunction-" + process.env.ENV,
      InvocationType: "Event",
      Payload: JSON.stringify(payload),
    };
    var res_3 = await lambda.invoke(params).promise();
    console.log(res_3);
    console.log(`results distributed with raceNo ${raceNo}`);
  }
  return "success";
};

const saveOnly = async (meetingId, user, deleteTemp = true) => {
  console.log("saveOnly function");
  let con = await helper.openDBConnection(process.env.ENV);
  try{
    const pm = await centaur.processed_meetings
      .findOne({ _id: meetingId })
      .select("processedMeetingData.meeting")
      .lean();
    if (!pm) return "ERROR: processed meeting not found";
    const tm = await centaur.temp_meetings
      .findOne({ _id: meetingId })
      .select("processedMeetingData.meeting meetingLoadHistory")
      .lean();
    if (!tm) return "ERROR: temp meeting not found";
    var diffObject = {};
    var dir = deleteTemp ? 0 : 1;
    meet1 = pm.processedMeetingData.meeting;
    meet2 = tm.processedMeetingData.meeting;
    var errors = [];
    diffObject = compareTheMeeting(meet1, meet2, dir, diffObject);
    dir++;
    diffObject = compareTheMeeting(meet2, meet1, dir, diffObject);
    errors = diffObject.errors
    diffObject = genDiffSummary(diffObject);
    diffObject.summary = genMeetingCompleteness(meet1);

    let checkChangeLog = await centaur.changelog
      .findOne({ meeting_id: meetingId })
      .lean();
    var i = 0;
    const upm = await centaur.processed_meetings
      .findOne({ _id: meetingId })
      .select("meetingLoadHistory")
      .lean();
    if (checkChangeLog) {
      var logs = checkChangeLog.changelog;
      logs.unshift({
        time: moment.utc().format(),
        bucket: "USER EDITS",
        file_path: user,
        trigger: "USER EDITS",
        changes: diffObject,
      });
      if (
        upm.meetingLoadHistory &&
        upm.meetingLoadHistory.length > tm.meetingLoadHistory.length
      ) {
        for (i; i < upm.meetingLoadHistory.length; i++) {
          if (upm.meetingLoadHistory[i].time === tm.meetingLoadHistory[0].time)
            break;
          logs.unshift(upm.meetingLoadHistory[i]);
          errors.push(`${upm.meetingLoadHistory[i].file_path} Failed to load`);
        }
      }
      var dataToUpdate = {
        changelog: logs,
      };
      let updateChangeLog = await centaur.changelog
        .updateOne(
          { _id: checkChangeLog._id },
          {
            $set: dataToUpdate,
          }
        )
        .lean();
      console.log(updateChangeLog);
    } else {
      var logs = [];
      if (tm.meetingLoadHistory && tm.meetingLoadHistory.length > 0) {
        logs = tm.meetingLoadHistory;
      } else {
        console.log("Error: couldnt find the load item for these changes!!");
      }
      logs.unshift({
        time: moment.utc().format(),
        bucket: "USER EDITS",
        file_path: user,
        trigger: "USER EDITS",
        changes: diffObject,
      });
      if (
        upm.meetingLoadHistory &&
        upm.meetingLoadHistory.length > tm.meetingLoadHistory.length
      ) {
        for (i; i < upm.meetingLoadHistory.length; i++) {
          if (
            upm.meetingLoadHistory[i].time.toString() ===
            tm.meetingLoadHistory[0].time.toString()
          )
            break;
          logs.unshift(upm.meetingLoadHistory[i]);
          errors.push(`${upm.meetingLoadHistory[i].file_path} Failed to load`);
        }
      }
      var id = uuid.v4();
      var dataToCreate = {
        _id: id,
        meeting_id: meetingId,
        changelog: logs,
      };

      var newRecord = new centaur.changelog(dataToCreate);
      var d = await newRecord.save();
      console.log(d);
    }
    console.log("moving to delete temp meeting and update meeting");
    const dtm = await centaur.temp_meetings.deleteOne({ _id: meetingId }).lean();

    var dataToUpdate = {};
    dataToUpdate["meetingLocked"] = "unlocked";
    dataToUpdate["validated"] = false;
    dataToUpdate["updatedAt"] = moment.utc().format();
    dataToUpdate["createdAt"] = moment.utc().format();

    if (errors.length > 0) {
      var npm = await centaur.processed_meetings
        .findOne({ _id: meetingId })
        .select("processedMeetingData.meeting meetingErrorCount")
        .lean();
      dataToUpdate["meetingErrorCount"] = npm.meetingErrorCount + errors.length;
      dataToUpdate["processedMeetingData.meeting.errors"] = errors.concat(
        npm.processedMeetingData.meeting.errors
      );
    }
    var fpm = await centaur.processed_meetings
      .updateOne(
        { _id: meetingId },
        {
          $set: dataToUpdate,
        }
      )
      .lean();

    return { data: "meeting changes saved reverted" };
  } catch (error) {
    if (con) await helper.closeDBConnection(con);
    console.error(`Error in compareMeetingAPI: ${error.message}`);
    throw error;
  } finally {
    if (con) await helper.closeDBConnection(con);
  }
};

const cancelEdit = async (meetingId) => {
  let con = await helper.openDBConnection(process.env.ENV);
  try{
    let checkChangeLog = await centaur.changelog
      .findOne({ meeting_id: meetingId })
      .lean();
    var i = 0;
    const upm = await centaur.processed_meetings
      .findOne({ _id: meetingId })
      .select("meetingLoadHistory")
      .lean();
    var tm = await centaur.temp_meetings.findOne({ _id: meetingId }).lean();
    var logs = [];
    var errors = [];
    if (checkChangeLog) {
      logs = checkChangeLog.changelog;
      if (
        upm.meetingLoadHistory &&
        upm.meetingLoadHistory.length > tm.meetingLoadHistory.length
      ) {
        for (i; i < upm.meetingLoadHistory.length; i++) {
          if (upm.meetingLoadHistory[i].time === tm.meetingLoadHistory[0].time)
            break;
          logs.unshift(upm.meetingLoadHistory[i]);
          errors.push(`${upm.meetingLoadHistory[i].file_path} Failed to load`);
        }
      }
      var dataToUpdate = {
        changelog: logs,
      };
      let updateChangeLog = await centaur.changelog
        .updateOne(
          { _id: checkChangeLog._id },
          {
            $set: dataToUpdate,
          }
        )
        .lean();
      console.log(updateChangeLog);
    } else {
      logs = [];
      if (upm.meetingLoadHistory && upm.meetingLoadHistory.length > 0) {
        logs = upm.meetingLoadHistory;
      } else {
        console.log("Error: couldnt find the load item for these changes!!");
      }
      if (
        upm.meetingLoadHistory &&
        upm.meetingLoadHistory.length > tm.meetingLoadHistory.length
      ) {
        for (i; i < upm.meetingLoadHistory.length; i++) {
          if (upm.meetingLoadHistory[i].time === tm.meetingLoadHistory[0].time)
            break;
          logs.unshift(upm.meetingLoadHistory[i]);
          errors.push(`${upm.meetingLoadHistory[i].file_path} Failed to load`);
        }
      }
      var id = uuid.v4();
      var dataToCreate = {
        _id: id,
        meeting_id: meetingId,
        changelog: logs,
      };

      var newRecord = new centaur.changelog(dataToCreate);
      var d = await newRecord.save();
      console.log(d);
    }

    const dpm = await centaur.processed_meetings
      .deleteOne({ _id: meetingId })
      .lean();

    tm.meetingLocked = "unlocked";
    if (errors.length > 0) {
      tm.meetingErrorCount = tm.meetingErrorCount + errors.length;
      tm.processedMeetingData.meeting.errors = errors.concat(
        tm.processedMeetingData.meeting.errors
      );
      tm.validated = false;
    }
    tm.updatedAt = moment.utc().format();
    tm.createdAt = moment.utc().format();
    console.log(tm);
    var newRecord = new centaur.processed_meetings(tm);
    var d = await newRecord.save();
    console.log(d);
    const dtm = await centaur.temp_meetings.deleteOne({ _id: meetingId }).lean();

    return { data: "meeting reverted" };
  } catch (error) {
    if (con) await helper.closeDBConnection(con);
    console.error(`Error in compareMeetingAPI: ${error.message}`);
    throw error;
  } finally {
    if (con) await helper.closeDBConnection(con);
  }
};

const genMeetingErrorCount = async (meetingId) => {
  console.log("genErrorId:", meetingId);
  const tempMeeting = await centaur.temp_meetings.findOne({ _id: meetingId }).lean();
  let dataToUpdate = {};

  if (!tempMeeting) {
    console.log("Meeting not found");
    return false;
  }

  let raceIndex = 0;
  let meetingErrorCount = 0;

  // Ensure processedMeetingData and its nested properties exist
  if (!tempMeeting.processedMeetingData?.meeting?.races?.race) {
    console.log("Invalid meeting data structure");
    return false;
  }

  for (const race of tempMeeting.processedMeetingData.meeting.races.race) {
    let horseIndex = 0;
    let raceErrorCount = 0;
    const inputRaceIndex = tempMeeting.inputMeetingData.races.race.findIndex((element) => element["@_id"] === race["@_id"]);
    // Ensure race.horses exists and is an array
    if (!Array.isArray(race.horses?.horse)) {
      console.log(`Invalid race data structure for race index ${raceIndex}`);
      continue;
    }

    for (const horse of race.horses.horse) {
      const inputHorseIndex = tempMeeting.inputMeetingData.races.race[inputRaceIndex].horses.horse.findIndex((element) => element["@_id"] === horse["@_id"]);
      if (Array.isArray(horse.errors) && horse.errors.length > 0) {
        raceErrorCount += horse.errors.length;
        meetingErrorCount += horse.errors.length;

        
          dataToUpdate[`inputMeetingData.races.race.${inputRaceIndex}.horses.horse.${inputHorseIndex}.errors`] = horse.errors;
        
      }

      if (Array.isArray(horse.error_log) && horse.error_log.length > 0) {
        dataToUpdate[`inputMeetingData.races.race.${inputRaceIndex}.horses.horse.${inputHorseIndex}.error_log`] = horse.error_log;
      }

      horseIndex++;
    }

    let raceErrors = Array.isArray(race.errors) ? race.errors : [];

    if (raceErrors.length > 0) {
      let horseCount = false;
      for (let i = 0; i < raceErrors.length; i++) {
        if (raceErrors[i].includes("Horse Error Count:")) {
          horseCount = true;
          console.log('pre-race errors:', raceErrors);
          if (raceErrorCount > 0) {
            console.log(raceErrorCount);
            raceErrors[i] = `Horse Error Count: ${raceErrorCount}`;
          } else {
            raceErrors.splice(i, 1);
            i--;
          }
          console.log('post-race errors:', raceErrors);
          break;
        }
      }
      if (!horseCount && raceErrorCount > 0) {
        raceErrors.unshift(`Horse Error Count: ${raceErrorCount}`);
      }
    } else if (raceErrorCount > 0) {
      raceErrors.push(`Horse Error Count: ${raceErrorCount}`);
    }

    meetingErrorCount += raceErrors.length;
    dataToUpdate[`inputMeetingData.races.race.${raceIndex}.errors`] = raceErrors;
    dataToUpdate[`processedMeetingData.meeting.races.race.${raceIndex}.errors`] = raceErrors;
    raceIndex++;
  }

  dataToUpdate["meetingErrorCount"] = meetingErrorCount;
  console.log('about to update');

  try {
    let updatedTemp = await centaur.temp_meetings.updateOne(
      { _id: meetingId },
      { $set: dataToUpdate }
    ).lean();
    console.log('db updated');
    console.log(updatedTemp);
  } catch (error) {
    console.error('Error updating database:', error);
    return false;
  }

  return true;
};
