const moment = require('moment')
const helper = require("./helper")
const uuid = require('uuid');
// Import Models
const centaur = require('@mediality/centaur')


const genResultsByMeetingId = async (meetingId) => {
    try{
        var data = await centaur.processed_meetings.findOne({ _id : meetingId }).lean()
        var result = await createUpdateMeetingForm(data)
        return result
    } catch(error){
        console.log(error)
        return false
    }

}

const genResultsByRace = async (meetingId,raceIndex) => {
    console.log('generating results for meeting '+meetingId+' race number '+raceIndex)
    try{
        var data = await centaur.processed_meetings.findOne({ _id : meetingId }).lean()
       
        var result = await createUpdateMeetingForm(data,raceIndex)
        
        return result
    } catch(error){
        console.log(error)
        return false
    }

}

const createIndex = async (indexData) => {
    console.log(indexData)
    try{
        var checkifexists = await centaur.form_index.find()
            .where("track_id").equals(indexData.track_id)
            .where("meeting_date").equals(indexData.meeting_date)
            .where("horse_id").equals(indexData.horse_id)
            .where("race_no").equals(indexData.race_no)

        if (checkifexists[0]){
            console.log('itexists')
            var updateIndex = await centaur.form_index.updateOne({ _id:checkifexists[0]._id}, {
                "$set": indexData
            }).lean()
            console.log(updateIndex)
        } else {
            console.log('making new index')
            var id = uuid.v4()
            var dataToCreate = {_id: id}
            let newIndex = new centaur.form_index(dataToCreate)
            var status = await newIndex.save();
            status = await centaur.form_index.updateOne({ _id: id}, {
                "$set": indexData
            }).lean()
            console.log(status)
        }
        return true
    } catch(err){
        console.log('Error updating form_index: ', err)
        return false
    }

}

const updateHorseResults = async (horseFormId,blinkerInd = "") => {
    //return
    try{
        var data = await centaur.form.find({ _id: horseFormId }).lean()
        if (data[0]){
            var cleansedResults = helper.cleanseBarrierTrials(data[0].form)

            console.log('Results for : ' + data[0].horse_name + " - " +  data[0].horse_id)

            var ts = 0, s1 = 0, s2 = 0, s3 = 0, s4 = 0, s5 = 0
            var dt = 0, d1 = 0, d2 = 0, d3 = 0
            var wt = 0, w1 = 0, w2 = 0, w3 = 0

            for (run of cleansedResults){
                var wet = false
                ts++
                if (['H','D','O','L','S','R','V','Dead','Soft','Slow','Heavy','Wet'].includes(run.track['@_condition'])){
                    wt++
                    wet = true
                } else {
                    dt++
                }
                if (run.finish_position == 1){
                    s1++
                    if (wet){w1++} else {d1++}
                } else if (run.finish_position == 2){
                    s2++
                    if (wet){w2++} else {d2++}
                } else if (run.finish_position == 3){
                    s3++
                    if (wet){w3++} else {d3++}
                } else if (run.finish_position == 4){
                    s4++
                } else if (run.finish_position == 5){
                    s5++
                }

            }

            var dataToUpdate = {
                HOR_START_TOTAL: ts,
                HOR_H_1STS_TOTAL: s1,
                HOR_H_2NDS_TOTAL: s2,
                HOR_H_3RDS_TOTAL: s3,
                HOR_H_4THS_TOTAL: s4,
                HOR_H_5THS_TOTAL: s5,
                HOR_DRY_START_TOTAL: dt,
                HOR_DRY_1ST_TOTAL: d1,
                HOR_DRY_2ND_TOTAL: d2,
                HOR_DRY_3RD_TOTAL: d3,
                HOR_WET_START_TOTAL: wt,
                HOR_WET_1ST_TOTAL: w1,
                HOR_WET_2ND_TOTAL: w2,
                HOR_WET_3RD_TOTAL: w3,
                HOR_CURRENT_BLINKER_IND: blinkerInd
            }

        }

        let status = await centaur.horses.updateOne({ HRN_HORSE_ID: data[0].horse_id }, {
            "$set": dataToUpdate
        }).lean()
        console.log(`Horse ${data[0].horse_id} runs Updated: `)
        console.log(status)
        if (status) {
            return true
        } else {
            return false
        }
    } catch(err){
        console.log('Error updating horse runs: ', err)
        return false
    }
}


const createUpdateMeetingForm = async (rawData, raceIndex = -1) => {
    console.log('crudmf');
    // console.log(rawData, eventId)
    var formUpdateSuccess = true;
    var meeting = rawData.processedMeetingData.meeting;
    var meetingData = {
        "meetingDate": rawData.meetingDate ? rawData.meetingDate : '',
        "_id": rawData._id ? rawData._id : '',
        "rail_position": meeting.rail_position ? meeting.rail_position : '',
        "night_meeting": meeting.track['@_night_meeting'] ?? ''
    };

    for (var i = 0; i < meeting.races.race.length; i++) {
        var event = meeting.races.race[i];

        if (((raceIndex > -1) && (raceIndex != i)) || !event.results_approved) {
            console.log('couldnt find the right event');
            console.log(event.results_approved);
            continue;
        }
        if (!event.duration || event.duration == "0:00.00" || event.duration == "00:00:00.000") {
            console.log("duration not found, aborting form from race " + event['@_number']);
            break;
        }
        if (!(event.race_stage.toLowerCase().includes('results'))) {
            console.log('race not at results');
            console.log(event.race_stage);
            continue;
        }
        console.log('found a race i can actually make!');

        var prizeMoney = 0;
        if (event.prizes && event.prizes.prize) {
            for (var j = 0; j < event.prizes.prize.length; j++) {
                if (['total_value', 'trophy_total_value'].includes(event.prizes.prize[j]['@_type'])) {
                    prizeMoney += parseInt(event.prizes.prize[j]['@_value']);
                }
            }
        }

        var other_runner = [];
        for (var j = 0; j < event.horses.horse.length; j++) {
            if ([1, 2, 3, 4, "1", "2", "3", "4"].includes(event.horses.horse[j].finish_position)) {
                var themargin;
                if ([1, 2, "1", "2"].includes(event.horses.horse[j].finish_position)) {
                    themargin = event.official_margin_1;
                } else if ([3, "3"].includes(event.horses.horse[j].finish_position)) {
                    themargin = event.official_margin_2;
                } else {
                    themargin = event.horses.horse[j]['margin'];
                }
                other_runner.push({
                    "@_position": event.horses.horse[j].finish_position || 0,
                    "@_horse": event.horses.horse[j]['@_name'] || '',
                    "@_jockey": event.horses.horse[j].jockey ? event.horses.horse[j].jockey['@_name'] : '',
                    "@_country": event.horses.horse[j]['@_country'] || '',
                    "@_weight": event.horses.horse[j].weight_carried || 0,
                    "@_barrier": event.horses.horse[j]['barrier'] || '',
                    "@_margin": themargin || ''
                });
            }
        }
        // Sort the other runners by position
        other_runner.sort((a, b) => parseFloat(a['@_position']) - parseFloat(b['@_position']));

        if (event.restrictions && event.restrictions['@_jockey']) {
            event.restrictions['@_jockey'] = saveJockeyRestrictions(event.restrictions['@_jockey']);
        }
        if (event.restrictions && event.restrictions['@_sex']) {
            event.restrictions['@_sex'] = setSexRestriction(event.restrictions['@_sex']);
        }
        if (event.restrictions && event.restrictions['@_age']) {
            event.restrictions['@_age'] = setAgeRestriction(event.restrictions['@_age']);
        }

        var theSectional = { '@_time': '', '@_distance': 0, '@_location': "" };
        if (event.sectional && event.sectional['@_time']) {
            theSectional = event.sectional;
        }

        let exCon = null;
        console.log('--------------');
        console.log(event.track['@_expected_condition']);

        if (event.track_condition) {
            if (event.track_condition.includes("(")) {
                // Parsing logic for conditions like "Good (4)"
                console.log(event.track_condition.split("("));
                var exConSplit = event.track_condition.split("(");
                var grading = exConSplit[1].slice(0, -1);
                var condition = '';
                switch (exConSplit[0]) {
                    case "Firm":
                        condition = "M";
                        break;
                    case "Good":
                        condition = "G";
                        break;
                    case "Soft":
                        condition = "O";
                        break;
                    case "Heavy":
                        condition = "H";
                        break;
                    case "Synthetic":
                        condition = "Y";
                        break;
                    case "Dirt":
                        condition = "Dirt";
                        break;
                    case "Sand":
                        condition = "Sand";
                        break;
                    case "Fast":
                        condition = "Fast";
                        break;
                    case "Dead":
                        condition = "Dead";
                        break;
                    case "Slow":
                        condition = "Slow";
                        break;
                    case "Wet":
                        condition = "Wet";
                        break;
                    default:
                        condition = "G";
                        break;
                }
                exCon = condition + grading;
            } else if (/^[A-Za-z]{1}\d{1,2}$/.test(event.track_condition)) {
                // Handles conditions like 'G4', 'H10', etc.
                let conditionLetter = event.track_condition.charAt(0);
                let grading = event.track_condition.slice(1);
                let condition = '';

                switch (conditionLetter.toUpperCase()) {
                    case 'F':
                        condition = 'M'; // Firm
                        break;
                    case 'G':
                        condition = 'G'; // Good
                        break;
                    case 'S':
                        condition = 'O'; // Soft
                        break;
                    case 'H':
                        condition = 'H'; // Heavy
                        break;
                    case 'Y':
                        condition = 'Y'; // Synthetic
                        break;
                    default:
                        condition = conditionLetter.toUpperCase();
                        break;
                }

                exCon = condition + grading;
            } else if (event.track_condition == 'Synthetic') {
                exCon = 'Y0';
            } else if (event.track_condition == 'Good') {
                exCon = 'G0';
            } else if (event.track_condition == 'Soft') {
                exCon = 'O0';
            } else if (event.track_condition == 'Heavy') {
                exCon = 'H0';
            } else if (event.track_condition == 'Dirt') {
                exCon = 'Dirt0';
            } else if (event.track_condition == 'Fast') {
                exCon = 'Fast0';
            } else if (event.track_condition == 'Sand') {
                exCon = 'Sand0';
            } else if (event.track_condition == 'Slow') {
                exCon = 'Slow0';
            } else if (event.track_condition == 'Dead') {
                exCon = 'Dead0';
            } else if (event.track_condition == 'Wet') {
                exCon = 'Wet0';
            }
        } else if (event.track['@_expected_condition']) {
            if (event.track['@_expected_condition'].includes("(")) {
                // Parsing logic for conditions like "Good (4)"
                console.log(event.track['@_expected_condition'].split("("));
                var exConSplit = event.track['@_expected_condition'].split("(");
                var grading = exConSplit[1].slice(0, -1);
                var condition = '';
                switch (exConSplit[0]) {
                    case "Firm":
                        condition = "M";
                        break;
                    case "Good":
                        condition = "G";
                        break;
                    case "Soft":
                        condition = "O";
                        break;
                    case "Heavy":
                        condition = "H";
                        break;
                    case "Synthetic":
                        condition = "Y";
                        break;
                    case "Dirt":
                        condition = "Dirt";
                        break;
                    case "Sand":
                        condition = "Sand";
                        break;
                    case "Fast":
                        condition = "Fast";
                        break;
                    case "Dead":
                        condition = "Dead";
                        break;
                    case "Slow":
                        condition = "Slow";
                        break;
                    case "Wet":
                        condition = "Wet";
                        break;
                    default:
                        condition = "G";
                        break;
                }
                exCon = condition + grading;
            } else if (/^[A-Za-z]{1}\d{1,2}$/.test(event.track['@_expected_condition'])) {
                // Handles conditions like 'G4', 'H10', etc.
                let conditionLetter = event.track['@_expected_condition'].charAt(0);
                let grading = event.track['@_expected_condition'].slice(1);
                let condition = '';

                switch (conditionLetter.toUpperCase()) {
                    case 'F':
                        condition = 'M'; // Firm
                        break;
                    case 'G':
                        condition = 'G'; // Good
                        break;
                    case 'S':
                        condition = 'O'; // Soft
                        break;
                    case 'H':
                        condition = 'H'; // Heavy
                        break;
                    case 'Y':
                        condition = 'Y'; // Synthetic
                        break;
                    default:
                        condition = conditionLetter.toUpperCase();
                        break;
                }

                exCon = condition + grading;
            }
        }

        // Fallback to default values if 'exCon' is still null
        if (!exCon) {
            if (event.track_type && event.track_type.includes('POLY')) {
                exCon = 'Y0';
            } else {
                exCon = 'G0';
            }
        }

        var eventData = {
            "event_id": event['@_id'],
            "@_number": event['@_number'] || 0,
            "@_name": event['@_shortname'] || '',
            "distance": event.distance || {},
            "restrictions": event.restrictions || {},
            "group": event.group || 0,
            "official_margin_1": event.official_margin_1 || '',
            "official_margin_2": event.official_margin_2 || '',
            "classes": event.classes || {},
            "event_prizemoney": prizeMoney || 0,
            "weight_type": event.weight_type ? setWeightType(event.weight_type) : '',
            "limit_weight": event.min_hcp_weight || '',
            "starters": event.starters || 0,
            "event_duration": event.duration || '',
            "sectional": theSectional,
            "other_runners": {
                "other_runner": other_runner || []
            },
            "track": {
                "@_name": meeting.track['@_name'] || '',
                "@_id": meeting.track['@_id'] || '',
                "@_location": meeting.track['@_location'] || '',
                "@_condition": exCon.charAt(0),
                "@_grading": exCon.slice(1),
                "@_track_surface": meeting.track['@_track_surface'] || '',
                "@_track_3char_abbrev": meeting.track['@_track_3char_abbrev'] || '',
                "@_track_4char_abbrev": meeting.track['@_track_4char_abbrev'] || '',
                "@_track_6char_abbrev": meeting.track['@_track_6char_abbrev'] || '',
                "@_country": meeting.track['@_country'] || ''
            }
        };

        //console.log(`Race ${event['@_number']} (${event['@_shortname']}) has exCon: ${exCon}`);

        for (var j = 0; j < event.horses.horse.length; j++) {
            var horseData = event.horses.horse[j];
            if (
                horseData.scratched == true ||
                horseData.scratched == 'true' ||
                ((!horseData.finish_position || horseData.finish_position == 'SCR') && horseData.finish_position !== 'FF')
            ) {
                continue;
            }
            console.log(`Horse ${horseData['@_name']} in Race ${event['@_number']} gets exCon: ${exCon}`);

            var bool = await createUpdateFormsTableResults(horseData, eventData, meetingData);
            if (!bool) {
                formUpdateSuccess = false;
            }
        }
    }

    return formUpdateSuccess;
};


const createUpdateFormsTableResults = async (horse, event, meeting) => {
    console.log('assembling form for race '+event['event_id']+' and horse '+horse['@_name'])
    try{
        // var prizemoney = horse.prizemoney_race ?? 0;
        // if (horse.prizemoney_race && horse.prizemoney_bonus){
        //     prizemoney = horse.prizemoney_race + horse.prizemoney_bonus
        // }
        horse.finish_position = setFinishPosition(horse.finish_position)

        if (horse.positions && !horse.positions['@_finish']) horse.positions['@_finish'] = horse.finish_position
        
        var formData = {
            "meeting_date": meeting.meetingDate ? meeting.meetingDate : '',
            "meeting_id": meeting._id ? meeting._id : '',
            "night_meeting": meeting.night_meeting,
            "rail_position": meeting.rail_position ? meeting.rail_position : '',
            "track": event.track ? event.track : {},
            "weight_type": event.weight_type ? event.weight_type : '',
            "limit_weight": event.limit_weight ? event.limit_weight : '',
            "event_id": event.event_id ? event.event_id : 0,
            "race": {
                "@_number": event['@_number'] ? event['@_number'] : 0,
                "@_name": event['@_name'] ? event['@_name'] : ''
            },
            "distance": event.distance ? event.distance : {},
            "event_duration": event.event_duration ? event.event_duration : '',
            "restrictions": event.restrictions ? event.restrictions : {},
            "group": event.group ? event.group : 0,
            "official_margin_1": event.official_margin_1 ? event.official_margin_1 :'',
            "official_margin_2": event.official_margin_2 ? event.official_margin_2 :'',
            "beaten_margin": horse.beaten_margin ? horse.beaten_margin : '',
            "starters": event.starters ? event.starters : 0,
            "classes":  event.classes ? event.classes : {},
            "event_prizemoney": event.event_prizemoney ?? 0,
            "sectional": event.sectional ? event.sectional : {},
            "trainer_id": horse.trainer['@_id'] ? horse.trainer['@_id'] : 0,
            "jockey": {
                "@_name": horse.jockey ? horse.jockey['@_name'] : '',
                "@_firstname": horse.jockey ? horse.jockey['@_firstname'] : '',
                "@_surname": horse.jockey ? horse.jockey['@_surname'] : '',
                "@_id": horse.jockey ? horse.jockey['@_id'] : 0,
                "@_apprentice_indicator": horse.jockey ? horse.jockey['@_apprentice_indicator'] : ''
            },
            "weight_carried": horse.weight_carried ? horse.weight_carried : 0,
            "weight_allocated": horse.weight ? horse.weight['@_total'] : 0,
            "barrier": horse.barrier ? horse.barrier : 0,
            "prices": horse.prices ? horse.prices : {},
            "decimalprices": horse.decimalprices ? horse.decimalprices : {},
            "positions":  horse.positions ? horse.positions : {},
            "finish_position": horse.finish_position ? horse.finish_position : 0,
            "margin": horse.margin ? horse.margin : '',
            "horse_prizemoney": horse.prizemoney_race ?? 0,
            "horse_prizemoney_bonus": horse.prizemoney_bonus ?? 0,
            "stewards_report": horse.stewards_report ? horse.stewards_report : '',
            "other_runners": event.other_runners ? event.other_runners : {},
            "gear_changes": horse.gear_changes ?? '',
            "running_gear": horse.running_gear ?? '',
            "rating":  horse.rating_result ?? {},
            "sectional_200" : horse.sectional_200 ?? ''
        }
        if (horse.favourite_indicator){
            formData.favourite_indicator = horse.favourite_indicator
        }
        formData.finish_position =  horse.finish_position ? horse.finish_position : (formData.finish_position?formData.finish_position:0)

        var formIndexData = {
            "track_id": event.track ? event.track['@_id'] : '',
            "meeting_id": meeting._id ? meeting._id : '',
            "meeting_date": meeting.meetingDate ? meeting.meetingDate : '',
            "event_id": event.event_id ? parseInt(event.event_id) : 0,
            "trainer_id":  horse.trainer['@_id'] ? horse.trainer['@_id'] : 0,
            "weight_carried": horse.weight_carried ? horse.weight_carried : 0,
            "race_no": event['@_number'] ? event['@_number'] : 0,
            "horse_id": horse['@_id'] ? horse['@_id'] : 0,
            "jockey_id": horse.jockey ? horse.jockey['@_id'] : 0,
            "finish_pos": (horse.finish_position && !isNaN(horse.finish_position)) ? horse.finish_position : 0
        }

        let status = await createUpdateInsertForm(horse['@_name'],horse['@_id'],event['event_id'],formData,formIndexData,horse.prizemoney_won)
        return status
    } catch (error){
        console.log(error)
        return false
    }
}

const createUpdateInsertForm = async (horseName,horseId,eventId,formData,indexData,total_prizemoney) => {

    var formArrayIndex = 0
    try{
        var data = await centaur.form.find({ horse_id: horseId }).lean()
        var horseFormId = ""
        var prevRun = 0
        var horseForm = false
        var blinkerInd = ""
        if (formData.running_gear && formData.running_gear.gear_item && formData.running_gear.gear_item.includes('Blinkers')) blinkerInd = "Y"
        if (data[0]){

            horseFormId = data[0]._id
            horseForm = data[0].form
            if (!horseForm){
                // let addFormContainer = await centaur.form.updateOne({ _id: data[0]._id }, {$push:  { 'form':[]}})
                // console.log(`form container added ${addFormContainer}`)
            } else {
                var formtodelete = -1
                for (var i=0;i<horseForm.length;i++){
                    if (horseForm[i].event_id == eventId){
                        formtodelete = i
                        console.log('found form item to update')
                    }
                }
                if (formtodelete > -1) {
                    horseForm.splice(formtodelete, 1)
                }
                formtodelete = -1
                for (var i=0;i<horseForm.length;i++){
                    if (horseForm[i].event_id == eventId){
                        formtodelete = i
                        console.log('found DUPLICATE form item to update')
                    }
                }
                if (formtodelete > -1) {
                    horseForm.splice(formtodelete, 1)
                }

                horseForm.push(formData)

                horseForm.sort((a,b) => a.meeting_date - b.meeting_date)
                var checkdate = ''

                for (var horseFormItem of horseForm){
                    if (checkdate){
                        var prevDate = moment(checkdate)
                        var meetDate = moment(horseFormItem.meeting_date)
                        horseFormItem.days_since_last_run = meetDate.diff(prevDate, 'days')
                    }
                    if (horseFormItem.classes && horseFormItem.classes.class_id  && horseFormItem.classes.class_id != 90){
                        checkdate = horseFormItem.meeting_date
                    }
                }
                horseForm.sort((a,b) => b.meeting_date - a.meeting_date)

                // for (var i=0;i<horseForm.length;i++){
                //     if (horseForm[i].event_id == eventId){
                //         console.log('found existing form entry for '+eventId)
                //         formArrayIndex = i;
                //         // remove matching form entry
                //         let removeFormItem = await centaur.form.updateOne({ _id: data[0]._id }, { $pull: { 'form': horseForm[i] }}).lean()
                //         console.log(removeFormItem)
                //         break
                //     }
                // }
                
                // if (horseForm[formArrayIndex+1]){
                //     var prevDate = moment(horseForm[formArrayIndex+1]['meeting_date'])
                //     var meetDate = moment(horseForm[formArrayIndex]['meeting_date'])
                //     prevRun = meetDate.diff(prevDate, 'days')
                //     console.log('dayssincelast: '+prevRun)
                // }
                // formData.days_since_last_run = prevRun


            }
        } else {
            var id = uuid.v4()
            var dataToCreate = {
                _id: id,
                horse_id: horseId,
                horse_name: horseName.toUpperCase()
            }
            // create new document in form table with horse data and this run of form
            let newHorse = new centaur.form(dataToCreate)
            var status = await newHorse.save();
            console.log("Form Created for new horse: " + horseName.toUpperCase())
            horseFormId = id

            horseForm = [formData]
        }
        
        let addForm = await centaur.form.updateOne({ _id: horseFormId }, {
                                                    "$set": {form : horseForm}
                                                }).lean()

        console.log("Horse Form for "+horseName+" Updated at position " + formArrayIndex)

        if (addForm){
            var runsupdate = await updateHorseResults(horseFormId,blinkerInd)
            console.log(runsupdate)
            var indexToMake = await createIndex(indexData)
            console.log(indexToMake)
            var prizeUpdate = await updatePrizeMoney(horseId,eventId,(formData.horse_prizemoney + (formData.horse_prizemoney_bonus ?? 0)) )
            console.log(prizeUpdate)
            return true
        } else {
            return false
        }
    } catch(error){
        console.log(error)
        return false
    }

}

const updatePrizeMoney = async (horse_id,event_id,newPrizemoney) =>{
    var horse = await centaur.horses.findOne({ HRN_HORSE_ID: horse_id }).lean()
    console.log(horse_id,event_id,newPrizemoney)
    console.log(horse)
    var horsePrizeArray = []
    var prizeToRemove = 0
    if (horse.HOR_RACE_PRIZEMONEY) {
        horsePrizeArray = horse.HOR_RACE_PRIZEMONEY
        for (var i=0;i<horsePrizeArray.length;i++){
            if (horsePrizeArray[i].event_id == event_id){
                prizeToRemove = horsePrizeArray[i].prize
                horsePrizeArray.splice(i,1)
            }
        }
    }
    horsePrizeArray.push({event_id:event_id,prize:newPrizemoney})
    console.log(`horseprizearray:`)
    console.log(horsePrizeArray)
    console.log(horse.HOR_TOTAL_PRIZEMONEY,newPrizemoney,prizeToRemove)
    var totalPrizeMoney = horse.HOR_TOTAL_PRIZEMONEY + (newPrizemoney - prizeToRemove)
    
    //CORRECTING from TISDB
    // if (totalMoney > 0) totalPrizeMoney = totalMoney
    //END CORRECTIONS
    var dataToUpdate = {
        HOR_TOTAL_PRIZEMONEY: totalPrizeMoney,
        HOR_RACE_PRIZEMONEY: horsePrizeArray
    }
    let status = await centaur.horses.updateOne({ HRN_HORSE_ID: horse_id }, {
        "$set": dataToUpdate
    }).lean()
    return status
}

const saveJockeyRestrictions = (rest) =>{
    var map = new Map([
        ["Apprentice Riders Only","A"],
        ["Apprentices Can Claim","C"],
        ["Eligible Riders Can Claim","E"],
        ["Gentlemen Riders Only","G"],
        ["Invited Riders","I"],
        ["Lady Riders Only","L"],
        ["Apprentices Cannot Claim","N"],
        ["Eligible Riders Cannot Claim","X"],
        ["Amateur Riders","M"],
        ["Hurdle Jockeys Only","H"]
    ])
    let result = map.get(rest)
    if (result == undefined) result = ''
    return result
}


const setAgeRestriction = (Id) => {
    var map = new Map([
        ["2yo", 1],
        ["2yo+", 2],
        ["2,3,4yo", 3],
        ["3yo", 4],
        ["3yo+", 5],
        ["4yo", 6],
        ["4yo+", 7],
        ["5yo", 8],
        ["5yo+", 9],
        ["6yo", 10],
        ["Aged", 11],
        ["2,3yo", 12],
        ["3,4yo", 13],
        ["3,4,5yo", 14],
        ["4,5yo", 15],
        ["4,5,6yo", 16],
        ["3,4,5,6", 17]
    ])
    let result = map.get(Id)
    if (result == undefined) result = Id
    return result
    }


const setSexRestriction = (Id) => {
    console.log('sex id',Id)
    var map = new Map([
        ["Fillies", 1],
        ["Mares", 2],
        ["Colts", 3],
        ["Geldings", 4],
        ["Horses", 5],
        ["Fillies & Mares", 6],
        ["Colts & Horses", 7],
        ["Colts & Geldings", 8],
        ["Horses & Geldings", 9],
        ["Horses & Mares", 10],
        ["Colts & Fillies", 11],
        ["Colts, Horses & Geldings", 12],
        ["Fillies, Colts & Geldings", 13],
        ["Mares, Horses & Geldings", 14],
        ["Fillies, Mares, Colts & Horses", 15],
        ["Fillies, Mares, Colts & Geldings", 16]
    ])
    let result = map.get(Id)
    if (result == undefined) result = Id
    return result
}

const setWeightType = (Id) => {
    var map = new Map([
        ["Handicap", "H"],
        ["Weight For Age", "W"],
        ["Weight For Age With Penalties", "X"],
        ["Set Weight", "S"],
        ["Set Weight With Penalties", "T"],
        ["Set Weight With Penalties and Allowances", "U"],
        ["Set Weights", "S"],
        ["Set Weights Plus Penalties", "T"],
        ["Set Weights Plus Penalties and Allowances", "U"],
        ["Special Weight", "P"],
        ["Catch Weight", "C"],
        ["Fly Weight", "F"],
        ["Set Weight With Allowances", "A"],
        ["Set Weights Plus Allowances", "A"],
        ["Quality", "Q"]
    ])
    let result = map.get(Id)
    if (result == undefined) result = Id
    return result
}

const setFinishPosition = (Id) => {
    var map = new Map([
        ["FF", 25],
        ["PU", 26],
        ["FL", 27],
        ["RO", 28],
        ["DQ", 29],
        ["NP", 30],
        ["LS", 31],
        ["LR", 32],
        ["SB", 33],
        ["SC", 34],
        ["BD", 35],
        ["UN", 36]
    ])
    let result = map.get(Id)
    if (result == undefined) result = Id
    return result
}

module.exports = {
    genResultsByMeetingId,
    genResultsByRace,
    updateHorseResults
}
