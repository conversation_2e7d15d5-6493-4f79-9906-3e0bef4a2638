const ftp = require("basic-ftp");
const mongoose = require("mongoose");
const Readable = require("stream").Readable;
const centaur = require("@mediality/centaur");
const helper = require("./library/helper");
const { create } = require("xmlbuilder2");
const { XMLBuilder } = require("fast-xml-parser");
const moment = require("moment");
let AWS = require("aws-sdk");
const uuid = require("uuid");
const https = require("https");
const options = {
  ignoreAttributes: false,
  ignoreNameSpace: false,
  attributeNamePrefix: "@_",
};

mongoose.set("debug", false);

let conn_env = "";
let conn = mongoose.connection;
let ftp_client = new ftp.Client((timeout = 10000));

conn.on("connected", function () {
  console.log("Database connection connected successfully");
});
conn.on("disconnected", function () {
  console.log("Database connection closed successfully");
});
conn.on("error", console.error.bind(console, "Connection error:"));

const DEFAULT_PARAMS = {
  is_validated: true,
  files: "FIELDS,FORM",
  xmlName: false,
  xmlValidation: false,
  attempt_auto_delivery: false,
  race_no: null,
  meeting_stage: null,
};

const BUCKET_NAMES = {
  deliveryService: process.env.ENV
    ? `mr-cen-delivery-service-xml-source-${process.env.ENV}`
    : "",
  validation:
    process.env.ENV === "prd"
      ? "xml-validation-bucket-prd"
      : "xml-validation-bucket",
  archive: process.env.ENV ? `results-file-archive-${process.env.ENV}` : "",
};

exports.handler = async (event) => {
  let response = {
    statusCode: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Headers": "*",
    },
    body: "",
  };
  if (
    event.queryStringParameters.is_validated &&
    event.queryStringParameters.is_validated === "false"
  ) {
    event.queryStringParameters.is_validated = false;
  }
  if (
    event.queryStringParameters.xmlValidation &&
    event.queryStringParameters.xmlValidation === "false"
  ) {
    event.queryStringParameters.xmlValidation = false;
  }
  try {
    await setupConnection();
    const params = { ...DEFAULT_PARAMS, ...event.queryStringParameters };
    const meeting_details = await getMeetingDetails(params.id);
    console.log(JSON.stringify(params));
    if (!validateMeeting(meeting_details, params)) {
      return createResponse(
        400,
        "Meeting not distributed due to validation issues"
      );
    }

    const result = await processMeeting(meeting_details, params);
    if(result && result.statusCode == 400){
      return createResponse(
        400,
        "Not distributing race-by-race results files for trials"
      );
    }
    console.log(JSON.stringify(result));
    response = createResponse(200, result);
  } catch (err) {
    console.error(err);
    response = createResponse(500, "Internal Server Error");
  } finally {
    await cleanup();
  }

  return response;
};

async function setupConnection() {
  conn_env = process.env.AWS_SAM_LOCAL
    ? "docker"
    : process.env.LOCAL
    ? "localhost"
    : "";
  const secretDetails = await centaur.getSecrets(process.env.centaurSecrets);
  const connDetails = await centaur.generateConnectionString(
    secretDetails,
    conn_env
  );
  await mongoose.connect(connDetails.CONNECTION_STRING, connDetails.PARAMETERS);
}

async function getMeetingDetails(id) {
  return await centaur.processed_meetings.findOne({ _id: id });
}

function validateMeeting(meeting_details, params) {
  if (params.is_validated && !meeting_details.validated) {
    return false;
  }
  const filesV1 = params.files.split(",");
  const isResultStaged = isResultsStage(meeting_details, filesV1);
  if (
    meeting_details.processedMeetingData.meeting.tab_indicator == "Trial" &&
    !isResultStaged
  ) {
    console.log(
      "Meeting not distributed because trial fields aren't set to distribute yet"
    );
    return false;
  }
  return true;
}

async function processMeeting(meeting_details, params) {
  const files = params.files.split(",");

  let fileNames = [];
  if (params.xmlName) {
    fileNames = await getXmlFileNames(meeting_details, files);
  }

  const isResults = isResultsStage(meeting_details, files);
  console.log("isResults", isResults);

  let result;
  if (isResults) {
    result = await processResults(meeting_details, params, files);
  } else {
    result = await processNonResults(meeting_details, params, files);
  }

  // If xmlName was true, include the file names in the result
  if (params.xmlName) {
    return {
      result: result,
      fileNames: fileNames,
    };
  }

  return result;
}

function isResultsStage(meeting_details, files) {
  const meeting_stage = meeting_details.meetingStage.toLowerCase();
  return meeting_stage.includes("results");
}

async function processResults(meeting_details, params, files = []) {
  if (files.includes("SCRATCHINGS") || files.includes("GEAR")) {
    return `${files} not distributed because meeting is at results`;
  }
  const product = meeting_details.processedMeetingData.meeting.product;
  const trial_suffix =
    meeting_details.processedMeetingData.meeting.tab_indicator == "Trial"
      ? "_T"
      : "";
  let results_meeting_filename = `${product["@_date"]}_${product["@_track"]}_RESULTS_XML${trial_suffix}.xml`;

  let meeting = JSON.parse(
    JSON.stringify(meeting_details.processedMeetingData).replace(
      /: *null/g,
      ': ""'
    )
  );
  let raceNo = params.race_no ? parseInt(params.race_no) : 0;

  if (raceNo > 0) {
    results_meeting_filename = `${product["@_date"]}_${product["@_track"]}_${raceNo}_RACE_BY_RACE_XML.xml`;
  }
  if (
    meeting_details.processedMeetingData.meeting.tab_indicator == "Trial" &&
    raceNo > 0
  ) {
    console.log("Not distributing race-by-race results files for trials");
    return createResponse(400, {
      error: true,
      message: 'Not distributing race-by-race results files for trials'
    });
  }
  
  const resultsJsonKeys = [
    "last_four_starts",
    "colours",
    "rating",
    "handicap_rating",
    "rating_wfa",
    "betting",
    "tip",
    "colours_link",
    "current_blinker_ind",
    "comment",
    "errors",
    "error_log",
    "last_ten_starts",
    "last_fifteen_starts",
    "last_twenty_starts",
    "FF5_dry",
    "FF5_wet",
    "FF_Dry_Rating_100",
    "FF_Wet_Rating_100",
    "sequence_no",
    "win_percentage",
    "place_percentage",
    "pace_value",
    "pace",
    "statistics",
    "win_distances",
    "weight",
    "jockey_id",
    "ratings",
    "form_comments",
    "barrier_number",
    "horse_colours_image",
    "horse_colours_image_png",
    "horse_colours_image_svg",
    "class_quality",
    "ballot_sequence",
    "locked"
  ];

  const raceJsonKeys = [
    "@_ra_name",
    "@_shortname",
    "errors",
    "error_log",
    "results_approved",
    "silksReady",
    "ra_comments",
    "temporary_weight_change",
    "confidence",
    "ra_id",
    "@_ra_id",
    "single_horse",
    "locked",
  ];

  let resultsMeeting = helper.resultsFilter(
    meeting,
    raceJsonKeys,
    resultsJsonKeys,
    raceNo
  );

  if (resultsMeeting === "abort") {
    return "Meeting not distributed because races are not resulted";
  }

  const builder = new XMLBuilder(options);
  let resultsXmlDataStr = builder.build(resultsMeeting);
  resultsXmlDataStr = helper.resultsXmlCleanup(resultsXmlDataStr);

  if (resultsXmlDataStr) {
    let uploadedFiles = [];
    if (!params.xmlValidation) {
      const uploadResult = await sendToS3(
        resultsXmlDataStr,
        results_meeting_filename,
        BUCKET_NAMES.deliveryService
      );
      uploadedFiles.push(uploadResult);
    } else {
      console.log("sending file", results_meeting_filename);
      const validationUpload = await sendToS3(
        resultsXmlDataStr,
        results_meeting_filename,
        BUCKET_NAMES.validation
      );
      uploadedFiles.push(validationUpload);
      if (raceNo === 0) {
        const archiveUpload = await sendToS3(
          resultsXmlDataStr,
          `${results_meeting_filename.slice(0, 6)}/${results_meeting_filename}`,
          BUCKET_NAMES.archive
        );
        uploadedFiles.push(archiveUpload);
      }
    }
    if (params.attempt_auto_delivery) {
      console.log("delivery attempt");
      const result = await performXMLValidationAPI(results_meeting_filename);
      console.log(result);
      if (!result) {
        return;
      }
      await sendToS3(
        resultsXmlDataStr,
        results_meeting_filename,
        BUCKET_NAMES.deliveryService
      );
    }
    await updateChangeLog(
      [results_meeting_filename],
      params.id,
      meeting_details.meetingLoadHistory ?? []
    );
    return {
      message: "Meeting Distributed",
      uploadedFiles: uploadedFiles,
    };
  }
  return "Results XML generation failed";
}

async function processNonResults(meeting_details, params, files) {
  const product = meeting_details.processedMeetingData.meeting.product;
  const meeting_stage = meeting_details.meetingStage.substring(0, 1);
  const stage_suffix = meeting_stage === "F" ? "A" : meeting_stage;
  let send_stage = (params.meeting_stage ?? meeting_details.meetingStage) == "Acceptances" ? "FinalFields" : (params.meeting_stage ?? meeting_details.meetingStage);

  const filenames = {
    FIELDS: `${product["@_date"]}_${product["@_track"]}_FIELDS_XML_${stage_suffix}.xml`,
    FORM: `${product["@_date"]}_${product["@_track"]}_FORM_XML_${stage_suffix}.xml`,
    GEAR: `${product["@_date"]}_${product["@_track"]}_GEARS_XML.xml`,
    SCRATCHINGS: `${product["@_date"]}_${product["@_track"]}_MEET_SCRATCHINGS_XML.xml`,
  };

  let meeting = JSON.parse(
    JSON.stringify(meeting_details.processedMeetingData).replace(
      /: *null/g,
      ': ""'
    )
  );
  const builder = new XMLBuilder(options);

  const fieldsJsonKeys = [
    "rating_wfa",
    "errors",
    "error_log",
    "weight_carried",
    "class_quality",
    "locked",
    "bonus_indicator",
  ];
  
  // Ensure race is always an array
  if (!Array.isArray(meeting.meeting.races.race)) {
    meeting.races.race = [meeting.races.race];
  }

  // Filter races based on meeting_stage if it's provided
  if (send_stage) {
    console.log(`Filtering races for stage: ${send_stage}`);
    meeting.meeting.races.race = meeting.meeting.races.race.filter((race) => {
      const raceStage = race.race_stage.toLowerCase();
      const sendStage = send_stage.toLowerCase();
    
      const matchesStage = 
        (raceStage === "acceptances" ? "finalfields" : raceStage) === sendStage ||
        ((raceStage === "results" || raceStage === "interim results") && 
         (sendStage === "results" || sendStage === "interim results"));
    
      console.log(`Race ${race["@_number"]} stage: ${race.race_stage}, matches: ${matchesStage}`);
      return matchesStage;
    });
    console.log(`Filtered to ${meeting.meeting.races.race.length} races`);

    // Check if any races remain after filtering
    if (meeting.meeting.races.race.length === 0) {
      console.log(`No races found for stage: ${params.meeting_stage}`);
      return {
        message: `No races found for stage: ${params.meeting_stage}`,
        uploadedFiles: [],
      };
    }
  }

  const fieldsMeeting = helper.fieldsFilter(meeting, fieldsJsonKeys);
  let uploadedFiles = [];
  for (const file of files) {
    let xmlDataStr;
    if (file === "FIELDS") {
      xmlDataStr = builder.build(fieldsMeeting);
      xmlDataStr = helper.fieldsAndFormXmlCleanup(xmlDataStr);
    } else if (file === "FORM") {
      const formMeeting = await helper.formFilter(
        JSON.parse(JSON.stringify(fieldsMeeting)),
        fieldsJsonKeys
      );
      xmlDataStr = builder.build(formMeeting);
      xmlDataStr = helper.fieldsAndFormXmlCleanup(xmlDataStr);
    } else if (file === "GEAR") {
      const gearsMeeting = helper.gearFilter(
        JSON.parse(JSON.stringify(fieldsMeeting))
      );
      xmlDataStr = builder.build(gearsMeeting);
    } else if (file === "SCRATCHINGS") {
      const scratchMeeting = helper.scratchingsFilter(
        JSON.parse(JSON.stringify(fieldsMeeting))
      );
      if (!scratchMeeting) continue;
      xmlDataStr = builder.build(scratchMeeting);
    }

    if (xmlDataStr) {
      const fileName = filenames[file];
      const bucketName =
        params.xmlValidation || params.attempt_auto_delivery
          ? BUCKET_NAMES.validation
          : BUCKET_NAMES.deliveryService;
      const uploadResult = await sendToS3(
        xmlDataStr,
        filenames[file],
        bucketName
      );
      uploadedFiles.push(uploadResult);

      if (!params.xmlValidation && !params.attempt_auto_delivery) {
        const validationUpload = await sendToS3(
          xmlDataStr,
          filenames[file],
          BUCKET_NAMES.validation
        );
        uploadedFiles.push(validationUpload);
      }
      if (params.attempt_auto_delivery) {
        console.log("attempt auto delivery");
        if (params.attempt_auto_delivery) {
          console.log("delivery attempt");
          const result = await performXMLValidationAPI(fileName);
          console.log(result);
          if (!result) {
            return;
          }
          await sendToS3(
            xmlDataStr,
            filenames[file],
            BUCKET_NAMES.deliveryService
          );
        }
      }
    }
  }

  await updateChangeLog(
    files,
    params.id,
    meeting_details.meetingLoadHistory ?? []
  );
  return {
    message: "Meeting Distributed",
    uploadedFiles: uploadedFiles,
  };
}

async function sendToS3(xmlDataStr, filename, bucketName) {
  console.log("Sending to bucket", bucketName);
  const S3 = new AWS.S3({ region: process.env.AWS_REGION });
  const document = create({ version: "1.0" })
    .ele(xmlDataStr)
    .end({ prettyPrint: true });
  const XMLStream = new Readable();
  XMLStream.push(document);
  XMLStream.push(null);

  const params = {
    Bucket: bucketName,
    Key: filename,
    Body: XMLStream,
    ContentType: "application/xml",
  };

  try {
    const s3Response = await S3.upload(params).promise();
    console.log(
      `File uploaded successfully. S3 Location: ${s3Response.Location}`
    );
    return {
      filename: filename,
      s3Path: s3Response.Location,
    };
  } catch (err) {
    console.error(`Error sending to S3: ${err}`);
    throw err;
  }
}

async function updateChangeLog(files, meetingId, fileLog) {
  let changeLog = await centaur.changelog
    .findOne({ meeting_id: meetingId })
    .lean();
  let newlog = {
    time: moment().toISOString(),
    bucket: "",
    file_path: "DELIVERY EVENT",
    trigger: files.join(" "),
  };
  if (changeLog) {
    let updatedChangeLog = changeLog.changelog;
    updatedChangeLog.unshift(newlog);
    await centaur.changelog
      .updateOne(
        { _id: changeLog._id },
        { $set: { changelog: updatedChangeLog } }
      )
      .lean();
  } else {
    fileLog.unshift(newlog);
    let newRecord = new centaur.changelog({
      _id: uuid.v4(),
      meeting_id: meetingId,
      changelog: fileLog,
    });
    await newRecord.save();
  }
  return "log updated";
}
function getXmlFileNames(meeting_details, files) {
  const product = meeting_details.processedMeetingData.meeting.product;
  const meeting_stage = meeting_details.meetingStage.substring(0, 1);
  const stage_suffix = meeting_stage === "F" ? "A" : meeting_stage;

  const results_meeting_filename = constructResultsMeetingFilename(
    meeting_details,
    product
  );

  const fileNames = {
    FIELDS: `${product["@_date"]}_${product["@_track"]}_FIELDS_XML_${stage_suffix}.xml`,
    FORM: `${product["@_date"]}_${product["@_track"]}_FORM_XML_${stage_suffix}.xml`,
    GEAR: `${product["@_date"]}_${product["@_track"]}_GEARS_XML.xml`,
    SCRATCHINGS: `${product["@_date"]}_${product["@_track"]}_MEET_SCRATCHINGS_XML.xml`,
    RESULTS: results_meeting_filename,
  };

  // Include the results filename if the meeting is at results stage
  if (isResultsStage(meeting_details, files)) {
    return [results_meeting_filename];
  }

  return files.map((file) => fileNames[file]).filter(Boolean);
}

function constructResultsMeetingFilename(meeting_details, product) {
  const trial_suffix =
    meeting_details.processedMeetingData.meeting.tab_indicator == "Trial"
      ? "_T"
      : "";
  let results_meeting_filename = `${product["@_date"]}_${product["@_track"]}_RESULTS_XML${trial_suffix}.xml`;

  let raceNo = meeting_details.params?.race_no
    ? parseInt(meeting_details.params.race_no)
    : 0;
  if (raceNo > 0) {
    results_meeting_filename = `${product["@_date"]}_${product["@_track"]}_${raceNo}_RACE_BY_RACE_XML.xml`;
  }

  return results_meeting_filename;
}

function createResponse(statusCode, message) {
  const validation_end_point =
    process.env.ENV === "prd"
      ? "https://m9mgot3zwg.execute-api.ap-southeast-2.amazonaws.com/prod/validate"
      : "https://wupw0vxbnf.execute-api.ap-southeast-2.amazonaws.com/prod/validate";
  return {
    statusCode,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Headers": "*",
    },
    body: JSON.stringify({
      message: message,
      VALIDATE_END_POINT: validation_end_point,
      VALIDATION_BUCKET: BUCKET_NAMES.validation,
    }),
  };
}

async function cleanup() {
  await conn.close();
  await ftp_client.close();
}

async function performXMLValidationAPI(fileName) {
  console.log(`Validating file ${fileName}...`);
  try {
    const validation_host =
      process.env.ENV === "prd"
        ? "m9mgot3zwg.execute-api.ap-southeast-2.amazonaws.com"
        : "wupw0vxbnf.execute-api.ap-southeast-2.amazonaws.com";
    const validationResult = await new Promise((resolve, reject) => {
      const options = {
        hostname: validation_host,
        port: 443,
        path: "/prod/validate",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      };

      const req = https.request(options, (res) => {
        let data = "";

        res.on("data", (chunk) => {
          data += chunk;
        });

        res.on("end", () => {
          resolve(JSON.parse(data));
        });
      });

      req.on("error", (error) => {
        reject(error);
      });

      req.write(
        JSON.stringify({
          bucket_name: BUCKET_NAMES.validation,
          object_key: fileName,
        })
      );
      req.end();
    });

    console.log("Validation Result:", validationResult);
    return {
      isValid:
        validationResult.message === "XML is valid against the XSD schema.",
      message: validationResult.message || "Unknown validation result",
    };
  } catch (error) {
    console.error(`Error validating file ${fileName}:`, error);
    return {
      isValid: false,
      message: "Failed to fetch validation result",
    };
  }
}
