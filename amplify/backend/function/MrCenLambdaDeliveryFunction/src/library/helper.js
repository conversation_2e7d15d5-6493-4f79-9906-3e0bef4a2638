const moment = require("moment");
const centaur = require("@mediality/centaur");

const fieldsFilter = (fields, jsonKeys) => {
  var emptyFieldsToClean = [
    "FF_Dry_Rating_100",
    "FF5_dry",
    "FF5_wet",
    "rating",
    "FF_Wet_Rating_100",
    "current_blinker_ind",
    "win_percentage",
    "place_percentage",
    "pace_value",
    "pace",
    "win_distances",
    "rating_wfa",
    "betting",
    "tip",
    "gear_changes",
    "colours_link",
    "comment",
    "scratched",
    "locked"
  ];
  var raceKeys = [
    "@_ra_name",
    "@_ra_id",
    "errors",
    "error_log",
    "@_expected_condition",
    "@_shortname",
    "apprentice_claim",
    "silksReady",
    "official_margin_1",
    "official_margin_2",
    "starters",
    "duration",
    "track_condition",
    "confidence",
    "single_horse",
    "locked"
  ];
  var emptyRaceFieldsToClean = [
    "group",
    "track_type",
    "min_hcp_weight",
    "temporary_weight_change",
    "locked"
  ];
  try {
    fields.meeting.stage = getMeetStage(fields.meeting.stage);
    fields.meeting["@_xmlns:xsi"] = "http://www.w3.org/2001/XMLSchema-instance";
    fields.meeting["@_xsi:noNamespaceSchemaLocation"] =
      "https://assets.medialityracing.com.au/xsd/MeetingV2.0.12.xsd";
    delete fields.meeting.silksReady;
    delete fields.meeting.errors;
    if (fields.meeting.track && !fields.meeting.track["@_surface"])
      delete fields.meeting.track["@_surface"];
    if (
      fields.meeting.track &&
      (fields.meeting.track["@_night_meeting"] == "N" ||
        fields.meeting.track["@_night_meeting"])
    ) {
      fields.meeting.track["@_night_meeting"] = "1";
    } else {
      delete fields.meeting.track["@_night_meeting"];
    }
    // if(fields.meeting.ra_meeting_id){
    //   delete fields.meeting["ra_meeting_id"];
    // }
    if (fields.meeting.track) {
      if (["HK", "SGP", "NZ"].includes(fields.meeting.track["@_state"])) {
        jsonKeys.push("betting");
      }
      delete fields.meeting.track.racing_direction;
      if (fields.meeting.track["@_name"])
        fields.meeting.track["@_name"] = fixNZTrackNames(
          fields.meeting.track["@_name"]
        );
    }
    try {
      if (fields.meeting.track && fields.meeting.track.weather !== null) {
        delete fields.meeting.track.weather["@_code"];
        delete fields.meeting.track.weather["@_description"];
      }
    } catch (err) {
      console.log(err);
    }

    if (fields.meeting.rail_position)
      fields.meeting.rail_position = cleanupTextForXml(
        fields.meeting.rail_position
      );
    var racestagedelete = true;
    for (race of fields.meeting.races.race) {
      console.log(race);
      let single_horse = false;
      //if (race.single_horse) single_horse = race.single_horse;
      for (key of raceKeys) {
        delete race[key];
      }
      if (
        race.race_stage == "Abandoned" ||
        getMeetStage(race.race_stage) != fields.meeting.stage
      ) {
        racestagedelete = false;
      }
      delete race.temporary_weight_change;
      if (race.records && !race.records.track_record)
        delete race.records.track_record;
      if (!race.records) race.records = "";
      for (cleanup of emptyRaceFieldsToClean) {
        if (!race[cleanup]) {
          delete race[cleanup];
        } else if (
          (race[cleanup] &&
            (race[cleanup] == 0 ||
              race[cleanup] == "" ||
              race[cleanup] == "0" ||
              race[cleanup] == [])) ||
          !race[cleanup]
        ) {
          delete race[cleanup];
        }
      }

      if (race["@_name"]) {
        race["@_name"] = cleanupTextForXml(race["@_name"]);
      }

      if (race.start_time && race.start_time != "") {
        race.start_time = timeconvert(race.start_time);
      }
      if (race.track && !race.track["@_track_surface"])
        delete race.track["@_track_surface"];

      delete race.track["@_expected_condition"];
      if (!race.restrictions["@_age"]) {
        delete race.restrictions["@_age"];
      }
      if (race.track && race.track["@_name"])
        race.track["@_name"] = fixNZTrackNames(race.track["@_name"]);
      if (race.weight_type) {
        race.weight_type = race.weight_type.replace(" Plus ", " With ");
      }

      for (horse of race.horses.horse) {
        if (horse.rating) {
          const ratingMatch = horse.rating.toString().match(/\d+/);
          if (ratingMatch) {
            horse.rating = parseInt(ratingMatch[0], 10);
          } else {
            horse.rating = 0;
          }
        }

        if (horse.FF5_dry) {
          const FF5_dryMatch = horse.FF5_dry.toString().match(/\d+/);
          if (FF5_dryMatch) {
            horse.FF5_dry = parseInt(FF5_dryMatch[0], 10);
          } else {
            horse.FF5_dry = 0;
          }
        }

        if (horse.FF5_wet) {
          const FF5_wetMatch = horse.FF5_wet.toString().match(/\d+/);
          if (FF5_wetMatch) {
            horse.FF5_wet = parseInt(FF5_wetMatch[0], 10);
          } else {
            horse.FF5_wet = 0;
          }
        }
        if (horse.jockey) {
          if (!horse.jockey["@_apprentice_indicator"])
            delete horse.jockey["@_apprentice_indicator"];
          if (!horse.jockey["@_allowance_weight"])
            delete horse.jockey["@_allowance_weight"];
        } else {
          horse.jockey = {
            "@_name": "",
            "@_firstname": "",
            "@_surname": "",
            "@_id": 0,
          };
        }
        for (key of jsonKeys) {
          delete horse[key];
        }
        for (cleanup of emptyFieldsToClean) {
          if (!horse[cleanup]) {
            delete horse[cleanup];
          } else if (
            (horse[cleanup] &&
              (horse[cleanup] == 0 ||
                horse[cleanup] == "" ||
                horse[cleanup] == "0" ||
                horse[cleanup] == [])) ||
            !horse[cleanup]
          ) {
            delete horse[cleanup];
          }
        }

        //'last_four_starts','last_ten_starts','last_fifteen_starts',
        //'last_twenty_starts'
        if (horse["@_name"]) {
          horse["@_name"] = capHorseName(horse["@_name"]);
        }
        if (horse.owners) {
          horse.owners = cleanupTextForXml(horse.owners);
        }
        if (horse.colours) {
          horse.colours = cleanupTextForXml(horse.colours);
        }
        if (horse.last_four_starts == "") delete horse.last_four_starts;
        if (horse.last_ten_starts == "") delete horse.last_ten_starts;
        if (horse.last_fifteen_starts == "") delete horse.last_fifteen_starts;
        if (horse.last_twenty_starts == "") delete horse.last_twenty_starts;
        if (!horse.scratched && horse.tip) {
          horse.selection = horse.tip;
        }
        delete horse.tip;
        if (horse.jockey && horse.jockey["@_id"] === "") {
          horse.jockey["@_id"] = 0;
        }
        if (horse.betting) {
          horse.market = {};
          horse.market["@_price"] = convertOdds(parseFloat(horse.betting));
          horse.market["@_price_decimal"] = parseFloat(horse.betting).toFixed(
            2
          );
          delete horse.betting;
        }
        if (horse.comment) {
          horse.comments = horse.comment;
          delete horse.comment;
        }
        if (horse.handicap_rating && horse.ratings && horse.ratings.rating) {
          horse.ratings.rating.push({
            "@_type": "handicap",
            "@_value": horse.handicap_rating ? horse.handicap_rating : 0,
            "@_date": "",
          });
          delete horse.handicap_rating;
        } else if (horse.ratings && horse.ratings.rating) {
          horse.ratings.rating.push({
            "@_type": "handicap",
            "@_value": 0,
            "@_date": "",
          });
          delete horse.handicap_rating;
        }
        if (horse.gear_changes && horse.gear_changes.gear_change) {
          for (gear_change of horse.gear_changes.gear_change) {
            gear_change["@_gear"] = gear_change["@_name"];
            delete gear_change["@_name"];
          }
        }
        if (horse.weight && horse.weight["@_penalty"]) {
          horse.weight["@_performance_penalty"] = horse.weight["@_penalty"];
          delete horse.weight["@_penalty"];
        }
        if (!horse.emergency_indicator || horse.emergency_indicator != "E")
          delete horse.emergency_indicator;
      }

      var theHorses = race.horses.horse;
      // Sort horses based on ballot_sequence and name
      if (race.race_stage === "Weights") {
        theHorses.sort((a, b) => {
          // Extract numeric value from ballot_sequence
          const getNumericValue = (seq) => {
            if (typeof seq === "number") return seq;
            if (typeof seq === "string") {
              const match = seq.match(/^(\d+)/);
              return match ? parseInt(match[1], 10) : 0;
            }
            return 0; // Default value if seq is neither number nor string
          };
      
          const aSeq = getNumericValue(a.ballot_sequence);
          const bSeq = getNumericValue(b.ballot_sequence);
      
          if (aSeq !== bSeq) {
            return aSeq - bSeq;
          }
      
          // If ballot_sequence is the same, compare weights
          const aWeight = parseFloat(a.weight?.["@_allocated"] || 0);
          const bWeight = parseFloat(b.weight?.["@_allocated"] || 0);
      
          if (aWeight !== bWeight) {
            return bWeight - aWeight; // Higher weight first
          }
      
          // Ensure that a["@_name"] and b["@_name"] are strings
          const aName = String(a["@_name"] || "");
          const bName = String(b["@_name"] || "");
      
          // If both ballot_sequence and weight are the same, sort alphabetically by name
          return aName.localeCompare(bName);
        });
      }
      
      // Delete ballot_sequence from each horse after sorting
      theHorses.forEach((horse) => {
        delete horse.ballot_sequence;
      });

      if (fields.meeting.track["@_state"] == "HK") {
        var newHorses = [];
        for (var newHorse of theHorses) {
          if (newHorse.emergency_indicator == "E" && newHorse.scratched)
            continue;
          newHorses.push(newHorse);
        }
        theHorses = newHorses.sort((a, b) => a.tab_number - b.tab_number);
      }
      if (single_horse) theHorses = [theHorses[0]];
      delete race.horses;
      race.horses = {};
      race.horses.horse = theHorses;
    }
    if (racestagedelete) {
      for (race of fields.meeting.races.race) {
        delete race.race_stage;
      }
    }
    var theRaces = fields.meeting.races.race;
    delete fields.meeting.races.race;
    fields.meeting.races.race = theRaces;
    console.log(fields);
    return fields;
  } catch (err) {
    console.log(`Error filtering XML 1 ${err}`);
    return "";
  }
};

const resultsFilter = (fields, raceKeys, jsonKeys, raceNo = 0) => {
  var emptyFieldsToClean = [
    "gear_changes",
    "beaten_margin",
    "margin_official",
    "stewards_report",
    "min_hcp_weight",
    "current_blinker_ind",
  ];
  var emptyRaceFieldsToClean = [
    "group",
    "track_type",
    "temporary_weight_change",
    "locked",
    "race_record_indicator"
  ];

  var currency = "";
  try {
    fields.meeting.stage = "R";
    fields.meeting["@_xmlns:xsi"] = "http://www.w3.org/2001/XMLSchema-instance";
    fields.meeting["@_xsi:noNamespaceSchemaLocation"] =
      "https://assets.medialityracing.com.au/xsd/ResultsV2.0.12.xsd";
    fields.meeting.events = {};
    fields.meeting.events.event = [];
    delete fields.meeting.silksReady;
    delete fields.meeting.errors;
    if (fields.meeting.product) {
      fields.meeting.product["@_directory"] = "RESULTS_XML";
      if (fields.meeting.product["@_date"].length == 8) {
        fields.meeting.product["@_date"] =
          fields.meeting.product["@_date"].slice(4);
      }
    }
    // if(fields.meeting.ra_meeting_id){
    //   delete fields.meeting["ra_meeting_id"];
    // }
    if (fields.meeting.track) {
      delete fields.meeting.track["@_expected_condition"];
      delete fields.meeting.track["@_racing_direction"];
      delete fields.meeting.track.racing_direction;
      if (fields.meeting.track["@_country"]) {
        currency = fields.meeting.track["@_country"];
      }
      if (
        fields.meeting.track &&
        (fields.meeting.track["@_night_meeting"] == "N" ||
          fields.meeting.track["@_night_meeting"])
      ) {
        fields.meeting.track["@_night_meeting"] = "1";
      } else {
        delete fields.meeting.track["@_night_meeting"];
      }
      fields.meeting.track["@_name"] = fixNZTrackNames(
        fields.meeting.track["@_name"]
      );
    }
    try {
      if (fields.meeting.track && fields.meeting.track.weather !== null) {
        delete fields.meeting.track.weather;
      }
    } catch (err) {}

    if (fields.meeting.rail_position)
      fields.meeting.rail_position = cleanupTextForXml(
        fields.meeting.rail_position
      );
    delete fields.meeting.silksReady;
    for (race of fields.meeting.races.race) {
      let abandonedRace = false;
      if (raceNo != 0 && raceNo != race["@_number"]) {
        continue;
      }
      for (key of raceKeys) {
        delete race[key];
      }
      if (
        ["acceptances", "finalfields"].includes(race.race_stage.toLowerCase())
      ) {
        return "abort";
      }
      if (race.race_stage != "Abandoned") {
        delete race.race_stage;
      } else {
        abandonedRace = true;
        race.duration = "NTT";
      }
      if (race.records && !race.records.track_record)
        delete race.records.track_record;
      if (race["@_name"]) {
        race["@_name"] = cleanupTextForXml(race["@_name"]);
      }
      for (cleanup of emptyRaceFieldsToClean) {
        if (!race[cleanup]) {
          delete race[cleanup];
        } else if (
          race[cleanup] &&
          (race[cleanup] == 0 ||
            race[cleanup] == "0" ||
            race[cleanup].length == 0)
        ) {
          delete race[cleanup];
        }
      }

      if (race.track) {
        if (race.track["@_name"])
          race.track["@_name"] = fixNZTrackNames(race.track["@_name"]);
        delete race.track["@_expected_condition"];
      }
      if (race.duration) {
        race.duration = formatTime(race.duration);
      }

      delete race.apprentice_claim;
      if (currency) {
        race.prizemoney_currency = currency;
      }

      if (race.official_margin_1) {
        race.official_margin_1 = marginSelect(race.official_margin_1);
      }
      if (race.official_margin_2) {
        race.official_margin_2 = marginSelect(race.official_margin_2);
      }
      if (!race.track_condition && race.track["@_expected_condition"]) {
        race.track_condition = conditionSelect(
          race.track["@_expected_condition"]
        );
        console.log(race.track_condition);
        delete race.track["@_expected_condition"];
      } else if (race.track_condition) {
        race.track_condition = conditionSelect(race.track_condition);
      }
      if (race.restrictions["@_age"] == "") {
        delete race.restrictions["@_age"];
      }
      if (race.restrictions["@_jockey"] == "") {
        delete race.restrictions["@_jockey"];
      }
      if (race.start_time == "0:00am" || race.start_time == "0000")
        delete race.start_time;
      if (race.start_time && race.start_time != "") {
        race.start_time = timeconvert(race.start_time);
      }
      if (race.min_hcp_weight && race.min_hcp_weight != 0) {
        race.min_hcp_weight = Number.parseFloat(race.min_hcp_weight);
      } else {
        delete race.min_hcp_weight;
      }
      if (
        !race.sectional ||
        !race.sectional["@_time"] ||
        race.sectional["@_time"].match(/[1-9]/).length < 1
      ) {
        delete race.sectional;
      } else {
        race.sectional["@_time"] = formatTime(race.sectional["@_time"]);
      }
      delete race.silksReady;
      var horsearray = [];
      for (horse of race.horses.horse) {
        delete horse.rating;
        delete horse.sectional_200;
        delete horse.bonus_indicator;
        if (abandonedRace){
          delete horse.positions;
          delete horse.weight;
          delete horse.jockey;
          delete horse.current_blinker_ind;
          delete horse.gear_changes;
          delete horse.running_gear;
          delete horse.prizemoney_race;
          delete horse.prizemoney_won;
          delete horse.barrier;
          delete horse.weight_carried;
          delete horse.decimalprices;
          delete horse.prices;
          delete horse.margin;
          horse.finish_position = "NP";
        } else if (horse.scratched) {
          if (!horse.finish_position) horse.finish_position = "SCR";
          delete horse.positions;
          delete horse.weight;
          delete horse.jockey;
          delete horse.current_blinker_ind;
          delete horse.gear_changes;
          delete horse.running_gear;
          delete horse.prizemoney_race;
          delete horse.prizemoney_won;
          delete horse.barrier;
          delete horse.weight_carried;
          delete horse.decimalprices;
          delete horse.prices;
          delete horse.margin;
        } else {
          if (
            horse.weight &&
            horse.weight_carried &&
            horse.weight["@_total"] &&
            horse.weight["@_total"] != horse.weight_carried
          ) {
            horse.weight_adj = horse.weight_carried - horse.weight["@_total"];
          }
          if (horse.weight_carried === 0 || horse.weight_carried === "0")
            horse.weight_carried = "";
          if (horse.decimalprices && !horse.decimalprices["@_starting"]) {
            delete horse.decimalprices;
            delete horse.prices;
            delete horse.favourite_indicator;
          } else {
            if (horse.decimalprices && !horse.decimalprices["@_mid"]) {
              delete horse.decimalprices["@_mid"];
              if (horse.prices) delete horse.prices["@_mid"];
            }
            if (horse.decimalprices && !horse.decimalprices["@_opening"]) {
              delete horse.decimalprices["@_opening"];
              if (horse.prices) delete horse.prices["@_opening"];
            }
            if (
              horse.decimalprices &&
              (!horse.prices || !horse.prices["@_starting"])
            ) {
              horse.prices = {};
              if (horse.decimalprices["@_starting"])
                horse.prices["@_starting"] = convertOdds(
                  parseFloat(horse.decimalprices["@_starting"])
                );
              if (horse.decimalprices["@_opening"])
                horse.prices["@_opening"] = convertOdds(
                  parseFloat(horse.decimalprices["@_opening"])
                );
              if (horse.decimalprices["@_mid"])
                horse.prices["@_mid"] = convertOdds(
                  parseFloat(horse.decimalprices["@_mid"])
                );
              delete horse.decimalprices["@_mid_2"];
            }
          }
          if (!horse.emergency_indicator || horse.emergency_indicator != "E")
            delete horse.emergency_indicator;
          if (horse.positions) {
            if (
              (typeof horse.positions["@_m1200"] === "string" &&
                horse.positions["@_m1200"].trim() === "") ||
              isNaN(horse.positions["@_m1200"])
            ) {
              delete horse.positions["@_m1200"];
            }
            if (
              (typeof horse.positions["@_m800"] === "string" &&
                horse.positions["@_m800"].trim() == "") ||
              isNaN(horse.positions["@_m800"])
            ) {
              delete horse.positions["@_m800"];
            }
            if (
              (typeof horse.positions["@_m400"] === "string" &&
                horse.positions["@_m400"].trim() == "") ||
              isNaN(horse.positions["@_m400"])
            ) {
              delete horse.positions["@_m400"];
            }
            if (
              (typeof horse.positions["@_settling_down"] === "string" &&
                horse.positions["@_settling_down"].trim() == "") ||
              isNaN(horse.positions["@_settling_down"])
            ) {
              delete horse.positions["@_settling_down"];
            }
          }
          if (!horse.positions) {
            delete horse.positions;
          }
          if (horse.jockey) {
            if (horse.jockey["@_apprentice_indicator"] == "") {
              delete horse.jockey["@_apprentice_indicator"];
            } else if (horse.jockey["@_apprentice_indicator"] == "Y") {
              horse.jockey["@_apprentice_indicator"] = "Y";
            }
            delete horse.jockey["@_allowance_weight"];
            if (horse.jockey["@_id"] === "") horse.jockey["@_id"] = 0;
            if (horse.jockey.statistics) {
              delete horse.jockey.statistics;
            }
          }

          if (["HK", "SGP", "NZ"].includes(fields.meeting.track["@_state"])) {
            var new_pmoney =
              horse.prizemoney_won +
              (horse.prizemoney_race ?? 0) +
              (horse.prizemoney_bonus ?? 0);
            horse.prizemoney_won = new_pmoney;
          }

          if (
            horse.current_blinker_ind &&
            (horse.current_blinker_ind == "Y" ||
              horse.current_blinker_ind == "B")
          ) {
            horse.blinkers_indicator = "B";
          }
          delete horse.current_blinker_ind;

          if (
            horse.gear_changes &&
            horse.gear_changes.gear_change &&
            horse.gear_changes.gear_change[0]
          ) {
            var gearChanges = "";
            for (gear of horse.gear_changes.gear_change) {
              gearChanges =
                gearChanges +
                gear["@_name"].toUpperCase() +
                " " +
                gear["@_option"].toUpperCase() +
                ". ";
              horse.gear_changes = gearChanges.replace(" .", ".").trim();
            }
          } else {
            delete horse.gear_changes;
          }
        }
        
        if (horse.trainer && horse.trainer.statistics) {
          delete horse.trainer.statistics;
        }
        if (horse.owners) {
          horse.owners = cleanupTextForXml(horse.owners);
        }
        for (key of jsonKeys) {
          delete horse[key];
        }
        for (cleanup of emptyFieldsToClean) {
          if (!horse[cleanup]) {
            delete horse[cleanup];
          } else if (
            horse[cleanup] &&
            (horse[cleanup] == 0 ||
              horse[cleanup] == "0" ||
              horse[cleanup].length == 0)
          ) {
            delete horse[cleanup];
          }
        }
        if (
          typeof horse.prizemoney_race === "number" &&
          horse.prizemoney_race > 0
        ) {
          horse.prizemoney_race = Math.round(horse.prizemoney_race);
        } else {
          delete horse.prizemoney_race;
        }
        if (
          typeof horse.prizemoney_bonus === "number" &&
          horse.prizemoney_bonus > 0
        ) {
          horse.prizemoney_bonus = Math.round(horse.prizemoney_bonus);
        } else {
          delete horse.prizemoney_bonus;
        }
        if (horse.horse_time) {
          horse.horse_time = formatTime(horse.horse_time);
        }

        if (horse.margin_official) {
          horse.margin_official = marginSelect(horse.margin_official);
        }
        if (
          horse.margin !== undefined &&
          horse.margin !== null &&
          horse.margin !== "" &&
          (typeof horse.margin === "string" ||
            typeof horse.margin === "number") &&
          (typeof horse.margin === "number" || !/\s/.test(horse.margin))
        ) {
          const parsedMargin = Number.parseFloat(horse.margin);
          if (!isNaN(parsedMargin) && parsedMargin >= 0) {
            horse.margin = Math.round(parsedMargin * 10) / 10;
          }
        } else {
          horse.margin = 0;
        }
        if (!horse.rating_result) {
          horse.rating = { "@_unadjusted": 0 };
        } else {
          horse.rating = adjustRatingValues(horse.rating_result);
        }
        delete horse.rating_result;

        if (horse.stewards_report && horse.stewards_report[0]) {
          if (horse.stewards_report[0].length != 1) {
            var stewardsText = "";
            var stewardsplit = horse.stewards_report[0].split(" ");
            var reports = [];
            reports.push(horse.stewards_report[1]);
            reports.push(horse.stewards_report[2]);
            for (var l = 0; l < stewardsplit.length; l++) {
              if (stewardsplit[l]) {
                stewardsText = stewardsText + formatStewards(stewardsplit[l]);
                stewardsText = stewardsText + ". ";
              }
            }
            var stewards_reports = [stewardsText.trim()];

            for (var i = 0; i < 2; i++) {
              if (reports[i]) {
                stewardsplit = reports[i].split(" ");
                if (
                  stewardsplit[0].length == 2 &&
                  (!stewardsplit[1] || stewardsplit[1].length == 2)
                ) {
                  stewardsText = "";
                  for (var l = 0; l < stewardsplit.length; l++) {
                    if (stewardsplit[l].length == 2) {
                      stewardsText =
                        stewardsText + formatStewards(stewardsplit[l]);
                      stewardsText = stewardsText + ". ";
                    }
                  }
                  stewards_reports.push(stewardsText.trim());
                } else {
                  stewards_reports.push(reports[i].trim());
                }
              }
            }
            horse.stewards_report = stewards_reports.join(" ").trim();
          }
        } else {
          delete horse.stewards_report;
        }
        horsearray.push(horse);
      }

      if (!abandonedRace){
        // Updated sorting logic
        horsearray.sort((a, b) => {
          let posA = a.finish_position;
          let posB = b.finish_position;
        
          // Ensure posA and posB are defined
          posA = posA ?? '';
          posB = posB ?? '';
        
          // Helper function to determine the sorting priority
          const getPriority = (pos) => {
            if (!isNaN(parseInt(pos))) return 0; // Numeric positions
            if (pos === "FF") return 1; // FF (Failed to Finish)
            if (pos === "SCR") return 2; // SCR (Scratched)
            return 3; // Other non-numeric values
          };
        
          const priorityA = getPriority(posA);
          const priorityB = getPriority(posB);
        
          if (priorityA !== priorityB) {
            return priorityA - priorityB;
          }
        
          // If both are numeric, sort numerically
          if (priorityA === 0) {
            return parseInt(posA) - parseInt(posB);
          }
        
          // For non-numeric values (except FF and SCR), sort alphabetically
          return posA.localeCompare(posB);
        });
      }
      

      // Special handling for Hong Kong races
      if (fields.meeting.track["@_state"] == "HK") {
        var newHorses = [];
        for (var newHorse of horsearray) {
          if (newHorse.emergency_indicator == "E" && newHorse.scratched)
            continue;
          newHorses.push(newHorse);
        }
        horsearray = newHorses;
      }

      delete race.horses;
      race.horses = {};
      race.horses.horse = horsearray;
      fields.meeting.events.event.push(race);
    }
    console.log(fields);
    delete fields.meeting.races;
    return fields;
  } catch (err) {
    console.log(`Error filtering XML  5${err}`);
    return "";
  }
};
function adjustRatingValues(ratingResult) {
  Object.keys(ratingResult).forEach((key) => {
    if (
      (typeof ratingResult[key] === "string" &&
        ratingResult[key].trim() === "") ||
      ratingResult[key] === null
    ) {
      ratingResult[key] = 0;
    }
  });
  return ratingResult;
}
const formFilter = async (formFields, jsonKeys) => {
  var theClasses = await centaur.race_classes.find().lean();
  const classMap = new Map(
    theClasses.map((object) => {
      return [object.CLA_CLASS_DB_ID, object.CLA_CLASS_SHORT_DISP];
    })
  );

  try {
    formFields.meeting.stage = getMeetStage(formFields.meeting.stage);
    formFields.meeting["@_xmlns:xsi"] =
      "http://www.w3.org/2001/XMLSchema-instance";
    formFields.meeting["@_xsi:noNamespaceSchemaLocation"] =
      "https://assets.medialityracing.com.au/xsd/MeetingFormV2.0.16.xsd";
    for (race of formFields.meeting.races.race) {
      var horseIds = [];
      for (horse of race.horses.horse) {
        for (key of jsonKeys) {
          delete horse[key];
        }
        horseIds.push(horse["@_id"]);
      }
      var forms = await getForms(horseIds);
      for (horse of race.horses.horse) {
        if (horse.rating) {
          const ratingMatch = horse.rating.toString().match(/\d+/);
          if (ratingMatch) {
            horse.rating = parseInt(ratingMatch[0], 10);
          } else {
            horse.rating = 0;
          }
        }

        if (horse.FF5_dry) {
          const FF5_dryMatch = horse.FF5_dry.toString().match(/\d+/);
          if (FF5_dryMatch) {
            horse.FF5_dry = parseInt(FF5_dryMatch[0], 10);
          } else {
            horse.FF5_dry = 0;
          }
        }

        if (horse.FF5_wet) {
          const FF5_wetMatch = horse.FF5_wet.toString().match(/\d+/);
          if (FF5_wetMatch) {
            horse.FF5_wet = parseInt(FF5_wetMatch[0], 10);
          } else {
            horse.FF5_wet = 0;
          }
        }
        for (formHorse of forms) {
          if (horse["@_id"] == formHorse.horse_id) {
            horse.forms = {};
            var theForm = formHorse.form;
            for (var k = 0; k < theForm.length; k++) {
              var form = theForm[k];
              // console.log(form.meeting_date)
              form.meeting_date = moment(form.meeting_date).format(
                "DD/MM/YYYY"
              );
              // form.meeting_date = moment(form.meeting_date)'
              // console.log(form.meeting_date)
              delete form.meeting_id;
              delete form.horse_id;
              delete form.sectional_200;
              delete form.form_count;
              delete form.distance_metres;
              delete form.track_name;
              delete form.class_name;
              delete form.ran_gear;
              delete form.horse_name;

              if (form.rail_position)
                form.rail_position = cleanupTextForXml(form.rail_position);
              if (form.night_meeting != "N") {
                delete form.night_meeting;
              }
              if (form.limit_weight == 0) {
                delete form.limit_weight;
              }
              if (form.weight_limit == 0) {
                delete form.weight_limit;
              } else if (form.weight_limit && !form.limit_weight) {
                form.limit_weight = form.weight_limit;
              }
              delete form.weight_limit;
              if (!form.blinker_indicator) {
                delete form.blinker_indicator;
              } else if (form.blinker_indicator == "Y") {
                form.blinker_indicator = "B";
              }

              if (form.race && form.race["@_name"]) {
                form.race["@_name"] = cleverCapFstLtr(form.race["@_name"]);
              } else {
                console.log("major issue with form item");
                console.log(horse);
                console.log(form);
              }
              if (form.track && form.track["@_condition"]) {
                form.track["@_condition"] = getTrackCond(
                  form.track["@_condition"]
                );
              }
              if (form.track && form.track["@_name"]) {
                form.track["@_name"] = cleverCapFstLtr(form.track["@_name"]);
              }
              if (form.track && !form.track["@_track_surface"])
                delete form.track["@_track_surface"];
              delete form.trainer_id;
              if (form.jockey && form.jockey["@_name"])
                form.jockey["@_name"] = cleverCapFstLtr(form.jockey["@_name"]);
              if (form.jockey && form.jockey["@_firstname"])
                form.jockey["@_firstname"] = cleverCapFstLtr(
                  form.jockey["@_firstname"]
                );
              if (form.jockey && form.jockey["@_surname"])
                form.jockey["@_surname"] = cleverCapFstLtr(
                  form.jockey["@_surname"]
                );
              if (
                form.jockey &&
                (!form.jockey["@_apprentice_indicator"] ||
                  form.jockey["@_apprentice_indicator"] == "N")
              )
                delete form.jockey["@_apprentice_indicator"];

              if (form.finish_position == 1 || form.finish_position == 2) {
                form.margin = getMarginDecimalValue(form.official_margin_1);
              } else if (form.finish_position == 3) {
                form.margin = parseFloat(
                  getMarginDecimalValue(form.official_margin_1) +
                    getMarginDecimalValue(form.official_margin_2)
                ).toFixed(1);
              } else {
                form.margin = getMarginDecimalValue(form.margin);
              }
              if (!form.positions["@_finish"])
                form.positions["@_finish"] = form.finish_position;
              if (form.classes && form.classes.class_id == 90)
                form.barrier_trial_indicator = "B";

              if (form.classes && form.classes.class_id) {
                form.classes.class =
                  classMap.get(form.classes.class_id) ??
                  form.classes.class ??
                  "";
              }

              if (
                !form.favourite_indicator ||
                /^\s*$/.test(form.favourite_indicator)
              ) {
                delete form.favourite_indicator;
              } else if (form.favourite_indicator.toUpperCase() === "EF") {
                form.favourite_indicator = "E";
              } else {
                form.favourite_indicator = form.favourite_indicator
                  .toUpperCase()
                  .trim();
              }
              if (form.group && form.group == 4) form.group = "LR";

              if (!form.beaten_margin) {
                delete form.beaten_margin;
              } else {
                form.beaten_margin =
                  Math.round(parseFloat(form.beaten_margin) * 100) / 100;
              }
              if (form.positions && form.finish_position > 24) {
                form.finish_position = setFinishPosition(form.finish_position);
              }
              if (form.finish_position && form.finish_position > 24) {
                form.finish_position = setFinishPosition(form.finish_position);
              }
              if (form.restrictions) {
                if (
                  form.restrictions["@_age"] &&
                  form.restrictions["@_age"] != 0
                ) {
                  form.restrictions["@_age"] = getAgeRestriction(
                    form.restrictions["@_age"]
                  );
                } else {
                  delete form.restrictions["@_age"];
                }
                if (
                  form.restrictions["@_sex"] &&
                  form.restrictions["@_sex"] != 0
                ) {
                  form.restrictions["@_sex"] = getSexRestriction(
                    form.restrictions["@_sex"]
                  );
                } else {
                  delete form.restrictions["@_sex"];
                }

                if (form.restrictions["@_jockey"]) {
                  form.restrictions["@_jockey"] = formatJockeyRestrictions(
                    form.restrictions["@_jockey"]
                  );
                }

                if (!form.restrictions["@_jockey"]) {
                  delete form.restrictions["@_jockey"];
                }
              }
              if (form.sectional) {
                if (["L", "l", "last"].includes(form.sectional["@_location"]))
                  form.sectional["@_location"] = "Last";
                if (["F", "f", "first"].includes(form.sectional["@_location"]))
                  form.sectional["@_location"] = "First";
                if (
                  form.sectional["@_time"] &&
                  form.sectional["@_time"].match(/[1-9]/g) &&
                  form.sectional["@_time"].match(/[1-9]/g).length > 0
                ) {
                  form.sectional["@_time"] = formatTime(
                    form.sectional["@_time"]
                  );
                } else {
                  delete form.sectional;
                }
              }
              if (form.weight_type)
                form.weight_type = getWeightType(form.weight_type);
              if (
                form.weight_allocated &&
                form.weight_allocated != form.weight_carried
              )
                form.weight_adj = form.weight_carried - form.weight_allocated;
              if (form.event_duration) {
                form.event_duration = formatTime(form.event_duration);
              }
              if (
                typeof form.horse_prizemoney === "number" &&
                form.horse_prizemoney > 0
              ) {
                form.horse_prizemoney = Math.round(form.horse_prizemoney);
              } else {
                delete form.horse_prizemoney;
              }
              if (
                typeof form.horse_prizemoney_bonus === "number" &&
                form.horse_prizemoney_bonus > 0
              ) {
                form.horse_prizemoney_bonus = Math.round(
                  form.horse_prizemoney_bonus
                );
              } else {
                delete form.horse_prizemoney_bonus;
              }
              try {
                if (form.rail_position) {
                  form.rail_position = form.rail_position
                    .toString()
                    .replace(/\&([^a])/g, "&amp;$1");
                  form.rail_position = form.rail_position.replace(/–/g, "-");
                }
              } catch (err) {
                console.log(err);
                console.log(horse);
                console.log(form);
              }

              if (form.decimalprices) {
                if (form.decimalprices["@_opening"]) {
                  form.decimalprices["@_opening"] = Number.parseFloat(
                    form.decimalprices["@_opening"]
                  ).toFixed(2);
                } else {
                  delete form.decimalprices["@_opening"];
                  delete form.prices["@_opening"];
                }
                if (form.decimalprices["@_starting"]) {
                  form.decimalprices["@_starting"] = Number.parseFloat(
                    form.decimalprices["@_starting"]
                  ).toFixed(2);
                  if (!form.prices || !form.prices["@_starting"]) {
                    form.prices = {};
                    form.prices["@_starting"] = convertOdds(
                      parseFloat(form.decimalprices["@_starting"])
                    );
                  }
                } else {
                  delete form.decimalprices["@_starting"];
                  delete form.prices["@_starting"];
                }
                if (form.decimalprices["@_mid"]) {
                  form.decimalprices["@_mid"] = Number.parseFloat(
                    form.decimalprices["@_mid"]
                  ).toFixed(2);
                } else {
                  delete form.decimalprices["@_mid"];
                  delete form.prices["@_mid"];
                }
                if (form.decimalprices["@_mid_2"]) {
                  form.decimalprices["@_mid_2"] = Number.parseFloat(
                    form.decimalprices["@_mid_2"]
                  ).toFixed(2);
                } else {
                  delete form.decimalprices["@_mid_2"];
                  delete form.prices["@_mid_2"];
                }
              }
              delete form.weight_allocated;
              if (form.positions) {
                if (
                  (typeof form.positions["@_m1200"] === "string" &&
                    form.positions["@_m1200"].trim() === "") ||
                  isNaN(form.positions["@_m1200"])
                ) {
                  delete form.positions["@_m1200"];
                }
                if (
                  (typeof form.positions["@_m800"] === "string" &&
                    form.positions["@_m800"].trim() == "") ||
                  isNaN(form.positions["@_m800"])
                ) {
                  delete form.positions["@_m800"];
                }

                if (
                  (typeof form.positions["@_m400"] === "string" &&
                    form.positions["@_m400"].trim() == "") ||
                  isNaN(form.positions["@_m400"])
                ) {
                  delete form.positions["@_m400"];
                }

                if (
                  (typeof form.positions["@_settling_down"] === "string" &&
                    form.positions["@_settling_down"].trim() == "") ||
                  isNaN(form.positions["@_settling_down"])
                ) {
                  delete form.positions["@_settling_down"];
                }
              }
              if (form.stewards_report && form.stewards_report[0]) {
                if (form.stewards_report[0].length != 1) {
                  var stewardsText = "";
                  var stewardsplit = form.stewards_report[0].split(" ");
                  var reports = [];
                  reports.push(form.stewards_report[1]);
                  reports.push(form.stewards_report[2]);
                  for (var l = 0; l < stewardsplit.length; l++) {
                    if (stewardsplit[l]) {
                      stewardsText =
                        stewardsText + formatStewards(stewardsplit[l]);
                      stewardsText = stewardsText + ". ";
                    }
                  }
                  var stewards_reports = [stewardsText.trim()];

                  for (var i = 0; i < 2; i++) {
                    if (reports[i]) {
                      stewardsplit = reports[i].split(" ");
                      if (
                        stewardsplit[0].length == 2 &&
                        (!stewardsplit[1] || stewardsplit[1].length == 2)
                      ) {
                        stewardsText = "";
                        for (var l = 0; l < stewardsplit.length; l++) {
                          if (stewardsplit[l].length == 2) {
                            stewardsText =
                              stewardsText + formatStewards(stewardsplit[l]);
                            stewardsText = stewardsText + ". ";
                          }
                        }
                        stewards_reports.push(stewardsText.trim());
                      } else {
                        stewards_reports.push(reports[i].trim());
                      }
                    }
                  }
                  form.stewards_reports = {};
                  form.stewards_reports.stewards_report = stewards_reports;
                  delete form.stewards_report;
                } else {
                  form.stewards_reports = {};
                  form.stewards_reports.stewards_report = [
                    form.stewards_report,
                  ];
                  delete form.stewards_report;
                }
              } else {
                delete form.stewards_report;
              }
              if (form.other_runners && form.other_runners.other_runner) {
                for (
                  var l = 0;
                  l < form.other_runners.other_runner.length;
                  l++
                ) {
                  form.other_runners.other_runner[l]["@_horse"] =
                    cleverCapFstLtr(
                      form.other_runners.other_runner[l]["@_horse"]
                    );
                  form.other_runners.other_runner[l]["@_jockey"] =
                    cleverCapFstLtr(
                      form.other_runners.other_runner[l]["@_jockey"]
                    );
                  if (
                    form.other_runners.other_runner[l]["@_position"] == 1 ||
                    form.other_runners.other_runner[l]["@_position"] == 2
                  ) {
                    form.other_runners.other_runner[l]["@_margin"] =
                      getMarginDecimalValue(form.official_margin_1);
                  } else if (
                    form.other_runners.other_runner[l]["@_position"] == 3
                  ) {
                    form.other_runners.other_runner[l]["@_margin"] = parseFloat(
                      getMarginDecimalValue(form.official_margin_1) +
                        getMarginDecimalValue(form.official_margin_2)
                    ).toFixed(1);
                  } else {
                    form.other_runners.other_runner[l]["@_margin"] =
                      getMarginDecimalValue(
                        form.other_runners.other_runner[l]["@_margin"]
                      );
                  }
                  if (
                    l > 0 &&
                    form.other_runners.other_runner[l]["@_position"] ==
                      form.other_runners.other_runner[l - 1]["@_position"] &&
                    form.other_runners.other_runner[l]["@_horse"]
                  ) {
                    form.other_runners.other_runner[l][
                      "@_dead_heat_indicator"
                    ] = "D";
                    form.other_runners.other_runner[l - 1][
                      "@_dead_heat_indicator"
                    ] = "D";
                  }
                }
                form.official_margin_1 = getMarginMap(form.official_margin_1);
                form.official_margin_2 = getMarginMap(form.official_margin_2);
                var h = 0;
                var emptyrundelete = false;
                for (runner of form.other_runners.other_runner) {
                  if (
                    runner["@_horse"].toUpperCase() ==
                    horse["@_name"].toUpperCase()
                  ) {
                    form.other_runners.other_runner.splice(h, 1);
                    break;
                  }
                  if (!runner["@_horse"]) {
                    form.other_runners.other_runner.splice(h, 1);
                    emptyrundelete = true;
                    break;
                  }
                  h++;
                }

                if (emptyrundelete) {
                  h = 0;
                  for (runner of form.other_runners.other_runner) {
                    if (!runner["@_horse"]) {
                      form.other_runners.other_runner.splice(h, 1);
                      extrarundelete = true;
                      break;
                    }
                    h++;
                  }
                  h = 0;
                  for (runner of form.other_runners.other_runner) {
                    if (!runner["@_horse"]) {
                      form.other_runners.other_runner.splice(h, 1);
                      extrarundelete = true;
                      break;
                    }
                    h++;
                  }
                }
              }

              if (form.gear_changes && form.gear_changes.gear_change) {
                for (gear_change of form.gear_changes.gear_change) {
                  gear_change["@_gear"] = gear_change["@_name"];
                  delete gear_change["@_name"];
                }
              }
              if (!form.rating) {
                delete form.rating;
              } else {
                if (!form.rating["@_handicap"]) form.rating["@_handicap"] = 0;
              }
            }
            horse.forms.form = formHorse.form;
          }
        }
      }
    }
    return formFields;
  } catch (err) {
    console.log(`Error filtering XML 4 ${err}`);
    return "";
  }
};

const gearFilter = (gearfields) => {
  var keysToClean = [
    "sire",
    "dam",
    "sire_of_dam",
    "trainer",
    "training_location",
    "owners",
    "colours",
    "horse_colours_image",
    "prizemoney_won",
    "FF5_dry",
    "FF5_wet",
    "FF_Dry_Rating_100",
    "FF_Wet_Rating_100",
    "current_blinker_ind",
    "statistics",
    "rating",
    "ratings",
    "weight",
    "tab_no",
    "barrier",
    "jockey",
    "last_four_starts",
    "last_ten_starts",
    "last_fifteen_starts",
    "last_twenty_starts",
    "pace_value",
    "pace",
    "win_distances",
    "win_percentage",
    "place_percentage",
    "tab_number",
    "comments",
    "market",
    "selection",
    "emergency_indicator",
    "horse_colours_image_png",
    "horse_colours_image_svg",
    "form_comments",
    "running_gear",
    "class_quality",
  ];

  var raceKeysToClean = [
    "start_time",
    "restrictions",
    "weight_type",
    "min_hcp_weight",
    "track_type",
    "classes",
    "race_type",
    "prizes",
    "records",
    "dual_track",
  ];

  try {
    gearfields.meeting["@_xsi:noNamespaceSchemaLocation"] = "";
    if (gearfields.meeting.product) {
      gearfields.meeting.product["@_directory"] = "GEARS_XML";
      if (gearfields.meeting.product["@_date"].length == 8) {
        gearfields.meeting.product["@_date"] =
          gearfields.meeting.product["@_date"].slice(4);
        gearfields.meeting.product["@_date"] =
          gearfields.meeting.product["@_date"].slice(2).toString() +
          gearfields.meeting.product["@_date"].slice(-2).toString();
      }
    }
    if (gearfields.meeting.track && gearfields.meeting.track["@_name"]) {
      gearfields.meeting.track["@_name"] = fixNZTrackNames(
        gearfields.meeting.track["@_name"]
      );
    }
    for (race of gearfields.meeting.races.race) {
      var hasGearChanges = false;
      for (key of raceKeysToClean) {
        delete race[key];
      }
      var newHorses = [];

      // Sort horses based on sequence_no in ascending order
      race.horses.horse.sort((a, b) => {
        return parseInt(a.sequence_no) - parseInt(b.sequence_no);
      });

      for (horse of race.horses.horse) {
        if (
          !horse.gear_changes ||
          !horse.gear_changes.gear_change ||
          !horse.gear_changes.gear_change[0]
        ) {
          continue;
        }
        hasGearChanges = true;
        for (key of keysToClean) {
          delete horse[key];
        }
        theGear = [];
        for (gear of horse.gear_changes.gear_change) {
          theGear.push(gear);
        }
        horse.gear_changes = {
          gear: theGear,
        };
        newHorses.push(horse);
      }
      race.has_gear_changes = hasGearChanges ? "true" : "false";
      race.horses.horse = newHorses;
    }
    console.log(gearfields);
    return gearfields;
  } catch (err) {
    console.log(`Error filtering XML  3 ${err}`);
    return "";
  }
};

const scratchingsFilter = (scratchfields) => {
  var keysToClean = [
    "sire",
    "dam",
    "sire_of_dam",
    "trainer",
    "training_location",
    "owners",
    "colours",
    "horse_colours_image",
    "prizemoney_won",
    "FF5_dry",
    "FF5_wet",
    "FF_Dry_Rating_100",
    "FF_Wet_Rating_100",
    "current_blinker_ind",
    "statistics",
    "rating",
    "ratings",
    "weight",
    "tab_no",
    "barrier",
    "jockey",
    "last_four_starts",
    "last_ten_starts",
    "last_fifteen_starts",
    "last_twenty_starts",
    "pace_value",
    "pace",
    "win_distances",
    "win_percentage",
    "place_percentage",
    "tab_number",
    "gear_changes",
    "scratched",
    "horse_colours_image_png",
    "horse_colours_image_svg",
    "form_comments",
    "market",
    "selection",
    "comments",
    "running_gear",
    "class_quality",
    "emergency_indicator",
  ];
  var raceKeysToClean = [
    "distance",
    "restrictions",
    "weight_type",
    "min_hcp_weight",
    "track_type",
    "classes",
    "race_type",
    "prizes",
    "records",
    "track",
    "group",
    "dual_track",
  ];
  try {
    scratchfields.meeting["@_xsi:noNamespaceSchemaLocation"] =
      "https://assets.medialityracing.com.au/xsd/ScratchingsV2.0.5.xsd";
    if (scratchfields.meeting.product) {
      scratchfields.meeting.product["@_directory"] = "MEET_SCRATCHINGS_XML";
      if (scratchfields.meeting.product["@_date"].length == 8) {
        scratchfields.meeting.product["@_date"] =
          scratchfields.meeting.product["@_date"].slice(4);
        scratchfields.meeting.product["@_date"] =
          scratchfields.meeting.product["@_date"].slice(2).toString() +
          scratchfields.meeting.product["@_date"].slice(-2).toString();
      }
    }
    if (scratchfields.meeting.dual_track)
      delete scratchfields.meeting.dual_track;
    if (scratchfields.meeting.track && scratchfields.meeting.track["@_name"]) {
      scratchfields.meeting.track["@_name"] = fixNZTrackNames(
        scratchfields.meeting.track["@_name"]
      );
    }
    var newraces = [];
    for (race of scratchfields.meeting.races.race) {
      var hasScratchings = false;
      for (key of raceKeysToClean) {
        delete race[key];
      }
      var scratchedHorses = [];
      for (horse of race.horses.horse) {
        if (horse.rating) {
          const ratingMatch = horse.rating.toString().match(/\d+/);
          if (ratingMatch) {
            horse.rating = parseInt(ratingMatch[0], 10);
          } else {
            horse.rating = 0;
          }
        }

        if (horse.FF5_dry) {
          const FF5_dryMatch = horse.FF5_dry.toString().match(/\d+/);
          if (FF5_dryMatch) {
            horse.FF5_dry = parseInt(FF5_dryMatch[0], 10);
          } else {
            horse.FF5_dry = 0;
          }
        }

        if (horse.FF5_wet) {
          const FF5_wetMatch = horse.FF5_wet.toString().match(/\d+/);
          if (FF5_wetMatch) {
            horse.FF5_wet = parseInt(FF5_wetMatch[0], 10);
          } else {
            horse.FF5_wet = 0;
          }
        }
        if (!horse.scratched) {
          continue;
        }
        horse["@_foaling_date"] = horse["@_foalingdate"];
        delete horse["@_foalingdate"];
        hasScratchings = true;
        for (key of keysToClean) {
          delete horse[key];
        }
        scratchedHorses.push(horse);
      }
      console.log(scratchedHorses);
      delete race.horses;
      race.scratched_horses = {};
      race.scratched_horses.horse = scratchedHorses;
      console.log(race);
      if (hasScratchings) newraces.push(race);
    }
    if (newraces.length === 0) return false;
    scratchfields.meeting.races.race = newraces;

    return scratchfields;
  } catch (err) {
    console.log(`Error filtering XML 2 ${err}`);
    return "";
  }
};

const getForms = async (horseIds) => {
  const results = await centaur.form.find({
    horse_id: { $in: horseIds },
  });
  if (results.length > 0) {
    return results;
  } else {
    console.log("Error with retrieving horse form for statistics");
    return [];
  }
};

const fieldsAndFormXmlCleanup = (xml) => {
  xml = xml.replace(/foalingdate/g, "foaling_date");
  xml = xml.replace(/tab_number/g, "tab_no");
  xml = xml.replace("<@_riding_weight/>", "");
  xml = xml.replace("<@_starting/>", "");
  xml = xml.replace(/<group>0<\/group>/g, "");
  // xml = xml.replace(/\&([^a])/g, "&amp;\1")
  return xml;
};
const resultsXmlCleanup = (xml) => {
  xml = xml.replace(/foalingdate/g, "foaling_date");
  xml = xml.replace(/tab_number/g, "tab_no");
  xml = xml.replace("<@_riding_weight/>", "");
  xml = xml.replace(/<group>0<\/group>/g, "");
  // xml = xml.replace(/\&([^a])/g, "&amp;\1")
  return xml;
};

const conditionSelect = (condition) => {
  var trackCond = "";
  switch (condition) {
    case "Y0":
      trackCond = "Synthetic";
      break;
    case "F1":
      trackCond = "Firm(1)";
      break;
    case "F2":
      trackCond = "Firm(2)";
      break;
    case "M1":
      trackCond = "Firm(1)";
      break;
    case "M2":
      trackCond = "Firm(2)";
      break;
    case "G3":
      trackCond = "Good(3)";
      break;
    case "G4":
      trackCond = "Good(4)";
      break;
    case "O5":
      trackCond = "Soft(5)";
      break;
    case "O6":
      trackCond = "Soft(6)";
      break;
    case "O7":
      trackCond = "Soft(7)";
      break;
    case "H8":
      trackCond = "Heavy(8)";
      break;
    case "H9":
      trackCond = "Heavy(9)";
      break;
    case "H10":
      trackCond = "Heavy(10)";
      break;
    case "H11":
      trackCond = "Heavy(11)";
      break;
    case "F0":
      trackCond = "Firm";
      break;
    case "M0":
      trackCond = "Firm";
      break;
    case "G0":
      trackCond = "Good";
      break;
    case "O0":
      trackCond = "Soft";
      break;
    case "S0":
      trackCond = "Soft";
      break;
    case "H0":
      trackCond = "Heavy";
      break;
    default:
      trackCond = condition;
      break;
  }
  return trackCond;
};

const marginSelect = (margin) => {
  var length = "";
  switch (margin) {
    case "Dead Heat":
      length = "DH";
      break;
    case "Nose":
      length = "NS";
      break;
    case "Short Half Head":
      length = "SHH";
      break;
    case "Short 1/2 Head":
      length = "SHH";
      break;
    case "Half Head":
      length = "HH";
      break;
    case "1/2 Head":
      length = "HH";
      break;
    case "Short Head":
      length = "SH";
      break;
    case "Head":
      length = "HD";
      break;
    case "Long Head":
      length = "LH";
      break;
    case "Half Neck":
      length = "HN";
      break;
    case "1/2 Neck":
      length = "HN";
      break;
    case "Short Neck":
      length = "SN";
      break;
    case "Neck":
      length = "NK";
      break;
    case "Long Neck":
      length = "LN";
      break;
    case "1/4 Len":
      length = "1/4";
      break;
    case "1/2 Len":
      length = "1/2";
      break;
    case "3/4 Len":
      length = "3/4";
      break;
    case "Distanced":
      length = "DS";
      break;
    default:
      length = margin;
      break;
  }
  return length;
};

const timeconvert = (time) => {
  var timeSplit = time.toString().split("");
  console.log(`cleanup time: ${time} ${timeSplit}`);
  var hour = parseInt(timeSplit[0].toString() + timeSplit[1].toString());
  var minute = parseInt(timeSplit[2].toString() + timeSplit[3].toString());
  console.log(`hour: ${hour} min: ${minute}`);
  if (hour > 12 && hour < 24) {
    return (
      (hour - 12).toString() +
      ":" +
      (minute > 9 ? minute.toString() : "0" + minute.toString()) +
      "pm"
    );
  } else if (hour == 24) {
    return (
      "00:" + (minute > 9 ? minute.toString() : "0" + minute.toString()) + "am"
    );
  } else if (hour == 12) {
    return (
      "12:" + (minute > 9 ? minute.toString() : "0" + minute.toString()) + "pm"
    );
  } else {
    return (
      hour.toString() +
      ":" +
      (minute > 9 ? minute.toString() : "0" + minute.toString()) +
      "am"
    );
  }
};

const getMarginValue = (value) => {
  var decValue = parseFloat(0.0);
  if (!value) return 0;
  if (value[0] === "0") {
    decValue = parseFloat(value) / 10;
    // console.log('getMarginValue',decValue)
    return decValue;
  }
  // console.log('getMarginValue',value)
  switch (value) {
    case "DH":
      decValue = value;
      break;
    case "NS":
      decValue = value;
      break;
    case "SHH":
      decValue = value;
      break;
    case "HH":
      decValue = value;
      break;
    case "SH":
      decValue = value;
      break;
    case "H":
      decValue = value;
      break;
    case "LH":
      decValue = value;
      break;
    case "HN":
      decValue = value;
      break;
    case "SN":
      decValue = value;
      break;
    case "NK":
      decValue = value;
      break;
    case "LN":
      decValue = value;
      break;
    case "DS":
      decValue = value;
      break;
    default:
      decValue = parseFloat(value);
      if (decValue > 100) decValue = decValue / 10;
      break;
  }
  // console.log('getMarginValue',decValue)
  return decValue;
};

const getMarginDecimalValue = (value) => {
  var decValue = parseFloat(0.0);
  if (!value) return 0;
  if (!value.toString().includes(".") && value[0] === "0") {
    decValue = parseFloat(value) / 10;
    // console.log('getMarginDecimalValue',decValue)
    return decValue;
  }
  // console.log('getMarginDecimalValue',value)
  var theVal = value.toString().toUpperCase();
  switch (theVal) {
    case "DH":
      decValue = 0;
      break;
    case "NS":
      decValue = 0.1;
      break;
    case "SHH":
      decValue = 0.1;
      break;
    case "HH":
      decValue = 0.1;
      break;
    case "SH":
      decValue = 0.1;
      break;
    case "SH HD":
      decValue = 0.1;
      break;
    case "SHD":
      decValue = 0.1;
      break;
    case "H":
      decValue = 0.2;
      break;
    case "HD":
      decValue = 0.2;
      break;
    case "LH":
      decValue = 0.2;
      break;
    case "HN":
      decValue = 0.2;
      break;
    case "SN":
      decValue = 0.2;
      break;
    case "SH NK":
      decValue = 0.2;
      break;
    case "NK":
      decValue = 0.3;
      break;
    case "LN":
      decValue = 0.4;
      break;
    case "DS":
      decValue = 99;
      break;
    default:
      decValue = parseFloat(value);
      if (decValue > 100) decValue = decValue / 10;
      break;
  }
  // Check if decValue is NaN and return 0 or any other default value
  if (isNaN(decValue)) {
    return 0;
  }
  // console.log('getMarginDecimalValue',decValue)
  return decValue;
};

const getMarginMap = (margin) => {
  var decValue = parseFloat(0.0);
  if (!margin) return 0;

  // console.log('getMarginMap',margin)
  var map = new Map([
    ["DH", "DH"],
    ["NS", "Ns"],
    ["SHH", "Sh1/2Hd"],
    ["SH1/2HD", "Sh1/2Hd"],
    ["HH", "Hf Hd"],
    ["HF HD", "Hf Hd"],
    ["SH", "Sh Hd"],
    ["SH HD", "Sh Hd"],
    ["H", "Hd"],
    ["HD", "Hd"],
    ["LH", "Lg hd"],
    ["LG HD", "Lg hd"],
    ["HN", "Hf Nk"],
    ["HF NK", "Hf Nk"],
    ["SN", "Sh Nk"],
    ["SH NK", "Sh Nk"],
    ["NK", "Nk"],
    ["LN", "Lg Nk"],
    ["LG NK", "Lg Nk"],
    ["001", "1/4"],
    ["002", "1/4"],
    ["003", "1/4"],
    ["004", "1/2"],
    ["005", "1/2"],
    ["006", "1/2"],
    ["007", "3/4"],
    ["008", "3/4"],
    ["009", "3/4"],
    ["0.1", "1/4"],
    ["0.2", "1/4"],
    ["0.3", "1/4"],
    ["0.4", "1/2"],
    ["0.5", "1/2"],
    ["0.6", "1/2"],
    ["0.7", "3/4"],
    ["0.8", "3/4"],
    ["0.9", "3/4"],
    [".1", "1/4"],
    [".2", "1/4"],
    [".3", "1/4"],
    [".4", "1/2"],
    [".5", "1/2"],
    [".6", "1/2"],
    [".7", "3/4"],
    [".8", "3/4"],
    [".9", "3/4"],
    [0.1, "1/4"],
    [0.2, "1/4"],
    [0.3, "1/4"],
    [0.4, "1/2"],
    [0.5, "1/2"],
    [0.6, "1/2"],
    [0.7, "3/4"],
    [0.8, "3/4"],
    [0.9, "3/4"],
    ["DS", "dist"],
  ]);

  let result = map.get(margin.toString().toUpperCase());
  if (result == undefined) {
    if (margin[0] === "0") {
      decValue = parseFloat(margin) / 10;
      // console.log('getMarginMap',decValue)
      return decValue;
    }
    decValue = parseFloat(margin);
    if (decValue > 100) decValue = decValue / 10;
    // console.log('getMarginMap',decValue)
    return decValue;
  } else {
    // console.log('getMarginMap',result)
    return result;
  }
};

const getAgeRestriction = (Id) => {
  var map = new Map([
    [1, "2yo"],
    [2, "2yo+"],
    [3, "2&3&4yo"],
    [4, "3yo"],
    [5, "3yo+"],
    [6, "4yo"],
    [7, "4yo+"],
    [8, "5yo"],
    [9, "5yo+"],
    [10, "6yo"],
    [11, "Aged"],
    [12, "2&3yo"],
    [13, "3&4yo"],
    [14, "3&4&5yo"],
    [15, "4&5yo"],
    [16, "4&5&6yo"],
    [17, "3&4&5&6"],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = "";
  return result;
};

const getSexRestriction = (Id) => {
  var map = new Map([
    [1, "Fillies"],
    [2, "Mares"],
    [3, "Colts"],
    [4, "Geldings"],
    [5, "Horses"],
    [6, "Fillies & Mares"],
    [7, "Colts & Horses"],
    [8, "Colts & Geldings"],
    [9, "Horses & Geldings"],
    [10, "Horses & Mares"],
    [11, "Colts & Fillies"],
    [12, "Colts, Horses & Geldings"],
    [13, "Fillies, Colts & Geldings"],
    [14, "Mares, Horses & Geldings"],
    [15, "Fillies, Mares, Colts & Horses"],
    [16, "Fillies, Mares, Colts & Geldings"],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = Id;
  return result;
};

const formatJockeyRestrictions = (rest) => {
  var map = new Map([
    ["A", "Apprentice Riders Only"],
    ["C", ""],
    ["E", "Eligible Riders Can Claim"],
    ["G", "Gentlemen Riders Only"],
    ["I", "Invited Riders"],
    ["L", "Lady Riders Only"],
    ["N", "Apprentices Cannot Claim"],
    ["X", "Eligible Riders Cannot Claim"],
    ["M", "Amateur Riders"],
    ["H", "Hurdle Jockeys Only"],
  ]);
  let result = map.get(rest);
  if (result == undefined) result = "";
  return result;
};

const getWeightType = (Id) => {
  var map = new Map([
    ["H", "Handicap"],
    ["W", "Weight For Age"],
    ["X", "Weight For Age With Penalties"],
    ["S", "Set Weight"],
    ["T", "Set Weight With Penalties"],
    ["U", "Set Weight With Penalties and Allowances"],
    ["P", "Special Weight"],
    ["C", "Catch Weight"],
    ["F", "Fly Weight"],
    ["A", "Set Weight With Allowances"],
    ["Q", "Quality"],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = Id;
  return result;
};
const getMeetStage = (Id) => {
  var map = new Map([
    ["Nominations", "N"],
    ["Weights", "W"],
    ["Acceptances", "A"],
    ["FinalFields", "A"],
    ["InterimResults", "R"],
    ["Results", "R"],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = Id;
  return result;
};

const getTrackCond = (Id) => {
  var map = new Map([
    ["M", "Firm"],
    ["G", "Good"],
    ["F", "Fast"],
    ["S", "Slow"],
    ["H", "Heavy"],
    ["O", "Soft"],
    ["A", "Sand"],
    ["R", "Fair"],
    ["D", "Dead"],
    ["V", "Very Heavy"],
    ["L", "Holding"],
    ["Y", "Synthetic"],
    ["Firm", "Firm"],
    ["Good", "Good"],
    ["Soft", "Soft"],
    ["Heavy", "Heavy"],
    ["Synthetic", "Synthetic"],
    ["Fast", "Fast"],
    ["Sand", "Sand"],
    ["Fair", "Fair"],
    ["Dead", "Dead"],
    ["Slow", "Slow"],
    ["Very Heavy", "Very Heavy"],
    ["Holding", "Holding"],
    ["Dirt", "Dirt"],
    ["Wet", "Wet"],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = "";
  return result;
};

const setFinishPosition = (Id) => {
  var map = new Map([
    [25, "FF"],
    ["25", "FF"],
    [26, "PU"],
    ["26", "PU"],
    [27, "FL"],
    ["27", "FL"],
    [28, "RO"],
    ["28", "RO"],
    [29, "DQ"],
    ["29", "DQ"],
    [30, "NP"],
    ["30", "NP"],
    [31, "LS"],
    ["31", "LS"],
    [32, "LR"],
    ["32", "LR"],
    [33, "SB"],
    ["33", "SB"],
    [34, "SC"],
    ["34", "SC"],
    [35, "BD"],
    ["35", "BD"],
    [36, "UN"],
    ["36", "UN"],
  ]);
  let result = map.get(Id);
  if (result == undefined) result = "UN";
  return result;
};

const formatStewards = (report) => {
  const stewards_options_1 = {
    "#": "Tightened for room",
    $: "Drifted back",
    "(": "Bucked",
    "/": "Disappointed for a run",
    1: "Struck interference",
    2: "Blundered",
    3: "Checked",
    4: "Eased",
    5: "Shifted out",
    6: "Carried wide",
    7: "Shifted in",
    8: "Hit rail",
    9: "Squeezed out",
    "=": "Crowded",
    "@": "Awkwardly placed",
    "^": "Restrained",
    A: "Shied",
    B: "Wide",
    I: "Blocked for run",
    J: "Eased",
    K: "Inconvenienced",
    M: "Laid in",
    N: "Laid out",
    O: "Hung in",
    P: "Hung out",
    Q: "Severely hampered",
    R: "Hampered",
    X: "Held up",
    Y: "Overraced",
    Z: "Weakened",
  };

  const stewards_options_2 = {
    "!": "near 50m ",
    "#": "near 250m ",
    $: "near 300m ",
    "%": "several times ",
    "&": "near 500m ",
    ")": "Near 900m",
    "*": "Near 700m",
    0: "during race",
    1: "near 100m ",
    2: "near 200m ",
    3: "near 400m ",
    4: "near 600m ",
    5: "near 800m ",
    6: "near 1000m ",
    7: "near 1200m ",
    8: "near 1400m ",
    9: "near 1600m ",
    "@": "near 150m ",
    A: "near 2000m ",
    B: "early on ",
    C: "near turn ",
    D: "in straight",
    E: "near post ",
    F: "at start ",
    G: "at crossing",
    H: "early stages ",
    J: "early, middle stages ",
    K: "concluding stages ",
    L: "on straightening",
    M: "middle stages ",
    T: "throughout ",
  };
  const stewards_options_3 = {
    "1N": "Bled from one nostril",
    "1P": "One paced",
    "2P": "Second - positive swab",
    "3P": "Third - positive swab",
    "3W": "Raced three wide",
    "4W": "Raced four wide",
    "5W": "Raced five wide",
    "B=": "Began awkwardly and lost ground",
    BO: "Choked down",
    BR: "Bumped rival",
    BS: "Bounded on jumping",
    BV: "Brushed rail",
    BX: "Struck barrier",
    C0: " ",
    C1: " ",
    C2: " ",
    C3: " ",
    C4: " ",
    C5: "Lost Ground",
    C6: " ",
    C7: "Raced Greenly",
    C8: "Broke Down",
    C9: "Disappointed for a run",
    CA: "Blocked for run",
    CB: " ",
    CC: "Pulled hard",
    CD: "Ran wide at turn",
    CE: "Hung in during race",
    CF: "Hung out during race",
    CG: "Ran off on turn",
    CH: "Clipped heels",
    CO: "Coughing",
    D0: " ",
    D1: "Rider's whip entangled",
    D2: " ",
    D3: "Reared at start",
    D4: "Left at start",
    D5: "Became unbalanced",
    D6: "Bucked at start",
    D7: "Began awkwardly",
    D8: "Tongue over bit",
    D9: "Failed due to track condition",
    DA: "Caused interference",
    DB: "Fractious in stalls",
    DC: "Backed on bar as gates opened",
    DD: "Swung sideways at start",
    DE: "Raced erratically",
    DF: "Slowly away",
    DI: "Ducked in",
    DO: "Ducked out",
    DP: "Promoted due to disqualification",
    DR: "Rider dropped rein",
    DV: "Distressed",
    E0: "Rider lost iron",
    E1: "Rider lost whip",
    E2: "Saddle slipped",
    E3: "Vet examination",
    E4: "Shifted ground",
    E5: "Head in adjoining stall",
    E6: "Struck head on barrier",
    E7: "Galloped on",
    E8: "Gear broke",
    E9: "Struck with another's whip",
    EA: "Bled",
    EB: "Fell",
    EC: "Lame",
    ED: "Injured",
    EE: "Lost a plate",
    EF: "Twisted a plate",
    EI: "Suffered Exercise-Induced Pulmonary Haemorrhage ",
    EJ: "Eased down",
    F0: " ",
    F1: " ",
    F2: "Ordered to trial",
    F3: "Shied at crossing",
    F4: "Rider told to use more care",
    F5: "Tightened for room",
    F6: "Swab taken by order of stewards",
    F7: "Inquiry into performance",
    F8: "Rider told to make more effort to improve position",
    F9: "Rider told to use more vigour",
    FA: "Rider charged with careless riding",
    FB: "Protest lodged, upheld",
    FC: "Protest lodged, dismissed",
    FD: "Protested against, upheld",
    FE: "Protested against, dismissed",
    FF: "Read full report",
    FG: "Failed to handle going",
    FI: "Floating incident - passed fit",
    FR: "Raced flat",
    FS: "Broke through barriers, cleared to start",
    GA: "Proved difficult to ride out",
    GB: "Difficult to load",
    GC: "Passed fit at barrier",
    GD: "Stewards queried run",
    GF: "Impeded by a fallen horse",
    GI: "Gear issue",
    HH: "Contacted hurdle",
    HS: "Contacted steeple",
    HU: "Got its head up",
    II: "Eye injury",
    KR: "Keen",
    L1: "Lame 1/5",
    L2: "Lame 2/5",
    L3: "Lame 3/5",
    L4: "Lame 4/5",
    L5: "Lame 5/5",
    "L=": "Loading incident - passed fit",
    LI: "Rider lost both irons",
    LP: "Lost plates",
    MI: "Minor injury",
    MS: "Muscle strain",
    MY: "Mounting yard incident - passed fit",
    NI: "Mouth injury",
    NR: "Failed to respond to riding",
    NS: "Replated at barriers",
    NX: "Not fully tested",
    PP: "Raced below expectations",
    RP: "Reluctant to proceed to barriers",
    RR: "Resented racing between runners",
    RS: "Sore",
    RW: "Rider concerned with action",
    S1: "Slowly away (1L)",
    S2: "Dwelt at start (2L)",
    S3: "Broke poorly (3L)",
    S4: "Commenced slowly (4L)",
    S5: "Blew start (5L)",
    S6: "Blew start (6L)",
    SL: "Second - rider weighed in light",
    SS: "Slipped at start",
    SX: "Bombed the start (7L+)",
    T1: "Fractious in barriers",
    TA: "Tempo against",
    TB: "Taken back from wide barrier",
    TF: "Too firm",
    TI: "Tendon injury",
    TL: "Third - rider weighed in light",
    TO: "Tailed off",
    TP: "Travelled poorly",
    TQ: "Tactics queried",
    TS: "Travelled well",
    TW: "Too wet",
    U1: "Pre race incident",
    U2: "Shin sore",
    U3: "Warning issued",
    U4: "Vet Certificate required",
    UL: "Unplaced - rider weighed in light",
    UP: "Under pressure turn",
    V0: "Bled second time - life time ban",
    V1: "Vetted - no abnormalities",
    V2: "Vetted - no abnormalities, ordered to trial",
    V3: "Cardiac arrhythmia",
    V4: "Elevated heart rate",
    V5: "Respiratory issues",
    V6: "Lacerations",
    V7: "In season",
    V8: "Poor recovery",
    V9: "Bled first time - three month ban",
    VA: "Heat stress",
    VB: "Blood in trachea",
    VC: "Irregular heart rate",
    VD: "Thumps",
    VE: "Tied up",
    W1: "Warning - barrier manners",
    W2: "Warning - uncompetitive",
    W3: "Warning - racing manners",
    WC: "Whip breach - rider charged",
    WL: "Winner - rider weighed in light",
    WN: "Weakened noticeably",
    WP: "Winner - positive swab",
    WR: "Whip breach - relegated",
    WS: "Wayward under pressure in straight",
    WT: "Wide throughout",
    WW: "Raced wide with cover",
    WY: "Raced wide without cover",
    XP: "Unplaced - positive swab",
  };

  var textReport = "";

  if (stewards_options_3[report]) {
    textReport = stewards_options_3[report];
  } else if (report.length == 2) {
    var splitAgain = report.split("");
    textReport = (
      (stewards_options_1[splitAgain[0]] ?? "") +
      " " +
      (stewards_options_2[splitAgain[1]] ?? "")
    ).trim();
  } else {
    return report;
  }

  return textReport;
};

const convertOdds = (num) => {
  var map = new Map([
    [1.05, "1/20"],
    [1.06, "1/16"],
    [1.07, "1/14"],
    [1.08, "1/12"],
    [1.09, "1/11"],
    [1.1, "1/10"],
    [1.12, "1/8"],
    [1.14, "1/7"],
    [1.16, "1/6"],
    [1.18, "2/11"],
    [1.2, "1/5"],
    [1.22, "2/9"],
    [1.24, "1/4"],
    [1.26, "4/15"],
    [1.28, "2/7"],
    [1.3, "4/13"],
    [1.35, "4/11"],
    [1.4, "2/5"],
    [1.45, "4/9"],
    [1.5, "1/2"],
    [1.55, "8/15"],
    [1.6, "4/7"],
    [1.65, "4/6"],
    [1.7, "8/11"],
    [1.75, "8/11"],
    [1.8, "4/5"],
    [1.85, "9/10"],
    [1.9, "9/10"],
    [1.95, "9/10"],
    [2, "1/1"],
    [2.05, "10/9"],
    [2.1, "10/9"],
    [2.15, "10/9"],
    [2.2, "5/4"],
    [2.25, "5/4"],
    [2.3, "5/4"],
    [2.35, "11/8"],
    [2.4, "11/8"],
    [2.45, "6/4"],
    [2.5, "6/4"],
    [2.6, "13/8"],
    [2.7, "7/4"],
    [2.8, "7/4"],
    [2.9, "15/8"],
    [3, "2/1"],
    [3.1, "2/1"],
    [3.2, "9/4"],
    [3.3, "9/4"],
    [3.4, "5/2"],
    [3.5, "5/2"],
    [3.6, "5/2"],
    [3.7, "11/4"],
    [3.8, "11/4"],
    [3.9, "3/1"],
    [4, "3/1"],
    [4.2, "13/4"],
    [4.4, "7/2"],
    [4.6, "7/2"],
    [4.8, "15/4"],
    [5, "4/1"],
    [5.5, "9/2"],
    [6, "5/1"],
    [6.5, "11/2"],
    [7, "6/1"],
    [7.5, "13/2"],
    [8, "7/1"],
    [8.5, "15/2"],
    [9, "8/1"],
    [9.5, "8/1"],
    [10, "9/1"],
    [11, "10/1"],
    [12, "11/1"],
    [13, "12/1"],
    [14, "12/1"],
    [15, "14/1"],
    [16, "15/1"],
    [17, "16/1"],
    [18, "16/1"],
    [19, "20/1"],
    [20, "20/1"],
    [21, "20/1"],
    [26, "25/1"],
    [31, "30/1"],
    [41, "40/1"],
    [51, "50/1"],
    [61, "60/1"],
    [71, "66/1"],
    [81, "80/1"],
    [91, "100/1"],
    [101, "100/1"],
    [151, "150/1"],
    [201, "200/1"],
    [301, "300/1"],
    [401, "400/1"],
    [501, "500/1"],
  ]);
  let result = map.get(num);
  while (result == undefined) {
    if (num < 1.05) num = 1.05;
    else if (num < 1.1) num = Math.round(num * 100) / 100;
    else if (num < 1.3) num = Math.round(num * 50) / 50;
    else if (num < 2.5) num = Math.round(num * 20) / 20;
    else if (num < 4) num = Math.round(num * 10) / 10;
    else if (num < 5) num = Math.round(num * 5) / 5;
    else if (num < 10) num = Math.round(num * 2) / 2;
    else if (num < 21) num = Math.round(num);
    else if (num < 31) num = Math.round(num / 5) * 5 + 1;
    else if (num < 101) num = Math.round(num / 10) * 10 + 1;
    else if (num < 201) num = Math.round(num / 50) * 50 + 1;
    else if (num < 501) num = Math.round(num / 100) * 100 + 1;
    else if (num > 501) num = 501;
    result = map.get(num);
    if (result == undefined) return "";
  }
  return result;
};

const cleverCapFstLtr = (str = "") => {
  //console.log(`cleanupname: ${str}`)
  if (!str) return "";
  str = str
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
  str = str
    .split("'")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join("'");
  str = str
    .split(".")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(".");
  str = str
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join("-");
  str = str
    .split("Mc")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join("Mc");
  str = str
    .split("&")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join("&");
  str = str
    .split("(")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join("(");
  str = str.replace(/('S |'S$)/g, "'s ");
  str = str.replace(/('T |'T$)/g, "'t ");
  return str.trim();
};

const capHorseName = (str = "") => {
  //console.log(`cleanupname: ${str}`)
  if (!str) return "";
  str = str
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
  return str.trim();
};

const formatTime = (time) => {
  if (!/\d/.test(time)) return "0:00.00";

  // Check if the time string has a one colon and one full colon
  if (time.split(":").length === 2 && time.split(".").length === 2) {
    // Split the time string into parts based on the colon and decimal point
    const parts = time.split(/[:.]/);

    // Extract the minutes, seconds, and milliseconds parts
    const minutes = parseInt(parts[0], 10);
    const seconds = parts[1].padStart(2, "0");
    const milliseconds = parts[2].padEnd(2, "0").slice(0, 2);

    // Return the formatted time string
    return `${minutes}:${seconds}.${milliseconds}`;
  }

  if (!time.includes(".")) {
    var decimal = parseFloat(time.replace(/.+?:([0-9]+)$/g, "0.$1")).toFixed(2);
    time = time.replace(/:[0-9]+$/g, "");
    time = time.replace(/^.*([0-9]:[0-9]{2})[^\.]*$/g, "$1");
    time = time + decimal.toString().slice(1);
  } else {
    var decimal = parseFloat(time.replace(/.+?\.([0-9]+)$/g, "0.$1")).toFixed(
      2
    );
    time = time.replace(/\.[0-9]+$/g, "");
    time = time.replace(/^.*([0-9]:[0-9]{2})[^\.]*$/g, "$1");
    time = time + decimal.toString().slice(1);
  }
  // Count the number of dots in the time string
  const dotCount = (time.match(/\./g) || []).length;

  // Check if the time string has exactly two dots
  if (dotCount >= 2) {
    let parts = time.split(".");
    if (parts.length > 3) {
      // Assume the last two parts are seconds and milliseconds
      const milliseconds = parts.pop().padEnd(2, "0").slice(0, 2);
      const seconds = parts.pop().padStart(2, "0");
      // Rejoin the remaining parts as minutes (if more than 1 part, they were split incorrectly by extra dots)
      const minutes = parseInt(parts.join(""), 10);
      return `${minutes}:${seconds}.${milliseconds}`;
    } else if (parts.length === 3) {
      // Correctly formatted time with minutes, seconds, and milliseconds
      const minutes = parseInt(parts[0], 10);
      const seconds = parts[1].padStart(2, "0");
      const milliseconds = parts[2].padEnd(2, "0").slice(0, 2);
      return `${minutes}:${seconds}.${milliseconds}`;
    } else if (parts.length === 2) {
      // Only seconds and milliseconds, assume 0 minutes
      const seconds = parts[0].padStart(2, "0");
      const milliseconds = parts[1].padEnd(2, "0").slice(0, 2);
      return `0:${seconds}.${milliseconds}`;
    } else {
      // Fallback for unexpected formats, returning a basic format
      return "0:00.00";
    }
  }

  return time;
};

const cleanupTextForXml = (text) => {
  text = text.toString();
  text = text.replace(/ \& /g, " &amp; ");
  text = text.replace(/"/g, "&#34;");
  text = text.replace(/</g, "&lt;");
  text = text.replace(/>/g, "&gt;");
  text = text.replace(/'/g, "&#39;");
  return text;
};

const fixNZTrackNames = (name) => {
  var map = new Map([["Pukekohe", "Pukekohe Park"]]);
  let result = map.get(name);
  if (result == undefined) result = name;
  return result;
};

module.exports = {
  cleverCapFstLtr,
  fieldsFilter,
  resultsFilter,
  formFilter,
  gearFilter,
  scratchingsFilter,
  fieldsAndFormXmlCleanup,
  resultsXmlCleanup,
};
