const { SQSClient, SendMessageCommand } = require('@aws-sdk/client-sqs');

// Initialize AWS client
const sqsClient = new SQSClient();
const AWS = require("aws-sdk");
const sqs = new AWS.SQS();
/**
 * Process daily polling and send to SQS
 */
async function processDailyPolling() {
  try {
    // Get yesterday's date for processing
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const startOfYesterday = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
    const endOfYesterday = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59, 999);
    
    console.log(`Preparing daily polling data for: ${startOfYesterday.toISOString()} to ${endOfYesterday.toISOString()}`);
    
    // Send message to SQS for database processing
    const messagePayload = {
      action: 'dailyPolling',
      dateRange: {
        startDate: startOfYesterday.toISOString(),
        endDate: endOfYesterday.toISOString()
      }
    };
    
    const queueResponse = await sendToSQSQueue(messagePayload);
    console.log('Daily polling request sent to queue:', queueResponse.MessageId);
    
    return {
      success: true,
      message: 'Daily polling request sent to queue for processing',
      messageId: queueResponse.MessageId
    };
  } catch (error) {
    console.error('Error in sending daily polling request:', error);
    throw error;
  }
}

/**
 * Send message to SQS queue
 */
async function sendToSQSQueue(messagePayload) {
  try {
    let queueUrl = null;
      const response = await sqs
        .getQueueUrl({
          QueueName: 'postgres-lambda-queue',
        })
        .promise();

    queueUrl = response.QueueUrl;
    console.log(`Found queue URL: ${queueUrl}`);
    const command = new SendMessageCommand({
      QueueUrl: queueUrl,
      MessageBody: JSON.stringify(messagePayload),
      MessageAttributes: {
        ActionType: {
          DataType: 'String',
          StringValue: messagePayload.action
        }
      }
    });

    return await sqsClient.send(command);
  } catch (error) {
    console.error('Error sending message to SQS:', error);
    throw error;
  }
}

/**
 * Process subscription data and send to SQS
 */
async function processSubscriptionData(body) {
  try {
    const deliveryInfo = body.pricingInfo.deliveryInfo;
    const clientEmail = deliveryInfo.clientId;
    const customer_name = deliveryInfo.customer_name;
    const client_type = deliveryInfo.client_type;
    // Create message payload for database processing
    const messagePayload = {
      action: 'processSubscription',
      subscriptionData: {
        clientInfo: {
          clientEmail: clientEmail,
          customer_name: customer_name,
          client_type: client_type,
        },
        meetingInfo: {
          meetingDate: new Date().toISOString(),
          trackCode: deliveryInfo.trackCode || 'Unknown Track',
          stageName: deliveryInfo.stage || 'Unknown Stage',
          originalDeliveryInfo: deliveryInfo
        },
        fileInfo: {
          fileType: deliveryInfo.fileType || 'Unknown Type',
          fileName: deliveryInfo.fileName || 'Unknown File',
          s3Path: deliveryInfo.destinationPath || '',
          calculatedCost: 1.00, // Default cost placeholder
          pricingDetails: body.pricingInfo
        }
      }
    };
    
    // Send to SQS queue for database processing
    const queueResponse = await sendToSQSQueue(messagePayload);
    console.log('Subscription data sent to queue for processing:', queueResponse.MessageId);
    
    return {
      success: true,
      message: 'Subscription data sent to queue for processing',
      messageId: queueResponse.MessageId
    };
  } catch (error) {
    console.error('Error in preparing subscription data:', error);
    throw error;
  }
}


/**
 * Lambda handler function
 */
exports.handler = async (event) => {
  // Handle direct invocation from scheduled rule
  if (event.action === 'dailyPolling') {
    console.log('Preparing daily polling request for queue...');
    try {
      const result = await processDailyPolling();
      console.log('Daily polling request sent:', result);
      return {
        statusCode: 200,
        body: JSON.stringify(result)
      };
    } catch (error) {
      console.error('Error preparing daily polling request:', error);
      return {
        statusCode: 500,
        body: JSON.stringify({ error: error.message })
      };
    }
  }
  
  // Process each SQS message by forwarding to the CDK stack
  if (event.Records) {
    for (const record of event.Records) {
      const body = JSON.parse(record.body);
      console.log('Processing message:', body);
      
      try {
        // Process and forward subscription messages
        if (body.action === 'processSubscription' && body.pricingInfo && body.pricingInfo.deliveryInfo) {
          console.log('Forwarding subscription data for processing...');
          const result = await processSubscriptionData(body);
          console.log('Subscription data forwarded:', result);
        } else {
          // Forward other messages directly to the queue
          const messagePayload = {
            action: 'forwardedMessage',
            originalMessage: body
          };
          const queueResponse = await sendToSQSQueue(messagePayload);
          console.log('Message forwarded to database queue:', queueResponse.MessageId);
        }
      } catch (error) {
        console.error('Error forwarding message:', error);
        throw error; // This will cause SQS to retry the message
      }
    }
  }
  
  return {
    statusCode: 200,
    body: 'Processing complete'
  };
};