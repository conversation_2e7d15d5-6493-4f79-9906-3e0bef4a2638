{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.8.2\",\"stackType\":\"function-Lambda\",\"metadata\":{}}", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}, "functioncentaurappCentaurAppCommonLayerArn": {"Type": "String", "Default": "functioncentaurappCentaurAppCommonLayerArn"}, "awsLocal": {"Type": "String"}, "centaurSecrets": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"ChargebeeQueue": {"Type": "AWS::SQS::Queue", "Properties": {"QueueName": {"Fn::If": ["ShouldNotCreateEnvResources", "Mr<PERSON>enChar<PERSON><PERSON><PERSON><PERSON><PERSON>", {"Fn::Join": ["", ["Mr<PERSON>enChar<PERSON>bee<PERSON><PERSON>ue-", {"Ref": "env"}]]}]}, "VisibilityTimeout": 300, "RedrivePolicy": {"deadLetterTargetArn": {"Fn::GetAtt": ["ChargebeeDLQ", "<PERSON><PERSON>"]}, "maxReceiveCount": 5}}}, "ChargebeeDLQ": {"Type": "AWS::SQS::Queue", "Properties": {"QueueName": {"Fn::If": ["ShouldNotCreateEnvResources", "MrCenChargebeeDLQ", {"Fn::Join": ["", ["MrCenChargebeeDLQ-", {"Ref": "env"}]]}]}, "MessageRetentionPeriod": 1209600}}, "LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "MrCenChargebeeAPI", {"Fn::Join": ["", ["MrCenChargebeeAPI", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "QUEUE_URL": {"Ref": "ChargebeeQueue"}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs18.x", "Layers": [{"Ref": "functioncentaurappCentaurAppCommonLayerArn"}], "VpcConfig": {"SecurityGroupIds": ["{{resolve:ssm:centaurLambdaDbSecurityGroup}}"], "SubnetIds": ["{{resolve:ssm:centaurVpcAppSubnet0}}", "{{resolve:ssm:centaurVpcAppSubnet1}}", "{{resolve:ssm:centaurVpcAppSubnet2}}"]}, "Timeout": 250, "MemorySize": "2048"}}, "LambdaSQSEventSourceMapping": {"Type": "AWS::Lambda::EventSourceMapping", "Properties": {"BatchSize": 10, "Enabled": true, "EventSourceArn": {"Fn::GetAtt": ["ChargebeeQueue", "<PERSON><PERSON>"]}, "FunctionName": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "centaurappLambdaRole0d285242", {"Fn::Join": ["", ["centaurappLambdaRole0d285242", "-", {"Ref": "env"}]]}]}, "Path": "/", "ManagedPolicyArns": ["arn:aws:iam::aws:policy/AmazonS3FullAccess", "arn:aws:iam::aws:policy/AmazonDocDBFullAccess", "arn:aws:iam::aws:policy/SecretsManagerReadWrite", "arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess", "arn:aws:iam::aws:policy/AmazonSQSFullAccess", "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole", "arn:aws:iam::aws:policy/AWSStepFunctionsFullAccess"], "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}, {"Effect": "Allow", "Action": ["lambda:InvokeAsync", "lambda:InvokeFunction"], "Resource": ["*"]}, {"Effect": "Allow", "Action": ["sqs:ReceiveMessage", "sqs:DeleteMessage", "sqs:Get<PERSON>ueueAttributes", "sqs:ChangeMessageVisibility"], "Resource": {"Fn::GetAtt": ["ChargebeeQueue", "<PERSON><PERSON>"]}}]}}}, "ChargebeePollingRule": {"Type": "AWS::Events::Rule", "Properties": {"Description": "Triggers Lambda function for daily Chargebee usage processing", "ScheduleExpression": "cron(5 0 * * ? *)", "State": "ENABLED", "Targets": [{"Arn": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}, "Id": "ChargebeePollingTarget", "Input": "{\"action\": \"dailyPolling\"}"}]}}, "ChargebeePollingPermission": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Ref": "LambdaFunction"}, "Principal": "events.amazonaws.com", "SourceArn": {"Fn::GetAtt": ["ChargebeePollingRule", "<PERSON><PERSON>"]}}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}, "QueueURL": {"Description": "URL of the SQS Queue", "Value": {"Ref": "ChargebeeQueue"}, "Export": {"Name": {"Fn::Join": [":", [{"Ref": "env"}, "ChargebeeQueueURL"]]}}}, "QueueARN": {"Description": "ARN of the SQS Queue", "Value": {"Fn::GetAtt": ["ChargebeeQueue", "<PERSON><PERSON>"]}, "Export": {"Name": {"Fn::Join": [":", [{"Ref": "env"}, "ChargebeeQueueARN"]]}}}, "QueueName": {"Description": "Name of the SQS Queue", "Value": {"Fn::GetAtt": ["ChargebeeQueue", "QueueName"]}}}}