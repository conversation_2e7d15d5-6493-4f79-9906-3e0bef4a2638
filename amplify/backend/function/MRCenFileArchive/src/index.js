const AWS = require('aws-sdk');
const s3 = new AWS.S3();

exports.handler = async (event, context) => {
    
    // Parse the SNS message to get the S3 event
    const snsMessage = JSON.parse(event.Records[0].Sns.Message);
    const key = snsMessage.Records[0].s3.object.key;
    const sourceBucket = snsMessage.Records[0].s3.bucket.name;
    const env = process.env.ENV;
    const destinationBucket = `mr-cen-file-storage-${env}`;
    
    // Extract the filename from the key (assuming 'unprocessed/' prefix)
    const filename = key.replace('unprocessed/', '');

     // Extract the date from the key
     let dateMatch = key.match(/unprocessed\/(\d{4})(\d{2})(\d{2})_/);
     if (!dateMatch) dateMatch = key.match(/unprocessed\/Ver4_(\d{4})_(\d{2})_(\d{2})_/);
     if (!dateMatch) {
        console.error('Date not found in the key:', key);
         return;
     }
     const year = dateMatch[1];
     const month = dateMatch[2];
     const day = dateMatch[3];
     const datePath = `${year}-${month}-${day}`;
     
     // Define the new destination key
     const date = Math.floor(new Date(snsMessage.Records[0].eventTime).getTime() / 1000);
     var filenameSplit = filename.split(".")
     var filenameExt = filenameSplit[filenameSplit.length - 1]
     var filenameRecon = filename.replace(/(\.xml|\.json)/g, '') + "_" + date + "." + filenameExt
     const destinationKey = `archived/${datePath}/${filenameRecon}`;
    
    const copySource = encodeURIComponent(sourceBucket + '/' + key);
    
    try {
        const response = await s3.copyObject({
            CopySource: copySource,
            Bucket: destinationBucket,
            Key: destinationKey
        }).promise();
        console.log(`File copied successfully. Destination key: ${destinationKey} ${JSON.stringify(response, null, 2)}`);
        
    } catch (error) {
        console.log("Error copying the file to the destination bucket:", error.message);
    }
};
