jest.mock('./db');
const {writeDeliveryLog} = require("./deliveryLogger");
const {delivery_logs} = require('../models/delivery_logs');

const response1 = require('./__mocks__/deliveryLogger/response1.json');
jest.mock('../models/delivery_logs');
jest.mock('./dbHelpers');

describe('writeDeliveryLog', () => {
    beforeEach(() => {
        jest.restoreAllMocks();
    });

    test('Should successfully receive the correct writeDeliveryLog payload', async () => {
        const isDateValid = dateString => {
            if (new Date(dateString) !== "Invalid Date" && !isNaN(new Date(dateString))) {
                return dateString === new Date(dateString).toISOString();
            }
            return false
        }

        delivery_logs.mockImplementation(functionParameters => {
            expect(Object.keys(functionParameters).length).toEqual(8);
            expect(response1.details.sourceBucketName).toEqual(functionParameters.details.sourceBucketName);
            expect(response1.details.sourceObjectKey).toEqual(functionParameters.details.sourceObjectKey);
            expect(response1.details.destinationBucketName).toEqual(functionParameters.details.destinationBucketName);
            expect(response1.details.status).toEqual(functionParameters.details.status);
            expect(response1.lambdaFunctionName).toEqual(functionParameters.lambdaFunctionName);
            expect(response1.cloudWatchLogGroupName).toEqual(functionParameters.cloudWatchLogGroupName);
            expect(response1.cloudWatchLogStreamName).toEqual(functionParameters.cloudWatchLogStreamName);
            expect(response1.amznTraceIdRoot).toEqual(functionParameters.amznTraceIdRoot);
            expect(response1.amznTraceIdParent).toEqual(functionParameters.amznTraceIdParent);
            expect(response1.amznTraceIdSampled).toEqual(functionParameters.amznTraceIdSampled);
            expect(isDateValid(response1.createdAt)).toBeTruthy()
            expect(isDateValid(functionParameters.createdAt.toISOString())).toBeTruthy()
        })

        await writeDeliveryLog({
            sourceBucketName: 'event.s3.source.bucketName',
            sourceObjectKey: 'event.s3.source.objectKey',
            destinationBucketName: 'event.s3.destination.bucketName',
            status: 'STARTING_GET_DELIVERY_MANIFEST',
        })
    })
})
