const mongoose = require("mongoose");
const {getSecrets, generateConnectionString} = require('./helper');

mongoose.set('debug', false);
mongoose.set('strictQuery', true)

const connect = async () => {
    try {
        const conn = mongoose.connection;
        let connStatus = 'disconnected';
        conn.on('connected', function () {
            console.log('Database connection connected successfully');
            connStatus = 'connected';
        });
        conn.on('disconnected', function () {
            console.log('Database connection close successfully');
            connStatus = 'disconnected';
        })
        conn.on('error', console.error.bind(console, 'Connection error:'));

        if (connStatus === 'disconnected') {
            if (process.env.LOCAL_DATABASE === 'true') {
                const DB_USERNAME = "root";
                const DB_PASSWORD = "example";
                const DB_NAME = "local-centaur";

                const DB_URL = "127.0.0.1"
                const CONNECTION_STRING = "mongodb://" + DB_USERNAME + ":" + DB_PASSWORD + "@" + DB_URL + "/" + DB_NAME

                await mongoose.connect(
                    CONNECTION_STRING,
                    {
                        directConnection: true,
                        ssl: false
                    }
                )
            } else {
                const secretDetails = await getSecrets(process.env.centaurSecrets)
                const connDetails = await generateConnectionString(secretDetails)
                await mongoose.connect(connDetails.CONNECTION_STRING, connDetails.PARAMETERS)
            }
        }
    } catch (e) {
        console.error(e)
    }
}


module.exports = {
    connect,
}
