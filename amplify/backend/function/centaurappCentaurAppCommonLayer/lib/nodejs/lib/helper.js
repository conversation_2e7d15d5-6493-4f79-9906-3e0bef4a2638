const util = require('util')
const {SecretsManagerClient, GetSecretValueCommand} = require("@aws-sdk/client-secrets-manager");
const {tracer} = require('./lambdaPowertools');
const moment = require('moment');


const inspectObject = async (jsonObject) => {
  console.log(
    util.inspect(
      jsonObject,
      { showHidden: false, depth: null, colors: true }
    )
  )
}


const getSecrets = async (secretName) => {
  const client = tracer.captureAWSv3Client(new SecretsManagerClient({}));
  var params = {
      SecretId: secretName,
  };
  const command = new GetSecretValueCommand(params);
  const fetchSecretString = await client.send(command)
  var aws_secrets = JSON.parse(fetchSecretString.SecretString)
  return aws_secrets
}


const generateConnectionString = async (aws_secrets, env) => {
  var DB_USERNAME = aws_secrets.DB_USERNAME
  var DB_PASSWORD = aws_secrets.DB_PASSWORD

  var DB_NAME = aws_secrets.DB_NAME
  var cert_path = __dirname + '/../cert/global-bundle.pem'

  if (env == "docker") {
      var DB_URL = "host.docker.internal:27000"
      var CONNECTION_STRING = "mongodb://" + DB_USERNAME + ":" + DB_PASSWORD + "@" + DB_URL + "/" + DB_NAME + "?tls=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
  } else if (env == "localhost" ) {
      var DB_URL = "127.0.0.1:27000"
      var CONNECTION_STRING = "mongodb://" + DB_USERNAME + ":" + DB_PASSWORD + "@" + DB_URL + "/" + DB_NAME + "?tls=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
  } else {
      var DB_URL = aws_secrets.DB_URL
      var CONNECTION_STRING = "mongodb://" + DB_USERNAME + ":" + DB_PASSWORD + "@" + DB_URL + "/" + DB_NAME + "?tls=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
  }

  var connDetails = {
      CONNECTION_STRING: CONNECTION_STRING,
      PARAMETERS: {
          directConnection: true,
          ssl: true,
          tlsCAFile: cert_path,
          sslValidate: false
      }
  }

  return connDetails
}

// pass in as many dates as available, eg:
// var ups = helper.calculateUPs(
//   formitem.meeting_date,
//   (horseForm[i+1] ? horseForm[i+1].meeting_date : 0 ),
//   (horseForm[i+2] ? horseForm[i+2].meeting_date : 0 ),
//   (horseForm[i+3] ? horseForm[i+3].meeting_date : 0 ),
//   (horseForm[i+4] ? horseForm[i+4].meeting_date : 0 )
// )
// returns a number from 1-4, or 0 if more than four (never use an 'up' stat after 4)

const calculateUPs = (run_1,run_2,run_3,run_4,run_5) => {
  var spell = 84
  var one = moment(run_1)
  if (run_2 == 0) return 1
  var two = moment(run_2)
  var gap = one.diff(two, 'days')
  if (gap >= spell) return 1
  if (run_3 == 0) return 2
  var three = moment(run_3)
  gap = two.diff(three, 'days')
  if (gap >= spell) return 2
  if (run_4 == 0) return 3
  var four = moment(run_4)
  gap = three.diff(four, 'days')
  if (gap >= spell) return 3
  if (run_5 == 0) return 4
  var five = moment(run_5)
  gap = four.diff(five, 'days')
  if (gap >= spell) return 4
  return 0
}

module.exports = {
    getSecrets,
    generateConnectionString,
    inspectObject,
    calculateUPs
}
