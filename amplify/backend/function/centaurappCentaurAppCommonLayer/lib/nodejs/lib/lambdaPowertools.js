const serviceName = process.env.AWS_LAMBDA_FUNCTION_NAME || 'LambdaPowerTools-env';
/** Use lambda powertools logging framework
 * https://awslabs.github.io/aws-lambda-powertools-typescript/latest/core/logger/
 */
const { Logger } = require('@aws-lambda-powertools/logger');
/** @param {Logger} logger */
const logger = new Logger({
    serviceName,
    logLevel: 'info',
    logEvent: true,
});

/** Use lambda powertools trace framework
 * https://awslabs.github.io/aws-lambda-powertools-typescript/latest/core/tracer/
 *
 * Also requires active tracing to be enabled on the lambda function and policy
 *  that allows sending of traces to X-Ray
 */
const { Tracer } = require('@aws-lambda-powertools/tracer');
/** @param {Tracer} tracer */
const tracer = new Tracer({
    serviceName,
    enabled: true,
});

const initTracer = () => {
    const segment = tracer.getSegment(); // This is the facade segment (the one that is created by AWS Lambda)
    // Create subsegment for the function & set it as active
    const subsegment = segment.addNewSubsegment(`## ${process.env._HANDLER}`);
    tracer.setSegment(subsegment);

    // Annotate the subsegment with the cold start & serviceName
    tracer.annotateColdStart();
    tracer.addServiceNameAnnotation();
}

module.exports = {
    logger,
    tracer,
    initTracer,
}

