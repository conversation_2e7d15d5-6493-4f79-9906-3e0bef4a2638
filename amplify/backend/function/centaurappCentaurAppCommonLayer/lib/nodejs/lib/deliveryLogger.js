const {connect} = require('./db');
const {delivery_logs} = require('../models/delivery_logs');
const {logger} = require('./lambdaPowertools');

const writeDeliveryLog = async details => {
    await connect();
    try {
        const getTraceIdMeta = () => {
            const traceId = process.env._X_AMZN_TRACE_ID || 'Root=1-63f7063a-3fa1b425e12f296fa4f456c8;Parent=61cda033b5bfebc6;Sampled=1';
            const [amznTraceIdRoot, amznTraceIdParent, amznTraceIdSampled] = traceId.split(';');

            return {
                amznTraceIdRoot,
                amznTraceIdParent,
                amznTraceIdSampled
            }
        }
        const logData = {
            details,
            lambdaFunctionName: process.env.AWS_LAMBDA_FUNCTION_NAME || 'MrCenLambdaGetDeliveryManifest-env',
            cloudWatchLogGroupName: process.env.AWS_LAMBDA_LOG_GROUP_NAME || '/aws/lambda/MrCenLambdaGetDeliveryManifest-env',
            cloudWatchLogStreamName: process.env.AWS_LAMBDA_LOG_STREAM_NAME || '2023/02/23/[$LATEST]XXXXXX5c7062446c799c7c4a79dd87163',
            ...getTraceIdMeta(),
            createdAt: new Date(),
        }

        const deliveryLog = new delivery_logs(logData);

        await deliveryLog.save();
    } catch (err) {
        logger.error('Error writing to delivery_log collection', err);
        throw err;
    }
}

module.exports = {
    writeDeliveryLog
}
