const {connect} = require('./db');
const {clients} = require('../models/clients');
const {tracks} = require('../models/tracks');

const getTracksData = async (TRK_TRACK_3CHAR_ABBREV) => {
    await connect();
    try {
        return tracks.findOne({
            TRK_TRACK_3CHAR_ABBREV
        });
    } catch (e) {
        console.error(e);
        return [];
    }
}

const getClientsData = async (trackCode, fileType, stage, getClassification, getRegion) => {
    await connect();
    const tracksData = await getTracksData(trackCode);
    console.log(stage)
    try {
        return clients.find({
            status: true,
            region_perms: getRegion(tracksData?.TRK_STATE_OF_TRACK),
            classifications: getClassification(tracksData?.TRK_LOCATION),
            files_access: fileType,
            stage_perms: stage
        })
    } catch (e) {
        console.error(e);
        return [];
    }
}

module.exports = {
    getClientsData,
}
