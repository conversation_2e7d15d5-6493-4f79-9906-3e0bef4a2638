const mongoose = require("mongoose")
const moment = require('moment');


const formIndexSchema = new mongoose.Schema({
    _id: Object,
    meeting_id: String,
    event_id: Number,
    race_no: Number,
    horse_id: Number,
    meeting_date: Date,
    trainer_id: Number,
    jockey_id: Number,
    finish_pos: Number,
    weight_carried: Number,
    track_id:  Number
},{ timestamps: {currentTime: () => moment.utc().format()} })
formIndexSchema.set('collection', 'form_index');
exports.form_index = mongoose.model("form_index", formIndexSchema)
