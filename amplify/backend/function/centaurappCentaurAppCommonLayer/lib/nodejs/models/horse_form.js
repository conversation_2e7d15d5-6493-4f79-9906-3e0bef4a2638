const mongoose = require('mongoose')


const horseFormSchema = new mongoose.Schema({
  FRM_HORSE_ID: Number,
  FRM_FORM_DATE: { type: Object },
  FRM_RACE_NO: Number,
  FRM_EVENT_ID: Number,
  FRM_FINISH_POSITION: Number,
  FRM_WEIGHT_CARRIED: Number,
  FRM_WEIGHT_ALLOCATED: Number,
  FRM_BARRIER_NO: Number,
  FRM_JOCKEY_NAME: String,
  FRM_DEAD_HEAT_IND: Number,
  FRM_HORSE_NAME: String,
  FRM_BLINKER_IND: String,
  FRM_TRAINER_ID: Number,
  FRM_JOCKEY_ID: Number,
  FRM_TAB_NO: Number,
  FRM_OPEN_PRICE_ODD_1: Number,
  FRM_OPEN_PRICE_ODD_2: Number,
  FRM_MID_PRICE_ODD_1: Number,
  FRM_MID_PRICE_ODD_2: Number,
  FRM_STARTING_PRICE_ODD_1: Number,
  FRM_STARTING_PRICE_ODD_2: Number,
  FRM_FAVOURITE_IND: String,
  FRM_APPRENTICE_IND: String,
  FRM_SETTLING_DOWN_POSITION: Number,
  FRM_1200_METER_POSITION: Number,
  FRM_800_METER_POSITION: Number,
  FRM_400_METER_POSITION: Number,
  FRM_FINISH_MARGIN: String,
  FRM_STEWARD_REPORT_CODE_1: String,
  FRM_STEWARD_REPORT_CODE_2: String,
  FRM_STEWARD_REPORT_CODE_3: String,
  FRM_TIMESTAMP: { type: Object },
  FRM_ROW_STATUS: String,
  FRM_OPEN_PRICE_DEC: Number,
  FRM_START_PRICE_DEC: Number,
  FRM_MID_PRICE_DEC: Number,
  FRM_BOBS_IND: String,
  FRM_TRUE_WEIGHT_ALLOWANCE: Number,
  FRM_RA_MARGIN: String,
})

horseFormSchema.set('collection', 'horse_form')

exports.horse_form = mongoose.model("horse_form", horseFormSchema)
