const mongoose = require("mongoose")
const moment = require('moment');


const jockeySchema = new mongoose.Schema({
    _id: 'string',
    JOC_JOCKEY_ID: { type: Number, default: 0 },
    JOC_JOCKEY_SURNAME: { type: String, default: '' },
    J<PERSON>_JOCKEY_FIRSTNAME: { type: String, default: '' },
    JOC_JOCKEY_DISPLAYNAME: { type: String, default: '' },
    JOC_JOCKEY_RANAME: { type: String, default: '' },
    JOC_JOCKEY_NZNAME: { type: String, default: '' },
    JOC_STATE_REGISTERED: { type: Number, default: 0 },
    JOC_JOCKEY_DISTRICT: { type: Number, default: 0 },
    JOC_JOCKEY_WEIGHT: { type: Number, default: 0 },
    JOC_JOCKEY_APPRENTICE_IND: { type: String, default: '' },
    JOC_JOCKEY_AMATEUR_IND: { type: String, default: '' },
    J<PERSON>_COUNTRY_CODE: { type: String, default: '' },
    J<PERSON>_TIMESTAMP: { type: Date },
    JOC_ROW_STATUS: { type: String, default: '' },
    JOC_JOCKEY_IN_USE_IND: { type: String, default: '' },
    JOC_RATINGS: { type: Object },
},{ timestamps: {currentTime: () => moment.utc().format()} })
exports.jockeys = mongoose.model("jockeys", jockeySchema)
