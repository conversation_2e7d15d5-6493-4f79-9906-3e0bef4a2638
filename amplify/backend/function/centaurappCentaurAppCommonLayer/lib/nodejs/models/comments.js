const mongoose = require('mongoose')


const commentsSchema = new mongoose.Schema({
  _id: String,
  Id: Number,
  Name: String,
  Order: Number,
  RunCount: Number,
  Exclusive: Array,
  CommentRules: { type: Object },
  name: String,
  active: Boolean,
  testing: Boolean,
  order: Number,
  ranking: Number,
  usecount: Number,
  rules: Array
})

commentsSchema.set('collection', 'comments')

exports.comments = mongoose.model("comments", commentsSchema)
