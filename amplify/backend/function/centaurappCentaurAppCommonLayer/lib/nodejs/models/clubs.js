const mongoose = require("mongoose")
const moment = require('moment');


const clubSchema = new mongoose.Schema({
  _id: 'string',
  CLB_CLUB_ID: { type: String, default: '' },
  CLB_CLUB_NAME: { type: String, default: '' },
  CLB_CLUB_ABBREV: { type: String, default: '' },
  CLB_STATE: { type: Number, default: 0 },
  CLB_COUNTRY_CODE: { type: String, default: '' },
  CLB_ALT_NAME: { type: String, default: '' },
  CLB_TIMESTAMP: { type: Date },
  CLB_ROW_STATUS: { type: String, default: '' }
},{ timestamps: {currentTime: () => moment.utc().format()} })
exports.clubs = mongoose.model("clubs", clubSchema)
