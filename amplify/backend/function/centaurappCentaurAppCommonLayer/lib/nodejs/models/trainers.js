const mongoose = require("mongoose")
const moment = require('moment');


const trainerSchema = new mongoose.Schema({
  _id: 'string',
  RISATrainerID: { type: Number, default: 0 },
  RISAPartnerID: { type: Number, default: 0 },
  Name: { type: String, default: '' },
  NZ_Name: { type: String, default: '' },
  Location: { type: String, default: '' },
  State: { type: Number, default: 0 },
  Postcode: { type: Number, default: 0 },
  PreferredName: { type: String, default: '' },
  Surname: { type: String, default: '' },
  Initials: { type: String, default: '' },
  TrainerID: { type: Number, default: 0 },
  TRN_TRAINER_ID: { type: Number, default: 0 },
  TRN_TRAINER_SURNAME: { type: String, default: '' },
  TRN_TRAINER_FIRSTNAME: { type: String, default: '' },
  TRN_TRAINER_DISPLAYNAME: { type: String, default: '' },
  TRN_TIMESTAMP: { type: Date, default: Date.now },
  TRN_ROW_STATUS: { type: String, default: '' },
  TRN_PARTNERSHIP_DISPLAYNAME: { type: String, default: '' },
  TRN_TRAINER_IN_USE_IND: { type: String, default: '' },
  TRN_RATINGS: { type: Object },
},{ timestamps: {currentTime: () => moment.utc().format()} })
exports.trainers = mongoose.model("trainers", trainerSchema)
