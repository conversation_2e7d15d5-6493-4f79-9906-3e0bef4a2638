const mongoose = require('mongoose')

const horseSchema = new mongoose.Schema({
  EVT_EVENT_DB_ID: Number,
  EVT_ORIGINAL_EVENT_ID: Number,
  EVT_MEETING_DATE: { type: Object },
  EVT_TRACK_ID: Number,
  EVT_PSEUDO_NO: Number,
  EVT_RACE_NO: Number,
  EVT_DIVISION_NO: Number,
  EVT_EVENT_NAME: String,
  EVT_SPONSOR: String,
  EVT_CLUB_ID: String,
  EVT_JOCKEY_RESTRICTION: String,
  EVT_GROUP_NO: Number,
  EVT_EVENT_NAME_ABBR: String,
  EVT_WEIGHT_TYPE: String,
  EVT_MINIMUM_HCP_WEIGHT: Number,
  EVT_MAXIMUM_HCP_WEIGHT: Number,
  EVT_WEIGHT_RAISED: Number,
  EVT_LIMIT_WEIGHT: Number,
  EVT_AGE_RESTRICTION: Number,
  EVT_SEX_RESTRICTION: Number,
  EVT_TRACK_CONDITION: Number,
  EVT_DISTANCE_ABOUT_IND: String,
  EVT_DISTANCE: Number,
  EVT_SCHEDULED_START_TIME: Number,
  EVT_ACTUAL_START_TIME: Number,
  EVT_DURATION: Number,
  EVT_SECTIONAL_TIME: Number,
  EVT_OFF_MARGIN_1: String,
  EVT_OFF_MARGIN_2: String,
  EVT_SECTIONAL_DISTANCE: String,
  EVT_SECTIONAL_1ST_LT_IND: String,
  EVT_TRIAL_IND: String,
  EVT_RACE_RECORD: Number,
  EVT_PRIZEMONEY_CURRENCY: String,
  EVT_EVENT_STAGE: String,
  EVT_NO_OF_STARTERS: Number,
  EVT_NO_OF_FINISHERS: Number,
  EVT_NOMINATIONS_TOTAL: Number,
  EVT_ABANDONED_POSTPONED_IND: String,
  EVT_EVENT_INCOMPLETE_FORM_FLAG: String,
  EVT_FORCED_RESULT_IND: String,
  EVT_TIMESTAMP: { type: Object },
  EVT_ROW_STATUS: String,
  EVT_SECTIONAL_1ST_LT_IND_2: String,
  EVT_SECTIONAL_TIME_2: Number,
  EVT_SECTIONAL_DISTANCE_2: Number,
  EVT_SECTIONAL_1ST_LT_IND_3: String,
  EVT_SECTIONAL_TIME_3: Number,
  EVT_SECTIONAL_DISTANCE_3: Number,
  EVT_SECTIONAL_1ST_LT_IND_4: String,
  EVT_SECTIONAL_TIME_4: Number,
  EVT_SECTIONAL_DISTANCE_4: Number,
  EVT_TRACK_COND_PREVIOUS: String,
  EVT_TRACK_TYPE: String,
  EVT_ACTUAL_TRACK_ID: String
})

exports.events = mongoose.model("events", horseSchema)
