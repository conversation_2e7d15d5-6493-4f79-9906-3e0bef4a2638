const mongoose = require("mongoose")
const moment = require('moment');


const tempMeetingsSchema = new mongoose.Schema({
  _id: String,
  meetingDate: Date,
  meetingStage: String,
  meetingState: String,
  meetingCountry: String,
  meetingStatus: { type: String, default: 'Active'},
  meetingErrorCount: { type: Number, default: 0},
  validated: { type: Boolean, default: false},
  AcceptanceFileCounter: { type: Number, default: 0},
  processedMeetingData: {type: Object},
  inputMeetingData: {type: Object},
  meetingLoadHistory: {type: Array},
  meetingLocked: { type: String, default: 'unlocked'}
}, { timestamps: {currentTime: () => moment.utc().format()} })
exports.temp_meetings = mongoose.model("temp_meetings", tempMeetingsSchema)
