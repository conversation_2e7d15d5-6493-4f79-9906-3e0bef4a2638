const mongoose = require("mongoose")
const moment = require('moment');


const registrationFilesSchema = new mongoose.Schema({
  _id: String,
  regFileDate: Date,
  nameChange: [{type: Object}],
  newHorses: [{type: Object}],
  updatedHorses: [{type: Object}],
  alertHorses: [{type:Object}],
  notFound: [{type:Object}]
}, { timestamps: {currentTime: () => moment.utc().format()} })
exports.registration_files = mongoose.model("registration_files", registrationFilesSchema)
