const mongoose = require("mongoose")

const deliveryLogsSchema = new mongoose.Schema({
    createdAt: Date,
    cloudWatchLogGroupName: String,
    cloudWatchLogStreamName: String,
    lambdaFunctionName: String,
    amznTraceIdRoot: String,
    amznTraceIdParent: String,
    amznTraceIdSampled: String,
    details: Object,
})

exports.delivery_logs = mongoose.model("delivery_logs", deliveryLogsSchema)
