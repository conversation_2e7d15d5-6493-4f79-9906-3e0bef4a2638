const mongoose = require('mongoose')

const ratingSchema = new mongoose.Schema({
  RAT_HORSE_ID: Number,
  RAT_EVENT_ID: Number,
  RAT_PREDICTIVE_RATING: String,
  RAT_FORM_RATING: String,
  RAT_FAST_RATING_DRY: String,
  RAT_FAST_RATING_WET: String,
  RAT_TIMESTAMP: { type: Object },
  RAT_SKY_RATING: String,
  RAT_NEWSKY_RATING: String,
  RAT_HANDICAP_RATING: String,
  RAT_POST_HANDICAP_RATING: String
})

exports.ratings = mongoose.model("ratings", ratingSchema)
