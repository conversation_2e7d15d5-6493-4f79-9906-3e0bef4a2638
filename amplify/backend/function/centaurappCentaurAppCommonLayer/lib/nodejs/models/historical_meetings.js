const mongoose = require("mongoose")
const moment = require('moment');


const historicalMeetingsSchema = new mongoose.Schema({
  _id: String,
  MET_MEETING_ID: { type: String, required: true },
  MET_TRACK_ID: { type: Number, required: true },
  MET_MEETING_DATE: { type: Date },
  MET_CLUB_ID: { type: String, default: '' },
  MET_TAB_IND: { type: String, default: '' },
  MET_WEATHER: { type: String, default: '' },
  MET_LOCATION: { type: String, default: '' },
  MET_BIS_RUN_TIMESTAMP: { type: Object },
  MET_ABANDONED_POSTPONED_IND: { type: String, default: '' },
  MET_EXPECTED_TRACK_CONDITION: { type: String, default: '' },
  MET_NEW_TRACK_ID: { type: Number, default: 0 },
  MET_NEW_MEETING_DATE: { type: Number, default: 0 },
  MET_TIMESTAMP: { type: Object, default: Date.now },
  MET_ROW_STATUS: { type: String, default: '' },
  MET_RESULT_RUN_TIMESTAMP: { type: Number, default: 0 },
  MET_NIGHT_IND: { type: String, default: '' },
  MET_EDITORIAL_LOCATION: { type: String, default: '' },
  MET_RAIL: { type: String, default: '' },
  MET_TAB_DISPLAY: { type: String, default: '' },
  MET_EXP_TRACK_COND_PREVIOUS: { type: String, default: '' },
  MET_DUAL_TRACK_IND: { type: String, default: '' },
  MET_ODDS_PUBLISHED_CLIENT_ID: { type: String, default: '' },
  EVENTS: {type: Array},
}, { timestamps: {currentTime: () => moment.utc().format()} })
exports.historical_meetings = mongoose.model("historical_meetings", historicalMeetingsSchema)
