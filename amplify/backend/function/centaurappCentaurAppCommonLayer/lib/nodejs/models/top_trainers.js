const mongoose = require("mongoose")
const moment = require('moment');


const topTrainerSchema = new mongoose.Schema({
    _id: 'string',
    Id: { type: Number, required: true },
    TrainerId: { type: Number, required: true },
    Location: { type: String, required: true },
    StateId: { type: Number, required: true }
},{ timestamps: {currentTime: () => moment.utc().format()} })
exports.top_trainers = mongoose.model("top_trainers", topTrainerSchema)

