const mongoose = require("mongoose")
const moment = require('moment');


const trackSchema = new mongoose.Schema({
  _id: 'string',
  TRK_TRACK_DB_ID: { type: Number, default: 0 },
  TRK_TRACK_NAME: { type: String, default: '' },
  TRK_TRACK_3CHAR_ABBREV: { type: String, default: '' },
  TRK_TRACK_6CHAR_ABBREV: { type: String, default: '' },
  TRK_RSB_TRACK_ABBREV: { type: String, default: '' },
  TRK_TRACK_SURFACE_TYPE: { type: String, default: '' },
  TRK_LENGTH_OF_STRAIGHT:{ type: Number, default: 0 },
  TRK_LENGTH_OF_CIRCUIT: { type: Number, default: 0 },
  TRK_DIRECTION_OF_RUNNING: { type: String, default: '' },
  TRK_LOCATION: { type: String, default: '' },
  TRK_COUNTRY_OF_TRACK: { type: String, default: '' },
  TRK_FILLER_1: { type: String, default: '' },
  TRK_STATE_OF_TRACK: { type: Number, default: 0 },
  TRK_TIMESTAMP: { type: Date, default: Date.now },
  TRK_ROW_STATUS: { type: String, default: '' },
  TRK_IN_BETWEEN_MARGINS: { type: String, default: '' },
  TRK_ALT_NAME: { type: String, default: '' }
},{ timestamps: {currentTime: () => moment.utc().format()} })
exports.tracks = mongoose.model("tracks", trackSchema)
