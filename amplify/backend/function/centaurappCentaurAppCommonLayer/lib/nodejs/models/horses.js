const mongoose = require('mongoose')
const moment = require('moment');

const horseMergedSchema = new mongoose.Schema({
  _id: 'string',
  HRN_HORSE_ID: { type: Number, required: true },
  HRN_PRIMARY_NAME_IND: { type: String, default: '' },
  HRN_COUNTRY_OF_NAME: { type: String, default: '' },
  HRN_HORSE_NAME: { type: String, default: '' },
  HRN_DISPLAY_NAME: { type: String, default: '' },
  HRN_NAME_CHANGE_DATE: { type: Date },
  HRN_TIMESTAMP: { type: Date, default: Date.now },
  HRN_ROW_STATUS: { type: String, default: '' },
  HOR_HORSE_DB_ID: { type: Number, default: 0 },
  HOR_FOALING_DATE: { type: Date },
  HOR_COUNTRY_OF_ORIGIN: { type: String, default: '' },
  HOR_SEX: { type: String, default: '' },
  <PERSON><PERSON>_COLOUR: { type: Number, default: 0 },
  HOR_SIRE_ID: { type: Number, default: 0 },
  HOR_DAM_ID: { type: Number, default: 0 },
  HOR_ENSCRIBE_DB_ID: { type: Number, default: 0 },
  HOR_TRAINING_LOCATION: { type: String, default: '' },
  HOR_TRAINER_ID: { type: Number, default: 0 },
  HOR_ALT_SIRE_ID: { type: Number, default: 0 },
  HOR_OWNER_NAMES: { type: String, default: '' },
  HOR_RACING_COLOURS: { type: String, default: '' },
  HOR_START_TOTAL: { type: Number, default: 0 },
  HOR_H_1STS_TOTAL: { type: Number, default: 0 },
  HOR_H_2NDS_TOTAL: { type: Number, default: 0 },
  HOR_H_3RDS_TOTAL: { type: Number, default: 0 },
  HOR_H_4THS_TOTAL: { type: Number, default: 0 },
  HOR_H_5THS_TOTAL: { type: Number, default: 0 },
  HOR_TOTAL_PRIZEMONEY: { type: Number, default: 0 },
  HOR_RACE_PRIZEMONEY: { type: Array},
  HOR_CURRENT_BLINKER_IND: { type: String, default: '' },
  HOR_BOBS_STATE: { type: Number, default: 0 },
  HOR_HORSE_INCOMPLETE_FORM_FLAG: { type: String, default: '' },
  HOR_TIMESTAMP: { type: Date, default: Date.now },
  HOR_ROW_STATUS: { type: String, default: '' },
  HOR_WET_START_TOTAL: { type: Number, default: 0 },
  HOR_WET_1ST_TOTAL: { type: Number, default: 0 },
  HOR_WET_2ND_TOTAL: { type: Number, default: 0 },
  HOR_WET_3RD_TOTAL: { type: Number, default: 0 },
  HOR_DRY_START_TOTAL: { type: Number, default: 0 },
  HOR_DRY_1ST_TOTAL: { type: Number, default: 0 },
  HOR_DRY_2ND_TOTAL: { type: Number, default: 0 },
  HOR_DRY_3RD_TOTAL: { type: Number, default: 0 },
  HRN_PREV_NAMES: {type: Array},
  HOR_RATINGS: { type: Object}
}, { timestamps: {currentTime: () => moment.utc().format()} })
exports.horses = mongoose.model("horses", horseMergedSchema)
