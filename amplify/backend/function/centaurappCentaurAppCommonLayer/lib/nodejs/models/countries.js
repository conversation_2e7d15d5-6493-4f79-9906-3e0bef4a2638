const mongoose = require("mongoose")
const moment = require('moment');


const countriesSchema = new mongoose.Schema({
  _id: 'string',
  COUNTRY_ID: { type: Number, default: 0 },
  COUNTRY_INPUT_VAL: { type: String, default: '' },
  COUNTRY_DISPLAY_ABBREV: { type: String, default: '' },
  COUNTRY_DISPLAY_NAME: { type: String, default: '' },
})

countriesSchema.set('collection', 'countries')

exports.countries = mongoose.model("countries", countriesSchema)
