const mongoose = require("mongoose")
const moment = require('moment');


const topJockeySchema = new mongoose.Schema({
    _id: 'string',
    Id: { type: Number, required: true },
    JockeyId: { type: Number, required: true },
    Location: { type: String, required: true },
    StateId: { type: Number, required: true }
},{ timestamps: {currentTime: () => moment.utc().format()} })
exports.top_jockeys = mongoose.model("top_jockeys", topJockeySchema)
