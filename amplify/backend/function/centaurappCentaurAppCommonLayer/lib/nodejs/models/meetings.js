const mongoose = require("mongoose")

const meetingSchema = new mongoose.Schema({
  MET_TRACK_ID: Number,
  MET_MEETING_DATE: { type: Object },
  MET_CLUB_ID: String,
  MET_TAB_IND: String,
  MET_WEATHER: String,
  MET_LOCATION: String,
  MET_BIS_RUN_TIMESTAMP: { type: Object },
  MET_ABANDONED_POSTPONED_IND: String,
  MET_EXPECTED_TRACK_CONDITION: String,
  MET_NEW_TRACK_ID: 0,
  MET_NEW_MEETING_DATE: 0,
  MET_TIMESTAMP: { type: Object },
  MET_ROW_STATUS: String,
  MET_RESULT_RUN_TIMESTAMP: 0,
  MET_NIGHT_IND: String,
  MET_EDITORIAL_LOCATION:String,
  MET_RAIL: String,
  MET_TAB_DISPLAY: String,
  MET_EXP_TRACK_COND_PREVIOUS: String,
  MET_DUAL_TRACK_IND: String,
  MET_ODDS_PUBLISHED_CLIENT_ID: String,
})

exports.meetings = mongoose.model("meetings", meetingSchema)
