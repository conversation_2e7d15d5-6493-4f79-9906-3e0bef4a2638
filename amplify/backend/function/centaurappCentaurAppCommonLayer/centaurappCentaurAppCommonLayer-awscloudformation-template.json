{"AWSTemplateFormatVersion": "2010-09-09", "Description": "Lambda layer resource stack creation using Amplify CLI", "Parameters": {"env": {"Type": "String"}, "deploymentBucketName": {"Type": "String"}, "s3Key": {"Type": "String"}, "description": {"Type": "String", "Default": ""}, "runtimes": {"Type": "List<String>"}}, "Resources": {"LambdaLayerVersion37695f53": {"Type": "AWS::Lambda::LayerVersion", "Properties": {"CompatibleRuntimes": {"Ref": "runtimes"}, "Content": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Description": {"Ref": "description"}, "LayerName": {"Fn::Sub": ["centaurappCentaurAppCommonLayer-${env}", {"env": {"Ref": "env"}}]}}, "DeletionPolicy": "Delete", "UpdateReplacePolicy": "<PERSON><PERSON>"}, "LambdaLayerPermissionPrivate37695f53": {"Type": "AWS::Lambda::LayerVersionPermission", "Properties": {"Action": "lambda:GetLayerVersion", "LayerVersionArn": {"Ref": "LambdaLayerVersion37695f53"}, "Principal": {"Ref": "AWS::AccountId"}}}, "LambdaLayerVersione540c246": {"Type": "AWS::Lambda::LayerVersion", "Properties": {"CompatibleRuntimes": ["nodejs18.x", "nodejs20.x"], "Content": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": "amplify-builds/centaurappCentaurAppCommonLayer-LambdaLayerVersione540c246-build.zip"}, "Description": "Updated layer version 2025-03-13T00:38:22.389Z", "LayerName": {"Fn::Sub": ["centaurappCentaurAppCommonLayer-${env}", {"env": {"Ref": "env"}}]}}, "DeletionPolicy": "Delete", "UpdateReplacePolicy": "<PERSON><PERSON>"}, "LambdaLayerPermissionPrivatee540c246": {"Type": "AWS::Lambda::LayerVersionPermission", "Properties": {"Action": "lambda:GetLayerVersion", "LayerVersionArn": "arn:aws:lambda:ap-southeast-2:************:layer:centaurappCentaurAppCommonLayer-stgblack:448", "Principal": {"Ref": "AWS::AccountId"}}}, "LambdaLayerPermissionPrivateLegacy15": {"Type": "AWS::Lambda::LayerVersionPermission", "Properties": {"Action": "lambda:GetLayerVersion", "LayerVersionArn": "arn:aws:lambda:ap-southeast-2:************:layer:centaurappCentaurAppCommonLayer-stgblack:15", "Principal": {"Ref": "AWS::AccountId"}}}}, "Outputs": {"Arn": {"Value": {"Ref": "LambdaLayerVersion37695f53"}}}}