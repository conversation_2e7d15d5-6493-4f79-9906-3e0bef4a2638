{"name": "mrcenlambdaclientdeliveryservice", "version": "2.0.0", "description": "Lambda function generated by Amplify", "main": "index.js", "license": "Apache-2.0", "scripts": {"test": "jest --verbose"}, "devDependencies": {"@aws-sdk/client-s3": "^3.282.0", "@aws-sdk/util-stream-node": "^3.290.0", "@mediality/centaur": "../../centaurappCentaurAppCommonLayer/lib/nodejs", "@types/aws-lambda": "^8.10.92", "jest": "^29.4.3", "mongoose": "^6.10.0", "xslt3": "^2.5.0"}, "dependencies": {"dotenv": "^16.0.3", "s3-etag": "^1.0.3", "saxon-js": "^2.5.0"}}