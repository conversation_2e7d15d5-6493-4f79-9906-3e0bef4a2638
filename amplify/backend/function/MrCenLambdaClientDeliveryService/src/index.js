const dotenv = require("dotenv").config();
const { generateETag } = require("s3-etag");
const fs = require("fs").promises;
const path = require("path");
const {
  S3Client,
  GetObjectCommand,
  PutObjectCommand,
  HeadObjectCommand,
} = require("@aws-sdk/client-s3");
const {
  lambdaPowertools: { initTracer, logger, tracer },
  deliveryLogger: { writeDeliveryLog },
} = require("@mediality/centaur");

const transformService = require("./service/transformService");
const { extractDeliveryInfo } = require("./service/pricingInfo");
const { FIELDS_ACCESS } = require("./lib/constants");
const { getTmpFilePath } = require("./lib/util");
const AWS = require("aws-sdk");
const sqs = new AWS.SQS();

let FINAL_STATUS, fullDestinationObjectKey, logData, s3Client;

/**
 * Checks if the meeting type (tab/non-tab) is permitted for this client
 * @param {Object} meetingData - The parsed meeting data from the XML
 * @param {Object} meetingTypePerms - Client's meeting type permissions
 * @returns {boolean} - Whether delivery should proceed
 */
const shouldDeliverToClient = (meetingData, meetingTypePerms) => {
  // Extract tab_indicator from meeting data
  const tabIndicator = meetingData?.meeting?.tab_indicator;

  if (!tabIndicator) {
    logger.warn(
      "Could not find tab_indicator in meeting data, defaulting to tab",
      { meetingData }
    );
    // Default to tab permissions if indicator not found
    return meetingTypePerms.tab === true;
  }

  // If tab_indicator is 'T', check if client accepts tab meetings
  if (tabIndicator === "T") {
    return meetingTypePerms.tab === true;
  }

  // If tab_indicator is 'N', check if client accepts non-tab meetings
  if (tabIndicator === "N") {
    return meetingTypePerms.non_tab === true;
  }

  // For any other value, default to tab permission
  return meetingTypePerms.tab === true;
};

/**
 * Extract meeting data from XML to determine if it's a tab or non-tab meeting
 * @param {string} xmlContent - The source XML content
 * @returns {Object} - Parsed meeting data object
 */
const extractMeetingData = (xmlContent) => {
  try {
    // Simple XML parsing to extract tab_indicator
    const tabIndicatorMatch = xmlContent.match(
      /<tab_indicator>(T|N)<\/tab_indicator>/i
    );
    console.log("tabIndicatorMatch", tabIndicatorMatch);
    if (tabIndicatorMatch && tabIndicatorMatch[1]) {
      console.log("tabIndicatorMatch[1]", tabIndicatorMatch[1]);
      return {
        meeting: {
          tab_indicator: tabIndicatorMatch[1],
        },
      };
    }

    logger.warn("Could not find tab_indicator in XML content");
    return { meeting: {
      tab_indicator: "N",
    } };
  } catch (error) {
    logger.error("Error extracting meeting data from XML", { error });
    return { meeting: {} };
  }
};

const getHeadObjectKeyMeta = async (event) => {
  let headObjectCommandResponse = { ETag: "" };
  try {
    const input = {
      Bucket: event.destination.bucketName,
      Key: fullDestinationObjectKey,
    };
    const headObjectCommand = new HeadObjectCommand(input);
    headObjectCommandResponse = await s3Client.send(headObjectCommand);
    logger.info(`The destination file ${fullDestinationObjectKey} exists`);
    logData = {
      ...logData,
      status: "DESTINATION_FILE_EXISTS",
    };
  } catch (err) {
    const httpStatusCode = err?.["$metadata"]?.httpStatusCode || 500;
    if (httpStatusCode === 404) {
      logger.info(
        `${event.destination.bucketName}/${fullDestinationObjectKey} doesn't exists.`,
        {
          Bucket: event.destination.bucketName,
          Key: fullDestinationObjectKey,
        }
      );

      logData = {
        ...logData,
        status: "DESTINATION_FILE_DOESNT_EXISTS_AND_IT_WILL_BE_CREATED",
      };
      FINAL_STATUS =
        "SUCCESSFULLY_PROCESSED_AND_UPLOADED_NEW_CLIENT_OUTPUT_XML_FILE";
    } else {
      logData = {
        ...logData,
        status: `DESTINATION_FILE_DOESNT_EXISTS_WITH_HTTP_STATUS_CODE_${httpStatusCode}`,
      };
      logger.error(err);
    }
    logger.info(
      `The destination file ${fullDestinationObjectKey} doesn't exists and it will be created. Status Code: ${httpStatusCode}`
    );
  }

  return headObjectCommandResponse;
};

const compareNewAndExistingETags = async (event, clientXmlContent) => {
  const objectKeyMeta = await getHeadObjectKeyMeta(event);

  if (objectKeyMeta.ETag) {
    // Write XML to temp file
    const tmpFilename = getTmpFilePath();
    await fs.writeFile(tmpFilename, clientXmlContent);

    const clientXmlContentETag = generateETag(tmpFilename);

    if (objectKeyMeta.ETag === `"${clientXmlContentETag}"`) {
      logger.info(`Successfully processed and finished with no changes`, {
        Bucket: event.destination.bucketName,
        Key: fullDestinationObjectKey,
      });

      FINAL_STATUS = "SUCCESSFULLY_PROCESSED_AND_FINISHED_WITH_NO_CHANGES";
      logData = {
        Bucket: event.destination.bucketName,
        Key: fullDestinationObjectKey,
        status: FINAL_STATUS,
      };
      await writeDeliveryLog(logData);
      return {
        event,
        status: FINAL_STATUS,
      };
    }
  }
};

const sendToS3 = async (clientXmlContent, event) => {
  const body = Buffer.from(clientXmlContent, "utf-8");
  const params = {
    Bucket: event.destination.bucketName,
    Key: fullDestinationObjectKey,
    Body: body,
    ContentType: "application/xml",
  };

  logger.info(
    `Uploading client xml output file to ${params.Bucket}/${params.Key}`
  );

  logData = {
    destinationBucketKey: `${params.Bucket}/${params.Key}`,
    status: "UPLOADING_CLIENT_XML_OUTPUT_FILE",
  };
  await writeDeliveryLog(logData);

  const putObjectCommand = new PutObjectCommand(params);
  const client_output = await s3Client.send(putObjectCommand);

  logger.info(FINAL_STATUS, {
    source: event,
    output: client_output,
  });

  logData = {
    destinationBucketKey: `${params.Bucket}/${params.Key}`,
    status: FINAL_STATUS,
  };
  await writeDeliveryLog(logData);
};

const processXmlTransformation = async (event) => {
  // Read source file from S3
  const getObjectCommand = new GetObjectCommand({
    Bucket: event.source.bucketName,
    Key: event.source.objectKey,
  });
  const response = await s3Client.send(getObjectCommand);

  // Transform the master (source) xml file content into the format permitted for the client
  // based on the clients field access permissions
  const masterXmlContent = await response.Body.transformToString("utf-8");

  // Extract meeting data to check tab/non-tab status
  const meetingData = extractMeetingData(masterXmlContent);
  console.log("event.meetingTypePerm", event.meetingTypePerms);
  let raMeetingId = null;
  const raMeetingIdMatch = masterXmlContent.match(
    /<ra_meeting_id>(\d+)<\/ra_meeting_id>/i
  );
  if (raMeetingIdMatch && raMeetingIdMatch[1]) {
    raMeetingId = raMeetingIdMatch[1];
    logger.info(`Extracted ra_meeting_id: ${raMeetingId}`);
  } else {
    logger.warn("Could not find ra_meeting_id in XML content");
  }
  let location = null;
  const locationMatch = masterXmlContent.match(
    /<track[^>]*location="([^"]+)"[^>]*>/i
  );
  if (locationMatch && locationMatch[1]) {
    location = locationMatch[1];
    logger.info(`Extracted location: ${location}`);
  } else {
    logger.warn("Could not find location attribute in track element");
  }
  // Check if this client should receive this file based on meeting type permissions
  if (!shouldDeliverToClient(meetingData, event.meetingTypePerms)) {
    logger.info(
      `Skipping delivery for client ${event.clientFolder} - meeting type not permitted`,
      {
        clientFolder: event.clientFolder,
        tabIndicator: meetingData?.meeting?.tab_indicator || "unknown",
        meetingTypePerms: event.meetingTypePerms,
      }
    );

    // Update log data
    logData = {
      ...logData,
      status: "SKIPPED_DELIVERY_MEETING_TYPE_NOT_PERMITTED",
      tabIndicator: meetingData?.meeting?.tab_indicator || "unknown",
      meetingTypePerms: event.meetingTypePerms,
    };

    await writeDeliveryLog(logData);

    // Return null to indicate no transformation needed
    return { xmlContent: null, raMeetingId , location};
  }

  const clientProps = {
    permissions: {
      includeGearChanges: event.fieldsAccess.includes(
        FIELDS_ACCESS.GEAR_CHANGES
      ),
      includeRatings: event.fieldsAccess.includes(FIELDS_ACCESS.RATINGS),
      includeSilks: event.fieldsAccess.includes(FIELDS_ACCESS.SILKS),
      includeBetting: event.fieldsAccess.includes(FIELDS_ACCESS.BETTING),
      includeSelections: event.fieldsAccess.includes(FIELDS_ACCESS.SELECTIONS),
      includeSpeedMaps: event.fieldsAccess.includes(FIELDS_ACCESS.SPEED_MAPS),
      includeComments: event.fieldsAccess.includes(FIELDS_ACCESS.COMMENT),
      includeRaIds: event.includeRaIds || false,
      racingAustralia: event?.racing_australia || { enabled: false },
      medialityRacing: event?.mediality_racing || { enabled: false },
    },
    raMeetingId: raMeetingId,
    location: location,
  };

  // Transform the master XML file using the default XSLT stylesheet
  // Note: When required we can pass in a different XSLT stylesheet to use (e.g. per file type)
  const xsltStylesheet = path.join(__dirname, "xslt", "default.xslt");
  const transformedXml = await transformService.transformAsync(
    masterXmlContent,
    xsltStylesheet,
    clientProps,
    fullDestinationObjectKey
  );
  
  // Return both the transformed XML and the extracted ra_meeting_id
  return { xmlContent: transformedXml, raMeetingId, location };
};

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event, context) => {
  initTracer();

  s3Client = tracer.captureAWSv3Client(new S3Client({}));
  fullDestinationObjectKey = `${event.clientFolder}/${event.destination.objectKey}`;
  FINAL_STATUS =
    "SUCCESSFULLY_PROCESSED_AND_REPLACED_EXISTING_CLIENT_OUTPUT_XML_FILE";

  logData = {
    sourceBucketName: event.source.bucketName,
    sourceObjectKey: event.source.objectKey,
    destinationBucketName: event.destination.bucketName,
    destinationObjectKey: event.destination.objectKey,
    clientFolder: event.clientFolder,
  };

  await writeDeliveryLog(logData);

  try {
    logger.addContext(context);
    logger.info(event);

    logData = {
      ...logData,
      status: "STARTING_CLIENT_DELIVERY_SERVICE",
    };

    logger.info(
      `Starting to process client delivery service for ${event.clientFolder}`,
      logData
    );

    await writeDeliveryLog(logData);

    const transformationResult = await processXmlTransformation(event);
    const { xmlContent: clientXmlContent, raMeetingId, location } = transformationResult;

    // If processXmlTransformation returned null, it means delivery should be skipped
    if (clientXmlContent === null) {
      return {
        event,
        status: "SKIPPED_DELIVERY_MEETING_TYPE_NOT_PERMITTED",
      };
    }

    const hasTheSameEtag = await compareNewAndExistingETags(
      event,
      clientXmlContent
    );

    if (hasTheSameEtag) {
      return hasTheSameEtag;
    }
    const clientProps = {
      permissions: {
        includeGearChanges: event.fieldsAccess.includes(
          FIELDS_ACCESS.GEAR_CHANGES
        ),
        includeRatings: event.fieldsAccess.includes(FIELDS_ACCESS.RATINGS),

        includeSilks: event.fieldsAccess.includes(FIELDS_ACCESS.SILKS),

        includeBetting: event.fieldsAccess.includes(FIELDS_ACCESS.BETTING),

        includeSelections: event.fieldsAccess.includes(
          FIELDS_ACCESS.SELECTIONS
        ),
        includeSpeedMaps: event.fieldsAccess.includes(FIELDS_ACCESS.SPEED_MAPS),
        includeComments: event.fieldsAccess.includes(FIELDS_ACCESS.COMMENT),
        includeRaIds: event.includeRaIds || false,
        racingAustralia: event?.racing_australia || { enabled: false },
        medialityRacing: event?.mediality_racing || { enabled: false },
      },
      raMeetingId: raMeetingId,
      location: location,
      chargebee_customer_id: event?.chargebee_customer_id || false,
      customer_name: event?.customer_name || null,
      client_type: event?.client_type || null,
    };
    const pricingInfo = extractDeliveryInfo(
      event,
      clientXmlContent,
      clientProps
    );
    logger.info("Pricing information for delivery:", pricingInfo);
    if (pricingInfo.deliveryInfo.country === "AUS") {
      let queueUrl = null;
      const response = await sqs
        .getQueueUrl({
          QueueName: process.env.CHARGEBEE_QUEUE_NAME,
        })
        .promise();

      queueUrl = response.QueueUrl;
      logger.info(`Found queue URL: ${queueUrl}`);
      // Send message to Chargebee queue
      const params = {
        QueueUrl: queueUrl,
        MessageBody: JSON.stringify({
          action: "processSubscription",
          pricingInfo: pricingInfo,
          raMeetingId: raMeetingId
        }),
      };
      await sqs.sendMessage(params).promise();
    }

    await sendToS3(clientXmlContent, event);
  } catch (err) {
    logger.error("Error transforming client output xml file", err);
    throw err;
  }

  return {
    event,
    status: FINAL_STATUS,
  };
};
