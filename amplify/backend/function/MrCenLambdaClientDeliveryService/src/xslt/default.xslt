<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:xs="http://www.w3.org/2001/XMLSchema"
                xmlns:array="http://www.w3.org/2005/xpath-functions/array"
                xmlns:map="http://www.w3.org/2005/xpath-functions/map"
                xmlns:math="http://www.w3.org/2005/xpath-functions/math"
                exclude-result-prefixes="#all"
                expand-text="yes"
                version="3.0">
    
    <xsl:output method="xml" indent="yes" encoding="UTF-8"/>
    <xsl:mode on-no-match="shallow-copy"/>
    <xsl:template match="*[local-name()='ra_meeting_id']"/>
    <xsl:param name="includeGearChanges" as="xs:boolean" select="false()"/>
    <xsl:param name="includeRatings" as="xs:boolean" select="false()"/>
    <xsl:param name="includeSilks" as="xs:boolean" select="false()"/>
    <xsl:param name="includeBetting" as="xs:boolean" select="false()"/>
    <xsl:param name="includeSelections" as="xs:boolean" select="false()"/>
    <xsl:param name="includeSpeedMaps" as="xs:boolean" select="false()"/>
    <xsl:param name="includeComments" as="xs:boolean" select="false()"/>
    <xsl:param name="includeRaIds" as="xs:boolean" select="false()"/>
    <xsl:template match="/*" mode="#all">
        <xsl:copy>
            <xsl:apply-templates select="@*, node()" mode="#current"/>
        </xsl:copy>
    </xsl:template>
     <xsl:template match="horse/@horse_RA_Id | jockey/@jockey_RA_Id | trainer/@trainer_RA_Id">
        <xsl:if test="$includeRaIds">
            <xsl:copy/>
        </xsl:if>
    </xsl:template>
    <xsl:template match="gear_changes">
        <xsl:if test="$includeGearChanges">
            <xsl:copy>
                <xsl:apply-templates select="@*, node()" mode="#current"/>
            </xsl:copy>
        </xsl:if>        
    </xsl:template>
    
    <xsl:template match="ratings | rating">
        <xsl:if test="$includeRatings">
            <xsl:copy>
                <xsl:apply-templates select="@*, node()" mode="#current"/>
            </xsl:copy>
        </xsl:if>        
    </xsl:template>

    <xsl:template match="horse_colours_image | horse_colours_image_png | horse_colours_image_svg">
        <xsl:if test="$includeSilks">
            <xsl:copy>
                <xsl:apply-templates select="@*, node()" mode="#current"/>
            </xsl:copy>
        </xsl:if>        
    </xsl:template>    
    
    <xsl:template match="market">
        <xsl:if test="$includeBetting">
            <xsl:copy>
                <xsl:apply-templates select="@*, node()" mode="#current"/>
            </xsl:copy>
        </xsl:if>        
    </xsl:template>  

    <xsl:template match="pace | pace_value">
        <xsl:if test="$includeSpeedMaps">
            <xsl:copy>
                <xsl:apply-templates select="@*, node()" mode="#current"/>
            </xsl:copy>
        </xsl:if>        
    </xsl:template>       

    <xsl:template match="selection">
        <xsl:if test="$includeSelections">
            <xsl:copy>
                <xsl:apply-templates select="@*, node()" mode="#current"/>
            </xsl:copy>
        </xsl:if>        
    </xsl:template>  

    <xsl:template match="comments | form_comments">
        <xsl:if test="$includeComments">
            <xsl:copy>
                <xsl:apply-templates select="@*, node()" mode="#current"/>
            </xsl:copy>
        </xsl:if>        
    </xsl:template>     
    
</xsl:stylesheet>