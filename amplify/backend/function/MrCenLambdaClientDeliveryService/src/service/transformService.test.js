const fs = require('fs');
const path = require('path');
const saxon = require("saxon-js");
require('@mediality/centaur/lib/lambdaPowertools')
require('@mediality/centaur')

jest.mock('@mediality/centaur')

const MOCK_MASTER_XML_FILE = '20230214_HAW_FORM_XML_A.xml';

const transformService = require('./transformService');
const {FIELDS_ACCESS} = require("../lib/constants");

describe('transformService', () => {

    const masterXmlFilename = path.join(__dirname, '..', 'test', 'data', MOCK_MASTER_XML_FILE);
    const masterXmlContent = fs.readFileSync(masterXmlFilename).toString();
    const xsltStylesheet = path.join(__dirname, '..', 'xslt', 'default.xslt');

    it('should transform without filtering', async () => {
        // Arrange
        const clientProps = {
            permissions: {
                includeGearChanges: FIELDS_ACCESS.GEAR_CHANGES === 'GEAR CHANGES',
                includeRatings: FIELDS_ACCESS.RATINGS === 'RATINGS',
                includeSilks: FIELDS_ACCESS.SILKS === 'SILKS',
                includeBetting: FIELDS_ACCESS.BETTING === 'BETTING',
                includeSelections: FIELDS_ACCESS.SELECTIONS === 'SELECTIONS',
                includeSpeedMaps: FIELDS_ACCESS.SPEED_MAPS === 'SPEED MAPS',
                includeComments: FIELDS_ACCESS.COMMENT === 'COMMENT',
            }
        };

        // Act
        const clientXmlContent = await transformService.transformAsync(masterXmlContent, xsltStylesheet, clientProps);

        // Assert
        /** Note: This comparison is only working when we "clean" up the test data
         *     - add the default encoding to the xml declaration
         *     - change indentation to 4 spaces
        */
        expect(masterXmlContent).toEqual(clientXmlContent);

        const masterXmlDoc = await saxon.getResource({
            text: masterXmlContent,
            type: "xml"
        });

        // Making sure these paths are existing before removing the tests below
        expect(saxon.XPath.evaluate('boolean(/meeting/races)', masterXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/gear_changes)', masterXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/ratings)', masterXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/rating)', masterXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/forms/form/rating)', masterXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image)', masterXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image_png)', masterXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image_svg)', masterXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/market)', masterXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/selection)', masterXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/pace)', masterXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/pace_value)', masterXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/comments)', masterXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/form_comments)', masterXmlDoc)).toBeTruthy();
    });

    it('should filter gear changes', async () => {
        // Arrange
        const clientProps = {
            permissions: {
                includeGearChanges: false,
                includeRatings: true,
                includeSilks: true,
                includeBetting: true,
                includeSelections: true,
                includeSpeedMaps: true,
                includeComments: true,
            }
        };

        // Act
        const clientXmlContent = await transformService.transformAsync(masterXmlContent, xsltStylesheet, clientProps);

        // Assert
        const clientXmlDoc = await saxon.getResource({
            text: clientXmlContent,
            type: "xml"
        });

        expect(saxon.XPath.evaluate('boolean(/meeting/races)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/gear_changes)', clientXmlDoc)).toBeFalsy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/ratings)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/rating)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/forms/form/rating)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image_png)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image_svg)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/market)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/selection)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/pace)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/pace_value)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/comments)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/form_comments)', clientXmlDoc)).toBeTruthy();
    });

    it('should filter ratings', async () => {
        // Arrange
        const clientProps = {
            permissions: {
                includeGearChanges: true,
                includeRatings: false,
                includeSilks: true,
                includeBetting: true,
                includeSelections: true,
                includeSpeedMaps: true,
                includeComments: true,
            }
        };

        // Act
        const clientXmlContent = await transformService.transformAsync(masterXmlContent, xsltStylesheet, clientProps);

        // Assert
        const clientXmlDoc = await saxon.getResource({
            text: clientXmlContent,
            type: "xml"
        });

        expect(saxon.XPath.evaluate('boolean(/meeting/races)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/gear_changes)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/ratings)', clientXmlDoc)).toBeFalsy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/rating)', clientXmlDoc)).toBeFalsy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/forms/form/rating)', clientXmlDoc)).toBeFalsy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image_png)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image_svg)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/market)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/selection)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/pace)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/pace_value)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/comments)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/form_comments)', clientXmlDoc)).toBeTruthy();
    });

    it('should filter silks', async () => {
        // Arrange
        const clientProps = {
            permissions: {
                includeGearChanges: true,
                includeRatings: true,
                includeSilks: false,
                includeBetting: true,
                includeSelections: true,
                includeSpeedMaps: true,
                includeComments: true,
            }
        };

        // Act
        const clientXmlContent = await transformService.transformAsync(masterXmlContent, xsltStylesheet, clientProps);

        // Assert
        const clientXmlDoc = await saxon.getResource({
            text: clientXmlContent,
            type: "xml"
        });

        expect(saxon.XPath.evaluate('boolean(/meeting/races)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/gear_changes)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/ratings)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/rating)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/forms/form/rating)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image)', clientXmlDoc)).toBeFalsy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image_png)', clientXmlDoc)).toBeFalsy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image_svg)', clientXmlDoc)).toBeFalsy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/market)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/selection)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/pace)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/pace_value)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/comments)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/form_comments)', clientXmlDoc)).toBeTruthy();
    });

    it('should filter betting', async () => {
        // Arrange
        const clientProps = {
            permissions: {
                includeGearChanges: true,
                includeRatings: true,
                includeSilks: true,
                includeBetting: false,
                includeSelections: true,
                includeSpeedMaps: true,
                includeComments: true,
            }
        };

        // Act
        const clientXmlContent = await transformService.transformAsync(masterXmlContent, xsltStylesheet, clientProps);

        // Assert
        const clientXmlDoc = await saxon.getResource({
            text: clientXmlContent,
            type: "xml"
        });

        expect(saxon.XPath.evaluate('boolean(/meeting/races)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/gear_changes)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/ratings)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/rating)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/forms/form/rating)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image_png)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image_svg)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/market)', clientXmlDoc)).toBeFalsy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/selection)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/pace)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/pace_value)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/comments)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/form_comments)', clientXmlDoc)).toBeTruthy();
    });

    it('should filter selections', async () => {
        // Arrange
        const clientProps = {
            permissions: {
                includeGearChanges: true,
                includeRatings: true,
                includeSilks: true,
                includeBetting: true,
                includeSelections: false,
                includeSpeedMaps: true,
                includeComments: true,
            }
        };

        // Act
        const clientXmlContent = await transformService.transformAsync(masterXmlContent, xsltStylesheet, clientProps);

        // Assert
        const clientXmlDoc = await saxon.getResource({
            text: clientXmlContent,
            type: "xml"
        });

        expect(saxon.XPath.evaluate('boolean(/meeting/races)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/gear_changes)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/ratings)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/rating)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/forms/form/rating)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image_png)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image_svg)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/market)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/selection)', clientXmlDoc)).toBeFalsy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/pace)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/pace_value)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/comments)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/form_comments)', clientXmlDoc)).toBeTruthy();
    });

    it('should filter speedmaps', async () => {
        // Arrange
        const clientProps = {
            permissions: {
                includeGearChanges: true,
                includeRatings: true,
                includeSilks: true,
                includeBetting: true,
                includeSelections: true,
                includeSpeedMaps: false,
                includeComments: true,
            }
        };

        // Act
        const clientXmlContent = await transformService.transformAsync(masterXmlContent, xsltStylesheet, clientProps);

        // Assert
        const clientXmlDoc = await saxon.getResource({
            text: clientXmlContent,
            type: "xml"
        });

        expect(saxon.XPath.evaluate('boolean(/meeting/races)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/gear_changes)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/ratings)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/rating)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/forms/form/rating)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image_png)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image_svg)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/market)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/selection)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/pace)', clientXmlDoc)).toBeFalsy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/pace_value)', clientXmlDoc)).toBeFalsy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/comments)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/form_comments)', clientXmlDoc)).toBeTruthy();
    });

    it('should filter comments', async () => {
        // Arrange
        const clientProps = {
            permissions: {
                includeGearChanges: true,
                includeRatings: true,
                includeSilks: true,
                includeBetting: true,
                includeSelections: true,
                includeSpeedMaps: true,
                includeComments: false,
            }
        };

        // Act
        const clientXmlContent = await transformService.transformAsync(masterXmlContent, xsltStylesheet, clientProps);

        // Assert
        const clientXmlDoc = await saxon.getResource({
            text: clientXmlContent,
            type: "xml"
        });

        expect(saxon.XPath.evaluate('boolean(/meeting/races)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/gear_changes)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/ratings)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/rating)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/forms/form/rating)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image_png)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/horse_colours_image_svg)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/market)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/selection)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/pace)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/pace_value)', clientXmlDoc)).toBeTruthy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/comments)', clientXmlDoc)).toBeFalsy();
        expect(saxon.XPath.evaluate('boolean(/meeting/races/race/horses/horse/form_comments)', clientXmlDoc)).toBeFalsy();
    });

});
