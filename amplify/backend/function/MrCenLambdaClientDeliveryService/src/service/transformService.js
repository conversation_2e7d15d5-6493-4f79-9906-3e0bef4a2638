/**
 * Transforms master xml into client output xml
 *
 * Expecting clientProps to be passed in with
 *  {
 *      clientOutputXmlFilename: string,
 *      permissions: {
 *          includeGearChanges: boolean,
 *          includeRatings: boolean,
 *          includeSilks: boolean,
 *          includeBetting: boolean,
 *          includeSelections: boolean,
 *          includeSpeedMaps: boolean,
 *          includeComments: boolean,
 *          includeRaIds: boolean
 *      }
 *  }
 */

const saxon = require("saxon-js");
const {lambdaPowertools: {logger}, deliveryLogger: {writeDeliveryLog}} = require("@mediality/centaur");
const env = saxon.getPlatform();

const transformService = {

    transformAsync: async (masterXmlContent, xsltStylesheet, clientProps, destinationPath='destinationPath') => {
        logger.info(`Starting transformAsync process`);

        let logData = {
            ...clientProps,
            destinationPath,
            status: 'STARTING_TRANSFORM_ASYNC_PROCESS',
        }
        await writeDeliveryLog(logData);
        try {
            // Compile our XSLT stylesheet into SEF format
            const xslt = env.parseXmlFromString(env.readFile(xsltStylesheet));
            xslt._saxonBaseUri = "file:///";
            const sef = saxon.compile(xslt);

            // Prepare parameters to pass into the stylesheet
            const params = {
                includeGearChanges: clientProps.permissions.includeGearChanges,
                includeRatings: clientProps.permissions.includeRatings,
                includeSilks: clientProps.permissions.includeSilks,
                includeBetting: clientProps.permissions.includeBetting,
                includeSelections: clientProps.permissions.includeSelections,
                includeSpeedMaps: clientProps.permissions.includeSpeedMaps,
                includeComments: clientProps.permissions.includeComments,
                includeRaIds: clientProps.permissions.includeRaIds || false, 
            };

            // Transform the master XML file using the specfied XSLT stylesheet
            const transformedXML = await saxon.transform({
                sourceText: masterXmlContent,
                stylesheetInternal: sef,
                stylesheetParams: params,
                destination: "serialized",
                outputProperties: {
                    method: "xml",
                    indent: true
                }
            }, 'async');

            logger.info(`Finished transformAsync process`);
            logData = {
                ...clientProps,
                destinationPath,
                status: 'FINISHED_TRANSFORM_ASYNC_PROCESS',
            }
            await writeDeliveryLog(logData);
            return transformedXML.principalResult;
        } catch (err) {
            logger.error('Error transformAsync client output xml file', err);
            throw err;
        }
    }

}

module.exports = transformService;
