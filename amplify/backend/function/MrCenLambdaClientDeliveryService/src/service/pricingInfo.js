const { parseFilename } = require("./lib/parsers.js");
const { getClassification, getRegion } = require("./lib/mappers.js");
/**
 * Constants for valid racing data parameters
 */
const VALID_PARAMETERS = {
  // Data Source & Price Model
  DATA_SOURCES: {
    RACING_AUSTRALIA: {
      WHOLESALE: "RW", // Racing Australia, Wholesale
      RETAIL: "RR",    // Racing Australia, Retail
    },
    MEDIALITY_RACING: {
      WHOLESALE: "MW", // Mediality Racing, Wholesale
      RETAIL: "MR",    // Mediality Racing, Retail
    }
  },

  // Data Types by Data Source
  DATA_TYPES: {
    RACING_AUSTRALIA: {
      NOMINATIONS: "N",  // Fields @ Nominations (No Form)
      WEIGHTS: "W",      // Fields @ Weights (No Form)
      ACCEPTANCES: "A",  // Fields @ Acceptances (No Form)
      RESULTS: "R",      // Results
    },
    MEDIALITY_RACING: {
      COMMENTS: "C",     // Comments
      RATINGS: "T",      // Ratings
      SILKS: "S",        // Jockey Silks
      FORM: "F",         // Form
    }
  },

  // Customer Types/Tiers by Data Source
  CUSTOMER_TIERS: {
    RACING_AUSTRALIA: [
      "A", // Above $100m in turnover
      "B", // Between $100m and $50m turnover
      "C", // Between $50m and $25m turnover
      "D", // Between $25m and $10m turnover
      "E", // Between $10m and $2m turnover
      "F", // Below $2m turnover
      "G"  // Personal Use
    ],
    MEDIALITY_RACING: [
      "1", // Tier 1
      "2", // Tier 2
      "P", // Personal Use
      "W"  // Wholesale
    ]
  },

  // Meet Locations (common across data sources)
  MEET_LOCATIONS: {
    METRO_SS: "S",   // Metro SS (Weekends)
    METRO_MID: "M",  // Metro Mid (Weekday)
    PROVINCIAL: "P", // Provincial
    COUNTRY: "C",    // Country
  },

  // Categories (only for Racing Australia)
  CATEGORIES: {
    TAB: "T",     // TAB
    NON_TAB: "N", // Non-TAB
  }
};

/**
 * Validate and determine data source based on provider and price model
 * @param {Object} provider - The racing provider configuration
 * @param {string} providerName - The name of the provider ('racingAustralia' or 'medialityRacing')
 * @returns {string|null} - Valid data source code or null if invalid
 */
function validateDataSource(provider, providerName) {
  if (!provider || !provider.enabled) {
    return null;
  }

  const priceModel = provider.price_model;

  if (providerName === 'racingAustralia') {
    if (priceModel === 'W') {
      return VALID_PARAMETERS.DATA_SOURCES.RACING_AUSTRALIA.WHOLESALE;
    } else {
      return VALID_PARAMETERS.DATA_SOURCES.RACING_AUSTRALIA.RETAIL;
    }
  } else if (providerName === 'medialityRacing') {
    if (priceModel === 'W') {
      return VALID_PARAMETERS.DATA_SOURCES.MEDIALITY_RACING.WHOLESALE;
    } else {
      return VALID_PARAMETERS.DATA_SOURCES.MEDIALITY_RACING.RETAIL;
    }
  }

  return null;
}

/**
 * Validate that the data type is valid for the given provider
 * @param {string} typeChar - The data type character
 * @param {string} dataSource - The data source code (RW, RR, MW, MR)
 * @returns {string|null} - Valid data type or null if invalid
 */
function validateDataType(typeChar, dataSource) {
  // For Racing Australia
  if (dataSource === VALID_PARAMETERS.DATA_SOURCES.RACING_AUSTRALIA.WHOLESALE ||
      dataSource === VALID_PARAMETERS.DATA_SOURCES.RACING_AUSTRALIA.RETAIL) {

    const validTypes = Object.values(VALID_PARAMETERS.DATA_TYPES.RACING_AUSTRALIA);
    return validTypes.includes(typeChar) ? typeChar : null;
  }

  // For Mediality Racing
  if (dataSource === VALID_PARAMETERS.DATA_SOURCES.MEDIALITY_RACING.WHOLESALE ||
      dataSource === VALID_PARAMETERS.DATA_SOURCES.MEDIALITY_RACING.RETAIL) {

    const validTypes = Object.values(VALID_PARAMETERS.DATA_TYPES.MEDIALITY_RACING);
    return validTypes.includes(typeChar) ? typeChar : null;
  }

  return null;
}

/**
 * Validate customer tier for the given provider
 * @param {string} customerTier - The customer tier value
 * @param {string} dataSource - The data source code (RW, RR, MW, MR)
 * @returns {string|null} - Valid customer tier or null if invalid
 */
function validateCustomerTier(customerTier, dataSource) {
  // For Racing Australia
  if (dataSource === VALID_PARAMETERS.DATA_SOURCES.RACING_AUSTRALIA.WHOLESALE ||
      dataSource === VALID_PARAMETERS.DATA_SOURCES.RACING_AUSTRALIA.RETAIL) {

    return VALID_PARAMETERS.CUSTOMER_TIERS.RACING_AUSTRALIA.includes(customerTier) ?
      customerTier : VALID_PARAMETERS.CUSTOMER_TIERS.RACING_AUSTRALIA[0]; // Default to A if invalid
  }

  // For Mediality Racing
  if (dataSource === VALID_PARAMETERS.DATA_SOURCES.MEDIALITY_RACING.WHOLESALE ||
      dataSource === VALID_PARAMETERS.DATA_SOURCES.MEDIALITY_RACING.RETAIL) {

    return VALID_PARAMETERS.CUSTOMER_TIERS.MEDIALITY_RACING.includes(customerTier) ?
      customerTier : VALID_PARAMETERS.CUSTOMER_TIERS.MEDIALITY_RACING[0]; // Default to 1 if invalid
  }

  return null;
}

/**
 * Validate meet location
 * @param {string} location - The meet location value
 * @returns {string} - Valid meet location or default
 */
function validateMeetLocation(location) {
  const validLocations = Object.values(VALID_PARAMETERS.MEET_LOCATIONS);
  return validLocations.includes(location) ?
    location : VALID_PARAMETERS.MEET_LOCATIONS.METRO_SS; // Default to S if invalid
}

/**
 * Validate category (TAB/NON-TAB)
 * @param {boolean} isTab - Whether the meeting is a TAB meeting
 * @returns {string} - Valid category code
 */
function validateCategory(isTab) {
  return isTab ? VALID_PARAMETERS.CATEGORIES.TAB : VALID_PARAMETERS.CATEGORIES.NON_TAB;
}

/**
 * Generate item price IDs based on event parameters with validation
 * @param {Object} event - The event object containing configuration
 * @param {string} typeChar - Type character (A/W/N/R)
 * @param {string} location - Meet location
 * @param {boolean} isTab - Whether it's a TAB meeting
 * @returns {Array} - Array of valid item price IDs
 */
function generateItemPriceIds(event, typeChar, location, isTab) {
  const billingCurrency = "AUD";
  const billingFrequency = "1M";

  // Validate location and category
  const validLocation = validateMeetLocation(location);
  const category = validateCategory(isTab);

  const ids = [];

  // Process Racing Australia if enabled
  if (event.racingAustralia && event.racingAustralia.enabled) {
    // Validate data source
    const dataSource = validateDataSource(event.racingAustralia, 'racingAustralia');

    if (dataSource) {
      // Validate data type
      const dataType = validateDataType(typeChar, dataSource);

      if (dataType) {
        // Validate customer tier
        const customerTier = validateCustomerTier(
          event.racingAustralia.pricing_tier,
          dataSource
        );

        if (customerTier) {
          ids.push(
            `${dataSource}-${dataType}-${customerTier}-${validLocation}-${category}-${billingCurrency}-${billingFrequency}`
          );

          console.log(`Generated Racing Australia item price ID: ${ids[ids.length-1]}`);
        } else {
          console.warn('Invalid Racing Australia customer tier:', event.racingAustralia.pricing_tier);
        }
      } else {
        console.warn('Invalid Racing Australia data type:', typeChar);
      }
    } else {
      console.warn('Invalid Racing Australia data source configuration');
    }
  }

  // Process Mediality Racing if enabled
  if (event.medialityRacing && event.medialityRacing.enabled) {
    // Validate data source
    const dataSource = validateDataSource(event.medialityRacing, 'medialityRacing');

    if (dataSource) {
      // Validate data type
      const dataType = validateDataType(typeChar, dataSource);

      if (dataType) {
        // Validate customer tier
        const customerTier = validateCustomerTier(
          event.medialityRacing.pricing_tier,
          dataSource
        );

        if (customerTier) {
          ids.push(
            `${dataSource}-${dataType}-${customerTier}-${validLocation}-${billingCurrency}-${billingFrequency}`
          );

          console.log(`Generated Mediality Racing item price ID: ${ids[ids.length-1]}`);
        } else {
          console.warn('Invalid Mediality Racing customer tier:', event.medialityRacing.pricing_tier);
        }
      } else {
        console.warn('Invalid Mediality Racing data type:', typeChar);
      }
    } else {
      console.warn('Invalid Mediality Racing data source configuration');
    }
  }

  return ids;
}

/**
 * Generate Mediality Racing item price IDs from fields access with validation
 * @param {Object} event - The event configuration
 * @param {Object} clientProps - Client properties
 * @param {string} typeChar - Type character
 * @param {string} location - Meet location
 * @returns {Array} - Array of valid item price IDs
 */
function generateMedialityItemPriceIdsFromFieldsAccess(
  event,
  clientProps,
  typeChar,
  location
) {
  // Map field access to data types
  const FIELD_ACCESS_TO_DATA_TYPE = {
    COMMENT: VALID_PARAMETERS.DATA_TYPES.MEDIALITY_RACING.COMMENTS,    // C
    RATINGS: VALID_PARAMETERS.DATA_TYPES.MEDIALITY_RACING.RATINGS,     // T
    SILKS: VALID_PARAMETERS.DATA_TYPES.MEDIALITY_RACING.SILKS,         // S
    FORM: VALID_PARAMETERS.DATA_TYPES.MEDIALITY_RACING.FORM,           // F
  };

  const ids = [];
  const billingCurrency = "AUD";
  const billingFrequency = "1M";

  // Validate location
  const validLocation = validateMeetLocation(location);

  // Only process if Mediality Racing is enabled and requirements are met
  if (
    !event.medialityRacing?.enabled ||
    !event.medialityRacing.pricing_tier ||
    typeChar != "A"  // Only process for Acceptances
  ) {
    return [];
  }

  // Validate data source
  const dataSource = validateDataSource(event.medialityRacing, 'medialityRacing');
  if (!dataSource) {
    console.warn('Invalid Mediality Racing data source configuration');
    return [];
  }

  // Validate customer tier
  const customerTier = validateCustomerTier(
    event.medialityRacing.pricing_tier,
    dataSource
  );

  if (!customerTier) {
    console.warn('Invalid Mediality Racing customer tier:', event.medialityRacing.pricing_tier);
    return [];
  }

  for (const field of event.fieldsAccess) {
    const dataType = FIELD_ACCESS_TO_DATA_TYPE[field];

    if (dataType) {
      // Note: For Mediality Racing, we don't include the category (T/N)
      const itemPriceId = `${dataSource}-${dataType}-${customerTier}-${validLocation}-${billingCurrency}-${billingFrequency}`;
      ids.push(itemPriceId);

      console.log(`Generated Mediality field access item price ID: ${itemPriceId}`);
    } else {
      console.warn(`No matching data type for field access: ${field}`);
    }
  }

  return ids;
}

const extractDeliveryInfo = (event, xmlContent, clientProps) => {
  try {
    console.log("event", event);
    console.log("clientProps", clientProps);
    const objectKey = event.source.objectKey;
    const { date, trackCode, fileCategory, typeChar, original, isValid } =
      parseFilename(objectKey);
    console.log("date", date);
    console.log("trackCode", trackCode);
    console.log("fileCategory", fileCategory);
    console.log("stage", typeChar);
    console.log("isValid", isValid);
    console.log("original", original);
    let location = clientProps.location || "";
    console.log("Original location:", location);
    if (location === "M") {
      const meetingDate = new Date(date);
      const dayOfWeek = meetingDate.getDay();
      // If it's a weekend (Saturday = 6 or Sunday = 0),change it to 'S'
      // If it's a weekday (Monday-Friday),  keep as 'M'
      if (dayOfWeek === 0 || dayOfWeek === 6) {
        // Weekend - change to 'S'
        location = "S";
        console.log("Location is M and event is on weekend, change to S");
      }
    } else {
      console.log(`Location is ${location}, keeping as is`);
    }
    // Parse XML to extract country
    let country = "";
    console.log("version", typeChar);
    // Extract country from XML using regex (for simplicity)
    const countryMatch = xmlContent.match(/<track\s+.*?country="([^"]+)"/);
    if (countryMatch && countryMatch[1]) {
      country = countryMatch[1];
    }
    // Check if the filename ends with T (for trail)
    const isTrail = objectKey.endsWith("T.xml");

    // Set stage_value based on stage
    let stage_value;
    switch (typeChar) {
      case "A":
        stage_value = "Acceptance";
        break;
      case "W":
        stage_value = "Weights";
        break;
      case "N":
        stage_value = "Nominations";
        break;
      default:
        stage_value = typeChar;
    }
    // Get meeting classification and region
    const classification = getClassification(trackCode);
    const region = getRegion(trackCode);

    // Parse tab_indicator from XML
    let isTab = false;
    const tabMatch = xmlContent.match(
      /<tab_indicator>([^<]+)<\/tab_indicator>/
    );
    if (tabMatch && tabMatch[1] === "T") {
      isTab = true;
    }

    // Determine if silks are included
    const hasSilks = clientProps.permissions.includeSilks;

    // Generate item price IDs based on fileCategory
    let itemPriceIds = [];
    let additionalMedialityItemPriceIds = [];

    console.log(`Processing fileCategory: ${fileCategory}`);

    if (fileCategory === "FIELDS") {
      // Generate Racing Australia item price IDs for FIELDS category
      console.log("Generating item price IDs for FIELDS category");
      itemPriceIds = generateItemPriceIds(event, typeChar, location, isTab);
    } else if (fileCategory === "FORM") {
      // Generate Mediality Racing item price IDs for FORM category
      console.log("Generating Mediality item price IDs for FORM category");
      additionalMedialityItemPriceIds = generateMedialityItemPriceIdsFromFieldsAccess(
        event,
        clientProps,
        typeChar,
        location,
        isTab
      );
    } else {
      // If fileCategory is not specified or is different, generate both (backward compatibility)
      console.log(`Unknown or missing fileCategory: ${fileCategory}, generating both types of IDs for backward compatibility`);
      itemPriceIds = generateItemPriceIds(event, typeChar, location, isTab);
      additionalMedialityItemPriceIds = generateMedialityItemPriceIdsFromFieldsAccess(
        event,
        clientProps,
        typeChar,
        location,
        isTab
      );
    }
      let RApricingTier;
      let MRpricingTier;
      if (event.racingAustralia && event.racingAustralia.enabled === true) {
        RApricingTier = event.racingAustralia.pricing_tier;
      }
      if (event.medialityRacing && event.medialityRacing.enabled === true) {
        MRpricingTier = event.medialityRacing.pricing_tier;
      }
    return {
      deliveryInfo: {
        // Client Information
        clientId: event.clientFolder,
        customer_name: event.customer_name,
        clientPermissions: clientProps.permissions,
        pricingTier: RApricingTier,
        MRpricingTier: MRpricingTier,
        client_type: clientProps.client_type,
        meetingId: clientProps.raMeetingId, // Added meetingId
        chargebee_customer_id: clientProps.chargebee_customer_id,
        meetingDate: date,
        trackCode,
        classification,
        region,
        isTab,
        country, // Added country
        stage_value, // Added stage_value
        isTrail, // Added isTrail flag
        location,
        // File Information
        original,
        typeChar,
        fileCategory,
        hasSilks,

        // Delivery Details
        sourcePath: event.source.objectKey,
        destinationPath: `${event.clientFolder}/${event.destination.objectKey}`,
        deliveryTimestamp: new Date().toISOString(),
        item_price_ids: [...itemPriceIds, ...additionalMedialityItemPriceIds],
        // Additional Metadata
        fieldsAccess: event.fieldsAccess || [],
        status: "DELIVERED",
      },

      // Include raw data for reference or additional processing
      rawData: {
        sourceEvent: event,
        clientProps,
        parsedFilename: { date, trackCode, fileCategory, typeChar },
        isAustralian: country === "AUS", // Flag for easy checking
      },
    };
  } catch (error) {
    console.error("Error extracting delivery information:", error);
    throw error;
  }
};

module.exports = {
  extractDeliveryInfo,
};
