/**
 * Parse metadata from filename based on Racing XML file naming convention
 * Format: YYYYMMDD_TRACK_TYPE_FORMAT_VERSION.xml
 * Example: 20250226_ROS_FIELDS_XML_A.xml
 * 
 * @param {string} filename - The filename to parse
 * @returns {Object} Parsed metadata
 */
const parseFilename = (filename) => {
    try {
        console.log('filename', filename);
        
        // Remove file extension
        const withoutExtension = filename.replace(/\.xml$/i, '');
        
        // First, check if it's a RACE_BY_RACE format
        const isRaceByRace = withoutExtension.includes('_RACE_BY_RACE_');
        
        // Split by underscore
        const parts = withoutExtension.split('_');
        console.log('parts', parts);
        
        // Validate parts length
        if (parts.length < 3) {
            throw new Error(`Invalid filename format: ${filename}. Expected at least 3 parts.`);
        }
        
        // 1. Extract date (just return the raw date string as requested)
        const date = parts[0];
        if (!/^\d{8}$/.test(date)) {
            throw new Error(`Invalid date format in filename: ${date}. Expected format: YYYYMMDD`);
        }
        
        // 2. Extract track code (second part)
        const trackCode = parts[1];
        if (!trackCode) {
            throw new Error('Track code is missing from filename');
        }
        
        // 3. Determine if it's FORM or FIELDS
        let fileCategory = '';
        // Check various parts for FORM or FIELDS
        for (let i = 2; i < parts.length; i++) {
            if (parts[i] === 'FORM') {
                fileCategory = 'FORM';
                break;
            } else if (parts[i] === 'FIELDS') {
                fileCategory = 'FIELDS';
                break;
            }
        }
        
        // 4. Determine type character (A, N, W, or R)
        let typeChar = '';
        
        // Check if filename contains _A, _N, or _W
        if (filename.includes('_A.xml')) {
            typeChar = 'A';
        } else if (filename.includes('_N.xml')) {
            typeChar = 'N';
        } else if (filename.includes('_W.xml')) {
            typeChar = 'W';
        } 
        // Check if it's RESULTS or RACE_BY_RACE or ends with _T.XML
        else if (
            isRaceByRace || 
            parts.includes('RESULTS') || 
            filename.toUpperCase().endsWith('_T.XML')
        ) {
            typeChar = 'R';
        }
        
        return {
            date,           // The raw date string
            trackCode,      // Track identifier 
            fileCategory,   // FORM or FIELDS if present
            typeChar,    
            original: filename
        };
    } catch (error) {
        console.error(`Error parsing filename: ${error.message}`);
        return {
            isValid: false,
            error: error.message,
            original: filename
        };
    }
};

/**
 * Validate if a filename matches the expected format
 * @param {string} filename - Filename to validate
 * @returns {boolean} - True if valid
 */
const isValidFilename = (filename) => {
    const parsed = parseFilename(filename);
    return parsed.isValid;
};

module.exports = {
    parseFilename,
    isValidFilename
};