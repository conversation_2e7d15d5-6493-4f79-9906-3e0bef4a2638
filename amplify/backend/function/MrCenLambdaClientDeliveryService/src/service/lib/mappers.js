const CLASSIFICATIONS = {
    'M': 'METRO',
    'C': 'COUNTRY',
    'P': 'PROVINCIAL',
    '': 'INTERNATIONAL',
    'I': 'INTERNATIONAL',
};

const REGIONS = {
    0: '',
    1: 'ACT',
    2: 'NSW',
    3: 'VIC',
    4: 'QLD',
    5: 'SA',
    6: 'WA',
    7: 'TAS',
    8: 'NT',
    9: 'NZ',
    10: 'HK',
    11: 'SGP',
};

const getClassification = location => {
    return CLASSIFICATIONS?.[location] || '';
}

const getRegion = state => {
    return REGIONS?.[state] || '';
}

module.exports = {
    CLASSIFICATIONS,
    REGIONS,
    getClassification,
    getRegion,
}
