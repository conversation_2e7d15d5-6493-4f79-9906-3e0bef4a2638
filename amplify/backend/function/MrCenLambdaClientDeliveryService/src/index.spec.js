const eventJson = require('./event.json');
const lambda = require('./index');
require('@mediality/centaur/lib/deliveryLogger')
const {lambdaPowertools: {tracer}} = require('@mediality/centaur')
const {sdkStreamMixin} = require('@aws-sdk/util-stream-node');
const {createReadStream} = require('fs');
const fs = require('fs').promises;
const transformService = require('./service/transformService');
const {getTmpFilePath} = require('./lib/util');

jest.mock('@mediality/centaur');
jest.mock('./service/transformService');
jest.mock('./lib/util')

const tmpFilePath = '/tmp/test.xml';
const testMasterFilePath = './test/data/test.xml';
const MASTER_FILE_ETAG = '"041f8d1344ebe05e2c822096fddc329c"'; // double quote is required

describe('lambdaService', () => {
    beforeEach(() => {
        jest.restoreAllMocks();
    });

    test('Should successfully upload a new client output xml file to S3', async () => {
        const xmlContent = await fs.readFile(testMasterFilePath, 'utf-8')
        const stream = createReadStream(testMasterFilePath);

        // wrap the Stream with SDK mixin
        const sdkStream = sdkStreamMixin(stream);

        const send = jest
            .fn()
            .mockImplementationOnce(() => ({
                Body: sdkStream
            }))
            .mockImplementationOnce(() => {
                throw {
                    '$metadata': {
                        httpStatusCode: 404,
                    },
                };
            })
            .mockImplementationOnce(() => ({
                ETag: '"PutCommandETag9748bb039d69ae"',
                VersionId: "RandomVersionId1XLOefGskR7mGDwcDk"
            }))

        tracer.captureAWSv3Client.mockImplementation(() => ({
            send
        }))

        transformService.transformAsync.mockReturnValue(xmlContent);

        const actualValue = await lambda.handler(eventJson, {});
        expect(actualValue.status).toEqual('SUCCESSFULLY_PROCESSED_AND_UPLOADED_NEW_CLIENT_OUTPUT_XML_FILE');
        expect(send).toHaveBeenCalledTimes(3);
    });

    test('Should successfully update an existing client output xml file to S3 with different ETag', async () => {
        getTmpFilePath.mockReturnValue(tmpFilePath);
        const xmlContent = await fs.readFile(testMasterFilePath, 'utf-8')
        const stream = createReadStream(testMasterFilePath);

        // wrap the Stream with SDK mixin
        const sdkStream = sdkStreamMixin(stream);

        const send = jest
            .fn()
            .mockImplementationOnce(() => ({
                Body: sdkStream
            }))
            .mockImplementationOnce(() => ({
                ETag: '"differentETag"'
            }))
            .mockImplementationOnce(() => ({
                ETag: '"PutCommandETag9748bb039d69ae"',
                VersionId: "RandomVersionId1XLOefGskR7mGDwcDk"
            }))

        tracer.captureAWSv3Client.mockImplementation(() => ({
            send
        }))

        transformService.transformAsync.mockReturnValue(xmlContent);

        const actualValue = await lambda.handler(eventJson, {});
        expect(actualValue.status).toEqual('SUCCESSFULLY_PROCESSED_AND_REPLACED_EXISTING_CLIENT_OUTPUT_XML_FILE');
        await fs.unlink(tmpFilePath);
        expect(send).toHaveBeenCalledTimes(3);
    });

    test('Should successfully complete the process with no content change based on ETag comparison', async () => {
        getTmpFilePath.mockReturnValue(tmpFilePath);
        const xmlContent = await fs.readFile(testMasterFilePath, 'utf-8')
        const stream = createReadStream(testMasterFilePath);

        // wrap the Stream with SDK mixin
        const sdkStream = sdkStreamMixin(stream);

        const send = jest
            .fn()
            .mockImplementationOnce(() => ({
                Body: sdkStream
            }))
            .mockImplementationOnce(() => ({
                ETag: MASTER_FILE_ETAG
            }))

        tracer.captureAWSv3Client.mockImplementation(() => ({
            send
        }));

        transformService.transformAsync.mockReturnValue(xmlContent);

        const actualValue = await lambda.handler(eventJson, {});
        expect(actualValue.status).toEqual('SUCCESSFULLY_PROCESSED_AND_FINISHED_WITH_NO_CHANGES');
        await fs.unlink(tmpFilePath);
        expect(send).toHaveBeenCalledTimes(2);
    });
});
