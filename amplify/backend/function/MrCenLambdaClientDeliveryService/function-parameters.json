{"lambdaLayers": [{"type": "ProjectLayer", "resourceName": "centaurappCentaurAppCommonLayer", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "stgblack"}], "environmentVariableList": [{"cloudFormationParameterName": "centaurSecrets", "environmentVariableName": "centaurSecrets"}, {"cloudFormationParameterName": "centaurSecretsId", "environmentVariableName": "centaurSecretsId"}]}