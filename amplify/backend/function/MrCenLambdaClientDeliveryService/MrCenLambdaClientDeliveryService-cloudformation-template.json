{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.10.0\",\"stackType\":\"function-Lambda\",\"metadata\":{}}", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}, "functioncentaurappCentaurAppCommonLayerArn": {"Type": "String", "Default": "functioncentaurappCentaurAppCommonLayerArn"}, "centaurSecrets": {"Type": "String"}, "centaurSecretsId": {"Type": "String"}, "customMrCenDeliveryServiceXmlOutputStoragebucketArn": {"Type": "String", "Default": "customMrCenDeliveryServiceXmlOutputStoragebucketArn"}, "customMrCenDeliveryServiceXmlSourceStoragebucketArn": {"Type": "String", "Default": "customMrCenDeliveryServiceXmlSourceStoragebucketArn"}, "customMrCenDeliveryServiceXmlOutputStoragebucketName": {"Type": "String", "Default": "customMrCenDeliveryServiceXmlOutputStoragebucketName"}, "customMrCenDeliveryServiceXmlSourceStoragebucketName": {"Type": "String", "Default": "customMrCenDeliveryServiceXmlSourceStoragebucketName"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "MrCenLambdaClientDeliveryService", {"Fn::Join": ["", ["MrCenLambdaClientDeliveryService", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "centaurSecrets": {"Ref": "centaurSecrets"}, "centaurSecretsId": {"Ref": "centaurSecretsId"}, "CHARGEBEE_QUEUE_NAME": {"Fn::Join": ["", ["Mr<PERSON>enChar<PERSON>bee<PERSON><PERSON>ue-", {"Ref": "env"}]]}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs18.x", "MemorySize": 2048, "Layers": [{"Ref": "functioncentaurappCentaurAppCommonLayerArn"}], "VpcConfig": {"SecurityGroupIds": ["{{resolve:ssm:centaurLambdaDbSecurityGroup}}"], "SubnetIds": ["{{resolve:ssm:centaurVpcAppSubnet0}}", "{{resolve:ssm:centaurVpcAppSubnet1}}", "{{resolve:ssm:centaurVpcAppSubnet2}}"]}, "Timeout": 25, "TracingConfig": {"Mode": "Active"}}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "centaurappLambdaRole0a435845", {"Fn::Join": ["", ["centaurappLambdaRole0a435845", "-", {"Ref": "env"}]]}]}, "Path": "/", "ManagedPolicyArns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole", "arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess"], "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "LambdaCloudwatchExecutionPolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "LambdaCloudwatchExecutionPolicy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}, "LambdaSQSExecutionPolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "LambdaSQSExecutionPolicy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["sqs:SendMessage", "sqs:GetQueueUrl", "sqs:<PERSON><PERSON><PERSON>ues"], "Resource": {"Fn::Sub": ["arn:aws:sqs:${region}:${account}:MrCenChargebeeQueue-${env}", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "env": {"Ref": "env"}}]}}]}}}, "LambdaSecretsManagerReadOnlyExecutionPolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "LambdaSecretsManagerReadOnlyExecutionPolicy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["secretsmanager:GetSecretValue"], "Resource": {"Fn::Sub": ["arn:aws:secretsmanager:${region}:${account}:secret:${centaurSecretsId}", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "centaurSecretsId": {"Ref": "centaurSecretsId"}}]}}]}}}, "LambdaS3ExecutionPolicy": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "LambdaS3ExecutionPolicy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Action": ["s3:PutObject", "s3:GetObject"], "Resource": [{"Fn::Join": ["", [{"Ref": "customMrCenDeliveryServiceXmlOutputStoragebucketArn"}, "/*"]]}], "Effect": "Allow"}, {"Action": ["s3:GetObject"], "Resource": [{"Fn::Join": ["", [{"Ref": "customMrCenDeliveryServiceXmlSourceStoragebucketArn"}, "/*"]]}], "Effect": "Allow"}]}, "Roles": [{"Ref": "LambdaExecutionRole"}]}, "DependsOn": "LambdaExecutionRole"}, "InvocationErrorAlarm": {"Type": "AWS::CloudWatch::Alarm", "DependsOn": ["LambdaFunction"], "Properties": {"AlarmActions": [{"Fn::Sub": ["arn:aws:sns:${region}:${account}:MrCenAlarms-${environment}", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "environment": {"Ref": "env"}}]}], "AlarmName": {"Fn::Join": ["", ["LambdaError-", {"Ref": "LambdaFunction"}]]}, "ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "FunctionName", "Value": {"Ref": "LambdaFunction"}}], "EvaluationPeriods": 1, "MetricName": "Errors", "Namespace": "AWS/Lambda", "Period": 60, "Statistic": "Maximum", "Threshold": 1, "TreatMissingData": "notBreaching"}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}}}