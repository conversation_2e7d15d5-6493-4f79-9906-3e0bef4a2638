const {
    DocDBClient,
    DescribeD<PERSON><PERSON>sCommand,
    StartDBClusterCommand,
    StartDBClusterCommandInput,
    StopDBClusterCommand,
    StopDBClusterCommandInput } = require("@aws-sdk/client-docdb");


const client = new DocDBClient();

exports.handler = async (event) => {
    try {
        if (process.env.ENV == 'stg' || process.env.ENV == 'dev') {
            var cluster_name = `mr-${process.env.ENV}-cen-docdb`
            var command = new DescribeDBClustersCommand(cluster_name);
            var response = await client.send(command);

            for (var index = 0; index < response.DBClusters.length; ) {
                if (response.DBClusters[index]['DBClusterIdentifier'] == cluster_name && response.DBClusters[index]['Status'] == 'available') {
                        console.log(`Stopping DB Cluster: ${cluster_name}`)
                        var dbComm = new StopDBClusterCommand({ DBClusterIdentifier: cluster_name });
                        var db_resp = await client.send(dbComm);
                        console.log(stopResp);
                } else if (
                    response.DBClusters[index]['DBClusterIdentifier'] == cluster_name &&
                    response.DBClusters[index]['Status'] == 'stopped') {
                        console.log(`Starting DB Cluster: ${cluster_name}`)
                        var dbComm = new StartDBClusterCommand({ DBClusterIdentifier: cluster_name });
                        var db_resp = await client.send(dbComm);
                        console.log(startResp);
                }
                else {
                    console.log("Found Database in unexpected state! Skipping Shutdown/Startup Operations")
                }
                index++;
            }
        }

        var response = {
            body: JSON.stringify(db_resp),
        };
        return response;
    } catch (err) {
        console.log(err);
    }
};


this.handler({});
