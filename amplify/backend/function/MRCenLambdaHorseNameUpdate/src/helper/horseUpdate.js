const helper = require("../library/helper");
const centaur = require("@mediality/centaur");
let con;
/**
 * Performs database operations using the `centaur` object based on provided horse information.
 *
 * @param {string} horseId - The ID of the horse.
 * @param {string} horseDisplayName - The display name of the horse.
 * @param {string[]} prevNames - Array of previous names of the horse.
 * @returns {Promise<void>}
 */
const updateHorseData = async (horseId, horseDisplayName, prevNames) => {
  try {
    con = await helper.openDBConnection();
    if (typeof prevNames === "string") {
      prevNames = prevNames.includes(";") ? prevNames.split(";") : [prevNames];
    }
    if (!prevNames) {
      prevNames = [];
    } else if (typeof prevNames === "string") {
      prevNames = prevNames.includes(";") ? prevNames.split(";") : [prevNames];
    }
    prevNames = prevNames
      .filter((name) => name)
      .map((name) => name.toUpperCase());
    const otherRuns = await centaur.form_index
      .find({ $and: [{ horse_id: horseId }, { finish_pos: { $lt: 5 } }] })
      .sort({ meeting_date: 1 })
      .lean();
    for (const run of otherRuns) {
      const formIndexRuns = await centaur.form_index
        .find({ event_id: run.event_id })
        .lean();
      runLoop: for (const runHorse of formIndexRuns) {
        const otherForm = await centaur.form
          .findOne({ horse_id: runHorse.horse_id })
          .lean();
        if (otherForm.form && otherForm.form.length > 0) {
          for (
            let otherIndex = 0;
            otherIndex < otherForm.form.length;
            otherIndex++
          ) {
            const otherItem = otherForm.form[otherIndex];

            if (
              String(otherItem.event_id).trim() === String(run.event_id).trim()
            ) {
              const others = otherItem.other_runners.other_runner;
              for (
                let otherRunnerIndex = 0;
                otherRunnerIndex < others.length;
                otherRunnerIndex++
              ) {
                const runner = others[otherRunnerIndex];
                if (
                  runner["@_horse"].toUpperCase() ===
                  horseDisplayName.toUpperCase()
                )
                  continue runLoop;
                if (prevNames.includes(runner["@_horse"].toUpperCase())) {
                  others[otherRunnerIndex]["@_horse"] = horseDisplayName;
                  const dataToUpdateOther = `form.${otherIndex}.other_runners.other_runner`;

                  await centaur.form
                    .updateOne(
                      { _id: otherForm._id },
                      { $set: { [dataToUpdateOther]: others } }
                    )
                    .lean();

                  continue runLoop;
                }
              }
            }
          }
        }
      }
    }
  } catch (error) {
    console.error("Error updating horse", error);
    if (con) await helper.closeDBConnection(con);
  } finally {
    if (con) await helper.closeDBConnection(con);
  }
};

module.exports = { updateHorseData };
