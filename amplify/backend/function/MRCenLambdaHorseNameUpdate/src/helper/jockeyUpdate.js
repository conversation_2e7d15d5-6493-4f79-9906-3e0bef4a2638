const centaur = require("@mediality/centaur");
const helper = require("../library/helper");
let con;
const getDistinctYears = async (jockey_id) => {
  try {
    const dates = await centaur.form_index.distinct("meeting_date", { "jockey_id": jockey_id }).lean();
    
    // Extract years and remove duplicates
    let months = dates.map((date) => {
      const d = new Date(date);
      return { year: d.getFullYear(), month: d.getMonth() + 1 };
    });

    // Remove duplicates
    let uniqueMonths = [...new Map(months.map(item => [item.year + '-' + item.month, item])).values()];

    // Sort by year and month
    uniqueMonths.sort((a, b) => b.year - a.year || b.month - a.month);
    return uniqueMonths;
  } catch (error) {
    console.error("Error fetching distinct years:", error);
    throw error;
  }
};

const updateJockeyData = async (jox) => {
  try {
    console.log("Updating jockey", jox);
    con = await helper.openDBConnection();
    const uniqueMonths = await getDistinctYears(jox.JOC_JOCKEY_ID);
    try {
      const stateMachineARN = `arn:aws:states:${process.env.REGION}:${process.env.AWS_ACCOUNT_ID}:stateMachine:MrCenDataUpdateStepFunction-${process.env.ENV}`;
      const aws = require("aws-sdk");
      var stepFunctions = new aws.StepFunctions();
      console.log('stateMachineARN', `${stateMachineARN}`);
      const paramsArray = {
        stateMachineArn: stateMachineARN,
        input: JSON.stringify({ uniqueMonths, jox }),
        name: `ProcessData-${new Date().getTime()}`, // Unique name for the execution
      };
      var response = await stepFunctions.startExecution(paramsArray).promise();
      if (response) {
        console.log(response);
        let data = `Step Function Execution started for jockeyInDB`;
        console.log(data);
      }
    } catch (error) {
      console.log("error", error);
    }
  } catch (error) {
    console.error("Error updating jockey data:", error);
    if (con) await helper.closeDBConnection(con);
  } finally {
    if (con) await helper.closeDBConnection(con);
  }
};

module.exports = { updateJockeyData };
