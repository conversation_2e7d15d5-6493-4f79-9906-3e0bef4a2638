const {parseFilename, getDestinationObjectKey} = require("./lib/parsers");
const {dbHelpers: {getClientsData}, deliveryLogger: {writeDeliveryLog}, lambdaPowertools: {logger, initTracer}} = require('@mediality/centaur');
const {getClassification, getRegion} = require("./lib/mappers");

exports.handler = async (event, context) => {
    try {
        initTracer();

        logger.addContext(context);
        logger.info(event);

        let logData = {
            sourceBucketName: event.s3.source.bucketName,
            sourceObjectKey: event.s3.source.objectKey,
            destinationBucketName: event.s3.destination.bucketName,
            status: 'STARTING_GET_DELIVERY_MANIFEST',
        }

        logger.info(`Starting to process delivery manifest`, logData);

        await writeDeliveryLog(logData);

        const objectKey = event.s3.source.objectKey;

        const {date, trackCode, fileType, stage} = parseFilename(objectKey);

        const clientData = await getClientsData(trackCode, fileType, stage, getClassification, getRegion) || [];
        const permissions = [];
        const clientsAffected = [];

        clientData.map(client => {
            clientsAffected.push(client?._id);

            const destinationObjectKey = getDestinationObjectKey(
                client?.ftp_use_sub_folders,
                client?.ftp_sub_folders,
                fileType,
                event.s3.source.objectKey
            );

            permissions.push({
                clientFolder: client?._id,
                fieldsAccess: client?.fields_access || [],
                meetingTypePerms: client?.meeting_type_perms || { tab: true, non_tab: true },
                includeRaIds: client?.include_ra_ids || false,
                chargebee_customer_id: client?.chargebee_customer_id || false,
                customer_name: client?.display_name && client?.display_name !== client?.account_holder
                    ? `${client?.account_holder}(${client?.display_name})`
                    : client?.account_holder,
                client_type: client?.client_type,
                racingAustralia: client?.racing_australia || { enabled: false },
                medialityRacing: client?.mediality_racing || { enabled: false },
                source: {
                    bucketName: event.s3.source.bucketName,
                    objectKey: event.s3.source.objectKey
                },
                destination: {
                    bucketName: event.s3.destination.bucketName,
                    objectKey: destinationObjectKey,
                },
                functions: {
                    MrCenLambdaClientDeliveryService: event.functions.MrCenLambdaClientDeliveryService
                }
            });
        });

        logData = {
            clientsAffected,
            sourceBucketName: event.s3.source.bucketName,
            sourceObjectKey: event.s3.source.objectKey,
            destinationBucketName: event.s3.destination.bucketName,
            status: 'FINISHED_GET_DELIVERY_MANIFEST',
        };

        logger.info(`Finished processing delivery manifest`, logData);

        await writeDeliveryLog(logData)

        return {
            permissions
        }
    } catch (err) {
        logger.error('Error getting clients delivery manifest', err);
        throw err;
    }
};
