const eventJson = require('./event.json');
const lambda = require('./index');
const dbHelpers = require('@mediality/centaur/lib/dbHelpers')
require('@mediality/centaur/lib/deliveryLogger')
const mockGetClientsData = require('./lib/__mocks__/dbHelpers/getClientsData.json');
const response1 = require('./lib/__mocks__/response/response1.json');
require('@mediality/centaur/lib/lambdaPowertools')

jest.mock('@mediality/centaur/lib/lambdaPowertools')
jest.mock('@mediality/centaur/lib/dbHelpers');
jest.mock('@mediality/centaur/lib/deliveryLogger');

describe('lambdaService', () => {
    beforeEach(() => {
        jest.restoreAllMocks();
    });

    test('Should successfully return a response with full payload', async () => {
        dbHelpers.getClientsData.mockResolvedValue(mockGetClientsData)
        const actualValue = await lambda.handler(eventJson);
        expect(actualValue).toEqual(response1);
    });

    test('Should successfully return a response with an empty payload', async () => {
        dbHelpers.getClientsData.mockResolvedValue([])
        const actualValue = await lambda.handler(eventJson);
        expect(actualValue).toEqual({
            "permissions": []
        });
    });
});
