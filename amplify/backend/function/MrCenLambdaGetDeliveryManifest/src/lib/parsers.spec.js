const parsers = require('./parsers');
const {clients} = require('@mediality/centaur');

const clientsUseSubFoldersDefaultFieldValues = clients.schema.paths.ftp_use_sub_folders.defaultValue;
const clientsSubFoldersDefaultFieldValues = clients.schema.paths.ftp_sub_folders.defaultValue;

describe('parsers.js tests', () => {
    beforeEach(() => {
        jest.restoreAllMocks();
    });

    test('Should successfully get the meta data based on the filename', async () => {
        const fileTypes = {};
        parsers.FILE_TYPES.map(fileType => {
            fileTypes[fileType.key] = fileType.fileType;
        })

        expect(parsers.parseFilename("20221017_HAW_FIELDS_XML.xml")).toEqual({
            date: '20221017',
            trackCode: 'HAW',
            fileType: fileTypes.FIELDS,
        });

        expect(parsers.parseFilename("20221017_TAO_FORMS_XML.xml")).toEqual({
            date: '20221017',
            trackCode: 'TAO',
            fileType: fileTypes.FORM,
        });

        expect(parsers.parseFilename("20221017_HAW_RESULTS_XML.xml")).toEqual({
            date: '20221017',
            trackCode: 'HAW',
            fileType: fileTypes.RESULTS,
        });

        expect(parsers.parseFilename("20221017_RAS_10_RACE_BY_RACE_XML.xml")).toEqual({
            date: '20221017',
            trackCode: 'RAS',
            fileType: fileTypes.RACE_BY_RACE,
        });

        expect(parsers.parseFilename("20230117_HAW_8_RACE_BY_RACE_XML.xml")).toEqual({
            date: '20230117',
            trackCode: 'HAW',
            fileType: fileTypes.RACE_BY_RACE,
        });

        expect(parsers.parseFilename("20221017_HAW_MEET_SCRATCHINGS_XML.xml")).toEqual({
            date: '20221017',
            trackCode: 'HAW',
            fileType: fileTypes.MEET_SCRATCHINGS,
        });

        expect(parsers.parseFilename("20221212_CWT_GEARS_XML.xml")).toEqual({
            date: '20221212',
            trackCode: 'CWT',
            fileType: fileTypes.GEARS,
        });
    });

    test('Should get the meta data based on an invalid filename', async () => {
        const fileTypes = {};
        parsers.FILE_TYPES.map(fileType => {
            fileTypes[fileType.key] = fileType.fileType;
        })

        expect(parsers.parseFilename("20221017_FIELDS_XML.xml")).toEqual({
            date: '20221017',
            trackCode: fileTypes.FIELDS,
            fileType: '',
        });

        expect(parsers.parseFilename("FORM_XML.xml")).toEqual({
            date: fileTypes.FORM,
            trackCode: 'XML.xml',
            fileType: '',
        });

        expect(parsers.parseFilename("20221212_CWT_gears_XML.xml")).toEqual({
            date: '20221212',
            trackCode: 'CWT',
            fileType: '',
        });

        expect(parsers.parseFilename("20221325_tao_gears_XML.xml")).toEqual({
            date: '20221325',
            trackCode: 'tao',
            fileType: '',
        });
    });

    test(`Should return destinationObjectKey without subfolder when client has ftp_use_sub_folders=false`, () => {
        const destinationObjectKey = parsers.getDestinationObjectKey(
            clientsUseSubFoldersDefaultFieldValues,
            clientsSubFoldersDefaultFieldValues,
            'GEAR CHANGES',
            '20221028_RAS_GEARS_XML.xml'
        );

        expect(destinationObjectKey).toEqual('20221028_RAS_GEARS_XML.xml');
    })

    test(`Should return destinationObjectKey without subfolder when fileType is unknown`, () => {
        const destinationObjectKey = parsers.getDestinationObjectKey(
            true,
            clientsSubFoldersDefaultFieldValues,
            'UNKNOWN FILE TYPE',
            '20221028_RAS_GEARS_XML.xml'
        );

        expect(destinationObjectKey).toEqual('20221028_RAS_GEARS_XML.xml');
    })

    test(`Should return destinationObjectKey with mr_fields subfolder for FIELDS fileType`, () => {
        const destinationObjectKey = parsers.getDestinationObjectKey(
            true,
            clientsSubFoldersDefaultFieldValues,
            'FIELDS',
            '20221028_RAS_FIELDS_XML.xml'
        );

        expect(destinationObjectKey).toEqual('mr_fields/20221028_RAS_FIELDS_XML.xml');
    })

    test(`Should return destinationObjectKey with mr_form subfolder for FORM fileType`, () => {
        const destinationObjectKey = parsers.getDestinationObjectKey(
            true,
            clientsSubFoldersDefaultFieldValues,
            'FORM',
            '20221028_RAS_FORM_XML.xml'
        );

        expect(destinationObjectKey).toEqual('mr_form/20221028_RAS_FORM_XML.xml');
    })

    test(`Should return destinationObjectKey with mr_results subfolder for RESULTS - COLLATED fileType`, () => {
        const destinationObjectKey = parsers.getDestinationObjectKey(
            true,
            clientsSubFoldersDefaultFieldValues,
            'RESULTS - COLLATED',
            '20221028_RAS_RESULTS_XML.xml'
        );

        expect(destinationObjectKey).toEqual('mr_results/20221028_RAS_RESULTS_XML.xml');
    })

    test(`Should return destinationObjectKey with mr_results subfolder for RESULTS - RACE BY RACE fileType`, () => {
        const destinationObjectKey = parsers.getDestinationObjectKey(
            true,
            clientsSubFoldersDefaultFieldValues,
            'RESULTS - RACE BY RACE',
            '20221028_RAS_RACE_BY_RACE_XML.xml'
        );

        expect(destinationObjectKey).toEqual('mr_results/20221028_RAS_RACE_BY_RACE_XML.xml');
    })

    test(`Should return destinationObjectKey with mr_scratchings subfolder for SCRATCHINGS fileType`, () => {
        const destinationObjectKey = parsers.getDestinationObjectKey(
            true,
            clientsSubFoldersDefaultFieldValues,
            'SCRATCHINGS',
            '20221028_RAS_MEET_SCRATCHINGS_XML.xml'
        );

        expect(destinationObjectKey).toEqual('mr_scratchings/20221028_RAS_MEET_SCRATCHINGS_XML.xml');
    })

    test(`Should return destinationObjectKey with mr_gear_changes subfolder for GEAR CHANGES fileType`, () => {
        const destinationObjectKey = parsers.getDestinationObjectKey(
            true,
            clientsSubFoldersDefaultFieldValues,
            'GEAR CHANGES',
            '20221028_RAS_GEARS_XML.xml'
        );

        expect(destinationObjectKey).toEqual('mr_gear_changes/20221028_RAS_GEARS_XML.xml');
    })
});
