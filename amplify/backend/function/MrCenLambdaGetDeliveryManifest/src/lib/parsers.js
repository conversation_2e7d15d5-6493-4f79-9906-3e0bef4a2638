const FILE_TYPES = [
    {
        key: 'RACE_BY_RACE',
        fileType: 'RESULTS - RACE BY RACE',
    },
    {
        key: 'FIELDS',
        fileType: 'FIELDS',
    },
    {
        key: 'FORM',
        fileType: 'FORM',
    },
    {
        key: 'RESULTS',
        fileType: 'RESULTS - COLLATED',
    },
    {
        key: 'MEET_SCRATCHINGS',
        fileType: 'SCRATCHINGS',
    },
    {
        key: 'GEARS',
        fileType: 'GEAR CHANGES',
    }
]

const MEET_STAGES = [
    {
        key: "A",
        stage: 'ACCEPTANCES'
    },
    {
        key: "F",
        stage: 'ACCEPTANCES'
    },
    {
        key: "W",
        stage: 'WEIGHTS'
    },
    {
        key: "N",
        stage: 'NOMINATIONS'
    }
]

const parseFiletype = fileType => (FILE_TYPES.find(FILE_TYPE => fileType.includes(FILE_TYPE.key)))?.fileType || ''

const parseMeetStage = stage => (MEET_STAGES.find(MEET_STAGES => stage.includes(MEET_STAGES.key)))?.stage || 'ACCEPTANCES'

const parseFilename = filename => {
    const [date, trackCode, ...remainingElements] = filename.split("_");
    remainingElements.pop();
    const fileType = parseFiletype(remainingElements.join("_"));
    var stage = 'ACCEPTANCES'
    if (['FIELDS','FORM'].includes(fileType)){
        var ext = filename.split("_")
        stage = parseMeetStage(ext[ext.length - 1].split('.')[0])
    }
    return {
        date,
        trackCode,
        fileType,
        stage
    }
}

const getDestinationObjectKey = (ftp_use_sub_folders, ftp_sub_folders, fileType, sourceObjectKey) => {
    const destinationSubfolder = ftp_use_sub_folders && ftp_sub_folders[fileType];
    return destinationSubfolder ? `${destinationSubfolder}/${sourceObjectKey}` : sourceObjectKey;
}

module.exports = {
    FILE_TYPES,
    parseFilename,
    getDestinationObjectKey
}
