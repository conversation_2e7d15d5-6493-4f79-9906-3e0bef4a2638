const mappers = require('./mappers');

describe('mappers.js tests', () => {
    beforeEach(() => {
        jest.restoreAllMocks();
    });

    test('Should successfully get classifications', async () => {
        expect(mappers.getClassification('M')).toEqual(mappers.CLASSIFICATIONS['M']);
        expect(mappers.getClassification('C')).toEqual(mappers.CLASSIFICATIONS['C']);
        expect(mappers.getClassification('P')).toEqual(mappers.CLASSIFICATIONS['P']);
        expect(mappers.getClassification('')).toEqual(mappers.CLASSIFICATIONS['']);
        expect(mappers.getClassification('I')).toEqual(mappers.CLASSIFICATIONS['I']);
    });

    test('Should return empty classifications', async () => {
        expect(mappers.getClassification('X')).toEqual('');
        expect(mappers.getClassification(1337)).toEqual('');
    });

    test('Should successfully get regions', async () => {
        expect(mappers.getRegion(0)).toEqual(mappers.REGIONS[0]);
        expect(mappers.getRegion(1)).toEqual(mappers.REGIONS[1]);
        expect(mappers.getRegion(2)).toEqual(mappers.REGIONS[2]);
        expect(mappers.getRegion(3)).toEqual(mappers.REGIONS[3]);
        expect(mappers.getRegion(4)).toEqual(mappers.REGIONS[4]);
        expect(mappers.getRegion(5)).toEqual(mappers.REGIONS[5]);
        expect(mappers.getRegion(6)).toEqual(mappers.REGIONS[6]);
        expect(mappers.getRegion(7)).toEqual(mappers.REGIONS[7]);
        expect(mappers.getRegion(8)).toEqual(mappers.REGIONS[8]);
        expect(mappers.getRegion(9)).toEqual(mappers.REGIONS[9]);
        expect(mappers.getRegion(10)).toEqual(mappers.REGIONS[10]);
        expect(mappers.getRegion(11)).toEqual(mappers.REGIONS[11]);
    });

    test('Should return empty classifications', async () => {
        expect(mappers.getRegion('X')).toEqual('');
        expect(mappers.getRegion(1337)).toEqual('');
    });
});
