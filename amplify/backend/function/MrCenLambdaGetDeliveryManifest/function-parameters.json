{"lambdaLayers": [{"type": "ProjectLayer", "resourceName": "centaurappCentaurAppCommonLayer", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "stgblack"}], "secretNames": [], "environmentVariableList": [{"cloudFormationParameterName": "centaurSecrets", "environmentVariableName": "centaurSecrets"}, {"cloudFormationParameterName": "centaurSecretsId", "environmentVariableName": "centaurSecretsId"}]}