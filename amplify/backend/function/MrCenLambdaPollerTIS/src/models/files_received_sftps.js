const moment = require('moment');
const mongoose = require("mongoose");

const filesReceivedSftpSchema = new mongoose.Schema({
  _id: String,
  type: String,
  name: String,
  s3file: String,
  size: Number,
  processedStatus: String,
  fileReceiptTime: Date,
  fileType: String,
  meetingUuid: String,
  meetingDate: String,
  meetingDate: Date,
  modifyTime: Number,
  accessTime: Number,
  rights: Object,
  owner: Number,
  group: Number,
}, { timestamps: {currentTime: () => moment.utc().format()} })
module.exports = mongoose.model("files_received_sftps", filesReceivedSftpSchema)
