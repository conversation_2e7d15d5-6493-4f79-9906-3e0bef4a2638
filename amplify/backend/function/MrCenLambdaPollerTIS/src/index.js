/**
 * Retrieves all files from an SFTP server, processes them, and saves the processed data to an S3 bucket and a MongoDB database.
 *
 * This function is the main entry point for the AWS Lambda function. It performs the following steps:
 * 1. Retrieves AWS secrets and generates a connection string for the MongoDB database.
 * 2. Connects to the MongoDB database.
 * 3. Retrieves the SFTP connection details for the "<EMAIL>" client.
 * 4. Retrieves the list of files from the SFTP server.
 * 5. Filters the list of files to exclude certain file types.
 * 6. Processes each file by:
 *    - Downloading the file from the SFTP server.
 *    - Normalizing the file name.
 *    - Parsing the XML data in the file.
 *    - Extracting relevant information from the parsed data (e.g., meeting date, venue, track name).
 *    - Uploading the file to an S3 bucket.
 *    - Creating a new record in the MongoDB database for the processed file.
 *    - Deleting the file from the SFTP server.
 * 7. Limits the number of files processed in a single execution to the value of the `FILE_PROCESS_LIMIT` environment variable.
 * 8. Cleans up the temporary directory after processing the files.
 * 9. Closes the MongoDB connection.
 *
 * @param {object} event - The event object passed to the AWS Lambda function.
 * @param {object} context - The context object passed to the AWS Lambda function.
 * @returns {Promise<number>} - The number of files processed.
 */
const fs = require('fs')
const fsExtra = require('fs-extra')
const mongoose = require('mongoose')
const getUuid = require('uuid-by-string')
const helper = require("./library/helper")
const AWSXRay = require("aws-xray-sdk-core")
const wrap = require('@dazn/lambda-powertools-pattern-basic')
const centaur = require('@mediality/centaur')

AWSXRay.enableAutomaticMode();



// timeDiff should be in milleseconds
// 5184000000 - 60 Days in milleseconds
// 1209600000 - 14 Days in milleseconds
var secondsSinceEpoch = Math.round(Date.now())
var timeDiff = process.env.TIME_DIFF || 1209600000
var fileProcessLimit = parseInt(process.env.FILE_PROCESS_LIMIT) || 15
var cert_path = __dirname + '/rds-cert/global-bundle.pem'
var conn = mongoose.connection;

conn.on('connected', function () {console.log('database connection  connected successfully');});
conn.on('disconnected', function () {console.log('database connection close successfully');})
conn.on('error', console.error.bind(console, 'connection error:'));

var MOCK_SFTP_DATA = process.env.MOCK_SFTP_DATA || "false"
var AWS_LOCAL = process.env.AWS_LOCAL || "false"


exports.getAllFiles = async (event, context) => {
    try {
        var segment = new AWSXRay.Segment('getAllFiles')
        var awsSecrets = await helper.getSecrets(process.env.centaurSecrets)
        var connDetails = await helper.generateConnectionString(awsSecrets)

        await mongoose.connect(
            connDetails.CONNECTION_STRING,
            connDetails.PARAMETERS)

        var client = await centaur.clients.findOne({ _id:'<EMAIL>' }).lean()
        // console.log(client)
        var SFTP_HOST =  client.ftp_address
        var SFTP_USER = client.ftp_username
        var SFTP_PASSWORD = client.ftp_password
        var SFTP_PORT = '21'

        if (process.env.ENV) { FILES_BUCKET_NAME = `mr-cen-file-storage-${process.env.ENV}`
        } else { var FILES_BUCKET_NAME = awsSecrets.FILES_BUCKET_NAME }

        var sftp_details = {
            SFTP_HOST: SFTP_HOST,
            SFTP_USER: SFTP_USER,
            SFTP_PASSWORD: SFTP_PASSWORD,
            SFTP_PORT: SFTP_PORT
        }
        // console.log(sftp_details)
        var filesData = await helper.getFTPFiles(sftp_details, "/wire/racing/");
        
        // console.log(filesData)
        fsExtra.emptyDir('/tmp/', err => {
            if (err) return console.error(err)
            console.log('directory emptied successfully!')})


        var processedFiles = 0
        var skippedFiles = 0
        var procArray = []
        var cleanedUpArray = []
        var clearArray = ['.txt','_Grey_','_Harn_','.aap']
        var toDeleteArray = []
        cleaningLoop: for (file_item of filesData){
            for (item of clearArray) if (file_item.name.includes(item)) {
                // await helper.deleteFileFromFTP(sftp_details, file_item['name'])
                continue cleaningLoop
            }
            console.log(file_item.name)
            cleanedUpArray.push(file_item)
        }
        console.log('cleanarray')
        console.log(cleanedUpArray)
        for (file_item of cleanedUpArray){
            var input = file_item
            // console.log(input)
            var fileConsiderationTime = secondsSinceEpoch - timeDiff
            const recordExists = await centaur.filesReceivedSftps.exists({_id:getUuid(input['name'],input['modifiedAt'].toString())})

            if (!(input['modifiedAt'] < fileConsiderationTime)) {
                console.log(input['name'], getUuid(input['name'],input['modifiedAt'].toString()))
            }

            if (input['modifiedAt'] < fileConsiderationTime) {
                //console.log("Skipping File is too old for consideration!")
                skippedFiles++
            } else if (recordExists) {
                console.log("Record Exists, Skipping!")
            } else {
                var meetname = input['name']
                // console.log(meetname)
                if (!procArray.includes(meetname)){
                    console.log("Processing File: ", input['name'])
                    input['normalisedFile'] = await helper.normaliseFileNameS3(input['name'])
                    await helper.downloadFileFromFTP(sftp_details, "/wire/racing/"+ input['name'], input['normalisedFile'])
                    // var env = process.env.ENV
                    var xmlData = await fs.readFileSync(`/tmp/${input['normalisedFile']}`, 'utf8');
                    var jsData = await helper.xmlToJs(xmlData)
                    input['meetingDate'] = await helper.getMeetingDate(input['normalisedFile'].split('_Raci_')[0])
                    var venueAbbr = jsData['meeting'] ? await jsData['meeting']['@_meetingJetbettrackid'] : input['normalisedFile'].split('_Raci_')[1]
                    var trackName = jsData['meeting'] ? await jsData['meeting']['@_meetingtrackid'] : input['normalisedFile'].split('_Raci_')[1]
                    input['meetingUuid'] = await getUuid(`${input['meetingDate']}-${venueAbbr}-${trackName}`)
                    input['meetingId'] = `${input['meetingDate']}-${venueAbbr}-${trackName}`
                    input['fileReceiptTime'] = new Date();
                    
                    input['fileType'] = 'Raci';

                    // console.log("Uploading File!")
                    // console.log(input)
                    var uploadResults = await helper.uploadFileS3(FILES_BUCKET_NAME, input, "/tmp/")

                    console.log("Creating DB Record")

                    var newRecord = new centaur.filesReceivedSftps({
                        _id: getUuid(input['name'],input['modifiedAt'].toString()),
                        type: input['type'],
                        name: input['name'],
                        fileType: input['fileType'],
                        meetingDate: input['meetingDate'],
                        meetingUuid: input['meetingUuid'],
                        meetingId: input['meetingId'],
                        s3file: uploadResults[0]['s3Location'],
                        modifyTime: input['modifiedAt'],
                        accessTime: input['accessTime'],
                        rights: input['rights'],
                        owner: 0,
                        group: 0
                    });

                    var d = await newRecord.save();
                    // console.log(d)
                    if (d) {
                        procArray.push(meetname)
                        // console.log(procArray)
                        console.log("Record Created!")
                        processedFiles++;
                        await helper.deleteFileFromFTP(sftp_details, "/wire/racing/"+ input['name'])
                        console.log(`Files Processed this execution: ${processedFiles}`)
                        console.log(`Files Skipped this execution due to consideration time: ${skippedFiles}`)
                    } else {
                        console.log("Record Creation Failed!")
                    }
                } else {
                    console.log(`Wont process multiple files of the same meeting at a single runthrough: ${input['name']}`)
                }
            }

            if (processedFiles == fileProcessLimit){
                console.log(`Processed ${processedFiles}, exiting loop`)
                break
            }
            
        }
        // Purge tmp directory once finished
        fsExtra.emptyDir('/tmp/', err => {
            if (err) return console.error(err)
                console.log('directory emptied successfully!')
            })
    } catch (err) {
        console.log(err)
        segment.addError(err)
    } finally {
        console.log(`Mongoose Connection State: ${conn.readyState}`);
        conn.close()
        segment.close()
    }
    return processedFiles;
}


exports.handler = wrap(async(event, context) => {
    
    if (MOCK_SFTP_DATA == "true") { console.log("Mocking SFTP data") }
    var fileCount = 0;
    var fileCount = await this.getAllFiles(event, context)
    
    var response = {
        statusCode: 200,
        body: { 'message': `File Count Processed: ${fileCount}` }
    }
    return response
});
