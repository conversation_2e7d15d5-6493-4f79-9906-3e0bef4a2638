{"name": "MrCenLambdaPollerTIS", "version": "2.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "MrCenLambdaPollerTIS", "version": "2.0.0", "license": "Apache-2.0", "devDependencies": {"@dazn/lambda-powertools-pattern-basic": "^1.29.0", "aws-xray-sdk-core": "^3.3.4", "fs-extra": "^10.0.0", "mongoose": "^6.1.3"}}, "node_modules/@aws-sdk/service-error-classification": {"version": "3.40.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">= 10.0.0"}}, "node_modules/@aws-sdk/types": {"version": "3.40.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">= 10.0.0"}}, "node_modules/@dazn/lambda-powertools-correlation-ids": {"version": "1.28.1", "dev": true, "license": "MIT"}, "node_modules/@dazn/lambda-powertools-logger": {"version": "1.28.1", "dev": true, "license": "MIT", "dependencies": {"@dazn/lambda-powertools-correlation-ids": "^1.28.1"}}, "node_modules/@dazn/lambda-powertools-middleware-correlation-ids": {"version": "1.29.0", "dev": true, "license": "MIT", "dependencies": {"@dazn/lambda-powertools-correlation-ids": "^1.28.1", "@dazn/lambda-powertools-logger": "^1.28.1"}}, "node_modules/@dazn/lambda-powertools-middleware-log-timeout": {"version": "1.29.0", "dev": true, "license": "MIT", "dependencies": {"@dazn/lambda-powertools-logger": "^1.28.1"}}, "node_modules/@dazn/lambda-powertools-middleware-sample-logging": {"version": "1.29.0", "dev": true, "license": "MIT", "dependencies": {"@dazn/lambda-powertools-correlation-ids": "^1.28.1", "@dazn/lambda-powertools-logger": "^1.28.1"}}, "node_modules/@dazn/lambda-powertools-pattern-basic": {"version": "1.29.0", "dev": true, "license": "MIT", "dependencies": {"@dazn/lambda-powertools-middleware-correlation-ids": "^1.29.0", "@dazn/lambda-powertools-middleware-log-timeout": "^1.29.0", "@dazn/lambda-powertools-middleware-sample-logging": "^1.29.0", "@middy/core": "^2.1.0"}}, "node_modules/@middy/core": {"version": "2.5.4", "dev": true, "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/@types/cls-hooked": {"version": "4.3.3", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/node": {"version": "17.0.2", "dev": true, "license": "MIT"}, "node_modules/@types/webidl-conversions": {"version": "6.1.1", "dev": true, "license": "MIT"}, "node_modules/@types/whatwg-url": {"version": "8.2.1", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/webidl-conversions": "*"}}, "node_modules/async-hook-jl": {"version": "1.7.6", "dev": true, "license": "MIT", "dependencies": {"stack-chain": "^1.3.7"}, "engines": {"node": "^4.7 || >=6.9 || >=7.3"}}, "node_modules/atomic-batcher": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/aws-xray-sdk-core": {"version": "3.3.4", "dev": true, "license": "Apache-2.0", "dependencies": {"@aws-sdk/service-error-classification": "^3.4.1", "@aws-sdk/types": "^3.4.1", "@types/cls-hooked": "^4.3.3", "atomic-batcher": "^1.0.2", "cls-hooked": "^4.2.2", "semver": "^5.3.0"}, "engines": {"node": ">= 10.x"}}, "node_modules/base64-js": {"version": "1.5.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/bson": {"version": "4.6.0", "dev": true, "license": "Apache-2.0", "dependencies": {"buffer": "^5.6.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/buffer": {"version": "5.7.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/cls-hooked": {"version": "4.2.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"async-hook-jl": "^1.7.6", "emitter-listener": "^1.0.1", "semver": "^5.4.1"}, "engines": {"node": "^4.7 || >=6.9 || >=7.3 || >=8.2.1"}}, "node_modules/debug": {"version": "4.3.3", "dev": true, "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/denque": {"version": "2.0.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=0.10"}}, "node_modules/emitter-listener": {"version": "1.1.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"shimmer": "^1.2.0"}}, "node_modules/fs-extra": {"version": "10.0.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/graceful-fs": {"version": "4.2.8", "dev": true, "license": "ISC"}, "node_modules/ieee754": {"version": "1.2.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/kareem": {"version": "2.3.2", "dev": true, "license": "Apache-2.0"}, "node_modules/memory-pager": {"version": "1.5.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/mongodb": {"version": "4.2.2", "dev": true, "license": "Apache-2.0", "dependencies": {"bson": "^4.6.0", "denque": "^2.0.1", "mongodb-connection-string-url": "^2.3.2"}, "engines": {"node": ">=12.9.0"}, "optionalDependencies": {"saslprep": "^1.0.3"}}, "node_modules/mongodb-connection-string-url": {"version": "2.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@types/whatwg-url": "^8.2.1", "whatwg-url": "^11.0.0"}}, "node_modules/mongoose": {"version": "6.1.3", "dev": true, "license": "MIT", "dependencies": {"bson": "^4.2.2", "kareem": "2.3.2", "mongodb": "4.2.2", "mpath": "0.8.4", "mquery": "4.0.0", "ms": "2.1.2", "regexp-clone": "1.0.0", "sift": "13.5.2", "sliced": "1.0.1"}, "engines": {"node": ">=12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mongoose"}}, "node_modules/mpath": {"version": "0.8.4", "dev": true, "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/mquery": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"debug": "4.x", "regexp-clone": "^1.0.0", "sliced": "1.0.1"}, "engines": {"node": ">=12.0.0"}}, "node_modules/ms": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/punycode": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/regexp-clone": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/saslprep": {"version": "1.0.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"sparse-bitfield": "^3.0.3"}, "engines": {"node": ">=6"}}, "node_modules/semver": {"version": "5.7.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/shimmer": {"version": "1.2.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/sift": {"version": "13.5.2", "dev": true, "license": "MIT"}, "node_modules/sliced": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/sparse-bitfield": {"version": "3.0.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"memory-pager": "^1.0.2"}}, "node_modules/stack-chain": {"version": "1.3.7", "dev": true, "license": "MIT"}, "node_modules/tr46": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"punycode": "^2.1.1"}, "engines": {"node": ">=12"}}, "node_modules/universalify": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/webidl-conversions": {"version": "7.0.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "node_modules/whatwg-url": {"version": "11.0.0", "dev": true, "license": "MIT", "dependencies": {"tr46": "^3.0.0", "webidl-conversions": "^7.0.0"}, "engines": {"node": ">=12"}}}}