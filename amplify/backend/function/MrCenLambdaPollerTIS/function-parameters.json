{"permissions": {}, "lambdaLayers": [{"type": "ProjectLayer", "resourceName": "centaurappCentaurAppCommonLayer", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "stgblack"}], "environmentVariableList": [{"cloudFormationParameterName": "timeDiff", "environmentVariableName": "TIME_DIFF"}, {"cloudFormationParameterName": "awsLocal", "environmentVariableName": "AWS_LOCAL"}, {"cloudFormationParameterName": "mockSftpData", "environmentVariableName": "MOCK_SFTP_DATA"}, {"cloudFormationParameterName": "fileProcessLimit", "environmentVariableName": "FILE_PROCESS_LIMIT"}], "secretNames": []}