{"name": "MrCenLambdaApiAdmin", "version": "2.0.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "MrCenLambdaApiAdmin", "version": "2.0.0", "license": "Apache-2.0", "devDependencies": {"@mediality/centaur": "../../centaurappCentaurAppCommonLayer/lib/nodejs"}}, "../../centaurappCentaurAppCommonLayer/lib/nodejs": {"name": "@mediality/centaur", "version": "1.0.31", "dev": true, "license": "ISC", "dependencies": {"@aws-lambda-powertools/logger": "^1.5.1", "@aws-lambda-powertools/tracer": "^1.5.1", "@aws-sdk/client-api-gateway": "^3.54.0", "@aws-sdk/client-s3": "^3.282.0", "@aws-sdk/client-secrets-manager": "^3.282.0", "@dazn/lambda-powertools-logger": "^1.28.1", "@dazn/lambda-powertools-pattern-basic": "^1.29.0", "@mediality/centaur": "./", "aws-sdk": "^2.1324.0", "aws-xray-sdk": "^3.3.4", "aws-xray-sdk-core": "^3.3.4", "axios": "^1.6.7", "basic-ftp": "^5.0.1", "fast-xml-parser": "^4.0.1", "fs": "^0.0.1-security", "fs-extra": "^10.0.0", "install": "^0.13.0", "moment": "^2.29.1", "mongoose": "^6.1.3", "pify": "^5.0.0", "uuid": "^8.3.2", "uuid-by-string": "^3.0.4", "validator": "^13.7.0", "xml2js": "^0.4.23", "xmlbuilder2": "^3.0.2"}, "devDependencies": {"jest": "^29.4.3"}}, "node_modules/@mediality/centaur": {"resolved": "../../centaurappCentaurAppCommonLayer/lib/nodejs", "link": true}}, "dependencies": {"@mediality/centaur": {"version": "file:../../centaurappCentaurAppCommonLayer/lib/nodejs", "requires": {"@aws-lambda-powertools/logger": "^1.5.1", "@aws-lambda-powertools/tracer": "^1.5.1", "@aws-sdk/client-api-gateway": "^3.54.0", "@aws-sdk/client-s3": "^3.282.0", "@aws-sdk/client-secrets-manager": "^3.282.0", "@dazn/lambda-powertools-logger": "^1.28.1", "@dazn/lambda-powertools-pattern-basic": "^1.29.0", "@mediality/centaur": "./", "aws-sdk": "^2.1324.0", "aws-xray-sdk": "^3.3.4", "aws-xray-sdk-core": "^3.3.4", "axios": "^1.6.7", "basic-ftp": "^5.0.1", "fast-xml-parser": "^4.0.1", "fs": "^0.0.1-security", "fs-extra": "^10.0.0", "install": "^0.13.0", "jest": "^29.4.3", "moment": "^2.29.1", "mongoose": "^6.1.3", "pify": "^5.0.0", "uuid": "^8.3.2", "uuid-by-string": "^3.0.4", "validator": "^13.7.0", "xml2js": "^0.4.23", "xmlbuilder2": "^3.0.2"}}}}