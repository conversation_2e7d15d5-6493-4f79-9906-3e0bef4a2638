const mongoose = require('mongoose')
const centaur = require('@mediality/centaur')
const AWSXRay = require("aws-xray-sdk-core")

const club_api = require("./library/crud_club")
const admin_api = require("./library/crud_admin")
const horse_api = require("./library/crud_horse")
const track_api = require("./library/crud_track")
const files_api = require("./library/crud_files")
const queues_api = require("./library/crud_queues")
const jockey_api = require("./library/crud_jockey")
const trainer_api = require("./library/crud_trainer")
const horse_form_api = require("./library/crud_horse_form")
const form_index_api = require("./library/search_form_index")
mongoose.set('debug', false);

/*
RFC: Governing API Operations
URI: https://datatracker.ietf.org/doc/html/rfc7231
*/


AWSXRay.enableAutomaticMode();

var conn = mongoose.connection;
conn.on('connected', function () { console.log('database connection  connected successfully'); });
conn.on('disconnected', function () { console.log('database connection close successfully'); })
conn.on('error', console.error.bind(console, 'connection error:'));


const api_handler = async (req) => {
  console.log(req)
  switch (req.path) {
    case "/admin/ping":
      console.log(`Case Match Path: ${req.path}`)
      return { "data": { "statusCode": 200, "body": { "message": "lambda prewarmed" } } }
    case "/admin/client":
      console.log(`Case Match Path: ${req.path}`)
      var data = await admin_api.client(req)
      return { "data": data }
    case "/admin/generate-api-key":
      console.log(`Case Match Path: ${req.path}`)
      var data = await admin_api.generate_api_key(req)
      return { "data": data }
    case "/admin/horse":
      console.log(`Case Match Path: ${req.path}`)
      var data = await horse_api.admin_horse_handler(req)
      return { "data": data }
    case "/admin/search/horse":
      console.log(`Case Match Path: ${req.path}`)
      var data = await horse_api.admin_search_horse_handler(req)
      return { "data": data }
    case "/admin/search/horse-form":
      console.log(`Case Match Path: ${req.path}`)
      var data = await horse_form_api.admin_search_horse_form_handler(req)
      return { "data": data }
    case "/admin/search/jockey":
      console.log(`Case Match Path: ${req.path}`)
      var data = await jockey_api.admin_search_jockey_handler(req)
      return { "data": data }
    case "/admin/search/track":
      console.log(`Case Match Path: ${req.path}`)
      var data = await track_api.admin_search_track_handler(req)
      return { "data": data }
    case "/admin/search/trainer":
      console.log(`Case Match Path: ${req.path}`)
      var data = await trainer_api.admin_search_trainer_handler(req)
      return { "data": data }
    case "/admin/search/club":
      console.log(`Case Match Path: ${req.path}`)
      var data = await club_api.admin_search_club_handler(req)
      return { "data": data }
    case "/admin/queues/list":
      console.log(`Case Match Path: ${req.path}`)
      var data = await queues_api.admin_queues_list_handler(req)
      return { "data": data }
    case "/admin/files/list":
      console.log(`Case Match Path: ${req.path}`)
      var data = await files_api.admin_files_list_handler(req)
      return { "data": data }
    case "/admin/files/download":
      console.log(`Case Match Path: ${req.path}`)
      var data = await files_api.admin_files_download_handler(req)
      return { "data": data }
    case "/admin/formIndex/search":
      console.log(`Case Match Path: ${req.path}`);
      var data = await form_index_api.admin_search_form_index_handler(req);
      return { "data": data }
    default:
      return { "msg": "No API Handler" }
  }
}

exports.handler = async (event) => {
  try {
    var response
    var admin_api_handler = new AWSXRay.Segment('admin_api_handler')
    console.log(`Mongoose Connection State: ${conn.readyState}`);
    if (conn.readyState != 1 ) {
      var secretDetails = await centaur.getSecrets(process.env.centaurSecrets)
      var connDetails = await centaur.generateConnectionString(secretDetails)
      await mongoose.connect(connDetails.CONNECTION_STRING, connDetails.PARAMETERS)
    }

    var req = {
      path: event.path,
      method: event.httpMethod,
      params: event.queryStringParameters,
      body: event.body
    }
    var retResults = await api_handler(req)
    
    var response = {
      statusCode: retResults.data.statusCode,
      headers: { "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Headers": "*" },
      body: JSON.stringify(retResults.data.body)
    }

    if (response.statusCode == 204) {
      delete response['body']
    }

    return response

  } catch (err) {
    console.log(err)
    admin_api_handler.addError(err)
  } finally {
    // await conn.close()
    console.log(`Mongoose Connection State: ${conn.readyState}`);
    admin_api_handler.close()
  }
};
