var moment = require('moment');
const AWS = require('aws-sdk');

const match_parent = async (childList, parentList, parent_type) => {
  var return_array = [];
  switch (parent_type) {
    case "dam":
      var parentType = "HOR_DAM_ID";
      var key_name = "DAM_NAME";
      break;
    case "sire":
      var parentType = "HOR_SIRE_ID";
      var key_name = "SIRE_NAME";
      break;
    case "sire_of_dam":
      var parentType = "HOR_SIRE_ID";
      var key_name = "SIRE_OF_DAM_NAME";
      var key_id = "SIRE_OF_DAM_ID";
      break;
  }

  for (var index = 0; index < childList.length; index++) {
    var parentfound = false
    for (var intIndex = 0; intIndex < parentList.length; intIndex++) {
      if (childList[index][parentType] == parentList[intIndex]['HRN_HORSE_ID']) {
        if (parent_type == "sire_of_dam") {
          if (parentList[intIndex].HRN_HORSE_NAME == "") {
            // If Blank We Can Transform the Values
            // childList[index]["HOR_SIRE_OF_DAM_NAME"] = "N/A";
          } else {
            childList[index]["HOR_SIRE_OF_DAM_NAME"] = parentList[intIndex].HRN_HORSE_NAME;
            childList[index]["HOR_SIRE_OF_DAM_COUNTRY"] = parentList[intIndex].HOR_COUNTRY_OF_ORIGIN;
            childList[index]["HOR_SIRE_OF_DAM_COMB"] = `${parentList[intIndex].HRN_HORSE_NAME} (${parentList[intIndex].HOR_COUNTRY_OF_ORIGIN})`;
          }
          childList[index]["HOR_SIRE_OF_DAM_ID"] = parentList[intIndex].HRN_HORSE_ID;
          
        } else if (parent_type == "dam") {
          if (parentList[intIndex].HRN_HORSE_NAME == "") {
            // If Blank We Can Transform the Values
            // childList[index]["HOR_DAM_NAME"] = "N/A";
            childList[index]["HOR_SIRE_OF_DAM_NAME"] = parentList[intIndex].HOR_SIRE_OF_DAM_NAME;
            childList[index]["HOR_SIRE_OF_DAM_ID"] = parentList[intIndex].HOR_SIRE_OF_DAM_ID;
            childList[index]["HOR_SIRE_OF_DAM_COUNTRY"] = parentList[intIndex].HOR_SIRE_OF_DAM_COUNTRY;
            childList[index]["HOR_SIRE_OF_DAM_COMB"] = `${parentList[intIndex].HOR_SIRE_OF_DAM_NAME} (${parentList[intIndex].HOR_SIRE_OF_DAM_COUNTRY})`;
          } else {
            childList[index]["HOR_DAM_NAME"] = parentList[intIndex].HRN_HORSE_NAME;
            childList[index]["HOR_DAM_COUNTRY"] = parentList[intIndex].HOR_COUNTRY_OF_ORIGIN;
            childList[index]["HOR_DAM_COMB"] = `${parentList[intIndex].HRN_HORSE_NAME} (${parentList[intIndex].HOR_COUNTRY_OF_ORIGIN})`;
            childList[index]["HOR_SIRE_OF_DAM_NAME"] = parentList[intIndex].HOR_SIRE_OF_DAM_NAME;
            childList[index]["HOR_SIRE_OF_DAM_ID"] = parentList[intIndex].HOR_SIRE_OF_DAM_ID;
            childList[index]["HOR_SIRE_OF_DAM_COUNTRY"] = parentList[intIndex].HOR_SIRE_OF_DAM_COUNTRY;
            childList[index]["HOR_SIRE_OF_DAM_COMB"] = `${parentList[intIndex].HOR_SIRE_OF_DAM_NAME} (${parentList[intIndex].HOR_SIRE_OF_DAM_COUNTRY})`;
          }
        } else if (parent_type == "sire") {
          if (parentList[intIndex].HRN_HORSE_NAME == "") {
            // If Blank We Can Transform the Values
            // childList[index]["HOR_SIRE_NAME"] = "N/A";
          } else {
            childList[index]["HOR_SIRE_NAME"] = parentList[intIndex].HRN_HORSE_NAME;
            childList[index]["HOR_SIRE_COUNTRY"] = parentList[intIndex].HOR_COUNTRY_OF_ORIGIN;
            childList[index]["HOR_SIRE_COMB"] = `${parentList[intIndex].HRN_HORSE_NAME} (${parentList[intIndex].HOR_COUNTRY_OF_ORIGIN})`;
          }
        }
        return_array.push(childList[index]);
        parentfound = true
        break;
      }
    }
    if (!parentfound) {
      switch (parent_type) {
        case "dam":
          childList[index]["HOR_DAM_NAME"] = '!! DAM NOT FOUND !!';
          childList[index]["HOR_DAM_COMB"] = '!! DAM NOT FOUND !!';
          childList[index]["HOR_DAM_ID"] = 0;
          childList[index]["HOR_SIRE_OF_DAM_NAME"] = '!! DAM NOT FOUND !!';
          childList[index]["HOR_SIRE_OF_DAM_COMB"] = '!! DAM NOT FOUND !!';
          childList[index]["HOR_SIRE_OF_DAM_ID"] = 0;
          break;
        case "sire":
          childList[index]["HOR_SIRE_NAME"] = '!! SIRE NOT FOUND !!';
          childList[index]["HOR_SIRE_COMB"] = '!! SIRE NOT FOUND !!';
          childList[index]["HOR_SIRE_ID"] = 0;
          break;
        case "sire_of_dam":
          childList[index]["HOR_SIRE_OF_DAM_NAME"] = '!! DAMSIRE NOT FOUND !!';
          childList[index]["HOR_SIRE_OF_DAM_COMB"] = '!! DAMSIRE NOT FOUND !!';
          childList[index]["HOR_SIRE_OF_DAM_ID"] = 0;
          break;
        
      }
      return_array.push(childList[index]);
    }

  }

  return return_array;
}

function isNumeric(str) {
  if (typeof str != "string") return false // we only process strings!  
  return !isNaN(str) && // use type coercion to parse the _entirety_ of the string (`parseFloat` alone does not do this)...
         !isNaN(parseFloat(str)) // ...and ensure strings of whitespace fail
}

const getHorseAge = (foalingDate) => {
  var a = moment(foalingDate);
  if (a.month() < 6){
      a.subtract(1, 'years');
  } 
  a.set({'month':0, 'date':1});
  var b = moment();
  if (b.get('month') < 7){
      b.subtract(1, 'years');
  }
  b.set({'month':0, 'date':1});
  result = b.diff(a, 'years')
  return result
}

const getHorseColor = (colorCode) => {
  var result = 0
  switch (colorCode) {
      case 1:
          // "CHESTNUT"
          result = "CH"
          break;
      case 2:
          // "BAY"
          result = "B"
          break;
      case 3:
          // "BROWN"
          result = "BR"
          break;
      case 4:
          // "BLACK"
          result = "BL"
          break;
      case 5:
          // "GREY"
          result = "GR"
          break;
      case 9:
          // "WHITE"
          result = "wh"
          break;
      case 14:
          // "BAY OR BROWN"
          result = "B OR BR"
          break;
      case 15:
          // "BROWN OR BLACK"
          result = "BR OR BL"
          break;
      case 16:
          // "GREY-CHESTNUT"
          result = "GR OR CH"
          break;
      case 17:
          // "GREY-BAY"
          result = "GR-B"
          break;
      case 18:
          // "GREY-BROWN"
          result = "GR-BR"
          break;
      case 19:
          // "GREY-BLACK"
          result = "GR-BL"
          break;
      case 20:
          // "GREY-ROAN"
          result = "GR-RO"
          break;
      case 21:
          // "ROAN"
          result = "RO"
          break;
      default:
          result = ""
  }
  return result
}

const mailAlert = async (subject,body,mail_list = "error") =>{
  // console.log('trying to send mail')
  var ses = new AWS.SES({region: 'ap-southeast-2'});
  
  var level = {
      "task" : [
          "<EMAIL>",
          "<EMAIL>"
      ],
      "error" : [
          "<EMAIL>",
          "<EMAIL>"
      ],
      "prod" : [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>"
      ],
      "alert" : [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>"
      ],
      "alarm" : [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>"
      ]
  }
  if (process.env != 'prd'){
      level = {
          "task" : [
              "<EMAIL>",
              "<EMAIL>"
          ],
          "error" : [
              "<EMAIL>",
              "<EMAIL>"
          ],
          "alert" : [
              "<EMAIL>",
              "<EMAIL>"
          ],
          "alarm" : [
              "<EMAIL>",
              "<EMAIL>"
          ]
      }
  }
  const emailParams = {
      Destination: {
          ToAddresses: level[mail_list],
      },
      Message: {
          Body: {
              Text: { Data: body },
          },
          Subject: { Data: "Pegasus DB "+mail_list.toUpperCase()+" "+subject },
      },
      Source: "<EMAIL>",
  };
      
  try {
          let key = await ses.sendEmail(emailParams).promise();
          // console.log('mail sent');      
  } catch (e) {
          console.log('mail failed', e);
  }  
  return 'mail process complete';
  
}
module.exports = {
  match_parent,
  isNumeric,
  getHorseAge,
  getHorseColor,
  mailAlert
};
