const {
  APIGatewayClient,
  GetUsagePlansCommand
} = require("@aws-sdk/client-api-gateway");


const apigateway = new APIGatewayClient({ region: process.env.AWS_REGION });


const get_usage_plan_id = async (usage_plan_name) => {
  var command = new GetUsagePlansCommand({});
  var data = await apigateway.send(command);
  for (var i = 0; i < data.items.length; i++) {
    if (data.items[i].name == usage_plan_name) {
      return data.items[i].id;
    }
  }
}

module.exports = {
  get_usage_plan_id,
}
