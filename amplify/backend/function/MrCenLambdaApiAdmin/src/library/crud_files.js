const centaur = require('@mediality/centaur')


const admin_files_list_handler = async (req) => {
  console.log("Got to the Admin Files List Handler");
  console.log(req);
  if (req.params == null) {
    var files = await centaur.filesReceivedSftps.find().sort({"modifyTime": -1}).limit(6000).lean()
  }

  if (files.length > 0) {
    return { statusCode: 200, body: files }
  } else {
    return { statusCode: 200, body: [] }
  }
}

const admin_files_download_handler = async (payload) => {
    // console.log('admin_file_download')
    // console.log(payload)
    var response = "Filename not received"
    if ((payload.path == "/admin/files/download") && (payload.params)) {
        if (('filename' in payload.params) && (payload.params.filename)) {
            var data = await getS3SignedUrl(payload.params.filename)
            console.log(data)
            if (data) {
                response = data
            }
        }
    }
    return response
}

const getS3SignedUrl = async (fileName) => {
    const AWS = require('aws-sdk')
    const s3 = new AWS.S3()
    const params = {
        Bucket: getBucketName(fileName),
        Key: getS3FileName(fileName),
        Expires: 60 * 60, // Expiration time in seconds
        ResponseContentDisposition: `attachment; filename="${fileName}"`,
    }
    try {
        var result = "Not Found"
        url = await s3.getSignedUrl('getObject', params)
        console.log(url)
        if (url) result = {"body":url}
        // }
        return result

    }
    catch (err) {
        console.log(err)
        return "S3 Signed URL generation error"
    }
}

const getBucketName = (s3Url) => {
    if (s3Url) {
        const arr = s3Url.split("/")
        var bucketName = arr[2]
        return bucketName
    }

}

const getS3FileName = (s3Url) => {
    if (s3Url) {
        var temp = split(s3Url, '/', 3)
        return temp[3]
    }
}

const split = (str, separator, limit) => {
    str = str.split(separator);
    if (str.length > limit) {
        var ret = str.splice(0, limit);
        ret.push(str.join(separator));
        return ret;
    }
    return str;
}

module.exports = {
    admin_files_list_handler,
    admin_files_download_handler
}
