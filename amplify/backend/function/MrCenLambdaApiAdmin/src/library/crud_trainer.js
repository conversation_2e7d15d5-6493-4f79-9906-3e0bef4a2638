const centaur = require('@mediality/centaur')


const admin_search_trainer_handler = async (req) => {
  console.log("Got to the Admin Search Trainers Handler");

  if (req.params == null) {
    var trainers = await centaur.trainers.find().sort("TRN_TRAINER_SURNAME").limit(500).lean()
  } else if (req.params != null) {
    var srch = req.params.s
    var trainers = []
    if (srch.charAt(0) == '"' && srch.charAt(srch.length - 1) == '"'){
      srch = srch.slice(1,-1)
      trainers = await centaur.trainers.find(
        {
          $or: [
            { TRN_TRAINER_SURNAME: { $regex: `^${srch}$`, $options: 'i' } },
            { TRN_TRAINER_FIRSTNAME: { $regex: `^${srch}$`, $options: 'i' } },
            { TRN_TRAINER_DISPLAYNAME: { $regex: `^${srch}$`, $options: 'i' } }
          ]
        }
      ).sort("TRN_TRAINER_SURNAME").limit(500).lean()
    } else {
      trainers = await centaur.trainers.find(
        {
          $or: [
            { TRN_TRAINER_SURNAME: { $regex: `${req.params.s}`, $options: 'i' } },
            { TRN_TRAINER_FIRSTNAME: { $regex: `${req.params.s}`, $options: 'i' } },
            { TRN_TRAINER_DISPLAYNAME: { $regex: `${req.params.s}`, $options: 'i' } }
          ]
        }
      ).sort("TRN_TRAINER_SURNAME").limit(500).lean()
    }
  }

  if (trainers.length > 0) {
    return { statusCode: 200, body: trainers }
  } else {
    return { statusCode: 200, body: [] }
  }
}


module.exports = {
  admin_search_trainer_handler,
}
