const centaur = require('@mediality/centaur');

const admin_search_form_index_handler = async (req) => {
  console.log("Got to the Admin Search Form Index Handler");

  try {
    console.log(JSON.stringify(req));
    const { fromDate, toDate, trainer_id, jockey_id, track_id } = req.params;

    if (!fromDate || !toDate || (!trainer_id && !jockey_id && !track_id)) {
      return {
        statusCode: 400,
        body: { "message": "Missing required parameters. Please provide fromDate, toDate, and either trainer_id, jockey_id, or track_id." }
      };
    }

    let query = {
      meeting_date: {
        $gte: new Date(fromDate),
        $lte: new Date(toDate)
      }
    };

    if (trainer_id) {
      query.trainer_id = parseInt(trainer_id);
    } else if (jockey_id) {
      query.jockey_id = parseInt(jockey_id);
    } else if (track_id) {
      query.track_id = parseInt(track_id);
    }

    const results = await centaur.form_index.find(
      query,
      {
        horse_id: 1,
        meeting_date: 1,
        race_no: 1,
        _id: 0
      }
    ).sort({ meeting_date: -1 });

    if (!results || results.length === 0) {
      return {
        statusCode: 404,
        body: { "message": "No results found for the given parameters." }
      };
    }

    return {
      statusCode: 200,
      body: results
    };

  } catch (err) {
    console.error('Error in the Admin Search Form Index admin search:', err);
    return { statusCode: 501, body: { "message": "Failed to search." } };
  }
};

module.exports = {
  admin_search_form_index_handler
};