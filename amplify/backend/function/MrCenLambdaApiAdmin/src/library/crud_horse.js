var moment = require('moment');
const helper = require('../helper/helper_horse')
const centaur = require('@mediality/centaur')


const admin_horse_handler = async (req) => {
  try {
    switch (req.method) {
      case "POST":
        console.log(`Received Method: ${req.method}`);
        if (req.body.hasOwnProperty('horse_ids')) {
          var data = await get_horses(req.body);
          return data;
        }
        break;
      case "GET":
        console.log(`Received Method: ${req.method}`);
        var data = await admin_search_horse_handler(req);
        console.log('Got back to Get');
        return data;
    }
  } catch (error) {
    console.error(error);
    return {
      statusCode: 500,
      body: { "message": "Internal Server Error" }
    };
  }
}

const admin_search_horse_handler = async (req) => {
  console.log("Got to the Admin Search Horse Handler");
  switch (req.method) {
    case "GET":
      console.log(`Received Method: ${req.method}`)
      // var data = await get_horses(req)

      var sire_list = [];
      var dam_list = [];
      var sire_of_dams_list = [];
      var ext = false
      console.log(req.params)

      if (req.params == null || req.params.since) {
        if (req.params && req.params.since) { 
          var sinceDate = moment(req.params.since).toISOString()
          var horses = await centaur.horses
          .find(
            { $and:[{HRN_HORSE_ID: { $ne: 0 }}, {HOR_FOALING_DATE:{$ne: null}}, {createdAt: {$ne:null}}, {createdAt: { $gt: sinceDate }}] }
          )
          .select({
            HRN_HORSE_ID: 1,
            HRN_HORSE_NAME: 1,
            HOR_COUNTRY_OF_ORIGIN: 1,
            HOR_FOALING_DATE: 1,
            HOR_DAM_ID: 1,
            HOR_SEX: 1,
            HOR_SIRE_ID: 1,
            HOR_COLOUR: 1
          }).sort('HRN_HORSE_NAME').lean();
          ext = true
        } else {
          var horses = await centaur.horses
          .find(
            { HRN_HORSE_ID: { $ne: 0 } }
          )
          .select({
            HRN_HORSE_ID: 1,
            HRN_HORSE_NAME: 1,
            HOR_COUNTRY_OF_ORIGIN: 1,
            HOR_FOALING_DATE: 1,
            HOR_DAM_ID: 1,
            HOR_SIRE_ID: 1
          }).sort('HRN_HORSE_NAME').limit(500).lean();
        }
      } else if (req.params.sire && req.params.s) {
        var horses = await searchSire(req.params.s)
      }else if (req.params.dam && req.params.s) {
        var horses = await searchDam(req.params.s)
      }else if (req.params.s) {
        var horses = await searchHorse(req.params.s)
      }
      // Build the Horses Sire and Dam Id List
      console.log(horses.length)
      for (var i = 0; i < horses.length; i++) {
        // if (horse[i].HOR_COUNTRY_OF_ORIGIN == "") {
        //   horse[i].HOR_COUNTRY_OF_ORIGIN = "N/A"
        // }
        if (ext) {
          horses[i].HOR_AGE = helper.getHorseAge(horses[i].HOR_FOALING_DATE)
          horses[i].HOR_COLOUR = helper.getHorseColor(horses[i].HOR_COLOUR)
          if (horses[i].HOR_COLOUR === "") {
            await helper.mailAlert(
              "Horse Color Not Found",
              `Arion rego API Horse color could not found, its empty. horseId: ${horses[i].HRN_HORSE_ID}  horseData: ${JSON.stringify(horses[i])}`,
              "prod"
            );
            console.log(
              `((ERROR)): NZ Horse Color Not Found - ${horses[i].HRN_HORSE_NAME}, id: ${horses[i].HRN_HORSE_ID}`
            );
          }
          if (horses[i].HOR_AGE > 3) horses[i].HOR_SEX.replace("F", "M").replace("C","H")
        } 
        horses[i].HOR_FOALING_DATE = moment(horses[i].HOR_FOALING_DATE).format('YYYY-MM-DD');
        sire_list.push(horses[i].HOR_SIRE_ID);
        dam_list.push(horses[i].HOR_DAM_ID);
      }

      var sires = await centaur.horses
        .find(
          { HRN_HORSE_ID: { $in: sire_list } }
        )
        .select({
          HRN_HORSE_ID: 1,
          HRN_HORSE_NAME: 1,
          HOR_COUNTRY_OF_ORIGIN: 1
        }).lean();
      var dams = await centaur.horses
        .find(
          { HRN_HORSE_ID: { $in: dam_list } })
        .select(
          {
            HRN_HORSE_ID: 1,
            HRN_HORSE_NAME: 1,
            HOR_SIRE_ID: 1,
            HOR_COUNTRY_OF_ORIGIN: 1
          }).lean();

      // Build the Sire of Dam Id List
      for (var i = 0; i < dams.length; i++) {
        sire_of_dams_list.push(dams[i].HOR_SIRE_ID);
      }

      var sire_of_dams = await centaur.horses
        .find(
          { HRN_HORSE_ID: { $in: sire_of_dams_list } }
        )
        .select({
          HRN_HORSE_ID: 1,
          HRN_HORSE_NAME: 1,
          HOR_COUNTRY_OF_ORIGIN: 1
        }).lean();

      var horses = await helper.match_parent(horses, sires, "sire")
      var dams = await helper.match_parent(dams, sire_of_dams, "sire_of_dam");
      var horses = await helper.match_parent(horses, dams, "dam")

      return {
        statusCode: 200,
        body: horses
      }
    default:
      return {
        statusCode: 501,
        body: { "message": "Not Implemented" }
      }
  }
}

const get_horses = async (payload) => {
  console.log("Got to the Horse Handler")
  console.log(payload)

  if (payload.body !== undefined && payload.body !== null) {
    var horse_ids_list = JSON.parse(payload.body)['horse_ids']
    var data = await centaur.horses.find(
      { HRN_HORSE_ID: { $in: horse_ids_list } }).lean()

    if (data) {
      return {
        statusCode: 200,
        body: data
      }
    } else {
      return {
        statusCode: 200,
        body: { "message": "No horses found." }
      }
    }
  }

  if (payload.params.id) {
    var horse_id = payload.params.id
    var data = await centaur.horses.find(
      { HRN_HORSE_ID: horse_id }).lean()

    if (data.length > 0) {
      return {
        statusCode: 200,
        body: data
      }
    } else {
      return {
        statusCode: 404,
        body: []
      }
    }
  }
}

const searchHorse = async (srch) => {
  var horses = []
  if (helper.isNumeric(srch)){
    horses = await centaur.horses
      .find(
        { HRN_HORSE_ID: parseInt(srch) }
      )
      .select({
        HRN_HORSE_ID: 1,
        HRN_HORSE_NAME: 1,
        HOR_COUNTRY_OF_ORIGIN: 1,
        HOR_FOALING_DATE: 1,
        HOR_DAM_ID: 1,
        HOR_SIRE_ID: 1
      }).sort('HRN_HORSE_NAME').limit(500).lean();
  } else if (srch.charAt(0) == '"' && srch.charAt(srch.length - 1) == '"'){
    srch = srch.slice(1,-1)
    console.log(`search horse: ${srch}`)
    horses = await centaur.horses
      .find({HRN_HORSE_NAME: { $regex: `^${srch}$`, $options: 'i' }})
      .select({
        HRN_HORSE_ID: 1,
        HRN_HORSE_NAME: 1,
        HOR_COUNTRY_OF_ORIGIN: 1,
        HOR_FOALING_DATE: 1,
        HOR_DAM_ID: 1,
        HOR_SIRE_ID: 1
      }).sort('HRN_HORSE_NAME').limit(500).lean();
      console.log(horses)
  } else {
    horses = await centaur.horses
      .find(
        { HRN_HORSE_NAME: { $regex: `${srch}`, $options: 'i' } }
      )
      .select({
        HRN_HORSE_ID: 1,
        HRN_HORSE_NAME: 1,
        HOR_COUNTRY_OF_ORIGIN: 1,
        HOR_FOALING_DATE: 1,
        HOR_DAM_ID: 1,
        HOR_SIRE_ID: 1
      }).sort('HRN_HORSE_NAME').limit(500).lean();
  }
  return horses
}

const searchDam = async (srch) => {
  var horses = []
  if (helper.isNumeric(srch.toString())){
    horses = await centaur.horses
      .find(
        { HOR_DAM_ID: parseInt(srch) }
      )
      .select({
        HRN_HORSE_ID: 1,
        HRN_HORSE_NAME: 1,
        HOR_COUNTRY_OF_ORIGIN: 1,
        HOR_FOALING_DATE: 1,
        HOR_DAM_ID: 1,
        HOR_SIRE_ID: 1
      }).sort('HRN_HORSE_NAME').limit(500).lean();
  } else {
    var dams = await centaur.horses.find({HRN_HORSE_NAME: { $regex: `^${srch}$`, $options: 'i' }})
      .select({
        HRN_HORSE_ID: 1
      }).sort('HRN_HORSE_NAME').limit(500).lean();
      for (dam of dams){
        var theHorses =  await centaur.horses.find({HOR_DAM_ID: dam.HRN_HORSE_ID}).select({
          HRN_HORSE_ID: 1,
          HRN_HORSE_NAME: 1,
          HOR_COUNTRY_OF_ORIGIN: 1,
          HOR_FOALING_DATE: 1,
          HOR_DAM_ID: 1,
          HOR_SIRE_ID: 1
        }).sort('HRN_HORSE_NAME').limit(500).lean()
        for (item of theHorses) horses.push(item)
      }
      console.log(horses)
  }
  return horses
}

const searchSire = async (srch) => {
  var horses = []
  if (helper.isNumeric(srch.toString())){
    console.log()
    horses = await centaur.horses
      .find(
        { HOR_SIRE_ID: parseInt(srch) }
      )
      .select({
        HRN_HORSE_ID: 1,
        HRN_HORSE_NAME: 1,
        HOR_COUNTRY_OF_ORIGIN: 1,
        HOR_FOALING_DATE: 1,
        HOR_DAM_ID: 1,
        HOR_SIRE_ID: 1
      }).sort('HRN_HORSE_NAME').limit(500).lean();
  } else {
    var sires = await centaur.horses.find({HRN_HORSE_NAME: { $regex: `^${srch}$`, $options: 'i' }})
      .select({
        HRN_HORSE_ID: 1
      }).sort('HRN_HORSE_NAME').limit(500).lean();
      for (sire of sires){
        var theHorses =  await centaur.horses.find({HOR_SIRE_ID: sire.HRN_HORSE_ID}).select({
          HRN_HORSE_ID: 1,
          HRN_HORSE_NAME: 1,
          HOR_COUNTRY_OF_ORIGIN: 1,
          HOR_FOALING_DATE: 1,
          HOR_DAM_ID: 1,
          HOR_SIRE_ID: 1
        }).sort('HRN_HORSE_NAME').limit(500).lean()
        for (item of theHorses) horses.push(item)
      }
      console.log(horses)
  }
  return horses
}


module.exports = {
  admin_horse_handler,
  admin_search_horse_handler,
}
