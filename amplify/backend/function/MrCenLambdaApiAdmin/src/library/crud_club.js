const centaur = require('@mediality/centaur')


const admin_search_club_handler = async (req) => {
  console.log("Got to the Admin Search Clubs Handler");
  console.log(req);
  if (req.params == null) {
    var clubs = await centaur.clubs.find().sort("CLB_CLUB_NAME").limit(500).lean()
  } else if (req.params != null) {
    var srch = req.params.s
    var clubs = []
    if (srch.charAt(0) == '"' && srch.charAt(srch.length - 1) == '"'){
      srch = srch.slice(1,-1)
      clubs = await centaur.clubs.find({ CLB_CLUB_NAME: { $regex: `^${srch}$`, $options: 'i' } }).sort('CLB_CLUB_NAME').limit(500).lean();
    } else {
      clubs = await centaur.clubs.find({ CLB_CLUB_NAME: { $regex: `${req.params.s}`, $options: 'i' } }).sort("CLB_CLUB_NAME").limit(500).lean()
    }
  }

  if (clubs.length > 0) {
    return { statusCode: 200, body: clubs }
  } else {
    return { statusCode: 200, body: [] }

  }
}


module.exports = {
  admin_search_club_handler,
}
