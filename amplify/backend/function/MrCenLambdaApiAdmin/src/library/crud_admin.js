const util = require('util')
const centaur = require('@mediality/centaur')
const helper = require('../helper/helper_admin')

const {
  APIGatewayClient,
  CreateUsagePlanKeyCommand,
  CreateApiKeyCommand,
  DeleteApiKeyCommand,
  GetApiKeysCommand,
  GetRestApisCommand,
  GetUsagePlansCommand
} = require("@aws-sdk/client-api-gateway");


const apigateway = new APIGatewayClient({ region: process.env.AWS_REGION });

const client = async (req) => {
  switch (req.method) {
    case "POST":
      console.log(`Recieved Method: ${req.method}`)
      var data = await client_create(req)
      return data
    case "GET":
      console.log(`Recieved Method: ${req.method}`)
      var data = await client_read(req)
      return data
    case "PUT":
      console.log(`Recieved Method: ${req.method}`)
      var data = await client_update(req)
      return data
    case "DELETE":
      console.log(`Recieved Method: ${req.method}`)
      var data = await client_delete(req)
      return data
    default:
      return "Internal Server Error"
  }
}

const client_create = async (payload) => {
  try {
    var body = JSON.parse(payload.body)

    if (Array.isArray(body)) {
      for (var i = 0; i < body.length; i++) {
        console.log('Found Array of Users')
        console.log(body[i])
        console.log(body[i].email)
        const userExists = await centaur.clients.exists({ _id: body[i].email })
        console.log(userExists)
        if (!(userExists)) {
          var data = {
            _id: body[i].email,
            account_holder: body[i].account_holder,
            display_name: body[i].display_name || body[i].account_holder,
            email: body[i].email,
            company: body[i].company,
            status: Boolean(body[i].status),
            ftp_username: body[i].ftp_username,
            ftp_password: body[i].ftp_password,
            ftp_address: body[i].ftp_address,
            ftp_use_sub_folders: false,
            ftp_sub_folders: {
              "FIELDS":"mr_fields",
              "FORM":"mr_form",
              "RESULTS - COLLATED":"mr_results",
              "RESULTS - RACE BY RACE":"mr_results",
              "GEAR CHANGES":"mr_gear_changes",
              "SCRATCHINGS":"mr_scratchings"
            },
            api_key: "",
            api_key_id: "",
            region_perms: body[i].region_perms,
            fields_access: body[i].fields_access,
            files_access: body[i].files_access,
            classifications: body[i].classifications,
            stage_perms: body[i].stage_perms
          }
          let new_client_record = new centaur.clients(data)
          var status = await new_client_record.save();
          console.log("New Client Created: " + status)
        } else {
          console.log(`Skipping Creation of User with email ${body[i].email} as it already exists, use PUT to update this user.`)
        }
      }

    } else {
      const userExists = await centaur.clients.exists({ _id: body.email })
      if (!(userExists)) {
        console.log('Found Dictionary for a Single User')
        var data = {
          _id: body.email,
          account_holder: body.account_holder,
          display_name: body.display_name || body.account_holder,
          email: body.email,
          company: body.company,
          status: Boolean(body.status),
          ftp_username: body.ftp_username,
          ftp_password: body.ftp_password,
          ftp_address: body.ftp_address,
          ftp_use_sub_folders: body.ftp_use_sub_folders,
          ftp_sub_folders: body.ftp_sub_folders ?? {
            "FIELDS":"mr_fields",
            "FORM":"mr_form",
            "RESULTS - COLLATED":"mr_results",
            "RESULTS - RACE BY RACE":"mr_results",
            "GEAR CHANGES":"mr_gear_changes",
            "SCRATCHINGS":"mr_scratchings"
          },
          api_key: "",
          region_perms: body.region_perms,
          fields_access: body.fields_access,
          files_access: body.files_access,
          classifications: body.classifications,
          stage_perms: body.stage_perms
        }
        let new_client_record = new centaur.clients(data)
        var status = await new_client_record.save();
        console.log("New Client Created: " + status)
      } else {
        console.log(`Skipping Creation of User with email ${body.email} as it already exists, use PUT to update this user.`)
        var status = false
      }
    }

    if (status) {
      return {
        statusCode: 201,
        body: { "message": "User Created" }
      }
    } else {
      return {
        statusCode: 409,
        body: { "message": "User Already Exists" }
      }
    }
  } catch (err) {
    console.log(err)
    console.log(err.code)
    if (err.code) {
      return {
        statusCode: 409,
        body: { "message": "User Already Exists" }
      }
    }
  }
}

const client_read = async (payload) => {
  try {
    var params = payload.params

    if (params == null) {
      console.log(`Found no params, list search`)
      var data = await centaur.clients.find({})
      return {
        statusCode: 200,
        body: data
      }
    } else if ('id' in params) {
      console.log(`Found Param: ${JSON.stringify(params)}`)
      var data = await centaur.clients.findOne({ _id: params.id }).lean()
      if (data) {
        return {
          statusCode: 200,
          body: data
        }
      }
    } else if ('search' in params) {
      console.log(`Found Param: ${JSON.stringify(params)}`)
      var search = params.search.trim()
      var data = await centaur.clients.find(
        { _id: { $regex: search, $options: 'i' } })
      return {
        statusCode: 200,
        body: data
      }
    }

  } catch (err) {
    console.log(err)
  }
}

const client_update = async (payload) => {
  try {
    var params = payload.params
    if ('id' in params) {
      console.log(`Found Param: ${JSON.stringify(params)}`)
      console.log(payload.body)

      const userExists = await centaur.clients.exists({ _id: params.id })
      if (userExists) {
        const updateData = JSON.parse(payload.body)

        // Update the main client
        var data = await centaur.clients.updateOne({ _id: params.id }, { $set: updateData })

        if (data) {
          console.log(`Successfully updated user: ${params.id}`)

          // If account_holder was updated and this client has associate_clients,
          // update their display_name fields
          if (updateData.account_holder && updateData.associate_clients && updateData.associate_clients.length > 0) {
            console.log(`Updating display_name for ${updateData.associate_clients.length} associated clients`)

            try {
              // Update display_name for all associated clients
              await centaur.clients.updateMany(
                { _id: { $in: updateData.associate_clients } },
                { $set: { display_name: updateData.account_holder } }
              )
              console.log(`Successfully updated display_name for associated clients`)
            } catch (associateUpdateError) {
              console.error(`Error updating associated clients display_name:`, associateUpdateError)
              // Don't fail the main update if associate update fails
            }
          }

          return {
            statusCode: 200,
            body: { "message": `Successfully updated user: ${params.id}` }
          }
        }
      } else {
        console.log(`User not found: ${params.id}`)
        return {
          statusCode: 404,
          body: { "message": `User not found: ${params.id}` }
        }
      }
    }
  } catch (err) {
    console.log(err)
    return {
      statusCode: 500,
      body: { "message": `Error updating user: ${err.message}` }
    }
  }
}

const client_delete = async (payload) => {
  try {
    var body = JSON.parse(payload.body)
    const userExists = await centaur.clients.exists({ _id: body.email })
    if (body.email && userExists) {
      var record_delete = await centaur.clients.deleteOne({ _id: body.email })
      if (record_delete) {
        return {
          statusCode: 200,
          body: { "message": `User Id, ${body.email}, has been deleted.` }
        }
      }
    } else if (!(userExists)) {
      return {
        statusCode: 204,
      }
    }
  } catch (err) {
    console.log(err)
  }
}

const generate_api_key = async (req) => {
  switch (req.method) {
    case "GET":
      console.log(`Recieved Method: ${req.method}`)
      var data = await api_key_read(req)
      return data
    case "POST":
      console.log(`Recieved Method: ${req.method}`)
      var data = await api_key_create(req)
      return data
    case "DELETE":
      console.log(`Recieved Method: ${req.method}`)
      var data = await api_key_delete(req)
      return data
    default:
      return "Internal Server Error"
  }
}

const api_key_read = async (payload) => {
  console.log(process.env.AWS_REGION)
  return {
    statusCode: 501,
    body: { "message": "Not Implemented" }
  }
}

const api_key_create = async (payload) => {
  var params = payload.params
  var command = new GetRestApisCommand({});
  var response = await apigateway.send(command);
  var data = await centaur.clients.findOne({ _id: params.id }).lean()
  console.log(data);

  if (data.api_key == '' && data.api_key_id == '') {
    command = new CreateApiKeyCommand({
      name: data.company,
      customerId: params.id,
      enabled: true
    });

    response = await apigateway.send(command);
    console.log(response)

    var data = await centaur.clients.updateOne(
      { _id: params.id },
      {
        $set:
        {
          api_key: response.value,
          api_key_id: response.id
        }
      })

    // Add the API Key to the usage Plan
    var up_params = {
      keyId: response.id,
      keyType: 'API_KEY',
      usagePlanId: await helper.get_usage_plan_id(
        `MrCenApiGatewayCustomer-${process.env.ENV}-usage-plan`)
    };
    command = new CreateUsagePlanKeyCommand(up_params);
    await apigateway.send(command);

    console.log(`API Key created for user ${params.id}`)
    return {
      statusCode: 200,
      body: { "message": `API Key created for user ${params.id}` }
    }
  } else {
    console.log(data);

    var up_params = {
      keyId: data.api_key_id,
      keyType: 'API_KEY',
      usagePlanId: await helper.get_usage_plan_id(
        `MrCenApiGatewayCustomer-${process.env.ENV}-usage-plan`)
    };
    command = new CreateUsagePlanKeyCommand(up_params);

    try {
      await apigateway.send(command);
    } catch (error) {
      console.log(error)
    }

    console.log(`An API key already exists for user ${params.id}`)
    return {
      statusCode: 409,
      body: { "message": `An API key already exists for user ${params.id}` }
    }
  }
}

const api_key_delete = async (payload) => {
  var params = payload.params
  var data = await centaur.clients.findOne({ _id: params.id }).lean()
  var response;
  var command;

  console.log(data);

  if (data.api_key != '' && data.api_key_id != '') {
    console.log(data.api_key);
    console.log(data.api_key_id);
    console.log(process.env.customerApiGateway);
    console.log(process.env.ENV);

    command = new DeleteApiKeyCommand({ apiKey: data.api_key_id });
    response = await apigateway.send(command);

    var data = await centaur.clients.updateOne(
      { _id: params.id },
      {
        $set:
        {
          api_key: '',
          api_key_id: ''
        }
      }
    )

    return {
      statusCode: 200,
      body: { "message": `API Key for user ${params.id} deleted.` }
    }
  } else {
    try {
      command = new GetApiKeysCommand({});
      response = await apigateway.send(command);
      var api_keys = response.items;

      // Loop over existing keys to be sure that a key for the company name
      // doesn't exists, delete it if it does, as this key is stale.
      for (var index = 0 ; index < api_keys.length ; index++ ) {
        if (data.company == api_keys[index].name) {
          command = new DeleteApiKeyCommand({ apiKey: api_keys[index].id });
          response = await apigateway.send(command);
        }
      }
    } catch (error) {
      console.log(error);
    }

    return {
      statusCode: 204,
      body: { "message": `API for user ${params.id} does not exists.` }
    }
  }
}


module.exports = {
  client,
  generate_api_key
}
