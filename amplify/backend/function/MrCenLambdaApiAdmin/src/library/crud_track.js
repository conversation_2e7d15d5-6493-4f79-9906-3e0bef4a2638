const centaur = require('@mediality/centaur')
const helper = require('../helper/helper_horse')

const admin_search_track_handler = async (req) => {
  console.log("Got to the Admin Search Tracks Handler");

  if (req.params == null) {
    var tracks = await centaur.tracks.find().sort("TRK_TRACK_NAME").limit(500).lean()
  } else if (req.params != null) {
    
    var srch = req.params.s
    var tracks = []
    if (helper.isNumeric(srch)){
      tracks = await centaur.tracks.find(
        { TRK_TRACK_DB_ID: parseInt(srch) }
      ).sort("TRK_TRACK_NAME").limit(500).lean()
    } else {
      if (srch.charAt(0) == '"' && srch.charAt(srch.length - 1) == '"'){
        srch = srch.slice(1,-1)
        tracks = await centaur.tracks.find({ TRK_TRACK_NAME: { $regex: `^${srch}$`, $options: 'i' } }).sort("TRK_TRACK_NAME").limit(500).lean()
      } else {
        tracks = await centaur.tracks.find(
          { TRK_TRACK_NAME: { $regex: `${req.params.s}`, $options: 'i' } }
        ).sort("TRK_TRACK_NAME").limit(500).lean()
      }
    }
  }

  if (tracks.length > 0) {
    return { statusCode: 200, body: tracks }
  } else {
    return { statusCode: 200, body: [] }
  }
}


module.exports = {
  admin_search_track_handler
}
