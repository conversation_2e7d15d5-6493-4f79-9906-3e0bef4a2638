const centaur = require('@mediality/centaur')


const admin_search_jockey_handler = async (req) => {
  console.log("Got to the Admin Search Horse Form Handler");

  if (req.params == null) {
    var jockeys = await centaur.jockeys.find().sort("JOC_JOCKEY_DISPLAYNAME").limit(500).lean()
  } else if (req.params != null) {
    var srch = req.params.s
    var jockeys = []
    if (srch.charAt(0) == '"' && srch.charAt(srch.length - 1) == '"'){
      srch = srch.slice(1,-1)
      jockeys = await centaur.jockeys.find({ JOC_JOCKEY_DISPLAYNAME: { $regex: `^${srch}$`, $options: 'i' } }).sort("JOC_JOCKEY_DISPLAYNAME").limit(500).lean()
      if (jockeys.length < 1){
        jockeys = await centaur.jockeys.find({ JOC_JOCKEY_SURNAME: { $regex: `^${srch}$`, $options: 'i' } }).sort("JOC_JOCKEY_SURNAME").limit(500).lean()
      }
    } else {
      jockeys = await centaur.jockeys.find(
        { JOC_JOCKEY_DISPLAYNAME: { $regex: `${req.params.s}`, $options: 'i' } }
      ).sort("JOC_JOCKEY_DISPLAYNAME").limit(500).lean()
    }
  }

  if (jockeys.length > 0) {
    return { statusCode: 200, body: jockeys }
  } else {
    return { statusCode: 200, body: [] }
  }
}


module.exports = {
  admin_search_jockey_handler
}
