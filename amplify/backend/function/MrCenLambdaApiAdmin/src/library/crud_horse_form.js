const centaur = require('@mediality/centaur')
const helper = require('../helper/helper_horse')


const admin_search_horse_form_handler = async (req) => {
  console.log("Got to the Admin Search Horse Form Handler");
  try{
    if (req.params == null) {
      var form = await centaur.form.find().sort("horse_name").sort({ horse_name: 1}).limit(30).lean()
    } else if (req.params != null) {
      var srch = req.params.s
      var horse_names = []
      if (helper.isNumeric(srch)){
        horse_names = await centaur.horses.find({ HRN_HORSE_ID: srch })
          .select({
            HRN_HORSE_NAME: 1,
            HRN_HORSE_ID: 1 })
          .sort('HRN_HORSE_NAME')
          .limit(30)
      } else if (srch.charAt(0) == '"' && srch.charAt(srch.length - 1) == '"'){
        srch = srch.slice(1,-1)
        horse_names = await centaur.horses.find({ HRN_HORSE_NAME: { $regex: `^${srch}$`, $options: 'i' } })
          .select({
            HRN_HORSE_NAME: 1,
            HRN_HORSE_ID: 1 })
          .sort('HRN_HORSE_NAME')
          .limit(30)
      } else {
        horse_names = await centaur.horses.find({ HRN_HORSE_NAME: { $regex: `^${req.params.s}`, $options: 'i' }})
          .select({
            HRN_HORSE_NAME: 1,
            HRN_HORSE_ID: 1 })
          .sort('HRN_HORSE_NAME')
          .limit(30)
      }

      var horse_ids = [];
      for (var i = 0; i < horse_names.length; i++) {
        horse_ids.push(horse_names[i].HRN_HORSE_ID)
      }

      var form = await centaur.form.find(
        { horse_id: { $in: horse_ids } }
      ).sort('horse_name').lean()
    }

    if (form.length > 0) {
      return { statusCode: 200, body: form }
    } else {
      return { statusCode: 200, body: [] }
    }
  } catch(err){
    console.log('Error in the horse form admin search:', err)
    return {statusCode: 501, body: {"message" : "Failed to search."}}
  }
}

const searchHorseForm = async (srch) => {
  var horses = []
  if (helper.isNumeric(srch)){
    horses = await centaur.horses
      .find(
        { HRN_HORSE_ID: parseInt(srch) }
      )
      .select({
        HRN_HORSE_ID: 1,
        HRN_HORSE_NAME: 1,
        HOR_COUNTRY_OF_ORIGIN: 1,
        HOR_FOALING_DATE: 1,
        HOR_DAM_ID: 1,
        HOR_SIRE_ID: 1
      }).sort('HRN_HORSE_NAME').limit(500).lean();
  } else if (srch.charAt(0) == '"' && srch.charAt(srch.length - 1) == '"'){
    srch = srch.slice(1,-1)
    console.log(`search horse: ${srch}`)
    horses = await centaur.horses
      .find({HRN_HORSE_NAME: { $regex: `^${srch}$`, $options: 'i' }})
      .select({
        HRN_HORSE_ID: 1,
        HRN_HORSE_NAME: 1,
        HOR_COUNTRY_OF_ORIGIN: 1,
        HOR_FOALING_DATE: 1,
        HOR_DAM_ID: 1,
        HOR_SIRE_ID: 1
      }).sort('HRN_HORSE_NAME').limit(500).lean();
      console.log(horses)
  } else {
    horses = await centaur.horses
      .find(
        { HRN_HORSE_NAME: { $regex: `${srch}`, $options: 'i' } }
      )
      .select({
        HRN_HORSE_ID: 1,
        HRN_HORSE_NAME: 1,
        HOR_COUNTRY_OF_ORIGIN: 1,
        HOR_FOALING_DATE: 1,
        HOR_DAM_ID: 1,
        HOR_SIRE_ID: 1
      }).sort('HRN_HORSE_NAME').limit(500).lean();
  }
  return horses
}

module.exports = {
  admin_search_horse_form_handler
}
