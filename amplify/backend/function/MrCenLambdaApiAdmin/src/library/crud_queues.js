const centaur = require('@mediality/centaur')


const admin_queues_list_handler = async (req) => {
    console.log("Got to the Admin Queues List Handler");

    var processingQueueName = `MrCenProcessingQueue-${process.env.ENV}`
    var deadLetterQueueName = `MrCenDeadLetterQueue-${process.env.ENV}`
    var processingQueueUrl = await getQueueURL(processingQueueName)
    var deadLetterQueueUrl = await getQueueURL(deadLetterQueueName)
    var processingQueue = await getEstimatedMessages(processingQueueUrl['QueueUrl'])
    var deadLetterQueue = await getEstimatedMessages(deadLetterQueueUrl['QueueUrl'])

    processing_count = deadLetterQueue['Attributes']['ApproximateNumberOfMessages']
    failed_count = processingQueue['Attributes']['ApproximateNumberOfMessages']

    processing_complete = await centaur.processed_meetings.estimatedDocumentCount().lean()

    const count = {
        processing: parseInt(processing_count),
        failed: parseInt(failed_count),
        completed: parseInt(processing_complete)
    }
    return { statusCode: 200, body: count }
}

const getEstimatedMessages = async (queueUrl) => {
    var REGION = process.env.region || 'ap-southeast-2'
    const AWS = require('aws-sdk');
    AWS.config.update({ region: REGION });
    var sqs = new AWS.SQS();
    var params = { QueueUrl: queueUrl, AttributeNames: ['All'] };
    const fetchSqsAttributes = await sqs.getQueueAttributes(params).promise();
    if (fetchSqsAttributes) {
        return fetchSqsAttributes
    } else {
        console.log("Error counting messages")
        return "Error"
    }
}

const getQueueURL = async (queueName) => {
    var REGION = process.env.region || 'ap-southeast-2'
    const AWS = require('aws-sdk');
    AWS.config.update({ region: REGION });
    var sqs = new AWS.SQS();
    var params = { QueueName: queueName };
    const fetchSqsUrl = await sqs.getQueueUrl(params).promise();
    if (fetchSqsUrl) {
        return fetchSqsUrl
    } else {
        console.log("Error getting Queue URL")
        return ''
    }

}


module.exports = {
    admin_queues_list_handler,
}
