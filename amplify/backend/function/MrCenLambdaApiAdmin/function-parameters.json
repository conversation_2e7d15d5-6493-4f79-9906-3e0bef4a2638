{"lambdaLayers": [{"type": "ProjectLayer", "resourceName": "centaurappCentaurAppCommonLayer", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "stgblack"}], "environmentVariableList": [{"cloudFormationParameterName": "awsLocal", "environmentVariableName": "awsLocal"}, {"cloudFormationParameterName": "centaurSecrets", "environmentVariableName": "centaurSecrets"}, {"cloudFormationParameterName": "customerApiGateway", "environmentVariableName": "customerApiGateway"}]}