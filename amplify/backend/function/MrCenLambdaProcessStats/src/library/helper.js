const util = require("util");
const moment = require("moment");

const inspectObject = async (jsonObject) => {
  console.log(
    util.inspect(jsonObject, { showHidden: false, depth: null, colors: true })
  );
};

const getSecrets = async (secretName) => {
  const AWS = require("aws-sdk");
  var secretsmanager = new AWS.SecretsManager();
  var params = {
    SecretId: secretName,
  };
  const fetchSecretString = await secretsmanager
    .getSecretValue(params)
    .promise();
  var aws_secrets = JSON.parse(fetchSecretString.SecretString);
  return aws_secrets;
};

/**
 * Establishes a connection to a MongoDB database based on the environment type.
 * @param {string} env - The environment type ('local' or 'production').
 * @returns {Promise} - A promise that resolves with the mongoose instance connected to the specified database.
 */
const openDBConnection = async (env) => {
  const local = require("../local");
  const mongoose = require("mongoose");
  const cert_path = "./rds-cert/global-bundle.pem";
  let response;

  if (env === "local") {
    const { DB_USERNAME, DB_PASSWORD, DB_URL, DB_NAME } = local;
    const CONNECTION_STRING = `mongodb://${DB_USERNAME}:${DB_PASSWORD}@${DB_URL}/${DB_NAME}`;

    try {
      await mongoose.connect(CONNECTION_STRING, {
        connectTimeoutMS: 1000,
        tlsCAFile: cert_path,
        directConnection: true,
        ssl: true,
        sslValidate: false,
        maxPoolSize: 10,
      });
      console.log("Connected to Local");
      response = "Connected to Local";
    } catch (err) {
      console.log("Not connected:", err);
      response = "Not Connected to Local";
    }
  } else {
    console.log("Running Online");
    const aws_secrets = await getSecrets(process.env.centaurSecrets);
    const { DB_USERNAME, DB_PASSWORD, DB_URL, DB_NAME } = aws_secrets;
    const CONNECTION_STRING = `mongodb://${DB_USERNAME}:${DB_PASSWORD}@${DB_URL}/${DB_NAME}?tls=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false`;

    try {
      await mongoose.connect(CONNECTION_STRING, {
        connectTimeoutMS: 1000,
        tlsCAFile: cert_path,
        directConnection: true,
        ssl: true,
        sslValidate: false,
        maxPoolSize: 10,
      });
      console.log("Connected to Online");
      response = "Connected Online";
    } catch (err) {
      response = "Not Connected Online";
      console.log(`Not connected error: ${err} response: ${response}`);
    }
  }

  return mongoose;
};

/**
 * Analyzes a list of horse racing performances to determine the finish positions of the last 'num' races,
 * considering spells (breaks) between races. Formats the output to show breaks and adjusts finish positions for display purposes.
 *
 * @param {number} num - The number of recent starts to process.
 * @param {Array} data - An array of objects representing each race, with properties for the race date (FRM_FORM_DATE) and finish position (FRM_FINISH_POSITION).
 * @returns {string} A string representing the formatted finish positions of the last 'num' races, including indications of spells (breaks) between races.
 */
const processStarts = (num, data) => {
  let result = "";
  try {
    const spell = 84;
    if (data.length === 0) return result;
    if (data.length === 1) {
      result =
        data[0].FRM_FINISH_POSITION > 9 ? "0" : data[0].FRM_FINISH_POSITION;
    }
    if (data.length >= 2) {
      for (let i = 0; i < num; i++) {
        if (data[i] && data[i + 1]) {
          const gap = Math.abs(
            moment(data[i].FRM_FORM_DATE).diff(
              moment(data[i + 1].FRM_FORM_DATE),
              "days"
            )
          );
          result = `${gap >= spell ? "x" : ""}${
            data[i].FRM_FINISH_POSITION > 9 ? "0" : data[i].FRM_FINISH_POSITION
          }${result}`;
        } else {
          continue;
        }
      }
    }
  } catch (err) {
    console.log("Error Generating process Starts" + err);
    return result;
  }
  return result;
};

/**
 * Filters out race events from a given dataset that do not meet specific class criteria related to barrier trials.
 *
 * @param {Array} data - An array of objects representing race events with properties nested under 'classes'.
 * @returns {Array} An array of race event objects that have passed the specified filtering criteria.
 */
const cleanseBarrierTrials = (data) => {
  let cleansedData = [];
  for (const raceEvent of data) {
    if (
      (raceEvent.classes &&
        raceEvent.classes.class_id &&
        raceEvent.classes.class_id != 90 &&
        (!raceEvent.classes.second_class_id ||
          raceEvent.classes.second_class_id != 90)) ||
      (raceEvent.classes &&
        raceEvent.classes.class &&
        raceEvent.classes.class == "Open" &&
        (!raceEvent.classes.second_class_id ||
          raceEvent.classes.second_class_id != 90))
    ) {
      cleansedData.push(raceEvent);
    }
  }
  return cleansedData;
};

/**
 * Checks if the second distance is within a range of 50 units above or below the first distance.
 *
 * @param {number|string} dist_1 - The first distance value.
 * @param {number|string} dist_2 - The second distance value.
 * @returns {boolean} Returns true if dist_2 is within the specified range of dist_1, otherwise false.
 */
const similarDistance = (dist_1, dist_2) => {
  const max = parseInt(dist_1) + 50;
  const min = parseInt(dist_1) - 50;

  return dist_2 > min && dist_2 < max;
};

/**
 * Calculates the number of (UPs) between given dates, based on a threshold of 84 days.
 *
 * @param {string} run_1 - The first date string representing a run.
 * @param {string} run_2 - The second date string representing a run.
 * @param {string} run_3 - The third date string representing a run.
 * @param {string} run_4 - The fourth date string representing a run.
 * @param {string} run_5 - The fifth date string representing a run.
 * @returns {number} The count of periods where the gap between consecutive dates is at least 84 days.
 */
const calculateUPs = (run_1, run_2, run_3, run_4, run_5) => {
  const runs = [run_1, run_2, run_3, run_4, run_5];
  const spell = 84;

  for (let i = 0; i < runs.length - 1; i++) {
    if (runs[i] === "0") {
      return i + 1;
    }

    const current = moment(runs[i]);
    const next = moment(runs[i + 1]);
    const gap = current.diff(next, "days");

    if (gap >= spell) {
      return i + 1;
    }
  }

  return 0;
};

/**
 * Calculates a weighted adjusted master rating (WAMR) for a given horse based on its characteristics and race conditions.
 * @param {number} master_rating - The base rating of the horse.
 * @param {number} age - The age of the horse.
 * @param {string} sex - The sex of the horse ('M' for male, 'F' for female).
 * @param {number} distance - The distance of the race in meters.
 * @param {number} weight - The weight of the horse in kilograms.
 * @param {string} meeting_date - The date of the race meeting.
 * @returns {Promise<number>} The calculated weighted adjusted master rating (WAMR) rounded to the nearest half-point.
 */
const generateRating = async (
  master_rating,
  age,
  sex,
  distance,
  weight,
  meeting_date
) => {
  // Fetch the weight-for-age adjustment
  const wfa = await getWfa(age, sex, distance, meeting_date);

  // Calculate the natural logarithm of the race distance
  const ld = Math.log(distance);

  // Calculate the adjusted weight-for-age
  const awfa = weight - wfa;

  // Calculate the weighted adjusted master rating
  let wamr = master_rating - (-0.184 * ld ** 2 + 3.3981 * ld - 14.016) * awfa;

  // Round the WAMR to the nearest half-point
  wamr = Math.round(wamr * 2) / 2;

  return wamr;
};

/**
 * Calculates the weight-for-age adjustment for a horse based on its age, sex, race distance, and the month of the meeting.
 * Adjusts the base weight-for-age value by a sex-based modifier.
 *
 * @param {number} age - Age of the horse.
 * @param {string} sex - Sex of the horse, either 'M' or 'F'.
 * @param {number} distance - Race distance in meters.
 * @param {string} date - Date of the race meeting, used to determine the month.
 * @returns {number} The adjusted weight-for-age value for the horse, or logs an error if the data is not found.
 */
const getWfa = async (age, sex, distance, date) => {
  if (age > 4) {
    age = "5+";
  } else {
    age = age.toString();
  }
  if (distance < 1201) {
    distance = "1200";
  } else if (distance < 1401) {
    distance = "1400";
  } else if (distance < 1601) {
    distance = "1600";
  } else if (distance < 2001) {
    distance = "2000";
  } else if (distance < 2401) {
    distance = "2400";
  } else {
    distance = "3200";
  }
  var theDate = moment(date);
  var theMonth = theDate.month() + 1;

  if (theMonth < 10) {
    theMonth = "0" + theMonth.toString();
  }

  var sexAdjust = 0;

  if (["M", "F"].includes(sex)) {
    sexAdjust = 2;
  }

  // console.log(age,sex,distance,theMonth)

  var wfa = {
    1200: {
      2: {
        "08": 42.5,
        "09": 42.5,
        10: 42.5,
        11: 42.5,
        12: 42.5,
        "01": 45.0,
        "02": 46.0,
        "03": 47.0,
        "04": 48.0,
        "05": 49.0,
        "06": 50.0,
        "07": 51.0,
      },
      3: {
        "08": 51.5,
        "09": 52.0,
        10: 53.0,
        11: 53.5,
        12: 54.5,
        "01": 55.0,
        "02": 55.5,
        "03": 56.0,
        "04": 56.5,
        "05": 57.0,
        "06": 57.5,
        "07": 58.0,
      },
      4: {
        "08": 58.5,
        "09": 58.5,
        10: 58.5,
        11: 58.5,
        12: 58.5,
        "01": 58.5,
        "02": 58.5,
        "03": 58.5,
        "04": 58.5,
        "05": 58.5,
        "06": 58.5,
        "07": 58.5,
      },
      "5+": {
        "08": 58.5,
        "09": 58.5,
        10: 58.5,
        11: 58.5,
        12: 58.5,
        "01": 58.5,
        "02": 58.5,
        "03": 58.5,
        "04": 58.5,
        "05": 58.5,
        "06": 58.5,
        "07": 58.5,
      },
    },
    1400: {
      2: {
        "08": 42.5,
        "09": 42.5,
        10: 42.5,
        11: 42.5,
        12: 42.5,
        "01": 44.0,
        "02": 45.0,
        "03": 46.0,
        "04": 47.0,
        "05": 48.0,
        "06": 49.0,
        "07": 50.0,
      },
      3: {
        "08": 50.5,
        "09": 51.0,
        10: 52.0,
        11: 53.0,
        12: 54.0,
        "01": 54.5,
        "02": 55.5,
        "03": 56.0,
        "04": 56.5,
        "05": 57.0,
        "06": 57.5,
        "07": 58.0,
      },
      4: {
        "08": 58.5,
        "09": 58.5,
        10: 58.5,
        11: 59.0,
        12: 59.0,
        "01": 59.0,
        "02": 59.0,
        "03": 59.0,
        "04": 59.0,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
      "5+": {
        "08": 59.0,
        "09": 59.0,
        10: 59.0,
        11: 59.0,
        12: 59.0,
        "01": 59.0,
        "02": 59.0,
        "03": 59.0,
        "04": 59.0,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
    },
    1600: {
      2: {
        "08": 42.5,
        "09": 42.5,
        10: 42.5,
        11: 42.5,
        12: 42.5,
        "01": 43.5,
        "02": 44.5,
        "03": 45.5,
        "04": 46.5,
        "05": 47.5,
        "06": 48.5,
        "07": 49.5,
      },
      3: {
        "08": 50.0,
        "09": 50.5,
        10: 51.0,
        11: 52.0,
        12: 53.0,
        "01": 54.0,
        "02": 55.0,
        "03": 56.0,
        "04": 56.5,
        "05": 57.0,
        "06": 57.5,
        "07": 58.0,
      },
      4: {
        "08": 58.5,
        "09": 58.5,
        10: 58.5,
        11: 59.0,
        12: 59.0,
        "01": 59.0,
        "02": 59.0,
        "03": 59.0,
        "04": 59.0,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
      "5+": {
        "08": 59.0,
        "09": 59.0,
        10: 59.0,
        11: 59.0,
        12: 59.0,
        "01": 59.0,
        "02": 59.0,
        "03": 59.0,
        "04": 59.0,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
    },
    2000: {
      2: {
        "08": 42.5,
        "09": 42.5,
        10: 42.5,
        11: 42.5,
        12: 42.5,
        "01": 42.5,
        "02": 43.5,
        "03": 44.5,
        "04": 45.5,
        "05": 46.5,
        "06": 47.5,
        "07": 48.5,
      },
      3: {
        "08": 49.0,
        "09": 49.5,
        10: 50.0,
        11: 51.0,
        12: 52.0,
        "01": 53.0,
        "02": 54.0,
        "03": 54.5,
        "04": 55.5,
        "05": 56.5,
        "06": 57.0,
        "07": 57.5,
      },
      4: {
        "08": 58.0,
        "09": 58.0,
        10: 58.0,
        11: 58.5,
        12: 58.5,
        "01": 58.5,
        "02": 59.0,
        "03": 59.0,
        "04": 59.0,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
      "5+": {
        "08": 59.0,
        "09": 59.0,
        10: 59.0,
        11: 59.0,
        12: 59.0,
        "01": 59.0,
        "02": 59.0,
        "03": 59.0,
        "04": 59.0,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
    },
    2400: {
      2: {
        "08": 42.5,
        "09": 42.5,
        10: 42.5,
        11: 42.5,
        12: 42.5,
        "01": 42.5,
        "02": 42.5,
        "03": 42.5,
        "04": 42.5,
        "05": 42.5,
        "06": 42.5,
        "07": 42.5,
      },
      3: {
        "08": 48.5,
        "09": 49.0,
        10: 49.5,
        11: 50.5,
        12: 51.0,
        "01": 52.0,
        "02": 53.0,
        "03": 54.0,
        "04": 54.5,
        "05": 55.5,
        "06": 56.0,
        "07": 57.0,
      },
      4: {
        "08": 57.5,
        "09": 57.5,
        10: 57.5,
        11: 58.0,
        12: 58.0,
        "01": 58.0,
        "02": 58.5,
        "03": 58.5,
        "04": 58.5,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
      "5+": {
        "08": 59.0,
        "09": 59.0,
        10: 59.0,
        11: 59.0,
        12: 59.0,
        "01": 59.0,
        "02": 59.0,
        "03": 59.0,
        "04": 59.0,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
    },
    3200: {
      2: {
        "08": 42.5,
        "09": 42.5,
        10: 42.5,
        11: 42.5,
        12: 42.5,
        "01": 42.5,
        "02": 42.5,
        "03": 42.5,
        "04": 42.5,
        "05": 42.5,
        "06": 42.5,
        "07": 42.5,
      },
      3: {
        "08": 48.0,
        "09": 48.5,
        10: 49.0,
        11: 50.0,
        12: 50.5,
        "01": 51.5,
        "02": 52.5,
        "03": 53.5,
        "04": 54.0,
        "05": 55.0,
        "06": 55.5,
        "07": 56.0,
      },
      4: {
        "08": 57.5,
        "09": 57.5,
        10: 57.5,
        11: 58.0,
        12: 58.0,
        "01": 58.0,
        "02": 58.5,
        "03": 58.5,
        "04": 58.5,
        "05": 59.0,
        "06": 59.0,
        "07": 59.0,
      },
      "5+": {
        "08": 59.5,
        "09": 59.5,
        10: 59.5,
        11: 59.5,
        12: 59.5,
        "01": 59.5,
        "02": 59.5,
        "03": 59.5,
        "04": 59.5,
        "05": 59.5,
        "06": 59.5,
        "07": 59.5,
      },
    },
  };

  if (wfa[distance] && wfa[distance][age] && wfa[distance][age][theMonth]) {
    // console.log(wfa[distance][age][theMonth] - sexAdjust)
    return wfa[distance][age][theMonth] - sexAdjust;
  } else {
    console.log("Error with ratings: wfa not found");
  }
};

/**
 * Retrieves the base rating for a given class ID from a predefined list.
 * If the class ID is not found, it defaults to a rating of 30.
 *
 * @param {number} classId - The class identifier for which the base rating is to be retrieved.
 * @returns {number} An integer representing the base rating for the given class ID. If the class ID is not in the list, it returns 30.
 */
const getBaseRatingByClass = (classId) => {
  var classRatingList = {
    196: 20,
    212: 20,
    213: 20,
    214: 20,
    197: 21,
    215: 21,
    216: 21,
    217: 21,
    218: 21,
    80: 22,
    81: 22,
    82: 22,
    19: 22,
    30: 22,
    83: 22,
    84: 22,
    97: 22,
    186: 22,
    187: 22,
    188: 22,
    189: 22,
    190: 22,
    198: 22,
    199: 22,
    200: 22,
    219: 22,
    220: 22,
    221: 22,
    222: 22,
    223: 22,
    224: 22,
    225: 22,
    226: 22,
    268: 22,
    269: 22,
    270: 22,
    271: 22,
    272: 22,
    273: 22,
    274: 22,
    275: 22,
    276: 22,
    315: 22,
    153: 23,
    201: 23,
    227: 23,
    228: 23,
    229: 23,
    230: 23,
    231: 23,
    277: 23,
    278: 23,
    279: 23,
    135: 24,
    154: 24,
    155: 24,
    156: 24,
    202: 24,
    211: 24,
    232: 24,
    233: 24,
    234: 24,
    235: 24,
    236: 24,
    280: 24,
    281: 24,
    282: 24,
    283: 24,
    284: 24,
    285: 24,
    286: 24,
    287: 24,
    288: 24,
    316: 24,
    157: 25,
    203: 25,
    237: 25,
    238: 25,
    239: 25,
    289: 25,
    290: 25,
    85: 26,
    240: 26,
    291: 26,
    292: 26,
    317: 26,
    293: 27,
    294: 27,
    18: 28,
    86: 28,
    98: 28,
    136: 28,
    158: 28,
    159: 28,
    204: 28,
    241: 28,
    242: 28,
    243: 28,
    244: 28,
    295: 28,
    296: 28,
    318: 28,
    137: 29,
    245: 29,
    246: 29,
    297: 29,
    54: 30,
    87: 30,
    138: 30,
    139: 30,
    140: 30,
    160: 30,
    161: 30,
    162: 30,
    205: 30,
    298: 30,
    299: 30,
    300: 30,
    301: 30,
    302: 30,
    319: 30,
    141: 31,
    142: 31,
    247: 31,
    303: 31,
    320: 31,
    17: 32,
    99: 32,
    143: 32,
    144: 32,
    145: 32,
    146: 32,
    163: 32,
    164: 32,
    165: 32,
    166: 32,
    248: 32,
    321: 32,
    147: 33,
    148: 33,
    249: 33,
    250: 33,
    304: 33,
    305: 33,
    322: 33,
    206: 34,
    251: 34,
    252: 34,
    306: 34,
    323: 34,
    88: 35,
    93: 35,
    100: 35,
    149: 35,
    167: 35,
    168: 35,
    169: 35,
    170: 35,
    171: 35,
    253: 35,
    254: 35,
    307: 35,
    309: 35,
    310: 35,
    311: 35,
    312: 35,
    313: 35,
    314: 35,
    324: 35,
    150: 36,
    151: 36,
    152: 36,
    207: 36,
    255: 36,
    256: 36,
    308: 36,
    325: 36,
    89: 37,
    172: 37,
    173: 37,
    174: 37,
    175: 37,
    257: 37,
    258: 37,
    326: 37,
    176: 38,
    177: 38,
    178: 38,
    179: 38,
    208: 38,
    259: 38,
    260: 38,
    180: 39,
    181: 39,
    182: 39,
    183: 39,
    184: 39,
    261: 39,
    262: 39,
    16: 40,
    185: 40,
    191: 40,
    192: 40,
    193: 40,
    194: 40,
    195: 40,
    209: 40,
    263: 40,
    264: 40,
    210: 41,
    265: 41,
    266: 41,
  };
  if (classRatingList[classId]) {
    return classRatingList[classId];
  } else {
    return 30;
  }
};

const getEntityRatingAdj = (meanPrice, priceStats, maxadjust, minadjust) => {
  console.log(meanPrice);
  console.log(priceStats);
  let entPrice = 0;
  if (priceStats.avg) entPrice = priceStats.avg - meanPrice;
  if (entPrice < 0)
    return (entPrice / (priceStats.max - priceStats.avg)) * maxadjust;
  else if (entPrice > 0)
    return (entPrice / (-priceStats.min + priceStats.avg)) * minadjust;
  else return 0;
};

const enterMarket = (horses, confidence) => {
  console.log(horses, confidence);
  let ratings = [];
  let tips = [];
  for (let the_horse of horses) {
    if (the_horse.rating)
      ratings.push({
        id: the_horse.id,
        rating: the_horse.rating,
        rawPercent: 0,
        finalPercent: 0,
        price: 0,
      });
    if (!the_horse.scratched && the_horse.tip)
      tips.push({ id: the_horse.id, tip: the_horse.tip });
  }
  ratings.sort((a, b) => a.rating - b.rating);
  let rawPercent = 0;
  let curve = 1;
  let lastrating = 0;
  let lastratingAdj = 0;
  for (let horse of ratings) {
    if (horse.rating <= lastrating + 0.5) {
      lastratingAdj += 0.1;
    } else {
      lastratingAdj = 0;
    }
    lastrating = horse.rating;
    let ratingDiff = horse.rating - ratings[0].rating;
    let ratingadj = 1;
    let furtheradj = 1;
    if (
      ratingDiff >
      ratings[ratings.length - 1].rating - (ratings[0].rating + 2)
    )
      ratingadj = 3.5;
    else if (
      ratingDiff >
      ratings[ratings.length - 1].rating - (ratings[0].rating + 4)
    )
      ratingadj = 2.5;
    else if (
      ratingDiff >
      ratings[ratings.length - 1].rating - (ratings[0].rating + 7)
    )
      ratingadj = 1.5;
    else if (
      ratingDiff <
      ratings[ratings.length - 1].rating - (ratings[0].rating + 10)
    )
      furtheradj = 2;
    else if (
      ratingDiff <
      ratings[ratings.length - 1].rating - (ratings[0].rating + 12)
    )
      furtheradj = 2.5;
    else if (
      ratingDiff <
      ratings[ratings.length - 1].rating - (ratings[0].rating + 15)
    )
      furtheradj = 3;
    else if (
      ratingDiff <
      ratings[ratings.length - 1].rating - (ratings[0].rating + 20)
    )
      furtheradj = 5;
    curve = ratingDiff * (ratingadj + lastratingAdj) + confidence / furtheradj;
    horse.rawPercent = confidence * curve;
    rawPercent += horse.rawPercent;
  }
  const percentadj = (1.1 + ratings.length / 100) / rawPercent;
  let tipPct = 0;
  let tipHorses = [];
  let tipNum =
    ratings.length / 2 < 4
      ? 4
      : ratings.length / 2 > 8
      ? 8
      : Math.round(ratings.length / 2);
  FinalRating: for (let i = 0; i < ratings.length; i++) {
    let horse = ratings[i];
    horse.finalPercent = horse.rawPercent * percentadj * 100;
    horse.price = rationalizeMarket(
      Math.round((100 / horse.finalPercent) * 10) / 10
    );
    horse.finalPercent = Math.round((100 / horse.price) * 2) / 2;
    if (i + 1 > ratings.length - tipNum) {
      if (tips.length > 0)
        for (const tip of tips) if (tip.id === horse.id) continue FinalRating;
      horse.tipPercent = horse.finalPercent * horse.finalPercent;
      tipHorses.unshift(horse);
      tipPct += horse.tipPercent;
    }
  }
  if (tips.length < 4 && tips.length > 0) {
    // reorder tips to move everything up
    tips.sort((a, b) => parseInt(a.tip) - parseInt(b.tip));
    let i = 0;
    for (let tip of tips) {
      i++;
      if (parseInt(tip.tip) !== i) tip.tip = i;
    }
    let j = 0;
    while (i < 4 && i < horses.length - 1) {
      i++;
      tips.push({ id: tipHorses[j].id, tip: i });
      j++;
    }
  } else if (tips.length === 0) {
    if (tipHorses.length === 0) {
      console.warn("No tipHorses available to assign tips.");
    } else {
      let i = 0;
      let maxIterations = 100; // Set a reasonable limit
      while (i < 4 && i < horses.length - 1 && maxIterations > 0) {
        maxIterations--;
        let totalTip = 0;
        let thisTip = Math.random() * tipPct;
        let foundHorse = false;
        tipHorseLoop: for (const horse of tipHorses) {
          for (let tip of tips) if (tip.id === horse.id) continue tipHorseLoop;
          totalTip += horse.tipPercent;
          if (thisTip <= totalTip) {
            i++;
            tips.push({ id: horse.id, tip: i });
            tipPct -= horse.tipPercent;
            foundHorse = true;
            break;
          }
        }
        if (!foundHorse) {
          // No horse found to tip, prevent infinite loop
          console.warn("No eligible horses found to assign tips.");
          break;
        }
      }
    }
  }

  for (let the_horse of horses) {
    for (const horse of ratings) {
      if (the_horse.id == horse.id) {
        the_horse.betting = horse.price;
      }
    }
    for (const horse of tips) {
      if (the_horse.id === horse.id) {
        the_horse.tip = horse.tip;
      }
    }
    if (the_horse.scratched) {
      the_horse.betting = "";
      the_horse.tip = "";
    }
  }
  return horses;
};
const rationalizeMarket = (num) => {
  if (num < 1) num = 501;
  else if (num < 1.1) num = Math.round(num * 100) / 100;
  else if (num < 1.3) num = Math.round(num * 50) / 50;
  else if (num < 2.5) num = Math.round(num * 20) / 20;
  else if (num < 4) num = Math.round(num * 10) / 10;
  else if (num < 5) num = Math.round(num * 5) / 5;
  else if (num < 10) num = Math.round(num * 2) / 2;
  else if (num < 21) num = Math.round(num);
  else if (num < 31) num = Math.round(num / 5) * 5 + 1;
  else if (num < 101) num = Math.round(num / 10) * 10 + 1;
  else if (num < 201) num = Math.round(num / 50) * 50 + 1;
  else if (num < 501) num = Math.round(num / 100) * 100 + 1;
  else if (num > 501) num = 501;
  return num;
};

module.exports = {
  openDBConnection,
  getSecrets,
  inspectObject,
  processStarts,
  cleanseBarrierTrials,
  similarDistance,
  calculateUPs,
  generateRating,
  getBaseRatingByClass,
  getEntityRatingAdj,
  enterMarket,
};
