const helper = require("../library/helper");
const centaur = require("@mediality/centaur");
const moment = require("moment");

/**
 * Asynchronously retrieves horse form data for a list of horse IDs using the `centaur` library.
 * Returns the results array if forms are found, otherwise logs an error and returns an empty array.
 *
 * @param {Object} conObj - An object representing the database connection (not used in the current implementation).
 * @param {Object} event - An object containing an `entrants` property, which is an array of horse IDs.
 * @returns {Array} - An array of horse forms if found, otherwise an empty array.
 */
const getForms = async (conObj, event) => {
  const horseIdList = event["entrants"];
  const results = await centaur.form.find({ horse_id: { $in: horseIdList } });

  if (results.length > 0) {
    return results;
  } else {
    console.log("Error with retrieving horse form for statistics");
    return [];
  }
};

const getHorses = async (conObj, event) => {
  const horseIdList = event["entrants"];

  const results = await centaur.horses
    .find({
      HRN_HORSE_ID: { $in: horseIdList },
    })
    .select({
      HRN_HORSE_NAME: 1,
      HRN_HORSE_ID: 1,
      HOR_START_TOTAL: 1,
      HOR_H_1STS_TOTAL: 1,
      HOR_H_2NDS_TOTAL: 1,
      HOR_H_3RDS_TOTAL: 1,
      HOR_WET_START_TOTAL: 1,
      HOR_WET_1ST_TOTAL: 1,
      HOR_WET_2ND_TOTAL: 1,
      HOR_WET_3RD_TOTAL: 1,
    });

  return results;
};
/**
 * Processes horse racing data to generate statistics for each horse based on its past performance.
 * Calculates various metrics such as total races, wins, places, and specific conditions like track type and weather conditions.
 * Evaluates the best performance ratings under different conditions.
 *
 * @param {object} conObj - Database connection object (not used in the current implementation).
 * @param {object} event - Object containing details about the event, including jockey and trainer pairings, track ID, and event distance.
 * @param {array} form - Array of horse form data, where each entry contains detailed past performance of a horse.
 * @returns {object} An object where each key is a `horse_id` and the value is an object containing detailed statistics and ratings for that horse.
 */
const genBasicStatsFromForm = async (conObj, event, form) => {
  let statistics = {};
  for (const formHorse of form) {
    const horseId = formHorse.horse_id;
    const jockey = event.pair_jockey[horseId] ? event.pair_jockey[horseId] : "";
    const trainer = event.pair_trainer[horseId]
      ? event.pair_trainer[horseId]
      : "";
    const horseForm = helper.cleanseBarrierTrials(formHorse.form);
    let st = 0,
      s1 = 0,
      s2 = 0,
      s3 = 0,
      srat = { "@_type": "career", "@_value": 0, "@_date": "" };
    let dt = 0,
      d1 = 0,
      d2 = 0,
      d3 = 0,
      drat = { "@_type": "distance", "@_value": 0, "@_date": "" };
    let tt = 0,
      t1 = 0,
      t2 = 0,
      t3 = 0,
      trat = { "@_type": "track", "@_value": 0, "@_date": "" };
    var ft = 0,
      f1 = 0,
      f2 = 0,
      f3 = 0,
      frat = { "@_type": "firm", "@_value": 0, "@_date": "" };
    let gt = 0,
      g1 = 0,
      g2 = 0,
      g3 = 0,
      grat = { "@_type": "good", "@_value": 0, "@_date": "" };
    let slt = 0,
      sl1 = 0,
      sl2 = 0,
      sl3 = 0,
      slrat = { "@_type": "soft", "@_value": 0, "@_date": "" };
    var ht = 0,
      h1 = 0,
      h2 = 0,
      h3 = 0,
      hrat = { "@_type": "heavy", "@_value": 0, "@_date": "" };
    let yt = 0,
      y1 = 0,
      y2 = 0,
      y3 = 0,
      yrat = { "@_type": "synthetic", "@_value": 0, "@_date": "" };
    let fut = 0,
      fu1 = 0,
      fu2 = 0,
      fu3 = 0,
      furat = { "@_type": "first_up", "@_value": 0, "@_date": "" };
    let sut = 0,
      su1 = 0,
      su2 = 0,
      su3 = 0,
      surat = { "@_type": "second_up", "@_value": 0, "@_date": "" };
    let tut = 0,
      tu1 = 0,
      tu2 = 0,
      tu3 = 0,
      turat = { "@_type": "third_up", "@_value": 0, "@_date": "" };
    let out = 0,
      ou1 = 0,
      ou2 = 0,
      ou3 = 0,
      ourat = { "@_type": "fourth_up", "@_value": 0, "@_date": "" };
    let jt = 0,
      j1 = 0,
      j2 = 0,
      j3 = 0,
      jrat = { "@_type": "jumps", "@_value": 0, "@_date": "" };
    let dtt = 0,
      dt1 = 0,
      dt2 = 0,
      dt3 = 0,
      dtrat = { "@_type": "distance_track", "@_value": 0, "@_date": "" };
    let wt = 0,
      w1 = 0,
      w2 = 0,
      w3 = 0,
      wrat = { "@_type": "wet", "@_value": 0, "@_date": "" };
    let nt = 0,
      n1 = 0,
      n2 = 0,
      n3 = 0,
      nrat = { "@_type": "night", "@_value": 0, "@_date": "" };
    let cjt = 0,
      cj1 = 0,
      cj2 = 0,
      cj3 = 0,
      cjrat = { "@_type": "current_jockey", "@_value": 0, "@_date": "" };
    let ctt = 0,
      ct1 = 0,
      ct2 = 0,
      ct3 = 0,
      ctrat = { "@_type": "current_trainer", "@_value": 0, "@_date": "" };
    let g1t = 0,
      g11 = 0,
      g12 = 0,
      g13 = 0,
      g1rat = { "@_type": "group_1", "@_value": 0, "@_date": "" };
    let g2t = 0,
      g21 = 0,
      g22 = 0,
      g23 = 0,
      g2rat = { "@_type": "group_2", "@_value": 0, "@_date": "" };
    let g3t = 0,
      g31 = 0,
      g32 = 0,
      g33 = 0,
      g3rat = { "@_type": "group_3", "@_value": 0, "@_date": "" };
    let glt = 0,
      gl1 = 0,
      gl2 = 0,
      gl3 = 0,
      glrat = { "@_type": "listed", "@_value": 0, "@_date": "" };
    let wfat = 0,
      wfa1 = 0,
      wfa2 = 0,
      wfa3 = 0,
      wfarat = { "@_type": "weight_for_age", "@_value": 0, "@_date": "" };
    let pt = 0,
      p1 = 0,
      p2 = 0,
      p3 = 0,
      prat = { "@_type": "preparation", "@_value": 0, "@_date": "" };
    let horsePrepared = true;

    for (let i = 0; i < horseForm.length; i++) {
      let formitem = horseForm[i];
      var finish_position = formitem.finish_position;
      if (
        formitem.rating &&
        formitem.rating["@_unadjusted"] &&
        formitem.rating["@_unadjusted"] > srat["@_value"]
      ) {
        srat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
        srat["@_value"] = formitem.rating["@_unadjusted"];
      }
      st++;
      if (finish_position == 1) s1++;
      if (finish_position == 2) s2++;
      if (finish_position == 3) s3++;
      if (
        [
          "H",
          "D",
          "O",
          "L",
          "S",
          "R",
          "V",
          "Dead",
          "Soft",
          "Slow",
          "Heavy",
          "Wet",
        ].includes(formitem.track["@_condition"])
      ) {
        wt++;
        if (finish_position == 1) w1++;
        if (finish_position == 2) w2++;
        if (finish_position == 3) w3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > wrat["@_value"]
        ) {
          wrat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          wrat["@_value"] = formitem.rating["@_unadjusted"];
        }
      }
      if (
        helper.similarDistance(formitem.distance["@_metres"], event.distance)
      ) {
        dt++;
        if (finish_position == 1) d1++;
        if (finish_position == 2) d2++;
        if (finish_position == 3) d3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > drat["@_value"]
        ) {
          drat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          drat["@_value"] = formitem.rating["@_unadjusted"];
        }
      }
      if (formitem.track["@_id"] == event.track_id) {
        tt++;
        if (finish_position == 1) t1++;
        if (finish_position == 2) t2++;
        if (finish_position == 3) t3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > trat["@_value"]
        ) {
          trat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          trat["@_value"] = formitem.rating["@_unadjusted"];
        }
      }
      if (formitem.night_meeting == "N") {
        nt++;
        if (finish_position == 1) n1++;
        if (finish_position == 2) n2++;
        if (finish_position == 3) n3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > nrat["@_value"]
        ) {
          nrat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          nrat["@_value"] = formitem.rating["@_unadjusted"];
        }
      }
      if (["M", "F", "Fast", "Firm"].includes(formitem.track["@_condition"])) {
        ft++;
        if (finish_position == 1) f1++;
        if (finish_position == 2) f2++;
        if (finish_position == 3) f3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > frat["@_value"]
        ) {
          frat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          frat["@_value"] = formitem.rating["@_unadjusted"];
        }
      } else if (
        ["G", "Good", "Sand", "Dirt"].includes(formitem.track["@_condition"])
      ) {
        gt++;
        if (finish_position == 1) g1++;
        if (finish_position == 2) g2++;
        if (finish_position == 3) g3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > grat["@_value"]
        ) {
          grat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          grat["@_value"] = formitem.rating["@_unadjusted"];
        }
      } else if (
        ["O", "S", "D", "Soft", "Dead", "Slow", "Wet"].includes(
          formitem.track["@_condition"]
        )
      ) {
        slt++;
        if (finish_position == 1) sl1++;
        if (finish_position == 2) sl2++;
        if (finish_position == 3) sl3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > slrat["@_value"]
        ) {
          slrat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          slrat["@_value"] = formitem.rating["@_unadjusted"];
        }
      } else if (["H", "Heavy"].includes(formitem.track["@_condition"])) {
        ht++;
        if (finish_position == 1) h1++;
        if (finish_position == 2) h2++;
        if (finish_position == 3) h3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > hrat["@_value"]
        ) {
          hrat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          hrat["@_value"] = formitem.rating["@_unadjusted"];
        }
      } else if (["Y", "Synthetic"].includes(formitem.track["@_condition"])) {
        yt++;
        if (finish_position == 1) y1++;
        if (finish_position == 2) y2++;
        if (finish_position == 3) y3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > yrat["@_value"]
        ) {
          yrat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          yrat["@_value"] = formitem.rating["@_unadjusted"];
        }
      }

      //calculate UPs like First-up, Second-up, Third-up, Fourth-up

      var ups = helper.calculateUPs(
        formitem.meeting_date,
        horseForm[i + 1] ? horseForm[i + 1].meeting_date : 0,
        horseForm[i + 2] ? horseForm[i + 2].meeting_date : 0,
        horseForm[i + 3] ? horseForm[i + 3].meeting_date : 0,
        horseForm[i + 4] ? horseForm[i + 4].meeting_date : 0
      );
      if (
        i == 0 &&
        moment(event.meetingDate).diff(moment(formitem.meeting_date), "days") >=
          90
      ) {
        horsePrepared = false;
      } else if (horsePrepared) {
        pt++;
        if (finish_position == 1) p1++;
        if (finish_position == 2) p2++;
        if (finish_position == 3) p3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > prat["@_value"]
        ) {
          prat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          prat["@_value"] = formitem.rating["@_unadjusted"];
        }
      }
      if (ups == 1) {
        fut++;
        if (finish_position == 1) fu1++;
        if (finish_position == 2) fu2++;
        if (finish_position == 3) fu3++;
        horsePrepared = false;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > furat["@_value"]
        ) {
          furat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          furat["@_value"] = formitem.rating["@_unadjusted"];
        }
      } else if (ups == 2) {
        sut++;
        if (finish_position == 1) su1++;
        if (finish_position == 2) su2++;
        if (finish_position == 3) su3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > surat["@_value"]
        ) {
          surat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          surat["@_value"] = formitem.rating["@_unadjusted"];
        }
      } else if (ups == 3) {
        tut++;
        if (finish_position == 1) tu1++;
        if (finish_position == 2) tu2++;
        if (finish_position == 3) tu3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > turat["@_value"]
        ) {
          turat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          turat["@_value"] = formitem.rating["@_unadjusted"];
        }
      } else if (ups == 4) {
        out++;
        if (finish_position == 1) ou1++;
        if (finish_position == 2) ou2++;
        if (finish_position == 3) ou3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > ourat["@_value"]
        ) {
          ourat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          ourat["@_value"] = formitem.rating["@_unadjusted"];
        }
      }
      if (
        [
          113, 110, 92, 62, 112, 109, 117, 111, 108, 116, 115, 63, 61, 64, 59,
          50, 53,
        ].includes(formitem.classes.class_id) ||
        (formitem.classes.second_class_id &&
          [
            113, 110, 92, 62, 112, 109, 117, 111, 108, 116, 115, 63, 61, 64, 59,
            50, 53,
          ].includes(formitem.classes.second_class_id))
      ) {
        jt++;
        if (finish_position == 1) j1++;
        if (finish_position == 2) j2++;
        if (finish_position == 3) j3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > jrat["@_value"]
        ) {
          jrat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          jrat["@_value"] = formitem.rating["@_unadjusted"];
        }
      }
      if (
        helper.similarDistance(formitem.distance["@_metres"], event.distance) &&
        formitem.track["@_id"] == event.track_id
      ) {
        dtt++;
        if (finish_position == 1) dt1++;
        if (finish_position == 2) dt2++;
        if (finish_position == 3) dt3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > dtrat["@_value"]
        ) {
          dtrat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          dtrat["@_value"] = formitem.rating["@_unadjusted"];
        }
      }
      if (formitem.jockey && formitem.jockey["@_id"] == jockey) {
        cjt++;
        if (finish_position == 1) cj1++;
        if (finish_position == 2) cj2++;
        if (finish_position == 3) cj3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > cjrat["@_value"]
        ) {
          cjrat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          cjrat["@_value"] = formitem.rating["@_unadjusted"];
        }
      }
      if (formitem.trainer_id && formitem.trainer_id == trainer) {
        // console.log('found same trainer!')
        ctt++;
        if (finish_position == 1) ct1++;
        if (finish_position == 2) ct2++;
        if (finish_position == 3) ct3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > ctrat["@_value"]
        ) {
          ctrat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          ctrat["@_value"] = formitem.rating["@_unadjusted"];
        }
      }
      if (formitem.weight_type && formitem.weight_type == "W") {
        // console.log('found same trainer!')
        wfat++;
        if (finish_position == 1) wfa1++;
        if (finish_position == 2) wfa2++;
        if (finish_position == 3) wfa3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > wfarat["@_value"]
        ) {
          wfarat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          wfarat["@_value"] = formitem.rating["@_unadjusted"];
        }
      }

      if (formitem.group == 1) {
        g1t++;
        if (finish_position == 1) g11++;
        if (finish_position == 2) g12++;
        if (finish_position == 3) g13++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > g1rat["@_value"]
        ) {
          g1rat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          g1rat["@_value"] = formitem.rating["@_unadjusted"];
        }
      } else if (formitem.group == 2) {
        g2t++;
        if (finish_position == 1) g21++;
        if (finish_position == 2) g22++;
        if (finish_position == 3) g23++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > g2rat["@_value"]
        ) {
          g2rat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          g2rat["@_value"] = formitem.rating["@_unadjusted"];
        }
      } else if (formitem.group == 3) {
        g3t++;
        if (finish_position == 1) g31++;
        if (finish_position == 2) g32++;
        if (finish_position == 3) g33++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > g3rat["@_value"]
        ) {
          g3rat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          g3rat["@_value"] = formitem.rating["@_unadjusted"];
        }
      } else if (formitem.group == 4 || formitem.group == "LR") {
        glt++;
        if (finish_position == 1) gl1++;
        if (finish_position == 2) gl2++;
        if (finish_position == 3) gl3++;
        if (
          formitem.rating &&
          formitem.rating["@_unadjusted"] &&
          formitem.rating["@_unadjusted"] > glrat["@_value"]
        ) {
          glrat["@_date"] = moment(formitem.meeting_date).format("DD/MM/YYYY");
          glrat["@_value"] = formitem.rating["@_unadjusted"];
        }
      }
    }
    statistics[horseId] = {};
    statistics[horseId].rating = 0;
    statistics[horseId].statistics = {};
    statistics[horseId].statistics.statistic = [
      {
        "@_type": "all",
        "@_total": st,
        "@_firsts": s1,
        "@_seconds": s2,
        "@_thirds": s3,
      },
      {
        "@_type": "preparation",
        "@_total": pt,
        "@_firsts": p1,
        "@_seconds": p2,
        "@_thirds": p3,
      },
      {
        "@_type": "distance",
        "@_total": dt,
        "@_firsts": d1,
        "@_seconds": d2,
        "@_thirds": d3,
      },
      {
        "@_type": "track",
        "@_total": tt,
        "@_firsts": t1,
        "@_seconds": t2,
        "@_thirds": t3,
      },
      {
        "@_type": "firm",
        "@_total": ft,
        "@_firsts": f1,
        "@_seconds": f2,
        "@_thirds": f3,
      },
      {
        "@_type": "good",
        "@_total": gt,
        "@_firsts": g1,
        "@_seconds": g2,
        "@_thirds": g3,
      },
      {
        "@_type": "soft",
        "@_total": slt,
        "@_firsts": sl1,
        "@_seconds": sl2,
        "@_thirds": sl3,
      },
      {
        "@_type": "heavy",
        "@_total": ht,
        "@_firsts": h1,
        "@_seconds": h2,
        "@_thirds": h3,
      },
      {
        "@_type": "synthetic",
        "@_total": yt,
        "@_firsts": y1,
        "@_seconds": y2,
        "@_thirds": y3,
      },
      {
        "@_type": "first_up",
        "@_total": fut,
        "@_firsts": fu1,
        "@_seconds": fu2,
        "@_thirds": fu3,
      },
      {
        "@_type": "second_up",
        "@_total": sut,
        "@_firsts": su1,
        "@_seconds": su2,
        "@_thirds": su3,
      },
      {
        "@_type": "third_up",
        "@_total": tut,
        "@_firsts": tu1,
        "@_seconds": tu2,
        "@_thirds": tu3,
      },
      {
        "@_type": "fourth_up",
        "@_total": out,
        "@_firsts": ou1,
        "@_seconds": ou2,
        "@_thirds": ou3,
      },
      {
        "@_type": "jumps",
        "@_total": jt,
        "@_firsts": j1,
        "@_seconds": j2,
        "@_thirds": j3,
      },
      {
        "@_type": "distance_track",
        "@_total": dtt,
        "@_firsts": dt1,
        "@_seconds": dt2,
        "@_thirds": dt3,
      },
      {
        "@_type": "wet",
        "@_total": wt,
        "@_firsts": w1,
        "@_seconds": w2,
        "@_thirds": w3,
      },
      {
        "@_type": "night",
        "@_total": nt,
        "@_firsts": n1,
        "@_seconds": n2,
        "@_thirds": n3,
      },
      {
        "@_type": "current_jockey",
        "@_total": cjt,
        "@_firsts": cj1,
        "@_seconds": cj2,
        "@_thirds": cj3,
      },
      {
        "@_type": "current_trainer",
        "@_total": ctt,
        "@_firsts": ct1,
        "@_seconds": ct2,
        "@_thirds": ct3,
      },
      {
        "@_type": "group_1",
        "@_total": g1t,
        "@_firsts": g11,
        "@_seconds": g12,
        "@_thirds": g13,
      },
      {
        "@_type": "group_2",
        "@_total": g2t,
        "@_firsts": g21,
        "@_seconds": g22,
        "@_thirds": g23,
      },
      {
        "@_type": "group_3",
        "@_total": g3t,
        "@_firsts": g31,
        "@_seconds": g32,
        "@_thirds": g33,
      },
      {
        "@_type": "listed",
        "@_total": glt,
        "@_firsts": gl1,
        "@_seconds": gl2,
        "@_thirds": gl3,
      },
      {
        "@_type": "weight_for_age",
        "@_total": wfat,
        "@_firsts": wfa1,
        "@_seconds": wfa2,
        "@_thirds": wfa3,
      },
    ];
    statistics[horseId].ratings = {};
    statistics[horseId].ratings.rating = [
      srat,
      prat,
      drat,
      trat,
      frat,
      grat,
      slrat,
      hrat,
      yrat,
      furat,
      surat,
      turat,
      ourat,
      jrat,
      dtrat,
      wrat,
      nrat,
      cjrat,
      ctrat,
      g1rat,
      g2rat,
      g3rat,
      glrat,
      wfarat,
    ];
  }
  return statistics;
};

/**
 * Asynchronously retrieves and aggregates racing statistics for horses and their progeny based on wet and dry track conditions, updating a provided statistics object.
 *
 * @param {Object} conObj - An object representing the database connection.
 * @param {Object} event - An object containing event details, including a list of horse IDs (`entrants`).
 * @param {Object} statsObject - An object to store the aggregated statistics for each horse.
 * @returns {Promise<Object>} - Returns an updated `statsObject` containing aggregated statistics for each horse, categorized into wet and dry track conditions.
 */
const statsProgeny = async (conObj, event, statsObject) => {
  const horseIdList = event["entrants"];
  try {
    var results = await centaur.horses
      .find({ HRN_HORSE_ID: { $in: horseIdList } })
      .select({
        HRN_HORSE_NAME: 1,
        HRN_HORSE_ID: 1,
        HOR_SIRE_ID: 1,
        HOR_DAM_ID: 1,
      });

    var sireList = results.map((horse) => horse.HOR_SIRE_ID);

    var sire_child_results = await centaur.horses
      .find({ HOR_SIRE_ID: { $in: sireList } })
      .select({
        HRN_HORSE_NAME: 1,
        HRN_HORSE_ID: 1,
        HOR_START_TOTAL: 1,
        HOR_WET_START_TOTAL: 1,
        HOR_WET_1ST_TOTAL: 1,
        HOR_WET_2ND_TOTAL: 1,
        HOR_WET_3RD_TOTAL: 1,
        HOR_DRY_START_TOTAL: 1,
        HOR_DRY_1ST_TOTAL: 1,
        HOR_DRY_2ND_TOTAL: 1,
        HOR_DRY_3RD_TOTAL: 1,
        HOR_SIRE_ID: 1,
        HOR_DAM_ID: 1,
      });

    for (let index = 0; index < results.length; index++) {
      let horseId = results[index].HRN_HORSE_ID;
      let [wstart, w1st, w2nd, w3rd] = [0, 0, 0, 0];
      let [dstart, d1st, d2nd, d3rd] = [0, 0, 0, 0];

      for (let intIndex = 0; intIndex < sire_child_results.length; intIndex++) {
        if (
          results[index].HOR_SIRE_ID == sire_child_results[intIndex].HOR_SIRE_ID
        ) {
          wstart = wstart + sire_child_results[intIndex].HOR_WET_START_TOTAL;
          w1st = w1st + sire_child_results[intIndex].HOR_WET_1ST_TOTAL;
          w2nd = w2nd + sire_child_results[intIndex].HOR_WET_2ND_TOTAL;
          w3rd = w3rd + sire_child_results[intIndex].HOR_WET_3RD_TOTAL;
          dstart = dstart + sire_child_results[intIndex].HOR_DRY_START_TOTAL;
          d1st = d1st + sire_child_results[intIndex].HOR_DRY_1ST_TOTAL;
          d2nd = d2nd + sire_child_results[intIndex].HOR_DRY_2ND_TOTAL;
          d3rd = d3rd + sire_child_results[intIndex].HOR_DRY_3RD_TOTAL;
        }
      }

      statsObject[horseId].statistics.statistic.push({
        "@_type": "sire_progeny_wet",
        "@_total": wstart,
        "@_firsts": w1st,
        "@_seconds": w2nd,
        "@_thirds": w3rd,
      });

      statsObject[horseId].statistics.statistic.push({
        "@_type": "sire_progeny_dry",
        "@_total": dstart,
        "@_firsts": d1st,
        "@_seconds": d2nd,
        "@_thirds": d3rd,
      });
    }
  } catch (err) {
    console.log(statsObject);
    console.log(err);
  }

  return statsObject;
};

/**
 * Asynchronously fetches and calculates statistics related to the progeny of the sires of dams (female parents) of a list of horses.
 * Aggregates performance data in wet and dry conditions for these progeny and updates a statistics object with this data.
 *
 * @param {Object} conObj - An object representing the database connection.
 * @param {Object} event - An object containing an array of horse IDs under the key `entrants`.
 * @param {Object} statsObject - An object where statistics will be stored and updated.
 * @returns {Promise<Object>} - Returns an updated `statsObject` containing aggregated statistics for each horse based on the performance of their dam's sire's progeny in wet and dry conditions.
 */
const statsSireOfDamProgeny = async (conObj, event, statsObject) => {
  const horseIdList = event["entrants"];

  const results = await centaur.horses
    .find({ HRN_HORSE_ID: { $in: horseIdList } })
    .select({
      HRN_HORSE_NAME: 1,
      HRN_HORSE_ID: 1,
      HOR_SIRE_ID: 1,
      HOR_DAM_ID: 1,
    });

  const damIdList = results.map((horse) => horse.HOR_DAM_ID);

  var SireOfDamList = await centaur.horses
    .find({ HRN_HORSE_ID: { $in: damIdList } })
    .select({
      HOR_SIRE_ID: 1,
      HRN_HORSE_ID: 1,
    });

  const sireOfDamIdList = SireOfDamList.map((dam) => dam.HOR_SIRE_ID);

  const SireOfDamChildResults = await centaur.horses
    .find({ HOR_SIRE_ID: { $in: sireOfDamIdList } })
    .select({
      HRN_HORSE_NAME: 1,
      HRN_HORSE_ID: 1,
      HOR_START_TOTAL: 1,
      HOR_SIRE_ID: 1,
      HOR_WET_START_TOTAL: 1,
      HOR_WET_1ST_TOTAL: 1,
      HOR_WET_2ND_TOTAL: 1,
      HOR_WET_3RD_TOTAL: 1,
      HOR_DRY_START_TOTAL: 1,
      HOR_DRY_1ST_TOTAL: 1,
      HOR_DRY_2ND_TOTAL: 1,
      HOR_DRY_3RD_TOTAL: 1,
    });

  for (let index = 0; index < results.length; index++) {
    let horseId = results[index].HRN_HORSE_ID;
    let damId = results[index].HOR_DAM_ID;
    let sireOfDam = SireOfDamList.find((dam) => dam.HRN_HORSE_ID === damId);
    let sireOfDamId = sireOfDam ? sireOfDam.HOR_SIRE_ID : null;

    let [wstart, w1st, w2nd, w3rd] = [0, 0, 0, 0];
    let [dstart, d1st, d2nd, d3rd] = [0, 0, 0, 0];

    for (
      let intIndex = 0;
      intIndex < SireOfDamChildResults.length;
      intIndex++
    ) {
      if (
        sireOfDamId &&
        sireOfDamId === SireOfDamChildResults[intIndex].HOR_SIRE_ID
      ) {
        wstart = wstart + SireOfDamChildResults[intIndex].HOR_WET_START_TOTAL;
        w1st = w1st + SireOfDamChildResults[intIndex].HOR_WET_1ST_TOTAL;
        w2nd = w2nd + SireOfDamChildResults[intIndex].HOR_WET_2ND_TOTAL;
        w3rd = w3rd + SireOfDamChildResults[intIndex].HOR_WET_3RD_TOTAL;
        dstart = dstart + SireOfDamChildResults[intIndex].HOR_DRY_START_TOTAL;
        d1st = d1st + SireOfDamChildResults[intIndex].HOR_DRY_1ST_TOTAL;
        d2nd = d2nd + SireOfDamChildResults[intIndex].HOR_DRY_2ND_TOTAL;
        d3rd = d3rd + SireOfDamChildResults[intIndex].HOR_DRY_3RD_TOTAL;
      }
    }

    statsObject[horseId].statistics.statistic.push({
      "@_type": "dam_sire_progeny_wet",
      "@_total": wstart,
      "@_firsts": w1st,
      "@_seconds": w2nd,
      "@_thirds": w3rd,
    });

    statsObject[horseId].statistics.statistic.push({
      "@_type": "dam_sire_progeny_dry",
      "@_total": dstart,
      "@_firsts": d1st,
      "@_seconds": d2nd,
      "@_thirds": d3rd,
    });
  }

  return statsObject;
};

const statsGetMissingRatings = async (conObj, event, statsObject, locked = false) => {
  if (!locked){
    console.log("entering missing ratings function");
    let horse_prices = [];
    for (const horse of event["entrants"]) {
      let existingRating = event.pair_rating[horse].rating;
      statsObject[horse].tip = event.pair_rating[horse].tip;
      if (existingRating == 0) {
        console.log(`no rating found for horse ${horse}`);
        let classBaseRating = await helper.getBaseRatingByClass(event.class_id);
        console.log(`class base raing ${classBaseRating}`);
        let horseBreedStats = statsObject[horse].statistics.statistic;
        let sirestats = {
          total: 0,
          win: 0,
          place: 0,
        };
        let damsirestats = {
          total: 0,
          win: 0,
          place: 0,
        };
        for (const stat of horseBreedStats) {
          if (
            stat["@_type"] == "sire_progeny_wet" ||
            stat["@_type"] == "sire_progeny_dry"
          ) {
            sirestats.total = sirestats.total + stat["@_total"];
            sirestats.win = sirestats.win + stat["@_firsts"];
            sirestats.place =
              sirestats.place +
              stat["@_firsts"] +
              stat["@_seconds"] +
              stat["@_thirds"];
          }
          if (
            stat["@_type"] == "dam_sire_progeny_wet" ||
            stat["@_type"] == "dam_sire_progeny_dry"
          ) {
            damsirestats.total = damsirestats.total + stat["@_total"];
            damsirestats.win = damsirestats.win + stat["@_firsts"];
            damsirestats.place =
              damsirestats.place +
              stat["@_firsts"] +
              stat["@_seconds"] +
              stat["@_thirds"];
          }
        }
        let sireadjust = 0;
        if (sirestats.total > 50) {
          sireadjust =
            ((sirestats.win / sirestats.total) * 100 -
              12.5 +
              ((sirestats.place / sirestats.total) * 100 - 37.5) * 0.5) *
            0.3;
          if (sireadjust < -6) {
            sireadjust = -6;
          } else if (sireadjust > 6) {
            sireadjust = 6;
          }
        }
        let damsireadjust = 0;
        if (damsirestats.total > 50) {
          damsireadjust =
            ((damsirestats.win / damsirestats.total) * 100 -
              12.5 +
              ((damsirestats.place / damsirestats.total) * 100 - 37.5) * 0.5) *
            0.1;
          if (damsireadjust < -2) {
            damsireadjust = -2;
          } else if (damsireadjust > 2) {
            damsireadjust = 2;
          }
        }
        let trainerId = event.pair_trainer[horse];
        let trainerAdjust = 0;
        if (trainerId && event.meeting_location){
          const trainer = await centaur.trainers.findOne({TRN_TRAINER_ID:trainerId}).select("TRN_RATINGS").lean()
          if (trainer.TRN_RATINGS && trainer.TRN_RATINGS.startPriceMeans[event.meeting_location] && trainer.TRN_RATINGS.startPriceStats[event.meeting_location]){
            trainerAdjust = helper.getEntityRatingAdj(trainer.TRN_RATINGS.startPriceMeans[event.meeting_location],trainer.TRN_RATINGS.startPriceStats[event.meeting_location],12,8);
          }
        }

        let jockeyId = event.pair_jockey[horse];
        console.log(jockeyId)
        let jockeyAdjust = 0;
        if (jockeyId && event.meeting_location){
          const jockey = await centaur.jockeys.findOne({JOC_JOCKEY_ID:jockeyId}).select("JOC_RATINGS").lean()
          if (jockey && 
            jockey.JOC_RATINGS && 
            jockey.JOC_RATINGS.startPriceMeans[event.meeting_location] && 
            jockey.JOC_RATINGS.startPriceStats[event.meeting_location]){
            jockeyAdjust = helper.getEntityRatingAdj(
              jockey.JOC_RATINGS.startPriceMeans[event.meeting_location],
              jockey.JOC_RATINGS.startPriceStats[event.meeting_location],
              12,
              8
            );
          }
        }
        
        
        let newMaster =
          classBaseRating +
          sireadjust +
          damsireadjust +
          trainerAdjust +
          jockeyAdjust;

        console.log(
          `for horse ${horse}, sire:${sireadjust} damsire:${damsireadjust} trainer:${trainerAdjust} jockey:${jockeyAdjust}, generated a MR of ${newMaster}`
        );
        // console.log(` for horse ${horse}`)
        let rating = await helper.generateRating(
          newMaster,
          event.pair_rating[horse].age,
          event.pair_rating[horse].sex,
          event.distance,
          event.pair_rating[horse].weight_carried,
          event.meetingDate
        );
        if (rating < 10) {
          rating = 10;
        }
        console.log(`for horse ${horse}, the rating is ${rating}`);
        statsObject[horse].rating = rating;
        statsObject[horse].FF5_dry = rating;
        statsObject[horse].FF5_wet = rating;
      } else {
        statsObject[horse].rating = existingRating;
        statsObject[horse].FF5_dry = existingRating;
        statsObject[horse].FF5_wet = existingRating;
      }
      if (!event.pair_rating[horse].scratched) {
        horse_prices.push({id:horse,rating:statsObject[horse].rating,betting:0,tip:statsObject[horse].tip});
      } else {
        statsObject[horse].betting = "";
        statsObject[horse].tip = "";
      }
    }
    horse_prices = helper.enterMarket(horse_prices,event.confidence);
    for (const horse of horse_prices){
      statsObject[horse.id].betting = horse.betting;
      statsObject[horse.id].tip = horse.tip;
    }
    console.log(statsObject)
  }
  //Calculate FAST 100s
  var fast100s = [];
  for (const horse of event["entrants"]) {
    fast100s.push({ horse: horse, rating: statsObject[horse].rating });
  }
  fast100s.sort((a, b) => b.rating - a.rating);
  var topRatingAdjust = 100 - fast100s[0].rating * 1.5;

  // console.log(topRatingCalc)
  for (const rating of fast100s) {
    if (rating.rating > 0) {
      statsObject[rating.horse].FF_Dry_Rating_100 = Math.round(
        rating.rating * 1.5 + topRatingAdjust
      );
      statsObject[rating.horse].FF_Wet_Rating_100 = Math.round(
        rating.rating * 1.5 + topRatingAdjust
      );
    }
  }
  // console.log(statsObject)
  return statsObject;
};

const updateMeeting = async (meetingId, raceId, stats) => {
  var resp = await centaur.temp_meetings.findOne({ _id: meetingId }).lean();
  if (!resp) {
    console.log("Meeting Not found");
    process.exit(1);
  }

  var horses = [];
  var raceIndex = -1;
  let raceStageMkt = "";
  let raceLocked = false;

  for (const race of resp.processedMeetingData.meeting.races.race) {
    raceIndex++;
    if (race["@_number"] == raceId) {
      raceStageMkt = ["Acceptances","FinalFields","Final Fields"].includes(race.race_stage) ? true : false;
      raceLocked = race.locked === true;
      horses = race.horses.horse;
      break;
    }
  }
  // Fetch the processed meeting data
  const processed_meeting = await centaur.processed_meetings.findOne({ _id: meetingId }).lean();
  
  const inputRaceIndex = resp.inputMeetingData.races.race.findIndex((element) => element["@_id"] === resp.processedMeetingData.meeting.races.race[raceIndex]["@_id"]);
  var finalrace = false;
  if (
    resp.meetingErrorCount == 0 &&
    raceIndex + 1 == resp.processedMeetingData.meeting.races.race.length
  )
    finalrace = true;

  // Loop through the horses in race and update each
  if (horses) {
    for (const horseIndex in horses) {
      let horseToUpdate =
        "processedMeetingData.meeting.races.race." +
        raceIndex +
        ".horses.horse." +
        horseIndex +
        ".";
      const inputHorseIndex = resp.inputMeetingData.races.race[inputRaceIndex].horses.horse.findIndex((element) => element["@_id"] === horses[horseIndex]["@_id"]);
      let horseInputToUpdate =
        "inputMeetingData.races.race." +
        inputRaceIndex +
        ".horses.horse." +
        inputHorseIndex +
        ".";

      let found = Object.entries(stats).find(
        (x) => x[0] == horses[horseIndex]["@_id"]
      );

      // Find the corresponding horse in the processed meeting
      let processedHorse;
      if (processed_meeting && processed_meeting.processedMeetingData?.meeting?.races?.race) {
        const processedRace = processed_meeting.processedMeetingData.meeting.races.race.find(
          r => r["@_number"] == resp.processedMeetingData.meeting.races.race[raceIndex]["@_number"]
        );
        if (processedRace && processedRace.horses?.horse) {
          processedHorse = processedRace.horses.horse.find(
            h => h["@_id"] == horses[horseIndex]["@_id"]
          );
        }
      }

      if (found) {
        let dataToUpdate = {};

        dataToUpdate[horseToUpdate + "statistics"] = found[1].statistics;

        // Handle 'rating' based on whether the race is locked
        if (raceLocked) {
          // If the race is locked, prevent 'rating' from being emptied
            // Use existing 'rating' from processed meeting
            dataToUpdate[horseToUpdate + "rating"] = processedHorse["rating"];
            dataToUpdate[horseToUpdate + "betting"] = processedHorse["betting"];
            dataToUpdate[horseToUpdate + "FF5_dry"] = processedHorse["FF5_dry"];
            dataToUpdate[horseToUpdate + "FF5_wet"] = processedHorse["FF5_wet"];
            dataToUpdate[horseToUpdate + "FF_Dry_Rating_100"] = processedHorse["FF_Dry_Rating_100"];
            dataToUpdate[horseToUpdate + "FF_Wet_Rating_100"] = processedHorse["FF_Wet_Rating_100"];
        } else {
          // If the race is not locked, use the new 'rating' value (even if it's empty)
          dataToUpdate[horseToUpdate + "rating"] = found[1]["rating"];
          dataToUpdate[horseToUpdate + "FF5_dry"] = found[1]["FF5_dry"];
          dataToUpdate[horseToUpdate + "FF5_wet"] = found[1]["FF5_wet"];
          dataToUpdate[horseToUpdate + "FF_Dry_Rating_100"] = found[1]["FF_Dry_Rating_100"];
          dataToUpdate[horseToUpdate + "FF_Wet_Rating_100"] = found[1]["FF_Wet_Rating_100"];
        }

        // Update other fields as usual
        dataToUpdate[horseToUpdate + "ratings.rating"] = found[1]["ratings"]["rating"];
       

        if (raceStageMkt && !raceLocked){
          dataToUpdate[horseToUpdate + "betting"] = found[1]["betting"];
          dataToUpdate[horseToUpdate + "tip"] =  found[1]["tip"];
          dataToUpdate[horseInputToUpdate + "tip"] =  found[1]["tip"];
        }

        let resp = await centaur.temp_meetings
          .updateOne(
            { _id: meetingId },
            {
              $set: dataToUpdate,
            }
          )
          .lean();
        if (resp) {
          // Horse stats updated successfully
        } else {
          console.log("Issue in updating Horse stats");
        }
      } else {
        console.log(
          "Stats Not found for horse: " +
            horses[horseIndex]["@_name"] +
            ", Id: " +
            horses[horseIndex]["@_id"]
        );
      }
    }
  } else {
    console.log(`Error identifying race ${raceId} in meeting ${meetingId}`);
    return false;
  }

  return finalrace;
};

module.exports = {
  getForms,
  getHorses,
  genBasicStatsFromForm,
  statsProgeny,
  statsSireOfDamProgeny,
  statsGetMissingRatings,
  updateMeeting,
};
