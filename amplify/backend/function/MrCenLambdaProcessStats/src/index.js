const stats = require("./library/stats")
const helper = require("./library/helper")
const AWSXRay = require("aws-xray-sdk-core")
const centaur = require('@mediality/centaur')

exports.handler = async (event) => {
    var segment = new AWSXRay.Segment('handler')
    var conObj = await helper.openDBConnection(process.env.env)
    console.log("Generating Stats for Meeting Id:" + event.meetingId)
    console.log("Generating Stats for Race Id:" + event.race)

    try {
        console.log(event)
        let horse_forms = await stats.getForms(conObj, event);
        let basicStatsObject = await stats.genBasicStatsFromForm(
            conObj, event, horse_forms);

        let advancedStatsObject = await stats.statsProgeny(
            conObj, event, basicStatsObject);

        let completeStatsObject = await stats.statsSireOfDamProgeny(
            conObj, event, advancedStatsObject);
        let completeStatsObjectWithRatings = await stats.statsGetMissingRatings(
                conObj, event, completeStatsObject, event.race.locked);
        
        var result = await stats.updateMeeting(event.meetingId, event.race, completeStatsObjectWithRatings)
        console.log(result)

    } catch (err) {
        const meet = await centaur.temp_meetings.findOne({ _id: event.meetingId }).lean()
        let errors = meet.processedMeetingData.meeting.errors
        let error = `statistics failed for race ${event.race} with error: ${err}`
        errors.push(error)
        var errorCount = meet.meetingErrorCount + 1
        let pathToUpdate = "processedMeetingData.meeting.errors"
        var dataToUpdate = {
            [pathToUpdate] : errors,
            meetingErrorCount : errorCount,
            validated: false
        }
        await centaur.temp_meetings.updateOne({ _id: event.meetingId }, {
            "$set": dataToUpdate
          }).lean()
          
        console.log(err)
        segment.addError(err)
    } finally {
        segment.close()
    }

    const response = {
        statusCode: 200,
        body: JSON.stringify('Meeting Updated :' + event.meetingId + ", Race Id:" + event.race),
    };

    await conObj.connection.close()
    return response;
};
