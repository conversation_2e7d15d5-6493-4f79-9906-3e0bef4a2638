const util = require('util')
const AWS = require('aws-sdk');
const moment = require('moment')




const getSecrets = async (secretName) => {
    const AWS = require('aws-sdk');
    var secretsmanager = new AWS.SecretsManager();
    var params = {
        SecretId: secretName,
    };
    const fetchSecretString = await secretsmanager.getSecretValue(params).promise();
    aws_secrets = JSON.parse(fetchSecretString.SecretString)
    return aws_secrets
}

const openDBConnection = async (env) => {
    const local = require("../local");
    const mongoose = require('mongoose');

    if (env == "local") {
        //Local environment

        var cert_path = './rds-cert/global-bundle.pem'
        console.log('Running Locally')
        DB_USERNAME = local.DB_USERNAME
        DB_PASSWORD = local.DB_PASSWORD
        DB_URL = local.DB_URL
        DB_NAME = local.DB_NAME
        CONNECTION_STRING = "mongodb://" + DB_USERNAME + ":" + DB_PASSWORD + "@" + DB_URL + "/" + DB_NAME
        CONNECTION_STRING = "mongodb://root:Test.123!@127.0.0.1:27000/dev-centaur?directConnection=true&ssl=true&retrywrites=false&tls=true"
        await mongoose.connect(CONNECTION_STRING, {
            connectTimeoutMS: 1000,
            tlsCAFile: cert_path,
            directConnection: true,
            ssl: true,
            sslValidate: false,
            maxPoolSize: 10

        }).then(
            () => {
                console.log('Connected to Local')
                response = "Connect to Local"
            },
            err => {
                console.log('Not connected:' + err)
                response = "Not Connected to Local"
            });

    } else {
        console.log('Running Online')
        var aws_secrets = await getSecrets(process.env.centaurSecrets)
        DB_USERNAME = aws_secrets.DB_USERNAME
        DB_PASSWORD = aws_secrets.DB_PASSWORD
        DB_URL = aws_secrets.DB_URL
        DB_NAME = aws_secrets.DB_NAME
        CONNECTION_STRING = "mongodb://" + DB_USERNAME + ":" + DB_PASSWORD + "@" + DB_URL + "/" + DB_NAME + "?tls=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
        await mongoose.connect(CONNECTION_STRING, {
            connectTimeoutMS: 1000,
            tlsCAFile: cert_path,
            directConnection: true,
            ssl: true,
            sslValidate: false,
            maxPoolSize: 10
        }).then(
            () => {
                console.log('Connected to Online')
                response = "Connect Online"
            },
            err => {
                console.log('Not connected:' + err)
                response = "Not Connected Online"
            });
    }
    return mongoose
}

const closeDBConnection = async (con) => {

    con.connection.close()
    console.log("Connection Closed")

}


module.exports = {
    getSecrets,
    openDBConnection,
    closeDBConnection
}
