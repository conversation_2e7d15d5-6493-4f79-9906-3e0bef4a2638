# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@aws-crypto/crc32@3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/crc32/-/crc32-3.0.0.tgz#07300eca214409c33e3ff769cd5697b57fdd38fa"
  integrity sha512-IzSgsrxUcsrejQbPVilIKy16kAT52EwB6zSaI+M3xxIhKh5+aldEyvI+z6erM7TCLB2BJsFrtHjp6/4/sr+3dA==
  dependencies:
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^1.11.1"

"@aws-crypto/crc32c@3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/crc32c/-/crc32c-3.0.0.tgz#016c92da559ef638a84a245eecb75c3e97cb664f"
  integrity sha512-ENNPPManmnVJ4BTXlOjAgD7URidbAznURqD0KvfREyc4o20DPYdEldU1f5cQ7Jbj0CJJSPaMIk/9ZshdB3210w==
  dependencies:
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^1.11.1"

"@aws-crypto/ie11-detection@^3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/ie11-detection/-/ie11-detection-3.0.0.tgz#640ae66b4ec3395cee6a8e94ebcd9f80c24cd688"
  integrity sha512-341lBBkiY1DfDNKai/wXM3aujNBkXR7tq1URPQDL9wi3AUbI80NR74uF1TXHMm7po1AcnFk8iu2S2IeU/+/A+Q==
  dependencies:
    tslib "^1.11.1"

"@aws-crypto/sha1-browser@3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/sha1-browser/-/sha1-browser-3.0.0.tgz#f9083c00782b24714f528b1a1fef2174002266a3"
  integrity sha512-NJth5c997GLHs6nOYTzFKTbYdMNA6/1XlKVgnZoaZcQ7z7UJlOgj2JdbHE8tiYLS3fzXNCguct77SPGat2raSw==
  dependencies:
    "@aws-crypto/ie11-detection" "^3.0.0"
    "@aws-crypto/supports-web-crypto" "^3.0.0"
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-locate-window" "^3.0.0"
    "@aws-sdk/util-utf8-browser" "^3.0.0"
    tslib "^1.11.1"

"@aws-crypto/sha256-browser@3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/sha256-browser/-/sha256-browser-3.0.0.tgz#05f160138ab893f1c6ba5be57cfd108f05827766"
  integrity sha512-8VLmW2B+gjFbU5uMeqtQM6Nj0/F1bro80xQXCW6CQBWgosFWXTx77aeOF5CAIAmbOK64SdMBJdNr6J41yP5mvQ==
  dependencies:
    "@aws-crypto/ie11-detection" "^3.0.0"
    "@aws-crypto/sha256-js" "^3.0.0"
    "@aws-crypto/supports-web-crypto" "^3.0.0"
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-locate-window" "^3.0.0"
    "@aws-sdk/util-utf8-browser" "^3.0.0"
    tslib "^1.11.1"

"@aws-crypto/sha256-js@3.0.0", "@aws-crypto/sha256-js@^3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/sha256-js/-/sha256-js-3.0.0.tgz#f06b84d550d25521e60d2a0e2a90139341e007c2"
  integrity sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==
  dependencies:
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^1.11.1"

"@aws-crypto/supports-web-crypto@^3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/supports-web-crypto/-/supports-web-crypto-3.0.0.tgz#5d1bf825afa8072af2717c3e455f35cda0103ec2"
  integrity sha512-06hBdMwUAb2WFTuGG73LSC0wfPu93xWwo5vL2et9eymgmu3Id5vFAHBbajVWiGhPO37qcsdCap/FqXvJGJWPIg==
  dependencies:
    tslib "^1.11.1"

"@aws-crypto/util@^3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/util/-/util-3.0.0.tgz#1c7ca90c29293f0883468ad48117937f0fe5bfb0"
  integrity sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==
  dependencies:
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-utf8-browser" "^3.0.0"
    tslib "^1.11.1"

"@aws-lambda-powertools/commons@^1.11.1":
  version "1.11.1"
  resolved "https://registry.yarnpkg.com/@aws-lambda-powertools/commons/-/commons-1.11.1.tgz#4c28a5457b8fb642fd0b431569af3406e35fb589"
  integrity sha512-REOINoiK7axtBKS9buYCkuhBNcmf6FKN1vboQvfIWabzyBrG+7jrUDGzx8UmurXI/F6GrCA7mLCIeYQfy2baUg==

"@aws-lambda-powertools/logger@^1.5.1":
  version "1.11.1"
  resolved "https://registry.yarnpkg.com/@aws-lambda-powertools/logger/-/logger-1.11.1.tgz#4c52ad7886462d7901008fd82f8c8c466030ffc8"
  integrity sha512-U1bzyVSwYNBtS9BuatILzE0jHgAg3Hm9MleGlrfFAnfLsVzVIMcUw0J5o2gBKzgqIcEQlSxboXhB4+5nYhgSVA==
  dependencies:
    "@aws-lambda-powertools/commons" "^1.11.1"
    lodash.merge "^4.6.2"

"@aws-lambda-powertools/tracer@^1.5.1":
  version "1.11.1"
  resolved "https://registry.yarnpkg.com/@aws-lambda-powertools/tracer/-/tracer-1.11.1.tgz#8751aba2d02d08c7f251b186c37fd3f4e8938f70"
  integrity sha512-JqAreKVKAZyKeljDk5xAyf0L/9r2nceiWlh+EG5VOS+jUMR7olC7Xq0f8uFBTxp0XKiWUA36oSh1uxLwgW03/A==
  dependencies:
    "@aws-lambda-powertools/commons" "^1.11.1"
    aws-xray-sdk-core "^3.4.1"

"@aws-sdk/chunked-blob-reader-native@3.310.0":
  version "3.310.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/chunked-blob-reader-native/-/chunked-blob-reader-native-3.310.0.tgz#98d43a6213557835b3bbb0cd2ee0a4e2088e916a"
  integrity sha512-RuhyUY9hCd6KWA2DMF/U6rilYLLRYrDY6e0lq3Of1yzSRFxi4bk9ZMCF0mxf/9ppsB5eudUjrOypYgm6Axt3zw==
  dependencies:
    "@aws-sdk/util-base64" "3.310.0"
    tslib "^2.5.0"

"@aws-sdk/chunked-blob-reader@3.310.0":
  version "3.310.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/chunked-blob-reader/-/chunked-blob-reader-3.310.0.tgz#2ada1b024a2745c2fe7e869606fab781325f981e"
  integrity sha512-CrJS3exo4mWaLnWxfCH+w88Ou0IcAZSIkk4QbmxiHl/5Dq705OLoxf4385MVyExpqpeVJYOYQ2WaD8i/pQZ2fg==
  dependencies:
    tslib "^2.5.0"

"@aws-sdk/client-api-gateway@^3.54.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-api-gateway/-/client-api-gateway-3.369.0.tgz#021a47e54ced995a6f2eef04454d953662f2dc7b"
  integrity sha512-yqVMcInqgpcTjmJjTUetUD5UQtDeyb8BLPgM7nxJIKVCxKXsJcb6Y7Y7jkkGB9a868h6DrsmfAMa1I/88Rgu2w==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/client-sts" "3.369.0"
    "@aws-sdk/credential-provider-node" "3.369.0"
    "@aws-sdk/middleware-host-header" "3.369.0"
    "@aws-sdk/middleware-logger" "3.369.0"
    "@aws-sdk/middleware-recursion-detection" "3.369.0"
    "@aws-sdk/middleware-sdk-api-gateway" "3.369.0"
    "@aws-sdk/middleware-signing" "3.369.0"
    "@aws-sdk/middleware-user-agent" "3.369.0"
    "@aws-sdk/types" "3.369.0"
    "@aws-sdk/util-endpoints" "3.369.0"
    "@aws-sdk/util-user-agent-browser" "3.369.0"
    "@aws-sdk/util-user-agent-node" "3.369.0"
    "@smithy/config-resolver" "^1.0.1"
    "@smithy/fetch-http-handler" "^1.0.1"
    "@smithy/hash-node" "^1.0.1"
    "@smithy/invalid-dependency" "^1.0.1"
    "@smithy/middleware-content-length" "^1.0.1"
    "@smithy/middleware-endpoint" "^1.0.1"
    "@smithy/middleware-retry" "^1.0.2"
    "@smithy/middleware-serde" "^1.0.1"
    "@smithy/middleware-stack" "^1.0.1"
    "@smithy/node-config-provider" "^1.0.1"
    "@smithy/node-http-handler" "^1.0.2"
    "@smithy/protocol-http" "^1.0.1"
    "@smithy/smithy-client" "^1.0.3"
    "@smithy/types" "^1.1.0"
    "@smithy/url-parser" "^1.0.1"
    "@smithy/util-base64" "^1.0.1"
    "@smithy/util-body-length-browser" "^1.0.1"
    "@smithy/util-body-length-node" "^1.0.1"
    "@smithy/util-defaults-mode-browser" "^1.0.1"
    "@smithy/util-defaults-mode-node" "^1.0.1"
    "@smithy/util-retry" "^1.0.2"
    "@smithy/util-stream" "^1.0.1"
    "@smithy/util-utf8" "^1.0.1"
    tslib "^2.5.0"

"@aws-sdk/client-cognito-identity@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-cognito-identity/-/client-cognito-identity-3.369.0.tgz#0444f5e84f90acab4bd59d6b60a65bcc74f60a24"
  integrity sha512-YZSjxtWJ70Xj4G230iDGLXJHF4asy1FrTnTkNfyMA3uHmhgL3kUI9yk9E93FRy9XWboI8a39WC0vEgr6zvuBFQ==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/client-sts" "3.369.0"
    "@aws-sdk/credential-provider-node" "3.369.0"
    "@aws-sdk/middleware-host-header" "3.369.0"
    "@aws-sdk/middleware-logger" "3.369.0"
    "@aws-sdk/middleware-recursion-detection" "3.369.0"
    "@aws-sdk/middleware-signing" "3.369.0"
    "@aws-sdk/middleware-user-agent" "3.369.0"
    "@aws-sdk/types" "3.369.0"
    "@aws-sdk/util-endpoints" "3.369.0"
    "@aws-sdk/util-user-agent-browser" "3.369.0"
    "@aws-sdk/util-user-agent-node" "3.369.0"
    "@smithy/config-resolver" "^1.0.1"
    "@smithy/fetch-http-handler" "^1.0.1"
    "@smithy/hash-node" "^1.0.1"
    "@smithy/invalid-dependency" "^1.0.1"
    "@smithy/middleware-content-length" "^1.0.1"
    "@smithy/middleware-endpoint" "^1.0.1"
    "@smithy/middleware-retry" "^1.0.2"
    "@smithy/middleware-serde" "^1.0.1"
    "@smithy/middleware-stack" "^1.0.1"
    "@smithy/node-config-provider" "^1.0.1"
    "@smithy/node-http-handler" "^1.0.2"
    "@smithy/protocol-http" "^1.0.1"
    "@smithy/smithy-client" "^1.0.3"
    "@smithy/types" "^1.1.0"
    "@smithy/url-parser" "^1.0.1"
    "@smithy/util-base64" "^1.0.1"
    "@smithy/util-body-length-browser" "^1.0.1"
    "@smithy/util-body-length-node" "^1.0.1"
    "@smithy/util-defaults-mode-browser" "^1.0.1"
    "@smithy/util-defaults-mode-node" "^1.0.1"
    "@smithy/util-retry" "^1.0.2"
    "@smithy/util-utf8" "^1.0.1"
    tslib "^2.5.0"

"@aws-sdk/client-s3@^3.282.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-s3/-/client-s3-3.369.0.tgz#78e3031092244ea54e57b9eb233b6add4c66bb1b"
  integrity sha512-nhLjpeCFt5KSypNP0B0VXJrhd5WCE4un4t6zHcb0rAIbmmRvILAby3e/3/3nmUTDp4MNriz5YW6dWI0sYtbJIA==
  dependencies:
    "@aws-crypto/sha1-browser" "3.0.0"
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/client-sts" "3.369.0"
    "@aws-sdk/credential-provider-node" "3.369.0"
    "@aws-sdk/hash-blob-browser" "3.369.0"
    "@aws-sdk/hash-stream-node" "3.369.0"
    "@aws-sdk/md5-js" "3.369.0"
    "@aws-sdk/middleware-bucket-endpoint" "3.369.0"
    "@aws-sdk/middleware-expect-continue" "3.369.0"
    "@aws-sdk/middleware-flexible-checksums" "3.369.0"
    "@aws-sdk/middleware-host-header" "3.369.0"
    "@aws-sdk/middleware-location-constraint" "3.369.0"
    "@aws-sdk/middleware-logger" "3.369.0"
    "@aws-sdk/middleware-recursion-detection" "3.369.0"
    "@aws-sdk/middleware-sdk-s3" "3.369.0"
    "@aws-sdk/middleware-signing" "3.369.0"
    "@aws-sdk/middleware-ssec" "3.369.0"
    "@aws-sdk/middleware-user-agent" "3.369.0"
    "@aws-sdk/signature-v4-multi-region" "3.369.0"
    "@aws-sdk/types" "3.369.0"
    "@aws-sdk/util-endpoints" "3.369.0"
    "@aws-sdk/util-user-agent-browser" "3.369.0"
    "@aws-sdk/util-user-agent-node" "3.369.0"
    "@aws-sdk/xml-builder" "3.310.0"
    "@smithy/config-resolver" "^1.0.1"
    "@smithy/eventstream-serde-browser" "^1.0.1"
    "@smithy/eventstream-serde-config-resolver" "^1.0.1"
    "@smithy/eventstream-serde-node" "^1.0.1"
    "@smithy/fetch-http-handler" "^1.0.1"
    "@smithy/hash-node" "^1.0.1"
    "@smithy/invalid-dependency" "^1.0.1"
    "@smithy/middleware-content-length" "^1.0.1"
    "@smithy/middleware-endpoint" "^1.0.1"
    "@smithy/middleware-retry" "^1.0.2"
    "@smithy/middleware-serde" "^1.0.1"
    "@smithy/middleware-stack" "^1.0.1"
    "@smithy/node-config-provider" "^1.0.1"
    "@smithy/node-http-handler" "^1.0.2"
    "@smithy/protocol-http" "^1.0.1"
    "@smithy/smithy-client" "^1.0.3"
    "@smithy/types" "^1.1.0"
    "@smithy/url-parser" "^1.0.1"
    "@smithy/util-base64" "^1.0.1"
    "@smithy/util-body-length-browser" "^1.0.1"
    "@smithy/util-body-length-node" "^1.0.1"
    "@smithy/util-defaults-mode-browser" "^1.0.1"
    "@smithy/util-defaults-mode-node" "^1.0.1"
    "@smithy/util-retry" "^1.0.2"
    "@smithy/util-stream" "^1.0.1"
    "@smithy/util-utf8" "^1.0.1"
    "@smithy/util-waiter" "^1.0.1"
    fast-xml-parser "4.2.5"
    tslib "^2.5.0"

"@aws-sdk/client-secrets-manager@^3.282.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-secrets-manager/-/client-secrets-manager-3.369.0.tgz#1a6aff7db5f649466fd001d7d2fc28d56135cfcf"
  integrity sha512-5cknNwc1fnpqIxb61sQWo8bUasfbM3pLHxAKpN+TZpZqEAnjf04GKKfXvBnx88ndPUf8wUK4I6FAhSeAI1pMCw==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/client-sts" "3.369.0"
    "@aws-sdk/credential-provider-node" "3.369.0"
    "@aws-sdk/middleware-host-header" "3.369.0"
    "@aws-sdk/middleware-logger" "3.369.0"
    "@aws-sdk/middleware-recursion-detection" "3.369.0"
    "@aws-sdk/middleware-signing" "3.369.0"
    "@aws-sdk/middleware-user-agent" "3.369.0"
    "@aws-sdk/types" "3.369.0"
    "@aws-sdk/util-endpoints" "3.369.0"
    "@aws-sdk/util-user-agent-browser" "3.369.0"
    "@aws-sdk/util-user-agent-node" "3.369.0"
    "@smithy/config-resolver" "^1.0.1"
    "@smithy/fetch-http-handler" "^1.0.1"
    "@smithy/hash-node" "^1.0.1"
    "@smithy/invalid-dependency" "^1.0.1"
    "@smithy/middleware-content-length" "^1.0.1"
    "@smithy/middleware-endpoint" "^1.0.1"
    "@smithy/middleware-retry" "^1.0.2"
    "@smithy/middleware-serde" "^1.0.1"
    "@smithy/middleware-stack" "^1.0.1"
    "@smithy/node-config-provider" "^1.0.1"
    "@smithy/node-http-handler" "^1.0.2"
    "@smithy/protocol-http" "^1.0.1"
    "@smithy/smithy-client" "^1.0.3"
    "@smithy/types" "^1.1.0"
    "@smithy/url-parser" "^1.0.1"
    "@smithy/util-base64" "^1.0.1"
    "@smithy/util-body-length-browser" "^1.0.1"
    "@smithy/util-body-length-node" "^1.0.1"
    "@smithy/util-defaults-mode-browser" "^1.0.1"
    "@smithy/util-defaults-mode-node" "^1.0.1"
    "@smithy/util-retry" "^1.0.2"
    "@smithy/util-utf8" "^1.0.1"
    tslib "^2.5.0"
    uuid "^8.3.2"

"@aws-sdk/client-sso-oidc@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-sso-oidc/-/client-sso-oidc-3.369.0.tgz#e2a12ce8904ba9b0893073fa6a97d5b1e06a7920"
  integrity sha512-NOnsRrkHMss9pE68uTPMEt1KoW6eWt4ZCesJayCOiIgmIA/AhXHz06IBCYJ9eu9Xbu/55FDr4X3VCtUf7Rfh6g==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/middleware-host-header" "3.369.0"
    "@aws-sdk/middleware-logger" "3.369.0"
    "@aws-sdk/middleware-recursion-detection" "3.369.0"
    "@aws-sdk/middleware-user-agent" "3.369.0"
    "@aws-sdk/types" "3.369.0"
    "@aws-sdk/util-endpoints" "3.369.0"
    "@aws-sdk/util-user-agent-browser" "3.369.0"
    "@aws-sdk/util-user-agent-node" "3.369.0"
    "@smithy/config-resolver" "^1.0.1"
    "@smithy/fetch-http-handler" "^1.0.1"
    "@smithy/hash-node" "^1.0.1"
    "@smithy/invalid-dependency" "^1.0.1"
    "@smithy/middleware-content-length" "^1.0.1"
    "@smithy/middleware-endpoint" "^1.0.1"
    "@smithy/middleware-retry" "^1.0.2"
    "@smithy/middleware-serde" "^1.0.1"
    "@smithy/middleware-stack" "^1.0.1"
    "@smithy/node-config-provider" "^1.0.1"
    "@smithy/node-http-handler" "^1.0.2"
    "@smithy/protocol-http" "^1.0.1"
    "@smithy/smithy-client" "^1.0.3"
    "@smithy/types" "^1.1.0"
    "@smithy/url-parser" "^1.0.1"
    "@smithy/util-base64" "^1.0.1"
    "@smithy/util-body-length-browser" "^1.0.1"
    "@smithy/util-body-length-node" "^1.0.1"
    "@smithy/util-defaults-mode-browser" "^1.0.1"
    "@smithy/util-defaults-mode-node" "^1.0.1"
    "@smithy/util-retry" "^1.0.2"
    "@smithy/util-utf8" "^1.0.1"
    tslib "^2.5.0"

"@aws-sdk/client-sso@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-sso/-/client-sso-3.369.0.tgz#eab8edb1470e4cced187671ca5c793bfa613fdb4"
  integrity sha512-SjJd9QGT9ccHOY64qnMfvVjrneBORIx/k8OdtL0nV2wemPqCM9uAm+TYZ01E91D/+lfXS+lLMGSidSA39PMIOA==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/middleware-host-header" "3.369.0"
    "@aws-sdk/middleware-logger" "3.369.0"
    "@aws-sdk/middleware-recursion-detection" "3.369.0"
    "@aws-sdk/middleware-user-agent" "3.369.0"
    "@aws-sdk/types" "3.369.0"
    "@aws-sdk/util-endpoints" "3.369.0"
    "@aws-sdk/util-user-agent-browser" "3.369.0"
    "@aws-sdk/util-user-agent-node" "3.369.0"
    "@smithy/config-resolver" "^1.0.1"
    "@smithy/fetch-http-handler" "^1.0.1"
    "@smithy/hash-node" "^1.0.1"
    "@smithy/invalid-dependency" "^1.0.1"
    "@smithy/middleware-content-length" "^1.0.1"
    "@smithy/middleware-endpoint" "^1.0.1"
    "@smithy/middleware-retry" "^1.0.2"
    "@smithy/middleware-serde" "^1.0.1"
    "@smithy/middleware-stack" "^1.0.1"
    "@smithy/node-config-provider" "^1.0.1"
    "@smithy/node-http-handler" "^1.0.2"
    "@smithy/protocol-http" "^1.0.1"
    "@smithy/smithy-client" "^1.0.3"
    "@smithy/types" "^1.1.0"
    "@smithy/url-parser" "^1.0.1"
    "@smithy/util-base64" "^1.0.1"
    "@smithy/util-body-length-browser" "^1.0.1"
    "@smithy/util-body-length-node" "^1.0.1"
    "@smithy/util-defaults-mode-browser" "^1.0.1"
    "@smithy/util-defaults-mode-node" "^1.0.1"
    "@smithy/util-retry" "^1.0.2"
    "@smithy/util-utf8" "^1.0.1"
    tslib "^2.5.0"

"@aws-sdk/client-sts@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-sts/-/client-sts-3.369.0.tgz#f635bf4ed2cc27e96f6615114134f0802f5827ed"
  integrity sha512-kyZl654U27gsQX9UjiiO4CX5M6kHwzDouwbhjc5HshQld/lUbJQ4uPpAwhlbZiqnzGeB639MdAGaSwrOOw2ixw==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/credential-provider-node" "3.369.0"
    "@aws-sdk/middleware-host-header" "3.369.0"
    "@aws-sdk/middleware-logger" "3.369.0"
    "@aws-sdk/middleware-recursion-detection" "3.369.0"
    "@aws-sdk/middleware-sdk-sts" "3.369.0"
    "@aws-sdk/middleware-signing" "3.369.0"
    "@aws-sdk/middleware-user-agent" "3.369.0"
    "@aws-sdk/types" "3.369.0"
    "@aws-sdk/util-endpoints" "3.369.0"
    "@aws-sdk/util-user-agent-browser" "3.369.0"
    "@aws-sdk/util-user-agent-node" "3.369.0"
    "@smithy/config-resolver" "^1.0.1"
    "@smithy/fetch-http-handler" "^1.0.1"
    "@smithy/hash-node" "^1.0.1"
    "@smithy/invalid-dependency" "^1.0.1"
    "@smithy/middleware-content-length" "^1.0.1"
    "@smithy/middleware-endpoint" "^1.0.1"
    "@smithy/middleware-retry" "^1.0.1"
    "@smithy/middleware-serde" "^1.0.1"
    "@smithy/middleware-stack" "^1.0.1"
    "@smithy/node-config-provider" "^1.0.1"
    "@smithy/node-http-handler" "^1.0.1"
    "@smithy/protocol-http" "^1.1.0"
    "@smithy/smithy-client" "^1.0.2"
    "@smithy/types" "^1.1.0"
    "@smithy/url-parser" "^1.0.1"
    "@smithy/util-base64" "^1.0.1"
    "@smithy/util-body-length-browser" "^1.0.1"
    "@smithy/util-body-length-node" "^1.0.1"
    "@smithy/util-defaults-mode-browser" "^1.0.1"
    "@smithy/util-defaults-mode-node" "^1.0.1"
    "@smithy/util-retry" "^1.0.1"
    "@smithy/util-utf8" "^1.0.1"
    fast-xml-parser "4.2.5"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-cognito-identity@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-cognito-identity/-/credential-provider-cognito-identity-3.369.0.tgz#8c8eba596f57d194f03e5a85e48bf58af0a5b984"
  integrity sha512-E69Ya4JnLO2ymtDZSGwbpXXMS4Pr3b3g+rZ3BduPc2fxRSLDfCxKE1GuO56u9pCbjZL6lJ+5FB8i7v0ptsVrOQ==
  dependencies:
    "@aws-sdk/client-cognito-identity" "3.369.0"
    "@aws-sdk/types" "3.369.0"
    "@smithy/property-provider" "^1.0.1"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-env@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-env/-/credential-provider-env-3.369.0.tgz#d4ae1df0f6feca14ed8c86372085f0bee266dc75"
  integrity sha512-EZUXGLjnun5t5/dVYJ9yyOwPAJktOdLEQSwtw7Q9XOxaNqVFFz9EU+TwYraV4WZ3CFRNn7GEIctVlXAHVFLm/w==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@smithy/property-provider" "^1.0.1"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-ini@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-ini/-/credential-provider-ini-3.369.0.tgz#c22fde2ac08fa6f6dca4ce02d3c23b26daa739a9"
  integrity sha512-12XXd4gnrn05adio/xPF8Nxl99L2FFzksbFILDIfSni7nLDX0m2XprnkswQiCKSbfDIQQsgnnh2F+HhorLuqfQ==
  dependencies:
    "@aws-sdk/credential-provider-env" "3.369.0"
    "@aws-sdk/credential-provider-process" "3.369.0"
    "@aws-sdk/credential-provider-sso" "3.369.0"
    "@aws-sdk/credential-provider-web-identity" "3.369.0"
    "@aws-sdk/types" "3.369.0"
    "@smithy/credential-provider-imds" "^1.0.1"
    "@smithy/property-provider" "^1.0.1"
    "@smithy/shared-ini-file-loader" "^1.0.1"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-node@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-node/-/credential-provider-node-3.369.0.tgz#690904830fa8037a425ac7f39b7af632099c2e93"
  integrity sha512-vxX4s33EpRDh7OhKBDVAPxdBxVHPOOj1r7nN6f0hZLw5WPeeffSjLqw+MnFj33gSO7Htnt+Q0cAJQzeY5G8q3A==
  dependencies:
    "@aws-sdk/credential-provider-env" "3.369.0"
    "@aws-sdk/credential-provider-ini" "3.369.0"
    "@aws-sdk/credential-provider-process" "3.369.0"
    "@aws-sdk/credential-provider-sso" "3.369.0"
    "@aws-sdk/credential-provider-web-identity" "3.369.0"
    "@aws-sdk/types" "3.369.0"
    "@smithy/credential-provider-imds" "^1.0.1"
    "@smithy/property-provider" "^1.0.1"
    "@smithy/shared-ini-file-loader" "^1.0.1"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-process@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-process/-/credential-provider-process-3.369.0.tgz#1517bd35212acca2888328e25862ee9fe963c309"
  integrity sha512-OyasKV3mZz6TRSxczRnyZoifrtYwqGBxtr75YP37cm/JkecDshHXRcE8Jt9LyBg/93oWfKou03WVQiY9UIDJGQ==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@smithy/property-provider" "^1.0.1"
    "@smithy/shared-ini-file-loader" "^1.0.1"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-sso@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-sso/-/credential-provider-sso-3.369.0.tgz#e89c311668774966a359e28f70914a0eeb25124f"
  integrity sha512-qXbEsmgFpGPbRVnwBYPxL53wQuue0+Z8tVu877itbrzpHm61AuQ04Hn8T1boKrr40excDuxiSrCX5oCKRG4srQ==
  dependencies:
    "@aws-sdk/client-sso" "3.369.0"
    "@aws-sdk/token-providers" "3.369.0"
    "@aws-sdk/types" "3.369.0"
    "@smithy/property-provider" "^1.0.1"
    "@smithy/shared-ini-file-loader" "^1.0.1"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-web-identity@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-web-identity/-/credential-provider-web-identity-3.369.0.tgz#d59881280c883efcdc8dd834a134d379e347218b"
  integrity sha512-oFGxC839pQTJ6djFEBuokSi3/jNjNMVgZSpg26Z23V/r3vKRSgXfVmeus1FLYIWg0jO7KFsMPo9eVJW6auzw6w==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@smithy/property-provider" "^1.0.1"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/credential-providers@^3.186.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-providers/-/credential-providers-3.369.0.tgz#808af378b190333f13b7cb7268da294b21631059"
  integrity sha512-c3H3iEiutebVvHQY7igvlAKup/P8dRdpf3QqJNOCga/w6tR+MMdjhJBanHDeJjmyREfBTPySkaNY2gsLODtmCg==
  dependencies:
    "@aws-sdk/client-cognito-identity" "3.369.0"
    "@aws-sdk/client-sso" "3.369.0"
    "@aws-sdk/client-sts" "3.369.0"
    "@aws-sdk/credential-provider-cognito-identity" "3.369.0"
    "@aws-sdk/credential-provider-env" "3.369.0"
    "@aws-sdk/credential-provider-ini" "3.369.0"
    "@aws-sdk/credential-provider-node" "3.369.0"
    "@aws-sdk/credential-provider-process" "3.369.0"
    "@aws-sdk/credential-provider-sso" "3.369.0"
    "@aws-sdk/credential-provider-web-identity" "3.369.0"
    "@aws-sdk/types" "3.369.0"
    "@smithy/credential-provider-imds" "^1.0.1"
    "@smithy/property-provider" "^1.0.1"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/hash-blob-browser@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/hash-blob-browser/-/hash-blob-browser-3.369.0.tgz#57689379436c183aa5d57f772eaa0418a1f26d85"
  integrity sha512-fx+6Qavc5dSuVm6vAXrA7oyPSu/gGW2W8YnSCmhDUCQw7UFB8b9Uc97sM43K8RNi0pj3cPevvgbab1m+E8Vs8A==
  dependencies:
    "@aws-sdk/chunked-blob-reader" "3.310.0"
    "@aws-sdk/chunked-blob-reader-native" "3.310.0"
    "@aws-sdk/types" "3.369.0"
    tslib "^2.5.0"

"@aws-sdk/hash-stream-node@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/hash-stream-node/-/hash-stream-node-3.369.0.tgz#5f364243df10349680466a38511ac238754d7d14"
  integrity sha512-v4xGoCHw8VLEa2HcvnNa5TMrmNS6iNVHKWpjWnq/zu7ZwtoJcRFsjEEQaW0EkfpoBtT0Ll7jHmSFS+q28xa/Fw==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@aws-sdk/util-utf8" "3.310.0"
    tslib "^2.5.0"

"@aws-sdk/is-array-buffer@3.310.0":
  version "3.310.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/is-array-buffer/-/is-array-buffer-3.310.0.tgz#f87a79f1b858c88744f07e8d8d0a791df204017e"
  integrity sha512-urnbcCR+h9NWUnmOtet/s4ghvzsidFmspfhYaHAmSRdy9yDjdjBJMFjjsn85A1ODUktztm+cVncXjQ38WCMjMQ==
  dependencies:
    tslib "^2.5.0"

"@aws-sdk/md5-js@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/md5-js/-/md5-js-3.369.0.tgz#2b371e00ad6fddad7d616db04a17746529ae5756"
  integrity sha512-gnwXE/9h1UufrafvCKdONuNEzqeiBfFJM68Ww3b2c9Eby7+BVv/O3jghxr9XAEM60A0CaEoLCqH+5Auh58NJag==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@aws-sdk/util-utf8" "3.310.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-bucket-endpoint@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-bucket-endpoint/-/middleware-bucket-endpoint-3.369.0.tgz#bf6513e4ecb6dd7922511f5b93fc8c69aa7579d0"
  integrity sha512-wcb8e40pOktygAeHwR9JmkZPZsc/UIHU7qdaKuKjE4MgLS3EUUp71iE4GMfFOpVrRlLlTAaGylaXVjFIcZuhnw==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@aws-sdk/util-arn-parser" "3.310.0"
    "@smithy/protocol-http" "^1.1.0"
    "@smithy/types" "^1.1.0"
    "@smithy/util-config-provider" "^1.0.1"
    tslib "^2.5.0"

"@aws-sdk/middleware-expect-continue@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-expect-continue/-/middleware-expect-continue-3.369.0.tgz#5287c3ff36ec1b5b847c551edead76470bfd413c"
  integrity sha512-uHUOjPDFHSaO6QTO0KGAl6sWbz3Kp21/AlO/qEexvP/F+12cSimR/f/mFLfAHvBCyftiD/6TFxf6p5WzkEkGBQ==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@smithy/protocol-http" "^1.1.0"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-flexible-checksums@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-flexible-checksums/-/middleware-flexible-checksums-3.369.0.tgz#b255a15f617fdc61c87b195b57b3c6b4ec1bc63a"
  integrity sha512-7oLXQbB6G2KrssFXH6iIdIbmI8Ex1VUQ+xnF1QBJcHasFY/Wn/WMAEZHtlk/J+eqHafR2UhlyncR80J1tZh9KA==
  dependencies:
    "@aws-crypto/crc32" "3.0.0"
    "@aws-crypto/crc32c" "3.0.0"
    "@aws-sdk/types" "3.369.0"
    "@smithy/is-array-buffer" "^1.0.1"
    "@smithy/protocol-http" "^1.1.0"
    "@smithy/types" "^1.1.0"
    "@smithy/util-utf8" "^1.0.1"
    tslib "^2.5.0"

"@aws-sdk/middleware-host-header@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-host-header/-/middleware-host-header-3.369.0.tgz#e77d948cb99f5aa9c6f546ad50971e34f8a42abd"
  integrity sha512-ysbur68WHY7RYpGfth1Iu0+S03nSCLtIHJ+CDVYcVcyvYxaAv6y3gvfrkH9oL220uX75UVLj3tCKgAaLUBy5uA==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@smithy/protocol-http" "^1.1.0"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-location-constraint@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-location-constraint/-/middleware-location-constraint-3.369.0.tgz#afcec7a4021e04dc00e6d3288a09a93cec691ec4"
  integrity sha512-zv9n9KjThMdcyDNxeR5PI+14HZCuOteUQYrAahBUsSwlZUF5PfscVWJVoZJHqWXduhPb5SIOZC0NJndfc3Jtfw==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-logger@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-logger/-/middleware-logger-3.369.0.tgz#1e8e08aa5e3a33b91c4815dfed26142f715dfcf1"
  integrity sha512-mp4gVRaFRRX+LEDEIlPxHOI/+k1jPPp0tuKyoyNZQS8IPOL+6bqFdPan03hkTjujeyaZOyRjpaXXat6k1HkHhw==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-recursion-detection@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.369.0.tgz#f3c1f723c05caad912a6de36be3708b231a5b663"
  integrity sha512-V7TNhHRTwiKlVXiaW2CYGcm3vObWdG5zU0SN7ZxHDT27eTRYL8ncVpDnQZ65HfekXL8T9llVibBTYYvZrxLJ1g==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@smithy/protocol-http" "^1.1.0"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-sdk-api-gateway@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-sdk-api-gateway/-/middleware-sdk-api-gateway-3.369.0.tgz#27da6b598800820ae0b02c9879843f92a885c0eb"
  integrity sha512-GlNqkyxxIZEU0EXxfLVLmjb+9VlqzM4bKsRATxUFnB8FpMfw9NeOl9RT/omae26rjnGE9AaIco+0kUaEEnVWsA==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@smithy/protocol-http" "^1.1.0"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-sdk-s3@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-sdk-s3/-/middleware-sdk-s3-3.369.0.tgz#211f1115793349a299486f319b9e5bbe5b985853"
  integrity sha512-hiZmGmsGiZXk2oKbgAUdnslPokpJWua/y6VD0XHv/yB1EOg2xhBLSzLRp/BpgoUjj+nEpk4wf4mxJyM35nvFeQ==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@aws-sdk/util-arn-parser" "3.310.0"
    "@smithy/protocol-http" "^1.1.0"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-sdk-sts@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-sdk-sts/-/middleware-sdk-sts-3.369.0.tgz#69557add85bf233d40d6f4b51245f16db018c155"
  integrity sha512-Igizyt7TWy8kTitvE6o7R1Cfa4qLqijS/WxqT1cnHscQyZFFiIJVNypWeV4V19DZ9Msb/feAQdc8EWgHvZvYGA==
  dependencies:
    "@aws-sdk/middleware-signing" "3.369.0"
    "@aws-sdk/types" "3.369.0"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-signing@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-signing/-/middleware-signing-3.369.0.tgz#8b4fc60168575055b1cb27d54dbd2bd443864c68"
  integrity sha512-55qihn+9/zjsHUNvEgc4OUWQBxVlKW9C+whVhdy8H8olwAnfOH1ui9xXQ+SAyBCD9ck3vAY89VmBeQQQGZVVQw==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@smithy/property-provider" "^1.0.1"
    "@smithy/protocol-http" "^1.1.0"
    "@smithy/signature-v4" "^1.0.1"
    "@smithy/types" "^1.1.0"
    "@smithy/util-middleware" "^1.0.1"
    tslib "^2.5.0"

"@aws-sdk/middleware-ssec@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-ssec/-/middleware-ssec-3.369.0.tgz#18ba0e2b978cfb8b3f750d5df8fb4a03385b3654"
  integrity sha512-neQeE7Z7gBvTRaK6PG6TZysW3ZiE/mMipNHLcHat2Dap2YO7Dcdzyge2MLwNQNL0d/34dpmV8ohMUw5SqnDoLw==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-user-agent@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.369.0.tgz#4d421d6cc767356eb24b60ee47cdbb179e55c312"
  integrity sha512-a7Wb3s0y+blGF654GZv3nI3ZMRARAGH7iQrF2gWGtb2Qq0f3TQGHmpoHddWObYxiFWYzdXdTC3kbsAW1zRwEAA==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@aws-sdk/util-endpoints" "3.369.0"
    "@smithy/protocol-http" "^1.1.0"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/service-error-classification@^3.4.1":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/service-error-classification/-/service-error-classification-3.369.0.tgz#1029ca9f99dff84b508f7ea5eea70d6244aae286"
  integrity sha512-M0wrrf1tOHsJSkF+R3ZFXr3CtfjkT9PJyShX71DhHQm9Ei00mzsjgFVSg+I91WuVC47kQIuBUmV45E1A8t8I8g==

"@aws-sdk/signature-v4-multi-region@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/signature-v4-multi-region/-/signature-v4-multi-region-3.369.0.tgz#ab29240cce41fe880fcaee5e50e5ce819e4c2321"
  integrity sha512-OodVH5mFcwpZxv0RC4fx7a0G6Pi6R73fA4bDgjmZHq+UOQs9ZaodAydZRKupvDpZhjAk/a4+CgSNIRsWfC6V1Q==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@smithy/protocol-http" "^1.1.0"
    "@smithy/signature-v4" "^1.0.1"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/token-providers@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/token-providers/-/token-providers-3.369.0.tgz#978efa15b54264a3bc3d3e02478980d2564e460e"
  integrity sha512-xIz8KbF4RMlMq0aAJbVocLB03OiqJIU5RLy+2t+bKMQ60fV4bnVINH5GxAMiFXiBIQVqfehFJlxJACtEphqQwA==
  dependencies:
    "@aws-sdk/client-sso-oidc" "3.369.0"
    "@aws-sdk/types" "3.369.0"
    "@smithy/property-provider" "^1.0.1"
    "@smithy/shared-ini-file-loader" "^1.0.1"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/types@3.369.0", "@aws-sdk/types@^3.222.0", "@aws-sdk/types@^3.4.1":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/types/-/types-3.369.0.tgz#9721ff6789437f7b73532c35e399382a6535ea73"
  integrity sha512-0LgII+RatF2OEFaFQcNyX72py4ZgWz+/JAv++PXv0gkIaTRnsJbSveQArNynEK+aAc/rZKWJgBvwT4FvLM2vgA==
  dependencies:
    "@smithy/types" "1.1.0"
    tslib "^2.5.0"

"@aws-sdk/util-arn-parser@3.310.0":
  version "3.310.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-arn-parser/-/util-arn-parser-3.310.0.tgz#861ff8810851be52a320ec9e4786f15b5fc74fba"
  integrity sha512-jL8509owp/xB9+Or0pvn3Fe+b94qfklc2yPowZZIFAkFcCSIdkIglz18cPDWnYAcy9JGewpMS1COXKIUhZkJsA==
  dependencies:
    tslib "^2.5.0"

"@aws-sdk/util-base64@3.310.0":
  version "3.310.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-base64/-/util-base64-3.310.0.tgz#d0fd49aff358c5a6e771d0001c63b1f97acbe34c"
  integrity sha512-v3+HBKQvqgdzcbL+pFswlx5HQsd9L6ZTlyPVL2LS9nNXnCcR3XgGz9jRskikRUuUvUXtkSG1J88GAOnJ/apTPg==
  dependencies:
    "@aws-sdk/util-buffer-from" "3.310.0"
    tslib "^2.5.0"

"@aws-sdk/util-buffer-from@3.310.0":
  version "3.310.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-buffer-from/-/util-buffer-from-3.310.0.tgz#7a72cb965984d3c6a7e256ae6cf1621f52e54a57"
  integrity sha512-i6LVeXFtGih5Zs8enLrt+ExXY92QV25jtEnTKHsmlFqFAuL3VBeod6boeMXkN2p9lbSVVQ1sAOOYZOHYbYkntw==
  dependencies:
    "@aws-sdk/is-array-buffer" "3.310.0"
    tslib "^2.5.0"

"@aws-sdk/util-endpoints@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-endpoints/-/util-endpoints-3.369.0.tgz#99f815bcb22a9905f116827949675c0e12c30013"
  integrity sha512-dkzhhMIvQRsgdomHi8fmgQ3df2cS1jeWAUIPjxV4lBikcvcF2U0CtvH9QYyMpluSNP1IYcEuONe8wfZGSrNjdg==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    tslib "^2.5.0"

"@aws-sdk/util-locate-window@^3.0.0":
  version "3.310.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-locate-window/-/util-locate-window-3.310.0.tgz#b071baf050301adee89051032bd4139bba32cc40"
  integrity sha512-qo2t/vBTnoXpjKxlsC2e1gBrRm80M3bId27r0BRB2VniSSe7bL1mmzM+/HFtujm0iAxtPM+aLEflLJlJeDPg0w==
  dependencies:
    tslib "^2.5.0"

"@aws-sdk/util-user-agent-browser@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.369.0.tgz#4cc7ae1a09c0d903d3ae436871a6153e94593854"
  integrity sha512-wrF0CqnfFac4sYr8jLZXz7B5NPxdW4GettH07Sl3ihO2aXsTvZ0RoyqzwF7Eve8ihbK0vCKt1S3/vZTOLw8sCg==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@smithy/types" "^1.1.0"
    bowser "^2.11.0"
    tslib "^2.5.0"

"@aws-sdk/util-user-agent-node@3.369.0":
  version "3.369.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.369.0.tgz#d6a0bbddc259600fccfc9935380a6141462b3785"
  integrity sha512-RkiGyWp+YUlK4njsvqD7S08aihEW8aMNrT5OXmLGdukEUGWMAyvIcq4XS8MxA02GRPUxTUNInLltXwc1AaDpCw==
  dependencies:
    "@aws-sdk/types" "3.369.0"
    "@smithy/node-config-provider" "^1.0.1"
    "@smithy/types" "^1.1.0"
    tslib "^2.5.0"

"@aws-sdk/util-utf8-browser@^3.0.0":
  version "3.259.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-utf8-browser/-/util-utf8-browser-3.259.0.tgz#3275a6f5eb334f96ca76635b961d3c50259fd9ff"
  integrity sha512-UvFa/vR+e19XookZF8RzFZBrw2EUkQWxiBW0yYQAhvk3C+QVGl0H3ouca8LDBlBfQKXwmW3huo/59H8rwb1wJw==
  dependencies:
    tslib "^2.3.1"

"@aws-sdk/util-utf8@3.310.0":
  version "3.310.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-utf8/-/util-utf8-3.310.0.tgz#4a7b9dcebb88e830d3811aeb21e9a6df4273afb4"
  integrity sha512-DnLfFT8uCO22uOJc0pt0DsSNau1GTisngBCDw8jQuWT5CqogMJu4b/uXmwEqfj8B3GX6Xsz8zOd6JpRlPftQoA==
  dependencies:
    "@aws-sdk/util-buffer-from" "3.310.0"
    tslib "^2.5.0"

"@aws-sdk/xml-builder@3.310.0":
  version "3.310.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/xml-builder/-/xml-builder-3.310.0.tgz#f0236f2103b438d16117e0939a6305ad69b7ff76"
  integrity sha512-TqELu4mOuSIKQCqj63fGVs86Yh+vBx5nHRpWKNUNhB2nPTpfbziTs5c1X358be3peVWA4wPxW7Nt53KIg1tnNw==
  dependencies:
    tslib "^2.5.0"

"@dazn/lambda-powertools-correlation-ids@^1.28.1":
  version "1.28.1"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-correlation-ids/-/lambda-powertools-correlation-ids-1.28.1.tgz#ff0b94de104154cbdf5825e9f2a5a789c4cb6e92"
  integrity sha512-/RZUT5ZlVcQxsWi+OolEwXUIsXHqffNeZ+eY4Je23s9VcztuqHuHeyrlJh1m5Kg76EsvQTq+5b1xvjf3J/6A1Q==

"@dazn/lambda-powertools-logger@^1.28.1":
  version "1.28.1"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-logger/-/lambda-powertools-logger-1.28.1.tgz#ac638e6e13552ac4e3a35613436f8a132e9bfe05"
  integrity sha512-vfnKgEwW/jv4PSkqRsEWPWLY5fkcjCnLrSZpca2Exh7pIUWZQN3FqLdpqs23caa+qtOCJ2JM8toa21uwSzYMLw==
  dependencies:
    "@dazn/lambda-powertools-correlation-ids" "^1.28.1"

"@dazn/lambda-powertools-middleware-correlation-ids@^1.29.0":
  version "1.29.0"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-middleware-correlation-ids/-/lambda-powertools-middleware-correlation-ids-1.29.0.tgz#e03e8184e2a3673a77d18d02576b44646caa060c"
  integrity sha512-kXOOKzEMKz6nYHUQo2GUvTqnQeXo1U6/RI87xUjSeztcjHaDZ0Jw6plUepZD+YawjfsVIrHKfnZrlN909utttA==
  dependencies:
    "@dazn/lambda-powertools-correlation-ids" "^1.28.1"
    "@dazn/lambda-powertools-logger" "^1.28.1"

"@dazn/lambda-powertools-middleware-log-timeout@^1.29.0":
  version "1.29.0"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-middleware-log-timeout/-/lambda-powertools-middleware-log-timeout-1.29.0.tgz#027b9fcaa0413b5d0e0261f16721be31079f9e9c"
  integrity sha512-BJv3DQdcuOCBfp93cFv3LgCcCBhwh4s8COmw4x+c3cEdkY6zajo9tHAikFea8Fv9ShDXAcUgnPpkv8EFMbAH+w==
  dependencies:
    "@dazn/lambda-powertools-logger" "^1.28.1"

"@dazn/lambda-powertools-middleware-sample-logging@^1.29.0":
  version "1.29.0"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-middleware-sample-logging/-/lambda-powertools-middleware-sample-logging-1.29.0.tgz#a0b403f7387e202b47df1bdc975c5e0ba09fd46f"
  integrity sha512-VHe3bSw0ch5Ql5tA3XvCta8db1Nr6NaSJ0Oj2oqQU+F15WJfqPD+reeKMgj3F1z8lJqXWAea3aD4nQT0PCTt6Q==
  dependencies:
    "@dazn/lambda-powertools-correlation-ids" "^1.28.1"
    "@dazn/lambda-powertools-logger" "^1.28.1"

"@dazn/lambda-powertools-pattern-basic@^1.29.0":
  version "1.29.0"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-pattern-basic/-/lambda-powertools-pattern-basic-1.29.0.tgz#d97d47730588cb93dc115402fbab12e4492c6948"
  integrity sha512-HYmu9eKVRYNu5Q2CYuOl3UmBMAfpHzvNJFRdR8f8F5DJLktsexapk1sDjZZq4bP1ZmduuSbG/mUN9nmtkCRWYw==
  dependencies:
    "@dazn/lambda-powertools-middleware-correlation-ids" "^1.29.0"
    "@dazn/lambda-powertools-middleware-log-timeout" "^1.29.0"
    "@dazn/lambda-powertools-middleware-sample-logging" "^1.29.0"
    "@middy/core" "^2.1.0"

"@mediality/centaur@../../centaurappCentaurAppCommonLayer/lib/nodejs":
  version "1.0.31"
  dependencies:
    "@aws-lambda-powertools/logger" "^1.5.1"
    "@aws-lambda-powertools/tracer" "^1.5.1"
    "@aws-sdk/client-api-gateway" "^3.54.0"
    "@aws-sdk/client-s3" "^3.282.0"
    "@aws-sdk/client-secrets-manager" "^3.282.0"
    "@dazn/lambda-powertools-logger" "^1.28.1"
    "@dazn/lambda-powertools-pattern-basic" "^1.29.0"
    "@mediality/centaur" "./"
    aws-sdk "^2.1324.0"
    aws-xray-sdk "^3.3.4"
    aws-xray-sdk-core "^3.3.4"
    axios "^1.6.7"
    basic-ftp "^5.0.1"
    fast-xml-parser "^4.0.1"
    fs "^0.0.1-security"
    fs-extra "^10.0.0"
    install "^0.13.0"
    moment "^2.29.1"
    mongoose "^6.1.3"
    pify "^5.0.0"
    uuid "^8.3.2"
    uuid-by-string "^3.0.4"
    validator "^13.7.0"
    xml2js "^0.4.23"
    xmlbuilder2 "^3.0.2"

"@mediality/centaur@./":
  version "2.0.0"

"@middy/core@^2.1.0":
  version "2.5.7"
  resolved "https://registry.yarnpkg.com/@middy/core/-/core-2.5.7.tgz#a1b3eff68881ff66b14b5051255791f7cbd3b471"
  integrity sha512-KX5Ud0SP+pol6PGkYtMCH4goHobs1XJo3OvEUwdiZUIjZgo56Q08nLu5N7Bs6P+FwGTQHA+hlQ3I5SZbfpO/jg==

"@oozcitak/dom@1.15.10":
  version "1.15.10"
  resolved "https://registry.yarnpkg.com/@oozcitak/dom/-/dom-1.15.10.tgz#dca7289f2b292cff2a901ea4fbbcc0a1ab0b05c2"
  integrity sha512-0JT29/LaxVgRcGKvHmSrUTEvZ8BXvZhGl2LASRUgHqDTC1M5g1pLmVv56IYNyt3bG2CUjDkc67wnyZC14pbQrQ==
  dependencies:
    "@oozcitak/infra" "1.0.8"
    "@oozcitak/url" "1.0.4"
    "@oozcitak/util" "8.3.8"

"@oozcitak/infra@1.0.8":
  version "1.0.8"
  resolved "https://registry.yarnpkg.com/@oozcitak/infra/-/infra-1.0.8.tgz#b0b089421f7d0f6878687608301fbaba837a7d17"
  integrity sha512-JRAUc9VR6IGHOL7OGF+yrvs0LO8SlqGnPAMqyzOuFZPSZSXI7Xf2O9+awQPSMXgIWGtgUf/dA6Hs6X6ySEaWTg==
  dependencies:
    "@oozcitak/util" "8.3.8"

"@oozcitak/url@1.0.4":
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/@oozcitak/url/-/url-1.0.4.tgz#ca8b1c876319cf5a648dfa1123600a6aa5cda6ba"
  integrity sha512-kDcD8y+y3FCSOvnBI6HJgl00viO/nGbQoCINmQ0h98OhnGITrWR3bOGfwYCthgcrV8AnTJz8MzslTQbC3SOAmw==
  dependencies:
    "@oozcitak/infra" "1.0.8"
    "@oozcitak/util" "8.3.8"

"@oozcitak/util@8.3.8":
  version "8.3.8"
  resolved "https://registry.yarnpkg.com/@oozcitak/util/-/util-8.3.8.tgz#10f65fe1891fd8cde4957360835e78fd1936bfdd"
  integrity sha512-T8TbSnGsxo6TDBJx/Sgv/BlVJL3tshxZP7Aq5R1mSnM5OcHY2dQaxLMu2+E8u3gN0MLOzdjurqN4ZRVuzQycOQ==

"@smithy/abort-controller@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/abort-controller/-/abort-controller-1.0.2.tgz#74caac052ecea15c5460438272ad8d43a6ccbc53"
  integrity sha512-tb2h0b+JvMee+eAxTmhnyqyNk51UXIK949HnE14lFeezKsVJTB30maan+CO2IMwnig2wVYQH84B5qk6ylmKCuA==
  dependencies:
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@smithy/config-resolver@^1.0.1", "@smithy/config-resolver@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/config-resolver/-/config-resolver-1.0.2.tgz#d4f556a44292b41b5c067662a4bd5049dea40e35"
  integrity sha512-8Bk7CgnVKg1dn5TgnjwPz2ebhxeR7CjGs5yhVYH3S8x0q8yPZZVWwpRIglwXaf5AZBzJlNO1lh+lUhMf2e73zQ==
  dependencies:
    "@smithy/types" "^1.1.1"
    "@smithy/util-config-provider" "^1.0.2"
    "@smithy/util-middleware" "^1.0.2"
    tslib "^2.5.0"

"@smithy/credential-provider-imds@^1.0.1", "@smithy/credential-provider-imds@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/credential-provider-imds/-/credential-provider-imds-1.0.2.tgz#7aa797c0d95448eb3dccb988b40e62db8989576f"
  integrity sha512-fLjCya+JOu2gPJpCiwSUyoLvT8JdNJmOaTOkKYBZoGf7CzqR6lluSyI+eboZnl/V0xqcfcqBG4tgqCISmWS3/w==
  dependencies:
    "@smithy/node-config-provider" "^1.0.2"
    "@smithy/property-provider" "^1.0.2"
    "@smithy/types" "^1.1.1"
    "@smithy/url-parser" "^1.0.2"
    tslib "^2.5.0"

"@smithy/eventstream-codec@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-codec/-/eventstream-codec-1.0.2.tgz#06d1b6e2510cb2475a39b3a20b0c75e751917c59"
  integrity sha512-eW/XPiLauR1VAgHKxhVvgvHzLROUgTtqat2lgljztbH8uIYWugv7Nz+SgCavB+hWRazv2iYgqrSy74GvxXq/rg==
  dependencies:
    "@aws-crypto/crc32" "3.0.0"
    "@smithy/types" "^1.1.1"
    "@smithy/util-hex-encoding" "^1.0.2"
    tslib "^2.5.0"

"@smithy/eventstream-serde-browser@^1.0.1":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-browser/-/eventstream-serde-browser-1.0.2.tgz#2f6c9de876ca5e3f35388df9cfa31aeb4281ac76"
  integrity sha512-8bDImzBewLQrIF6hqxMz3eoYwEus2E5JrEwKnhpkSFkkoj8fDSKiLeP/26xfcaoVJgZXB8M1c6jSEZiY3cUMsw==
  dependencies:
    "@smithy/eventstream-serde-universal" "^1.0.2"
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@smithy/eventstream-serde-config-resolver@^1.0.1":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-config-resolver/-/eventstream-serde-config-resolver-1.0.2.tgz#37a55970c31f3e4a38d66933ab14398351553daf"
  integrity sha512-SeiJ5pfrXzkGP4WCt9V3Pimfr3OM85Nyh9u/V4J6E0O2dLOYuqvSuKdVnktV0Tcmuu1ZYbt78Th0vfetnSEcdQ==
  dependencies:
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@smithy/eventstream-serde-node@^1.0.1":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-node/-/eventstream-serde-node-1.0.2.tgz#1c8ba86f70ecdad19c3a25b48b0f9a03799c2a0d"
  integrity sha512-jqSfi7bpOBHqgd5OgUtCX0wAVhPqxlVdqcj2c4gHaRRXcbpCmK0DRDg7P+Df0h4JJVvTqI6dy2c0YhHk5ehPCw==
  dependencies:
    "@smithy/eventstream-serde-universal" "^1.0.2"
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@smithy/eventstream-serde-universal@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-universal/-/eventstream-serde-universal-1.0.2.tgz#66c1ccc639cb64049291200bcda476b26875fd8e"
  integrity sha512-cQ9bT0j0x49cp8TQ1yZSnn4+9qU0WQSTkoucl3jKRoTZMzNYHg62LQao6HTQ3Jgd77nAXo00c7hqUEjHXwNA+A==
  dependencies:
    "@smithy/eventstream-codec" "^1.0.2"
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@smithy/fetch-http-handler@^1.0.1", "@smithy/fetch-http-handler@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/fetch-http-handler/-/fetch-http-handler-1.0.2.tgz#4186ee6451de22e867f43c05236dcff43eca6e91"
  integrity sha512-kynyofLf62LvR8yYphPPdyHb8fWG3LepFinM/vWUTG2Q1pVpmPCM530ppagp3+q2p+7Ox0UvSqldbKqV/d1BpA==
  dependencies:
    "@smithy/protocol-http" "^1.1.1"
    "@smithy/querystring-builder" "^1.0.2"
    "@smithy/types" "^1.1.1"
    "@smithy/util-base64" "^1.0.2"
    tslib "^2.5.0"

"@smithy/hash-node@^1.0.1":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/hash-node/-/hash-node-1.0.2.tgz#dc65203a348d29e45c493ead3e772e4f7dfb5bc0"
  integrity sha512-K6PKhcUNrJXtcesyzhIvNlU7drfIU7u+EMQuGmPw6RQDAg/ufUcfKHz4EcUhFAodUmN+rrejhRG9U6wxjeBOQA==
  dependencies:
    "@smithy/types" "^1.1.1"
    "@smithy/util-buffer-from" "^1.0.2"
    "@smithy/util-utf8" "^1.0.2"
    tslib "^2.5.0"

"@smithy/invalid-dependency@^1.0.1":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/invalid-dependency/-/invalid-dependency-1.0.2.tgz#0a9d82d1a14e5bdbdc0bd2cef5f457c85a942920"
  integrity sha512-B1Y3Tsa6dfC+Vvb+BJMhTHOfFieeYzY9jWQSTR1vMwKkxsymD0OIAnEw8rD/RiDj/4E4RPGFdx9Mdgnyd6Bv5Q==
  dependencies:
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@smithy/is-array-buffer@^1.0.1", "@smithy/is-array-buffer@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/is-array-buffer/-/is-array-buffer-1.0.2.tgz#224702a2364d698f0a36ecb2c240c0c9541ecfb6"
  integrity sha512-pkyBnsBRpe+c/6ASavqIMRBdRtZNJEVJOEzhpxZ9JoAXiZYbkfaSMRA/O1dUxGdJ653GHONunnZ4xMo/LJ7utQ==
  dependencies:
    tslib "^2.5.0"

"@smithy/middleware-content-length@^1.0.1":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-content-length/-/middleware-content-length-1.0.2.tgz#63099f8d01b3419b65e21cfd07b0c2ef47d1f473"
  integrity sha512-pa1/SgGIrSmnEr2c9Apw7CdU4l/HW0fK3+LKFCPDYJrzM0JdYpqjQzgxi31P00eAkL0EFBccpus/p1n2GF9urw==
  dependencies:
    "@smithy/protocol-http" "^1.1.1"
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@smithy/middleware-endpoint@^1.0.1":
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-endpoint/-/middleware-endpoint-1.0.3.tgz#ff4b1c0a83eb8d8b8d3937f434a95efbbf43e1cd"
  integrity sha512-GsWvTXMFjSgl617PCE2km//kIjjtvMRrR2GAuRDIS9sHiLwmkS46VWaVYy+XE7ubEsEtzZ5yK2e8TKDR6Qr5Lw==
  dependencies:
    "@smithy/middleware-serde" "^1.0.2"
    "@smithy/types" "^1.1.1"
    "@smithy/url-parser" "^1.0.2"
    "@smithy/util-middleware" "^1.0.2"
    tslib "^2.5.0"

"@smithy/middleware-retry@^1.0.1", "@smithy/middleware-retry@^1.0.2":
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-retry/-/middleware-retry-1.0.4.tgz#8e9de0713dac7f7af405477d46bd4525ca7b9ea8"
  integrity sha512-G7uRXGFL8c3F7APnoIMTtNAHH8vT4F2qVnAWGAZaervjupaUQuRRHYBLYubK0dWzOZz86BtAXKieJ5p+Ni2Xpg==
  dependencies:
    "@smithy/protocol-http" "^1.1.1"
    "@smithy/service-error-classification" "^1.0.3"
    "@smithy/types" "^1.1.1"
    "@smithy/util-middleware" "^1.0.2"
    "@smithy/util-retry" "^1.0.4"
    tslib "^2.5.0"
    uuid "^8.3.2"

"@smithy/middleware-serde@^1.0.1", "@smithy/middleware-serde@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-serde/-/middleware-serde-1.0.2.tgz#87b3a0211602ae991d9b756893eb6bf2e3e5f711"
  integrity sha512-T4PcdMZF4xme6koUNfjmSZ1MLi7eoFeYCtodQNQpBNsS77TuJt1A6kt5kP/qxrTvfZHyFlj0AubACoaUqgzPeg==
  dependencies:
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@smithy/middleware-stack@^1.0.1", "@smithy/middleware-stack@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-stack/-/middleware-stack-1.0.2.tgz#d241082bf3cb315c749dda57e233039a9aed804e"
  integrity sha512-H7/uAQEcmO+eDqweEFMJ5YrIpsBwmrXSP6HIIbtxKJSQpAcMGY7KrR2FZgZBi1FMnSUOh+rQrbOyj5HQmSeUBA==
  dependencies:
    tslib "^2.5.0"

"@smithy/node-config-provider@^1.0.1", "@smithy/node-config-provider@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/node-config-provider/-/node-config-provider-1.0.2.tgz#2d391b96a9e10072e7e0a3698427400f4ef17ec4"
  integrity sha512-HU7afWpTToU0wL6KseGDR2zojeyjECQfr8LpjAIeHCYIW7r360ABFf4EaplaJRMVoC3hD9FeltgI3/NtShOqCg==
  dependencies:
    "@smithy/property-provider" "^1.0.2"
    "@smithy/shared-ini-file-loader" "^1.0.2"
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@smithy/node-http-handler@^1.0.1", "@smithy/node-http-handler@^1.0.2", "@smithy/node-http-handler@^1.0.3":
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/@smithy/node-http-handler/-/node-http-handler-1.0.3.tgz#89b556ca2bdcce7a994a9da1ea265094d76d4791"
  integrity sha512-PcPUSzTbIb60VCJCiH0PU0E6bwIekttsIEf5Aoo/M0oTfiqsxHTn0Rcij6QoH6qJy6piGKXzLSegspXg5+Kq6g==
  dependencies:
    "@smithy/abort-controller" "^1.0.2"
    "@smithy/protocol-http" "^1.1.1"
    "@smithy/querystring-builder" "^1.0.2"
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@smithy/property-provider@^1.0.1", "@smithy/property-provider@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/property-provider/-/property-provider-1.0.2.tgz#f99f104cbd6576c9aca9f56cb72819b4a65208e1"
  integrity sha512-pXDPyzKX8opzt38B205kDgaxda6LHcTfPvTYQZnwP6BAPp1o9puiCPjeUtkKck7Z6IbpXCPUmUQnzkUzWTA42Q==
  dependencies:
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@smithy/protocol-http@^1.0.1", "@smithy/protocol-http@^1.1.0", "@smithy/protocol-http@^1.1.1":
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/protocol-http/-/protocol-http-1.1.1.tgz#10977cf71631eed4f5ad1845408920238d52cdba"
  integrity sha512-mFLFa2sSvlUxm55U7B4YCIsJJIMkA6lHxwwqOaBkral1qxFz97rGffP/mmd4JDuin1EnygiO5eNJGgudiUgmDQ==
  dependencies:
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@smithy/querystring-builder@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/querystring-builder/-/querystring-builder-1.0.2.tgz#ce861f6cbd14792c83aa19b4967a19923bd0706e"
  integrity sha512-6P/xANWrtJhMzTPUR87AbXwSBuz1SDHIfL44TFd/GT3hj6rA+IEv7rftEpPjayUiWRocaNnrCPLvmP31mobOyA==
  dependencies:
    "@smithy/types" "^1.1.1"
    "@smithy/util-uri-escape" "^1.0.2"
    tslib "^2.5.0"

"@smithy/querystring-parser@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/querystring-parser/-/querystring-parser-1.0.2.tgz#559d09c46b21e6fbda71e95deda4bcd8a46bdecc"
  integrity sha512-IWxwxjn+KHWRRRB+K2Ngl+plTwo2WSgc2w+DvLy0DQZJh9UGOpw40d6q97/63GBlXIt4TEt5NbcFrO30CKlrsA==
  dependencies:
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@smithy/service-error-classification@^1.0.3":
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/@smithy/service-error-classification/-/service-error-classification-1.0.3.tgz#c620c1562610d3351985eb6dd04262ca2657ae67"
  integrity sha512-2eglIYqrtcUnuI71yweu7rSfCgt6kVvRVf0C72VUqrd0LrV1M0BM0eYN+nitp2CHPSdmMI96pi+dU9U/UqAMSA==

"@smithy/shared-ini-file-loader@^1.0.1", "@smithy/shared-ini-file-loader@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/shared-ini-file-loader/-/shared-ini-file-loader-1.0.2.tgz#c6e79991d87925bd18e0adae00c97da6c8ecae1e"
  integrity sha512-bdQj95VN+lCXki+P3EsDyrkpeLn8xDYiOISBGnUG/AGPYJXN8dmp4EhRRR7XOoLoSs8anZHR4UcGEOzFv2jwGw==
  dependencies:
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@smithy/signature-v4@^1.0.1":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/signature-v4/-/signature-v4-1.0.2.tgz#3a7b10ac66c337b404aa061e5f268f0550729680"
  integrity sha512-rpKUhmCuPmpV5dloUkOb9w1oBnJatvKQEjIHGmkjRGZnC3437MTdzWej9TxkagcZ8NRRJavYnEUixzxM1amFig==
  dependencies:
    "@smithy/eventstream-codec" "^1.0.2"
    "@smithy/is-array-buffer" "^1.0.2"
    "@smithy/types" "^1.1.1"
    "@smithy/util-hex-encoding" "^1.0.2"
    "@smithy/util-middleware" "^1.0.2"
    "@smithy/util-uri-escape" "^1.0.2"
    "@smithy/util-utf8" "^1.0.2"
    tslib "^2.5.0"

"@smithy/smithy-client@^1.0.2", "@smithy/smithy-client@^1.0.3":
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/@smithy/smithy-client/-/smithy-client-1.0.4.tgz#96d03d123d117a637c679a79bb8eae96e3857bd9"
  integrity sha512-gpo0Xl5Nyp9sgymEfpt7oa9P2q/GlM3VmQIdm+FeH0QEdYOQx3OtvwVmBYAMv2FIPWxkMZlsPYRTnEiBTK5TYg==
  dependencies:
    "@smithy/middleware-stack" "^1.0.2"
    "@smithy/types" "^1.1.1"
    "@smithy/util-stream" "^1.0.2"
    tslib "^2.5.0"

"@smithy/types@1.1.0":
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/@smithy/types/-/types-1.1.0.tgz#f30a23202c97634cca5c1ac955a9bf149c955226"
  integrity sha512-KzmvisMmuwD2jZXuC9e65JrgsZM97y5NpDU7g347oB+Q+xQLU6hQZ5zFNNbEfwwOJHoOvEVTna+dk1h/lW7alw==
  dependencies:
    tslib "^2.5.0"

"@smithy/types@^1.1.0", "@smithy/types@^1.1.1":
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/@smithy/types/-/types-1.1.1.tgz#949394a22e13e7077471bae0d18c146e5f62c456"
  integrity sha512-tMpkreknl2gRrniHeBtdgQwaOlo39df8RxSrwsHVNIGXULy5XP6KqgScUw2m12D15wnJCKWxVhCX+wbrBW/y7g==
  dependencies:
    tslib "^2.5.0"

"@smithy/url-parser@^1.0.1", "@smithy/url-parser@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/url-parser/-/url-parser-1.0.2.tgz#fb59be6f2283399443d9e7afe08ebf63b3c266bb"
  integrity sha512-0JRsDMQe53F6EHRWksdcavKDRjyqp8vrjakg8EcCUOa7PaFRRB1SO/xGZdzSlW1RSTWQDEksFMTCEcVEKmAoqA==
  dependencies:
    "@smithy/querystring-parser" "^1.0.2"
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@smithy/util-base64@^1.0.1", "@smithy/util-base64@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-base64/-/util-base64-1.0.2.tgz#6cdd5a9356dafad3c531123c12cd77d674762da0"
  integrity sha512-BCm15WILJ3SL93nusoxvJGMVfAMWHZhdeDZPtpAaskozuexd0eF6szdz4kbXaKp38bFCSenA6bkUHqaE3KK0dA==
  dependencies:
    "@smithy/util-buffer-from" "^1.0.2"
    tslib "^2.5.0"

"@smithy/util-body-length-browser@^1.0.1":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-body-length-browser/-/util-body-length-browser-1.0.2.tgz#4a9a49497634b5f25ab5ff73f1a8498010b0024a"
  integrity sha512-Xh8L06H2anF5BHjSYTg8hx+Itcbf4SQZnVMl4PIkCOsKtneMJoGjPRLy17lEzfoh/GOaa0QxgCP6lRMQWzNl4w==
  dependencies:
    tslib "^2.5.0"

"@smithy/util-body-length-node@^1.0.1":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-body-length-node/-/util-body-length-node-1.0.2.tgz#bc4969022f7d9ffcb239d626d80a85138e986df6"
  integrity sha512-nXHbZsUtvZeyfL4Ceds9nmy2Uh2AhWXohG4vWHyjSdmT8cXZlJdmJgnH6SJKDjyUecbu+BpKeVvSrA4cWPSOPA==
  dependencies:
    tslib "^2.5.0"

"@smithy/util-buffer-from@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-buffer-from/-/util-buffer-from-1.0.2.tgz#27e19573d721962bd2443f23d4edadb8206b2cb5"
  integrity sha512-lHAYIyrBO9RANrPvccnPjU03MJnWZ66wWuC5GjWWQVfsmPwU6m00aakZkzHdUT6tGCkGacXSgArP5wgTgA+oCw==
  dependencies:
    "@smithy/is-array-buffer" "^1.0.2"
    tslib "^2.5.0"

"@smithy/util-config-provider@^1.0.1", "@smithy/util-config-provider@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-config-provider/-/util-config-provider-1.0.2.tgz#4d2e867df1cc7b4010d1278bd5767ce1b679dae9"
  integrity sha512-HOdmDm+3HUbuYPBABLLHtn8ittuRyy+BSjKOA169H+EMc+IozipvXDydf+gKBRAxUa4dtKQkLraypwppzi+PRw==
  dependencies:
    tslib "^2.5.0"

"@smithy/util-defaults-mode-browser@^1.0.1":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-1.0.2.tgz#31ad7b9bce7e38fd57f4a370ee416373b4fbd432"
  integrity sha512-J1u2PO235zxY7dg0+ZqaG96tFg4ehJZ7isGK1pCBEA072qxNPwIpDzUVGnLJkHZvjWEGA8rxIauDtXfB0qxeAg==
  dependencies:
    "@smithy/property-provider" "^1.0.2"
    "@smithy/types" "^1.1.1"
    bowser "^2.11.0"
    tslib "^2.5.0"

"@smithy/util-defaults-mode-node@^1.0.1":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-1.0.2.tgz#b295fe2a18568c1e21a85b6557e2b769452b4d95"
  integrity sha512-9/BN63rlIsFStvI+AvljMh873Xw6bbI6b19b+PVYXyycQ2DDQImWcjnzRlHW7eP65CCUNGQ6otDLNdBQCgMXqg==
  dependencies:
    "@smithy/config-resolver" "^1.0.2"
    "@smithy/credential-provider-imds" "^1.0.2"
    "@smithy/node-config-provider" "^1.0.2"
    "@smithy/property-provider" "^1.0.2"
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@smithy/util-hex-encoding@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-hex-encoding/-/util-hex-encoding-1.0.2.tgz#5b9f2162f2a59b2d2aa39992bd2c7f65b6616ab6"
  integrity sha512-Bxydb5rMJorMV6AuDDMOxro3BMDdIwtbQKHpwvQFASkmr52BnpDsWlxgpJi8Iq7nk1Bt4E40oE1Isy/7ubHGzg==
  dependencies:
    tslib "^2.5.0"

"@smithy/util-middleware@^1.0.1", "@smithy/util-middleware@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-middleware/-/util-middleware-1.0.2.tgz#c3d4c7a6cd31bde33901e54abd7700c8ca73dab3"
  integrity sha512-vtXK7GOR2BoseCX8NCGe9SaiZrm9M2lm/RVexFGyPuafTtry9Vyv7hq/vw8ifd/G/pSJ+msByfJVb1642oQHKw==
  dependencies:
    tslib "^2.5.0"

"@smithy/util-retry@^1.0.1", "@smithy/util-retry@^1.0.2", "@smithy/util-retry@^1.0.4":
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/@smithy/util-retry/-/util-retry-1.0.4.tgz#9d95df3884981414163d5f780d38e3529384d9ad"
  integrity sha512-RnZPVFvRoqdj2EbroDo3OsnnQU8eQ4AlnZTOGusbYKybH3269CFdrZfZJloe60AQjX7di3J6t/79PjwCLO5Khw==
  dependencies:
    "@smithy/service-error-classification" "^1.0.3"
    tslib "^2.5.0"

"@smithy/util-stream@^1.0.1", "@smithy/util-stream@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-stream/-/util-stream-1.0.2.tgz#2d33aa5168e51d1dd7937c32a09c8334d2da44d9"
  integrity sha512-qyN2M9QFMTz4UCHi6GnBfLOGYKxQZD01Ga6nzaXFFC51HP/QmArU72e4kY50Z/EtW8binPxspP2TAsGbwy9l3A==
  dependencies:
    "@smithy/fetch-http-handler" "^1.0.2"
    "@smithy/node-http-handler" "^1.0.3"
    "@smithy/types" "^1.1.1"
    "@smithy/util-base64" "^1.0.2"
    "@smithy/util-buffer-from" "^1.0.2"
    "@smithy/util-hex-encoding" "^1.0.2"
    "@smithy/util-utf8" "^1.0.2"
    tslib "^2.5.0"

"@smithy/util-uri-escape@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-uri-escape/-/util-uri-escape-1.0.2.tgz#c69a5423c9baa7a045a79372320bd40a437ac756"
  integrity sha512-k8C0BFNS9HpBMHSgUDnWb1JlCQcFG+PPlVBq9keP4Nfwv6a9Q0yAfASWqUCtzjuMj1hXeLhn/5ADP6JxnID1Pg==
  dependencies:
    tslib "^2.5.0"

"@smithy/util-utf8@^1.0.1", "@smithy/util-utf8@^1.0.2":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-utf8/-/util-utf8-1.0.2.tgz#b34c27b4efbe4f0edb6560b6d4f743088302671f"
  integrity sha512-V4cyjKfJlARui0dMBfWJMQAmJzoW77i4N3EjkH/bwnE2Ngbl4tqD2Y0C/xzpzY/J1BdxeCKxAebVFk8aFCaSCw==
  dependencies:
    "@smithy/util-buffer-from" "^1.0.2"
    tslib "^2.5.0"

"@smithy/util-waiter@^1.0.1":
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/@smithy/util-waiter/-/util-waiter-1.0.2.tgz#3b1498a2d4b92e78eafacc8c76f314e30eb7a5e9"
  integrity sha512-+jq4/Vd9ejPzR45qwYSePyjQbqYP9QqtyZYsFVyfzRnbGGC0AjswOh7txcxroafuEBExK4qE+L/QZA8wWXsJYw==
  dependencies:
    "@smithy/abort-controller" "^1.0.2"
    "@smithy/types" "^1.1.1"
    tslib "^2.5.0"

"@types/aws-lambda@^8.10.92":
  version "8.10.119"
  resolved "https://registry.yarnpkg.com/@types/aws-lambda/-/aws-lambda-8.10.119.tgz#aaf010a9c892b3e29a290e5c49bfe8bcec82c455"
  integrity sha512-Vqm22aZrCvCd6I5g1SvpW151jfqwTzEZ7XJ3yZ6xaZG31nUEOEyzzVImjRcsN8Wi/QyPxId/x8GTtgIbsy8kEw==

"@types/body-parser@*":
  version "1.19.2"
  resolved "https://registry.yarnpkg.com/@types/body-parser/-/body-parser-1.19.2.tgz#aea2059e28b7658639081347ac4fab3de166e6f0"
  integrity sha512-ALYone6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g==
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/cls-hooked@^4.3.3":
  version "4.3.4"
  resolved "https://registry.yarnpkg.com/@types/cls-hooked/-/cls-hooked-4.3.4.tgz#b38c53c2bbb0131646bb70cdd91c6f851b4208b4"
  integrity sha512-IBarK4du6E+0vRg7Pt33RtbhvtmXIO1Jfwd825/RbGBEBR0uzUjPBRvYsQ9MWYmRq/mkWSesytux8aHZXi6kyw==
  dependencies:
    "@types/node" "*"

"@types/connect@*":
  version "3.4.35"
  resolved "https://registry.yarnpkg.com/@types/connect/-/connect-3.4.35.tgz#5fcf6ae445e4021d1fc2219a4873cc73a3bb2ad1"
  integrity sha512-cdeYyv4KWoEgpBISTxWvqYsVy444DOqehiF3fM3ne10AmJ62RSyNkUnxMJXHQWRQQX2eR94m5y1IZyDwBjV9FQ==
  dependencies:
    "@types/node" "*"

"@types/express-serve-static-core@^4.17.33":
  version "4.17.35"
  resolved "https://registry.yarnpkg.com/@types/express-serve-static-core/-/express-serve-static-core-4.17.35.tgz#c95dd4424f0d32e525d23812aa8ab8e4d3906c4f"
  integrity sha512-wALWQwrgiB2AWTT91CB62b6Yt0sNHpznUXeZEcnPU3DRdlDIz74x8Qg1UUYKSVFi+va5vKOLYRBI1bRKiLLKIg==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*":
  version "4.17.17"
  resolved "https://registry.yarnpkg.com/@types/express/-/express-4.17.17.tgz#01d5437f6ef9cfa8668e616e13c2f2ac9a491ae4"
  integrity sha512-Q4FmmuLGBG58btUnfS1c1r/NQdlp3DMfGDGig8WhfpA2YRUtEkxAjkZb0yvplJGYdF1fsQ81iMDcH24sSCNC/Q==
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/http-errors@*":
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/@types/http-errors/-/http-errors-2.0.1.tgz#20172f9578b225f6c7da63446f56d4ce108d5a65"
  integrity sha512-/K3ds8TRAfBvi5vfjuz8y6+GiAYBZ0x4tXv1Av6CWBWn0IlADc+ZX9pMq7oU0fNQPnBwIZl3rmeLp6SBApbxSQ==

"@types/mime@*":
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/@types/mime/-/mime-3.0.1.tgz#5f8f2bca0a5863cb69bc0b0acd88c96cb1d4ae10"
  integrity sha512-Y4XFY5VJAuw0FgAqPNd6NNoV44jbq9Bz2L7Rh/J6jLTiHBSBJa9fxqQIvkIld4GsoDOcCbvzOUAbLPsSKKg+uA==

"@types/mime@^1":
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/@types/mime/-/mime-1.3.2.tgz#93e25bf9ee75fe0fd80b594bc4feb0e862111b5a"
  integrity sha512-YATxVxgRqNH6nHEIsvg6k2Boc1JHI9ZbH5iWFFv/MTkchz3b1ieGDa5T0a9RznNdI0KhVbdbWSN+KWWrQZRxTw==

"@types/mysql@*":
  version "2.15.21"
  resolved "https://registry.yarnpkg.com/@types/mysql/-/mysql-2.15.21.tgz#7516cba7f9d077f980100c85fd500c8210bd5e45"
  integrity sha512-NPotx5CVful7yB+qZbWtXL2fA4e7aEHkihHLjklc6ID8aq7bhguHgeIoC1EmSNTAuCgI6ZXrjt2ZSaXnYX0EUg==
  dependencies:
    "@types/node" "*"

"@types/node@*":
  version "20.4.1"
  resolved "https://registry.yarnpkg.com/@types/node/-/node-20.4.1.tgz#a6033a8718653c50ac4962977e14d0f984d9527d"
  integrity sha512-JIzsAvJeA/5iY6Y/OxZbv1lUcc8dNSE77lb2gnBH+/PJ3lFR1Ccvgwl5JWnHAkNHcRsT0TbpVOsiMKZ1F/yyJg==

"@types/pg@*":
  version "8.10.2"
  resolved "https://registry.yarnpkg.com/@types/pg/-/pg-8.10.2.tgz#7814d1ca02c8071f4d0864c1b17c589b061dba43"
  integrity sha512-MKFs9P6nJ+LAeHLU3V0cODEOgyThJ3OAnmOlsZsxux6sfQs3HRXR5bBn7xG5DjckEFhTAxsXi7k7cd0pCMxpJw==
  dependencies:
    "@types/node" "*"
    pg-protocol "*"
    pg-types "^4.0.1"

"@types/qs@*":
  version "6.9.7"
  resolved "https://registry.yarnpkg.com/@types/qs/-/qs-6.9.7.tgz#63bb7d067db107cc1e457c303bc25d511febf6cb"
  integrity sha512-FGa1F62FT09qcrueBA6qYTrJPVDzah9a+493+o2PCXsesWHIn27G98TsSMs3WPNbZIEj4+VJf6saSFpvD+3Zsw==

"@types/range-parser@*":
  version "1.2.4"
  resolved "https://registry.yarnpkg.com/@types/range-parser/-/range-parser-1.2.4.tgz#cd667bcfdd025213aafb7ca5915a932590acdcdc"
  integrity sha512-EEhsLsD6UsDM1yFhAvy0Cjr6VwmpMWqFBCb9w07wVugF7w9nfajxLuVmngTIpgS6svCnm6Vaw+MZhoDCKnOfsw==

"@types/send@*":
  version "0.17.1"
  resolved "https://registry.yarnpkg.com/@types/send/-/send-0.17.1.tgz#ed4932b8a2a805f1fe362a70f4e62d0ac994e301"
  integrity sha512-Cwo8LE/0rnvX7kIIa3QHCkcuF21c05Ayb0ZfxPiv0W8VRiZiNW/WuRupHKpqqGVGf7SUA44QSOUKaEd9lIrd/Q==
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-static@*":
  version "1.15.2"
  resolved "https://registry.yarnpkg.com/@types/serve-static/-/serve-static-1.15.2.tgz#3e5419ecd1e40e7405d34093f10befb43f63381a"
  integrity sha512-J2LqtvFYCzaj8pVYKw8klQXrLLk7TBZmQ4ShlcdkELFKGwGMfevMLneMMRkMgZxotOD9wg497LpC7O8PcvAmfw==
  dependencies:
    "@types/http-errors" "*"
    "@types/mime" "*"
    "@types/node" "*"

"@types/webidl-conversions@*":
  version "7.0.0"
  resolved "https://registry.yarnpkg.com/@types/webidl-conversions/-/webidl-conversions-7.0.0.tgz#2b8e60e33906459219aa587e9d1a612ae994cfe7"
  integrity sha512-xTE1E+YF4aWPJJeUzaZI5DRntlkY3+BCVJi0axFptnjGmAoWxkyREIh/XMrfxVLejwQxMCfDXdICo0VLxThrog==

"@types/whatwg-url@^8.2.1":
  version "8.2.2"
  resolved "https://registry.yarnpkg.com/@types/whatwg-url/-/whatwg-url-8.2.2.tgz#749d5b3873e845897ada99be4448041d4cc39e63"
  integrity sha512-FtQu10RWgn3D9U4aazdwIE2yzphmTJREDqNdODHrbrZmmMqI0vMheC/6NE/J1Yveaj8H+ela+YwWTjq5PGmuhA==
  dependencies:
    "@types/node" "*"
    "@types/webidl-conversions" "*"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.yarnpkg.com/argparse/-/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==
  dependencies:
    sprintf-js "~1.0.2"

async-hook-jl@^1.7.6:
  version "1.7.6"
  resolved "https://registry.yarnpkg.com/async-hook-jl/-/async-hook-jl-1.7.6.tgz#4fd25c2f864dbaf279c610d73bf97b1b28595e68"
  integrity sha512-gFaHkFfSxTjvoxDMYqDuGHlcRyUuamF8s+ZTtJdDzqjws4mCt7v0vuV79/E2Wr2/riMQgtG4/yUtXWs1gZ7JMg==
  dependencies:
    stack-chain "^1.3.7"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

atomic-batcher@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/atomic-batcher/-/atomic-batcher-1.0.2.tgz#d16901d10ccec59516c197b9ccd8930689b813b4"
  integrity sha512-EFGCRj4kLX1dHv1cDzTk+xbjBFj1GnJDpui52YmEcxxHHEWjYyT6l51U7n6WQ28osZH4S9gSybxe56Vm7vB61Q==

available-typed-arrays@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz#92f95616501069d07d10edb2fc37d3e1c65123b7"
  integrity sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==

aws-sdk@^2.1324.0:
  version "2.1414.0"
  resolved "https://registry.yarnpkg.com/aws-sdk/-/aws-sdk-2.1414.0.tgz#5888319adba0e98fc95f7b6044bb598edcbf6713"
  integrity sha512-WhqTWiTZRUxWITvUG5VMPYGdCLNAm4zOTDIiotbErR9x+uDExk2CAGbXE8HH11+tD8PhZVXyukymSiG+7rJMMg==
  dependencies:
    buffer "4.9.2"
    events "1.1.1"
    ieee754 "1.1.13"
    jmespath "0.16.0"
    querystring "0.2.0"
    sax "1.2.1"
    url "0.10.3"
    util "^0.12.4"
    uuid "8.0.0"
    xml2js "0.5.0"

aws-xray-sdk-core@3.5.0, aws-xray-sdk-core@^3.3.4, aws-xray-sdk-core@^3.4.1:
  version "3.5.0"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk-core/-/aws-xray-sdk-core-3.5.0.tgz#72ec1f5215477b5e04a745cb638a6e0f8c91a707"
  integrity sha512-T3mL9mGwnfGyZrf7RsZp702+prTCEMzX7zrqD7flwMZeb6ymXlSgREmeXys80r/9CHFgq/+JR+IclM+hep0yRw==
  dependencies:
    "@aws-sdk/service-error-classification" "^3.4.1"
    "@aws-sdk/types" "^3.4.1"
    "@types/cls-hooked" "^4.3.3"
    atomic-batcher "^1.0.2"
    cls-hooked "^4.2.2"
    semver "^7.3.8"

aws-xray-sdk-express@3.5.0:
  version "3.5.0"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk-express/-/aws-xray-sdk-express-3.5.0.tgz#a5af51af4f415d51f65423aa7a7e0cbfb856b372"
  integrity sha512-nbdghqiHPUe7P04guh/fkBmHCsbY5pwQrDFJAqYimNK3EsB+pq2/FW7m9vodPmHSbqeTgwO/oQUiCnEQ9luf5A==
  dependencies:
    "@types/express" "*"

aws-xray-sdk-mysql@3.5.0:
  version "3.5.0"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk-mysql/-/aws-xray-sdk-mysql-3.5.0.tgz#2bb4ddc671c5349b97100fbc957bda3b4dfa0dae"
  integrity sha512-vAEfSPMQq48Fg3H45voljAJT8KX79tLEzW1qz6Kb9Gqv11wihu05074L/DArQnvHnG0DaCPttZD7/nOGFc6sYA==
  dependencies:
    "@types/mysql" "*"

aws-xray-sdk-postgres@3.5.0:
  version "3.5.0"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk-postgres/-/aws-xray-sdk-postgres-3.5.0.tgz#3e73049908e5446ba0e1c3d165a2383ca170ec91"
  integrity sha512-3LadT1yuZYLS9RlqdgDQt5z3vojevrggnuGofT2EqsS5EQBVz7NNLYrJdTJsd4TYVVsToNxDBQI5peb2HC8TRw==
  dependencies:
    "@types/pg" "*"

aws-xray-sdk@^3.3.4:
  version "3.5.0"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk/-/aws-xray-sdk-3.5.0.tgz#ac487761ef8952fd772f36f9aaa69c9a663e61ca"
  integrity sha512-u9iKR9QAafeoCVk4J1PH6/NbNc/dNsF5T9CoNAST7bMLnGwT9YN4l+N9zxLbuspWv8cdBPoz8dDfbyZNDHgYQQ==
  dependencies:
    aws-xray-sdk-core "3.5.0"
    aws-xray-sdk-express "3.5.0"
    aws-xray-sdk-mysql "3.5.0"
    aws-xray-sdk-postgres "3.5.0"

axios@^1.6.7:
  version "1.6.8"
  resolved "https://registry.yarnpkg.com/axios/-/axios-1.6.8.tgz#66d294951f5d988a00e87a0ffb955316a619ea66"
  integrity sha512-v/ZHtJDU39mDpyBoFVkETcd/uNdxrWRrg3bKpOKzXFA6Bvqopts6ALSMU3y6ijYxbw2B+wPrIv46egTzJXCLGQ==
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

base64-js@^1.0.2, base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://registry.yarnpkg.com/base64-js/-/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

basic-ftp@^5.0.1:
  version "5.0.3"
  resolved "https://registry.yarnpkg.com/basic-ftp/-/basic-ftp-5.0.3.tgz#b14c0fe8111ce001ec913686434fe0c2fb461228"
  integrity sha512-QHX8HLlncOLpy54mh+k/sWIFd0ThmRqwe9ZjELybGZK+tZ8rUb9VO0saKJUROTbE+KhzDUT7xziGpGrW8Kmd+g==

bowser@^2.11.0:
  version "2.11.0"
  resolved "https://registry.yarnpkg.com/bowser/-/bowser-2.11.0.tgz#5ca3c35757a7aa5771500c70a73a9f91ef420a8f"
  integrity sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==

bson@^4.7.2:
  version "4.7.2"
  resolved "https://registry.yarnpkg.com/bson/-/bson-4.7.2.tgz#320f4ad0eaf5312dd9b45dc369cc48945e2a5f2e"
  integrity sha512-Ry9wCtIZ5kGqkJoi6aD8KjxFZEx78guTQDnpXWiNthsxzrxAK/i8E6pCHAIZTbaEFWcOCvbecMukfK7XUvyLpQ==
  dependencies:
    buffer "^5.6.0"

buffer@4.9.2:
  version "4.9.2"
  resolved "https://registry.yarnpkg.com/buffer/-/buffer-4.9.2.tgz#230ead344002988644841ab0244af8c44bbe3ef8"
  integrity sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

buffer@^5.6.0:
  version "5.7.1"
  resolved "https://registry.yarnpkg.com/buffer/-/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

call-bind@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/call-bind/-/call-bind-1.0.2.tgz#b1d4e89e688119c3c9a903ad30abb2f6a919be3c"
  integrity sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

cls-hooked@^4.2.2:
  version "4.2.2"
  resolved "https://registry.yarnpkg.com/cls-hooked/-/cls-hooked-4.2.2.tgz#ad2e9a4092680cdaffeb2d3551da0e225eae1908"
  integrity sha512-J4Xj5f5wq/4jAvcdgoGsL3G103BtWpZrMo8NEinRltN+xpTZdI+M38pyQqhuFU/P792xkMFvnKSf+Lm81U1bxw==
  dependencies:
    async-hook-jl "^1.7.6"
    emitter-listener "^1.0.1"
    semver "^5.4.1"

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.yarnpkg.com/combined-stream/-/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

debug@4.x:
  version "4.3.4"
  resolved "https://registry.yarnpkg.com/debug/-/debug-4.3.4.tgz#1319f6579357f2338d3337d2cdd4914bb5dcc865"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

emitter-listener@^1.0.1:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/emitter-listener/-/emitter-listener-1.1.2.tgz#56b140e8f6992375b3d7cb2cab1cc7432d9632e8"
  integrity sha512-Bt1sBAGFHY9DKY+4/2cV6izcKJUf5T7/gkdmkxzX/qv9CcGH8xSwVRW5mtX03SWJtRTWSOpzCuWN9rBFYZepZQ==
  dependencies:
    shimmer "^1.2.0"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/esprima/-/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==

events@1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/events/-/events-1.1.1.tgz#9ebdb7635ad099c70dcc4c2a1f5004288e8bd924"
  integrity sha512-kEcvvCBByWXGnZy6JUlgAp2gBIUjfCAV6P6TgT1/aaQKcmuAEC4OZTV1I4EWQLz2gxZw76atuVyvHhTxvi0Flw==

fast-xml-parser@4.2.5, fast-xml-parser@^4.0.1:
  version "4.2.5"
  resolved "https://registry.yarnpkg.com/fast-xml-parser/-/fast-xml-parser-4.2.5.tgz#a6747a09296a6cb34f2ae634019bf1738f3b421f"
  integrity sha512-B9/wizE4WngqQftFPmdaMYlXoJlJOYxGQOanC77fq9k8+Z0v5dDSVh+3glErdIROP//s/jgb7ZuxKfB8nVyo0g==
  dependencies:
    strnum "^1.0.5"

follow-redirects@^1.15.6:
  version "1.15.6"
  resolved "https://registry.yarnpkg.com/follow-redirects/-/follow-redirects-1.15.6.tgz#7f815c0cda4249c74ff09e95ef97c23b5fd0399b"
  integrity sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==

for-each@^0.3.3:
  version "0.3.3"
  resolved "https://registry.yarnpkg.com/for-each/-/for-each-0.3.3.tgz#69b447e88a0a5d32c3e7084f3f1710034b21376e"
  integrity sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==
  dependencies:
    is-callable "^1.1.3"

form-data@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/form-data/-/form-data-4.0.0.tgz#93919daeaf361ee529584b9b31664dc12c9fa452"
  integrity sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

fs-extra@^10.0.0:
  version "10.1.0"
  resolved "https://registry.yarnpkg.com/fs-extra/-/fs-extra-10.1.0.tgz#02873cfbc4084dde127eaa5f9905eef2325d1abf"
  integrity sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs@^0.0.1-security:
  version "0.0.1-security"
  resolved "https://registry.yarnpkg.com/fs/-/fs-0.0.1-security.tgz#8a7bd37186b6dddf3813f23858b57ecaaf5e41d4"
  integrity sha512-3XY9e1pP0CVEUCdj5BmfIZxRBTSDycnbqhIOGec9QYtmVH2fbLpj86CFWkrNOkt/Fvty4KZG5lTglL9j/gJ87w==

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/function-bind/-/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==

get-intrinsic@^1.0.2, get-intrinsic@^1.1.3:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/get-intrinsic/-/get-intrinsic-1.2.1.tgz#d295644fed4505fc9cde952c37ee12b477a83d82"
  integrity sha512-2DcsyfABl+gVHEfCOaTrWgyt+tb6MSEGmKq+kI5HwLbIYgjgmMcV8KQ41uaKz1xxUcn9tJtgFbQUEVcEbd0FYw==
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"

gopd@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/gopd/-/gopd-1.0.1.tgz#29ff76de69dac7489b7c0918a5788e56477c332c"
  integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
  dependencies:
    get-intrinsic "^1.1.3"

graceful-fs@^4.1.6, graceful-fs@^4.2.0:
  version "4.2.11"
  resolved "https://registry.yarnpkg.com/graceful-fs/-/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

has-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/has-proto/-/has-proto-1.0.1.tgz#1885c1305538958aff469fef37937c22795408e0"
  integrity sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==

has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/has-symbols/-/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"
  integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/has-tostringtag/-/has-tostringtag-1.0.0.tgz#7e133818a7d394734f941e73c3d3f9291e658b25"
  integrity sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==
  dependencies:
    has-symbols "^1.0.2"

has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/has/-/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==
  dependencies:
    function-bind "^1.1.1"

ieee754@1.1.13:
  version "1.1.13"
  resolved "https://registry.yarnpkg.com/ieee754/-/ieee754-1.1.13.tgz#ec168558e95aa181fd87d37f55c32bbcb6708b84"
  integrity sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg==

ieee754@^1.1.13, ieee754@^1.1.4:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/ieee754/-/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==

inherits@^2.0.3:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

install@^0.13.0:
  version "0.13.0"
  resolved "https://registry.yarnpkg.com/install/-/install-0.13.0.tgz#6af6e9da9dd0987de2ab420f78e60d9c17260776"
  integrity sha512-zDml/jzr2PKU9I8J/xyZBQn8rPCAY//UOYNmR01XwNwyfhEWObo2SWfSl1+0tm1u6PhxLwDnfsT/6jB7OUxqFA==

ip@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/ip/-/ip-2.0.0.tgz#4cf4ab182fee2314c75ede1276f8c80b479936da"
  integrity sha512-WKa+XuLG1A1R0UWhl2+1XQSi+fZWMsYKffMZTTYsiZaUD8k2yDAj5atimTUD2TZkyCkNEeYE5NhFZmupOGtjYQ==

is-arguments@^1.0.4:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/is-arguments/-/is-arguments-1.1.1.tgz#15b3f88fda01f2a97fec84ca761a560f123efa9b"
  integrity sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-callable@^1.1.3:
  version "1.2.7"
  resolved "https://registry.yarnpkg.com/is-callable/-/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==

is-generator-function@^1.0.7:
  version "1.0.10"
  resolved "https://registry.yarnpkg.com/is-generator-function/-/is-generator-function-1.0.10.tgz#f1558baf1ac17e0deea7c0415c438351ff2b3c72"
  integrity sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==
  dependencies:
    has-tostringtag "^1.0.0"

is-typed-array@^1.1.10, is-typed-array@^1.1.3:
  version "1.1.10"
  resolved "https://registry.yarnpkg.com/is-typed-array/-/is-typed-array-1.1.10.tgz#36a5b5cb4189b575d1a3e4b08536bfb485801e3f"
  integrity sha512-PJqgEHiWZvMpaFZ3uTc8kHPM4+4ADTlDniuQL7cU/UDA0Ql7F70yGfHph3cLNe+c9toaigv+DFzTJKhc2CtO6A==
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"

isarray@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==

jmespath@0.16.0:
  version "0.16.0"
  resolved "https://registry.yarnpkg.com/jmespath/-/jmespath-0.16.0.tgz#b15b0a85dfd4d930d43e69ed605943c802785076"
  integrity sha512-9FzQjJ7MATs1tSpnco1K6ayiYE3figslrXA72G2HQ/n76RzvYlofyi5QM+iX4YRs/pu3yzxlVQSST23+dMDknw==

js-md5@^0.7.3:
  version "0.7.3"
  resolved "https://registry.yarnpkg.com/js-md5/-/js-md5-0.7.3.tgz#b4f2fbb0b327455f598d6727e38ec272cd09c3f2"
  integrity sha512-ZC41vPSTLKGwIRjqDh8DfXoCrdQIyBgspJVPXHBGu4nZlAEvG3nf+jO9avM9RmLiGakg7vz974ms99nEV0tmTQ==

js-sha1@^0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/js-sha1/-/js-sha1-0.6.0.tgz#adbee10f0e8e18aa07cdea807cf08e9183dbc7f9"
  integrity sha512-01gwBFreYydzmU9BmZxpVk6svJJHrVxEN3IOiGl6VO93bVKYETJ0sIth6DASI6mIFdt7NmfX9UiByRzsYHGU9w==

js-yaml@3.14.1:
  version "3.14.1"
  resolved "https://registry.yarnpkg.com/js-yaml/-/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.yarnpkg.com/jsonfile/-/jsonfile-6.1.0.tgz#bc55b2634793c679ec6403094eb13698a6ec0aae"
  integrity sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

kareem@2.5.1:
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/kareem/-/kareem-2.5.1.tgz#7b8203e11819a8e77a34b3517d3ead206764d15d"
  integrity sha512-7jFxRVm+jD+rkq3kY0iZDJfsO2/t4BBPeEb2qKn2lR/9KhuksYk5hxzfRYWMPV8P/x2d0kHD306YyWLzjjH+uA==

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.yarnpkg.com/lodash.merge/-/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.yarnpkg.com/lru-cache/-/lru-cache-6.0.0.tgz#6d6fe6570ebd96aaf90fcad1dafa3b2566db3a94"
  integrity sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==
  dependencies:
    yallist "^4.0.0"

memory-pager@^1.0.2:
  version "1.5.0"
  resolved "https://registry.yarnpkg.com/memory-pager/-/memory-pager-1.5.0.tgz#d8751655d22d384682741c972f2c3d6dfa3e66b5"
  integrity sha512-ZS4Bp4r/Zoeq6+NLJpP+0Zzm0pR8whtGPf1XExKLJBAczGMnSi3It14OiNCStjQjM6NU1okjQGSxgEZN8eBYKg==

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12:
  version "2.1.35"
  resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

moment@^2.29.1:
  version "2.29.4"
  resolved "https://registry.yarnpkg.com/moment/-/moment-2.29.4.tgz#3dbe052889fe7c1b2ed966fcb3a77328964ef108"
  integrity sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==

mongodb-connection-string-url@^2.5.4:
  version "2.6.0"
  resolved "https://registry.yarnpkg.com/mongodb-connection-string-url/-/mongodb-connection-string-url-2.6.0.tgz#57901bf352372abdde812c81be47b75c6b2ec5cf"
  integrity sha512-WvTZlI9ab0QYtTYnuMLgobULWhokRjtC7db9LtcVfJ+Hsnyr5eo6ZtNAt3Ly24XZScGMelOcGtm7lSn0332tPQ==
  dependencies:
    "@types/whatwg-url" "^8.2.1"
    whatwg-url "^11.0.0"

mongodb@4.16.0:
  version "4.16.0"
  resolved "https://registry.yarnpkg.com/mongodb/-/mongodb-4.16.0.tgz#8b0043de7b577c6a7e0ce44a2ca7315b9c0a7927"
  integrity sha512-0EB113Fsucaq1wsY0dOhi1fmZOwFtLOtteQkiqOXGklvWMnSH3g2QS53f0KTP+/6qOkuoXE2JksubSZNmxeI+g==
  dependencies:
    bson "^4.7.2"
    mongodb-connection-string-url "^2.5.4"
    socks "^2.7.1"
  optionalDependencies:
    "@aws-sdk/credential-providers" "^3.186.0"
    saslprep "^1.0.3"

mongoose@^6.1.3:
  version "6.11.3"
  resolved "https://registry.yarnpkg.com/mongoose/-/mongoose-6.11.3.tgz#26e5de0437c470f09c5a71a188a75718efc6c84a"
  integrity sha512-M1Y5PjttgV51YDa30u7GVMVypQSlNZF/jUhlzTBAmaz5C9FvOr8eih/VLhhO7xtTSlcVTFQS1dqlQNMbtfUowQ==
  dependencies:
    bson "^4.7.2"
    kareem "2.5.1"
    mongodb "4.16.0"
    mpath "0.9.0"
    mquery "4.0.3"
    ms "2.1.3"
    sift "16.0.1"

mpath@0.9.0:
  version "0.9.0"
  resolved "https://registry.yarnpkg.com/mpath/-/mpath-0.9.0.tgz#0c122fe107846e31fc58c75b09c35514b3871904"
  integrity sha512-ikJRQTk8hw5DEoFVxHG1Gn9T/xcjtdnOKIU1JTmGjZZlg9LST2mBLmcX3/ICIbgJydT2GOc15RnNy5mHmzfSew==

mquery@4.0.3:
  version "4.0.3"
  resolved "https://registry.yarnpkg.com/mquery/-/mquery-4.0.3.tgz#4d15f938e6247d773a942c912d9748bd1965f89d"
  integrity sha512-J5heI+P08I6VJ2Ky3+33IpCdAvlYGTSUjwTPxkAr8i8EoduPMBX2OY/wa3IKZIQl7MU4SbFk8ndgSKyB/cl1zA==
  dependencies:
    debug "4.x"

ms@2.1.2:
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/ms/-/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

ms@2.1.3:
  version "2.1.3"
  resolved "https://registry.yarnpkg.com/ms/-/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

obuf@~1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/obuf/-/obuf-1.1.2.tgz#09bea3343d41859ebd446292d11c9d4db619084e"
  integrity sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==

pg-int8@1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/pg-int8/-/pg-int8-1.0.1.tgz#943bd463bf5b71b4170115f80f8efc9a0c0eb78c"
  integrity sha512-WCtabS6t3c8SkpDBUlb1kjOs7l66xsGdKpIPZsg4wR+B3+u9UAum2odSsF9tnvxg80h4ZxLWMy4pRjOsFIqQpw==

pg-numeric@1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/pg-numeric/-/pg-numeric-1.0.2.tgz#816d9a44026086ae8ae74839acd6a09b0636aa3a"
  integrity sha512-BM/Thnrw5jm2kKLE5uJkXqqExRUY/toLHda65XgFTBTFYZyopbKjBe29Ii3RbkvlsMoFwD+tHeGaCjjv0gHlyw==

pg-protocol@*:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/pg-protocol/-/pg-protocol-1.6.0.tgz#4c91613c0315349363af2084608db843502f8833"
  integrity sha512-M+PDm637OY5WM307051+bsDia5Xej6d9IR4GwJse1qA1DIhiKlksvrneZOYQq42OM+spubpcNYEo2FcKQrDk+Q==

pg-types@^4.0.1:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/pg-types/-/pg-types-4.0.1.tgz#31857e89d00a6c66b06a14e907c3deec03889542"
  integrity sha512-hRCSDuLII9/LE3smys1hRHcu5QGcLs9ggT7I/TCs0IE+2Eesxi9+9RWAAwZ0yaGjxoWICF/YHLOEjydGujoJ+g==
  dependencies:
    pg-int8 "1.0.1"
    pg-numeric "1.0.2"
    postgres-array "~3.0.1"
    postgres-bytea "~3.0.0"
    postgres-date "~2.0.1"
    postgres-interval "^3.0.0"
    postgres-range "^1.1.1"

pify@^5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/pify/-/pify-5.0.0.tgz#1f5eca3f5e87ebec28cc6d54a0e4aaf00acc127f"
  integrity sha512-eW/gHNMlxdSP6dmG6uJip6FXN0EQBwm2clYYd8Wul42Cwu/DK8HEftzsapcNdYe2MfLiIwZqsDk2RDEsTE79hA==

postgres-array@~3.0.1:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/postgres-array/-/postgres-array-3.0.2.tgz#68d6182cb0f7f152a7e60dc6a6889ed74b0a5f98"
  integrity sha512-6faShkdFugNQCLwucjPcY5ARoW1SlbnrZjmGl0IrrqewpvxvhSLHimCVzqeuULCbG0fQv7Dtk1yDbG3xv7Veog==

postgres-bytea@~3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/postgres-bytea/-/postgres-bytea-3.0.0.tgz#9048dc461ac7ba70a6a42d109221619ecd1cb089"
  integrity sha512-CNd4jim9RFPkObHSjVHlVrxoVQXz7quwNFpz7RY1okNNme49+sVyiTvTRobiLV548Hx/hb1BG+iE7h9493WzFw==
  dependencies:
    obuf "~1.1.2"

postgres-date@~2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/postgres-date/-/postgres-date-2.0.1.tgz#638b62e5c33764c292d37b08f5257ecb09231457"
  integrity sha512-YtMKdsDt5Ojv1wQRvUhnyDJNSr2dGIC96mQVKz7xufp07nfuFONzdaowrMHjlAzY6GDLd4f+LUHHAAM1h4MdUw==

postgres-interval@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/postgres-interval/-/postgres-interval-3.0.0.tgz#baf7a8b3ebab19b7f38f07566c7aab0962f0c86a"
  integrity sha512-BSNDnbyZCXSxgA+1f5UU2GmwhoI0aU5yMxRGO8CdFEcY2BQF9xm/7MqKnYoM1nJDk8nONNWDk9WeSmePFhQdlw==

postgres-range@^1.1.1:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/postgres-range/-/postgres-range-1.1.3.tgz#9ccd7b01ca2789eb3c2e0888b3184225fa859f76"
  integrity sha512-VdlZoocy5lCP0c/t66xAfclglEapXPCIVhqqJRncYpvbCgImF0w67aPKfbqUMr72tO2k5q0TdTZwCLjPTI6C9g==

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz#e102f16ca355424865755d2c9e8ea4f24d58c3e2"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"
  integrity sha512-RofWgt/7fL5wP1Y7fxE7/EmTLzQVnB0ycyibJ0OOHIlJqTNzglYFxVwETOcIoJqJmpDXJ9xImDv+Fq34F/d4Dw==

punycode@^2.1.1:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-2.3.0.tgz#f67fa67c94da8f4d0cfff981aee4118064199b8f"
  integrity sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA==

querystring@0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/querystring/-/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"
  integrity sha512-X/xY82scca2tau62i9mDyU9K+I+djTMUsvwf7xnUX5GLvVzgJybOJf4Y6o9Zx3oJK/LSXg5tTZBjwzqVPaPO2g==

saslprep@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/saslprep/-/saslprep-1.0.3.tgz#4c02f946b56cf54297e347ba1093e7acac4cf226"
  integrity sha512-/MY/PEMbk2SuY5sScONwhUDsV2p77Znkb/q3nSVstq/yQzYJOH/Azh29p9oJLsl3LnQwSvZDKagDGBsBwSooag==
  dependencies:
    sparse-bitfield "^3.0.3"

sax@1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/sax/-/sax-1.2.1.tgz#7b8e656190b228e81a66aea748480d828cd2d37a"
  integrity sha512-8I2a3LovHTOpm7NV5yOyO8IHqgVsfK4+UuySrXU8YXkSRX7k6hCV9b3HrkKCr3nMpgj+0bmocaJJWpvp1oc7ZA==

sax@>=0.6.0:
  version "1.2.4"
  resolved "https://registry.yarnpkg.com/sax/-/sax-1.2.4.tgz#2816234e2378bddc4e5354fab5caa895df7100d9"
  integrity sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw==

semver@^5.4.1:
  version "5.7.2"
  resolved "https://registry.yarnpkg.com/semver/-/semver-5.7.2.tgz#48d55db737c3287cd4835e17fa13feace1c41ef8"
  integrity sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==

semver@^7.3.8:
  version "7.5.4"
  resolved "https://registry.yarnpkg.com/semver/-/semver-7.5.4.tgz#483986ec4ed38e1c6c48c34894a9182dbff68a6e"
  integrity sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==
  dependencies:
    lru-cache "^6.0.0"

shimmer@^1.2.0:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/shimmer/-/shimmer-1.2.1.tgz#610859f7de327b587efebf501fb43117f9aff337"
  integrity sha512-sQTKC1Re/rM6XyFM6fIAGHRPVGvyXfgzIDvzoq608vM+jeyVD0Tu1E6Np0Kc2zAIFWIj963V2800iF/9LPieQw==

sift@16.0.1:
  version "16.0.1"
  resolved "https://registry.yarnpkg.com/sift/-/sift-16.0.1.tgz#e9c2ccc72191585008cf3e36fc447b2d2633a053"
  integrity sha512-Wv6BjQ5zbhW7VFefWusVP33T/EM0vYikCaQ2qR8yULbsilAT8/wQaXvuQ3ptGLpoKx+lihJE3y2UTgKDyyNHZQ==

smart-buffer@^4.2.0:
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/smart-buffer/-/smart-buffer-4.2.0.tgz#6e1d71fa4f18c05f7d0ff216dd16a481d0e8d9ae"
  integrity sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==

socks@^2.7.1:
  version "2.7.1"
  resolved "https://registry.yarnpkg.com/socks/-/socks-2.7.1.tgz#d8e651247178fde79c0663043e07240196857d55"
  integrity sha512-7maUZy1N7uo6+WVEX6psASxtNlKaNVMlGQKkG/63nEDdLOWNbiUMoLK7X4uYoLhQstau72mLgfEWcXcwsaHbYQ==
  dependencies:
    ip "^2.0.0"
    smart-buffer "^4.2.0"

sparse-bitfield@^3.0.3:
  version "3.0.3"
  resolved "https://registry.yarnpkg.com/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz#ff4ae6e68656056ba4b3e792ab3334d38273ca11"
  integrity sha512-kvzhi7vqKTfkh0PZU+2D2PIllw2ymqJKujUcyPMd9Y75Nv4nPbGJZXNhxsgdQab2BmlDct1YnfQCguEvHr7VsQ==
  dependencies:
    memory-pager "^1.0.2"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==

stack-chain@^1.3.7:
  version "1.3.7"
  resolved "https://registry.yarnpkg.com/stack-chain/-/stack-chain-1.3.7.tgz#d192c9ff4ea6a22c94c4dd459171e3f00cea1285"
  integrity sha512-D8cWtWVdIe/jBA7v5p5Hwl5yOSOrmZPWDPe2KxQ5UAGD+nxbxU0lKXA4h85Ta6+qgdKVL3vUxsbIZjc1kBG7ug==

strnum@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/strnum/-/strnum-1.0.5.tgz#5c4e829fe15ad4ff0d20c3db5ac97b73c9b072db"
  integrity sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==

tr46@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/tr46/-/tr46-3.0.0.tgz#555c4e297a950617e8eeddef633c87d4d9d6cbf9"
  integrity sha512-l7FvfAHlcmulp8kr+flpQZmVwtu7nfRV7NZujtN0OqES8EL4O4e0qqzL0DC5gAvx/ZC/9lk6rhcUwYvkBnBnYA==
  dependencies:
    punycode "^2.1.1"

tslib@^1.11.1:
  version "1.14.1"
  resolved "https://registry.yarnpkg.com/tslib/-/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==

tslib@^2.3.1, tslib@^2.5.0:
  version "2.6.0"
  resolved "https://registry.yarnpkg.com/tslib/-/tslib-2.6.0.tgz#b295854684dbda164e181d259a22cd779dcd7bc3"
  integrity sha512-7At1WUettjcSRHXCyYtTselblcHl9PJFFVKiCAy/bY97+BPZXSQ2wbq0P9s8tK2G7dFQfNnlJnPAiArVBVBsfA==

universalify@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/universalify/-/universalify-2.0.0.tgz#75a4984efedc4b08975c5aeb73f530d02df25717"
  integrity sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ==

url@0.10.3:
  version "0.10.3"
  resolved "https://registry.yarnpkg.com/url/-/url-0.10.3.tgz#021e4d9c7705f21bbf37d03ceb58767402774c64"
  integrity sha512-hzSUW2q06EqL1gKM/a+obYHLIO6ct2hwPuviqTTOcfFVc61UbfJ2Q32+uGL/HCPxKqrdGB5QUwIe7UqlDgwsOQ==
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

util@^0.12.4:
  version "0.12.5"
  resolved "https://registry.yarnpkg.com/util/-/util-0.12.5.tgz#5f17a6059b73db61a875668781a1c2b136bd6fbc"
  integrity sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

uuid-by-string@^3.0.4:
  version "3.0.7"
  resolved "https://registry.yarnpkg.com/uuid-by-string/-/uuid-by-string-3.0.7.tgz#3c9b7e60c3d4a1bf5da5dfb2601721acc813d8fc"
  integrity sha512-9xf+GAcwzLLGL2Z2Vb7hmi7jWIAKSiuaI5cLFsKw1IIlm7S5VpqvdJ5S7N36hqdy0v7DAwnnENJVAeev57/H1A==
  dependencies:
    js-md5 "^0.7.3"
    js-sha1 "^0.6.0"

uuid@8.0.0:
  version "8.0.0"
  resolved "https://registry.yarnpkg.com/uuid/-/uuid-8.0.0.tgz#bc6ccf91b5ff0ac07bbcdbf1c7c4e150db4dbb6c"
  integrity sha512-jOXGuXZAWdsTH7eZLtyXMqUb9EcWMGZNbL9YcGBJl4MH4nrxHmZJhEHvyLFrkxo+28uLb/NYRcStH48fnD0Vzw==

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://registry.yarnpkg.com/uuid/-/uuid-8.3.2.tgz#80d5b5ced271bb9af6c445f21a1a04c606cefbe2"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

validator@^13.7.0:
  version "13.9.0"
  resolved "https://registry.yarnpkg.com/validator/-/validator-13.9.0.tgz#33e7b85b604f3bbce9bb1a05d5c3e22e1c2ff855"
  integrity sha512-B+dGG8U3fdtM0/aNK4/X8CXq/EcxU2WPrPEkJGslb47qyHsxmbggTWK0yEA4qnYVNF+nxNlN88o14hIcPmSIEA==

webidl-conversions@^7.0.0:
  version "7.0.0"
  resolved "https://registry.yarnpkg.com/webidl-conversions/-/webidl-conversions-7.0.0.tgz#256b4e1882be7debbf01d05f0aa2039778ea080a"
  integrity sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==

whatwg-url@^11.0.0:
  version "11.0.0"
  resolved "https://registry.yarnpkg.com/whatwg-url/-/whatwg-url-11.0.0.tgz#0a849eebb5faf2119b901bb76fd795c2848d4018"
  integrity sha512-RKT8HExMpoYx4igMiVMY83lN6UeITKJlBQ+vR/8ZJ8OCdSiN3RwCq+9gH0+Xzj0+5IrM6i4j/6LuvzbZIQgEcQ==
  dependencies:
    tr46 "^3.0.0"
    webidl-conversions "^7.0.0"

which-typed-array@^1.1.2:
  version "1.1.10"
  resolved "https://registry.yarnpkg.com/which-typed-array/-/which-typed-array-1.1.10.tgz#74baa2789991905c2076abb317103b866c64e69e"
  integrity sha512-uxoA5vLUfRPdjCuJ1h5LlYdmTLbYfums398v3WLkM+i/Wltl2/XyZpQWKbN++ck5L64SR/grOHqtXCUKmlZPNA==
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"
    is-typed-array "^1.1.10"

xml2js@0.5.0:
  version "0.5.0"
  resolved "https://registry.yarnpkg.com/xml2js/-/xml2js-0.5.0.tgz#d9440631fbb2ed800203fad106f2724f62c493b7"
  integrity sha512-drPFnkQJik/O+uPKpqSgr22mpuFHqKdbS835iAQrUC73L2F5WkboIRd63ai/2Yg6I1jzifPFKH2NTK+cfglkIA==
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xml2js@^0.4.23:
  version "0.4.23"
  resolved "https://registry.yarnpkg.com/xml2js/-/xml2js-0.4.23.tgz#a0c69516752421eb2ac758ee4d4ccf58843eac66"
  integrity sha512-ySPiMjM0+pLDftHgXY4By0uswI3SPKLDw/i3UXbnO8M/p28zqexCUoPmQFrYD+/1BzhGJSs2i1ERWKJAtiLrug==
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder2@^3.0.2:
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/xmlbuilder2/-/xmlbuilder2-3.1.1.tgz#b977ef8a6fb27a1ea7ffa7d850d2c007ff343bc0"
  integrity sha512-WCSfbfZnQDdLQLiMdGUQpMxxckeQ4oZNMNhLVkcekTu7xhD4tuUDyAPoY8CwXvBYE6LwBHd6QW2WZXlOWr1vCw==
  dependencies:
    "@oozcitak/dom" "1.15.10"
    "@oozcitak/infra" "1.0.8"
    "@oozcitak/util" "8.3.8"
    js-yaml "3.14.1"

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "https://registry.yarnpkg.com/xmlbuilder/-/xmlbuilder-11.0.1.tgz#be9bae1c8a046e76b31127726347d0ad7002beb3"
  integrity sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/yallist/-/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==
