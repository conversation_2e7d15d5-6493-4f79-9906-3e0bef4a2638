/* Amplify Params - DO NOT EDIT
	ENV
	centaurSecrets
	AWS_LOCAL
Amplify Params - DO NOT EDIT */

const helper = require("./library/helper")
const centaur = require('@mediality/centaur')


var con

exports.handler = async (event) => {

    try {
        con = await helper.openDBConnection()

        var date = new Date()
        date.setDate(date.getDate() - 7);
        
        var clearOldDeliveryLogs = await centaur.delivery_logs.deleteMany({createdAt:{$lt:date.toISOString()}})
        console.log(clearOldDeliveryLogs)

        var clearOldChangeLogs =  await centaur.changelog.deleteMany({"changelog.0.time":{$lt:date.toISOString()}})
        console.log(clearOldChangeLogs)

        
        const response = {
            statusCode: 200,
            headers: { "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Headers": "*" },
            body: JSON.stringify({
                "changelogs": clearOldChangeLogs,
                "delivery_logs": clearOldDeliveryLogs
            })
        }
        return response

    } catch (err) {
        console.log("Error in Handler: " + err)
        await helper.closeDBConnection(con)
    } finally {
        await helper.closeDBConnection(con)

    }
};