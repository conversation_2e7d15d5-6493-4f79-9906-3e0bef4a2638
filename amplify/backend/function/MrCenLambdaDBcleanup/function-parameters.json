{"permissions": {}, "lambdaLayers": [{"type": "ProjectLayer", "resourceName": "centaurappCentaurAppCommonLayer", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "stgblack"}], "environmentVariableList": [{"cloudFormationParameterName": "region", "environmentVariableName": "REGION"}, {"cloudFormationParameterName": "env", "environmentVariableName": "ENV"}, {"cloudFormationParameterName": "awsLocal", "environmentVariableName": "AWS_LOCAL"}, {"cloudFormationParameterName": "centaurSecrets", "environmentVariableName": "centaurSecrets"}]}