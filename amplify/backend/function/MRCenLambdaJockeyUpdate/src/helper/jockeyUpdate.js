const centaur = require("@mediality/centaur");
const helper = require("../library/helper");
let con;
const updateJockeyData = async (jox, year, month) => {
  try {
    console.log("Updating jockey for", year, month, ":", jox);
    con = await helper.openDBConnection();
    const startDate = new Date(year, month - 1, 1); // Month is 0-indexed
    const endDate = new Date(year, month, 0, 23, 59, 59, 999);

    const joxIndex = await centaur.form_index
      .find({
        jockey_id: jox.JOC_JOCKEY_ID,
        meeting_date: { $gte: startDate, $lte: endDate },
      })
      .sort({ event_id: 1 })
      .lean();
    for (const run of joxIndex) {
      const horseForm = await centaur.form
        .findOne({ horse_id: run.horse_id })
        .lean();
      const horseDb = await centaur.horses.findOne({
        HRN_HORSE_ID: run.horse_id,
      });

      if (horseForm && horseForm.form && horseForm.form.length > 0) {
        for (
          let indexForm = 0;
          indexForm < horseForm.form.length;
          indexForm++
        ) {
          const formItem = horseForm.form[indexForm];
          if ( formItem && formItem.event_id == run.event_id) {
            await updateJockeyInForm(horseForm._id, indexForm, jox);
            if (run.finish_pos < 5) {
              await updateOtherRunners(
                run,
                horseForm.horse_name,
                jox,
                horseDb.HRN_HORSE_NAME
              );
            }
          }
        }
      }
    }
  } catch (error) {
    console.error("Error updating jockey data:", error);
    if (con) await helper.closeDBConnection(con);
  } finally {
    if (con) await helper.closeDBConnection(con);
  }
};

const updateJockeyInForm = async (formId, indexForm, jox) => {
  let updateData = {};
  console.log(`updateJockeyInForm JockeyInForm ${formId} ${indexForm}`);
  // Updating only the specific fields within the jockey object
  updateData[`form.${indexForm}.jockey.@_name`] = jox.JOC_JOCKEY_DISPLAYNAME;
  updateData[`form.${indexForm}.jockey.@_surname`] = jox.JOC_JOCKEY_SURNAME;
  updateData[`form.${indexForm}.jockey.@_firstname`] = jox.JOC_JOCKEY_FIRSTNAME;
  await centaur.form.updateOne({ _id: formId }, { $set: updateData }).lean();
};

const updateOtherRunners = async (run, horseName, jox, horseNameInDb) => {
  const formIndexRuns = await centaur.form_index
    .find({ event_id: run.event_id })
    .lean();
  for (const other of formIndexRuns) {
    const otherForm = await centaur.form
      .findOne({ horse_id: other.horse_id })
      .lean();

    if (otherForm && otherForm.form && otherForm.form.length > 0) {
      for (
        let otherIndex = 0;
        otherIndex < otherForm.form.length;
        otherIndex++
      ) {
        const otherItem = otherForm.form[otherIndex];

        if (otherItem && otherItem.event_id == run.event_id) {
          const others = otherItem.other_runners.other_runner;

          for (
            let otherRunnerIndex = 0;
            otherRunnerIndex < others.length;
            otherRunnerIndex++
          ) {
            if (
              others[otherRunnerIndex]["@_horse"].toLowerCase() ==
                horseName.toLowerCase() ||
              others[otherRunnerIndex]["@_horse"].toLowerCase() == horseNameInDb.toLowerCase()
            ) {
              others[otherRunnerIndex]["@_jockey"] = jox.JOC_JOCKEY_DISPLAYNAME;
              let dataToUpdateOther = `form.${otherIndex}.other_runners.other_runner`;
              await centaur.form
                .updateOne(
                  { _id: otherForm._id },
                  { $set: { [dataToUpdateOther]: others } }
                )
                .lean();
            }
          }
        }
      }
    }
  }
};

module.exports = { updateJockeyData };
