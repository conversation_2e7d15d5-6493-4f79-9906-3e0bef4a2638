/* Amplify Params - DO NOT EDIT
	ENV
	centaurSecrets
	AWS_LOCAL
Amplify Params - DO NOT EDIT */
const jockeyUpdate = require("./helper/jockeyUpdate");
/**
 * AWS Lambda function triggered by an event.
 * Logs the received event and context, as well as specific properties of the event object.
 * Calls the `updateHorseData` function from the `updateJockeyData` module, passing in the horse ID, display name, and previous names from the event object.
 * Returns a response with a status code of 200 and a success message if the update is successful.
 * Logs any error that occurs to the console.
 *
 * @param {object} event - An object containing information about the event that triggered the Lambda function.
 * @param {object} context - An object containing information about the execution context of the Lambda function.
 * @returns {object} - Success response object or error log.
 */
exports.handler = async (event, context) => {
  try {
    const year = event.year;
    const jox = event.jox;
    const month = event.month;
    console.log(year, jox);
    console.log("Received event:", JSON.stringify(event, null, 2));
    console.log("Context:", context);
    await jockeyUpdate.updateJockeyData(jox, year, month);
    return {
      statusCode: 200,
      body: JSON.stringify({ message: "Process completed successfully" }),
    };
  } catch (err) {
    console.log("err", err);
  }
};
