const moment = require('moment');
const mongoose = require("mongoose");
mongoose.set("debug", false);

const openDBConnection = async (env) => {
    const local = require("./local");
    const mongoose = require('mongoose');

    //if ((env == "local") || (env == "abhinav")) {
    if (env == "local") {
        //Local environment

        var cert_path = './rds-cert/global-bundle.pem'
        console.log('Running Locally')
        DB_USERNAME = local.DB_USERNAME
        DB_PASSWORD = local.DB_PASSWORD
        DB_URL = local.DB_URL
        DB_NAME = local.DB_NAME
        CONNECTION_STRING = "mongodb://" + DB_USERNAME + ":" + DB_PASSWORD + "@" + DB_URL + "/" + DB_NAME
        CONNECTION_STRING = "mongodb://root:Test.123!@127.0.0.1:27000/dev-centaur?directConnection=true&ssl=true&retrywrites=false&tls=true"
        await mongoose.connect(CONNECTION_STRING, {
            connectTimeoutMS: 1000,
            tlsCAFile: cert_path,
            directConnection: true,
            ssl: true,
            sslValidate: false,
            maxPoolSize: 10

        }).then(
            () => {
                console.log('Connected to Local')
                response = "Connect to Local"
            },
            err => {
                console.log('Not connected:' + err)
                response = "Not Connected to Local"
            });

    } else {
        console.log('Running Online')
        var aws_secrets = await getSecrets(process.env.centaurSecrets)
        DB_USERNAME = aws_secrets.DB_USERNAME
        DB_PASSWORD = aws_secrets.DB_PASSWORD
        DB_URL = aws_secrets.DB_URL
        DB_NAME = aws_secrets.DB_NAME
        CONNECTION_STRING = "mongodb://" + DB_USERNAME + ":" + DB_PASSWORD + "@" + DB_URL + "/" + DB_NAME + "?tls=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
        await mongoose.connect(CONNECTION_STRING, {
            connectTimeoutMS: 1000,
            tlsCAFile: cert_path,
            directConnection: true,
            ssl: true,
            sslValidate: false,
            maxPoolSize: 10
        }).then(
            () => {
                console.log('Connected to Online')
                response = "Connect Online"
            },
            err => {
                console.log('Not connected:' + err)
                response = "Not Connected Online"
            });
    }
    return mongoose
}


const closeDBConnection = async (con) => {

    con.connection.close()
    console.log("Connection Closed")

}
const getPastDateByDay = (days = 0) => {
    var date = moment();
    var result = date.subtract(days, 'day').format('YYYY-MM-DD') //+ "T00:00:00.000+00.00";
    return result
}
const cleanseBarrierTrials = (data) => {
    var cleanseddata = []
    for (const raceEvent of data) {
        // if (raceEvent.weight_carried > 0){
        //     cleanseddata.push(raceEvent)
        // }
        if ((raceEvent.classes && raceEvent.classes.class_id && raceEvent.classes.class_id != 90 && (!raceEvent.classes.second_class_id || raceEvent.classes.second_class_id != 90)) || (raceEvent.classes && raceEvent.classes.class && raceEvent.classes.class == "Open" && (!raceEvent.classes.second_class_id || raceEvent.classes.second_class_id != 90) )){
          cleanseddata.push(raceEvent)
        }
    }
    return cleanseddata
  }
const getBaseRatingByClass = (classId) => {
    var classRatingList = {
      196: 20,
      212: 20,
      213: 20,
      214: 20,
      197: 21,
      215: 21,
      216: 21,
      217: 21,
      218: 21,
      80: 22,
      81: 22,
      82: 22,
      19: 22,
      30: 22,
      83: 22,
      84: 22,
      97: 22,
      186: 22,
      187: 22,
      188: 22,
      189: 22,
      190: 22,
      198: 22,
      199: 22,
      200: 22,
      219: 22,
      220: 22,
      221: 22,
      222: 22,
      223: 22,
      224: 22,
      225: 22,
      226: 22,
      268: 22,
      269: 22,
      270: 22,
      271: 22,
      272: 22,
      273: 22,
      274: 22,
      275: 22,
      276: 22,
      315: 22,
      153: 23,
      201: 23,
      227: 23,
      228: 23,
      229: 23,
      230: 23,
      231: 23,
      277: 23,
      278: 23,
      279: 23,
      135: 24,
      154: 24,
      155: 24,
      156: 24,
      202: 24,
      211: 24,
      232: 24,
      233: 24,
      234: 24,
      235: 24,
      236: 24,
      280: 24,
      281: 24,
      282: 24,
      283: 24,
      284: 24,
      285: 24,
      286: 24,
      287: 24,
      288: 24,
      316: 24,
      157: 25,
      203: 25,
      237: 25,
      238: 25,
      239: 25,
      289: 25,
      290: 25,
      85: 26,
      240: 26,
      291: 26,
      292: 26,
      317: 26,
      293: 27,
      294: 27,
      18: 28,
      86: 28,
      98: 28,
      136: 28,
      158: 28,
      159: 28,
      204: 28,
      241: 28,
      242: 28,
      243: 28,
      244: 28,
      295: 28,
      296: 28,
      318: 28,
      137: 29,
      245: 29,
      246: 29,
      297: 29,
      54: 30,
      87: 30,
      138: 30,
      139: 30,
      140: 30,
      160: 30,
      161: 30,
      162: 30,
      205: 30,
      298: 30,
      299: 30,
      300: 30,
      301: 30,
      302: 30,
      319: 30,
      141: 31,
      142: 31,
      247: 31,
      303: 31,
      320: 31,
      17: 32,
      99: 32,
      143: 32,
      144: 32,
      145: 32,
      146: 32,
      163: 32,
      164: 32,
      165: 32,
      166: 32,
      248: 32,
      321: 32,
      147: 33,
      148: 33,
      249: 33,
      250: 33,
      304: 33,
      305: 33,
      322: 33,
      206: 34,
      251: 34,
      252: 34,
      306: 34,
      323: 34,
      88: 35,
      93: 35,
      100: 35,
      149: 35,
      167: 35,
      168: 35,
      169: 35,
      170: 35,
      171: 35,
      253: 35,
      254: 35,
      307: 35,
      309: 35,
      310: 35,
      311: 35,
      312: 35,
      313: 35,
      314: 35,
      324: 35,
      150: 36,
      151: 36,
      152: 36,
      207: 36,
      255: 36,
      256: 36,
      308: 36,
      325: 36,
      89: 37,
      172: 37,
      173: 37,
      174: 37,
      175: 37,
      257: 37,
      258: 37,
      326: 37,
      176: 38,
      177: 38,
      178: 38,
      179: 38,
      208: 38,
      259: 38,
      260: 38,
      180: 39,
      181: 39,
      182: 39,
      183: 39,
      184: 39,
      261: 39,
      262: 39,
      16: 40,
      185: 40,
      191: 40,
      192: 40,
      193: 40,
      194: 40,
      195: 40,
      209: 40,
      263: 40,
      264: 40,
      210: 41,
      265: 41,
      266: 41,
    };
    if (classRatingList[classId]) {
      return classRatingList[classId];
    } else {
      return 30;
    }
};
const getSecrets = async (secretName) => {
    const AWS = require('aws-sdk');
    var secretsmanager = new AWS.SecretsManager();
    var params = {
        SecretId: secretName,
    };
    const fetchSecretString = await secretsmanager.getSecretValue(params).promise();
    aws_secrets = JSON.parse(fetchSecretString.SecretString)
    return aws_secrets
}
module.exports = {
    getBaseRatingByClass,
    cleanseBarrierTrials,
    getPastDateByDay,
    closeDBConnection,
    openDBConnection,
    getSecrets
};
