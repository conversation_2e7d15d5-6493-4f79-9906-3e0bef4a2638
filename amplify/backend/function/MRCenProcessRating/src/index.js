const mongoose = require("mongoose");
const moment = require("moment");
const helper = require("./helper");
// Import Models
const centaur = require("@mediality/centaur");
mongoose.set("debug", false);

exports.handler = async (event) => {
  console.log(`EVENT: ${JSON.stringify(event)}`);
  let meetingId = 100;
  console.log(`meetingId 1: ${meetingId} `);
  const payload = {
    meetingId: meetingId,
  };
  console.log(`Payload: 2: ${JSON.stringify(payload)}`);
  await processFormRatings(JSON.stringify(payload));
  return {
    statusCode: 200,
    body: JSON.stringify("Compare meeting done successfully!"),
  };
};

const processFormRatings = async (ratFileContent) => {
  console.log(`Processing file: ${ratFileContent}`);
  const pastDate = new Date(helper.getPastDateByDay(730));
  console.log(pastDate.toString());
  let con = await helper.openDBConnection(process.env.ENV);

  const calculateStats = (arr) => {
    if (arr.length > 0) {
      const filteredArr = arr.filter(num => num !== "");
      const sum = filteredArr.reduce((a, b) => a + b, 0);
      return {
        avg: Math.round((sum / filteredArr.length) * 100) / 100,
        min: Math.round(Math.min(...filteredArr) * 100) / 100,
        max: Math.round(Math.max(...filteredArr) * 100) / 100,
      };
    }
    return { avg: "", min: "", max: "" };
  };

  try {
    // Use aggregation pipeline to get unique trainer_ids and pre-filter data
    const uniqueTrainersData = await centaur.form_index.aggregate([
      {
        $match: { meeting_date: { $gt: pastDate }, weight_carried: { $gt: 0 } },
      },
      {
        $group: {
          _id: "$trainer_id",
          horses: { $addToSet: "$horse_id" },
        },
      },
      { $project: { _id: 0, trainer_id: "$_id", horses: 1 } },
    ]);

    console.log(uniqueTrainersData.length);

    const locations = ["M", "P", "C"];
    const calculateMean = (arr) =>
      arr.length > 6
        ? Math.round((arr.reduce((a, b) => a + b, 0) / arr.length) * 100) / 100
        : "";

    // Process trainers in batches
    const batchSize = 50;
    let trainerData = [];
    const jockeyData = new Map();

    for (let i = 0; i < uniqueTrainersData.length; i += batchSize) {
      const batch = uniqueTrainersData.slice(i, i + batchSize);

      await Promise.all(
        batch.map(async ({ trainer_id, horses }) => {
          const results = {
            startPrices: { M: [], P: [], C: [] },
            openPrices: { M: [], P: [], C: [] },
            ratings: { M: [], P: [], C: [] },
          };

          const horseForms = await centaur.form
            .find({ horse_id: { $in: horses } }, { form: 1, horse_id: 1 })
            .lean();

          horseForms.forEach((horseform) => {
            const noBTs = helper.cleanseBarrierTrials(horseform.form);
            const firstRun = noBTs[noBTs.length - 1];

            if (
              firstRun &&
              moment(firstRun.meeting_date).isAfter(moment(pastDate))
            ) {
              const location = firstRun.track["@_location"];
              if (locations.includes(location)) {
                if (firstRun.decimalprices["@_starting"]) {
                  results.startPrices[location].push(
                    parseFloat(firstRun.decimalprices["@_starting"])
                  );
                }

                if (firstRun.decimalprices["@_opening"])
                  results.openPrices[location].push(
                    parseFloat(firstRun.decimalprices["@_opening"])
                  );
                if (
                  firstRun.rating &&
                  firstRun.rating["@_unadjusted"] &&
                  firstRun.classes.class_id
                ) {
                  results.ratings[location].push(
                    firstRun.rating["@_unadjusted"] -
                      parseFloat(
                        helper.getBaseRatingByClass(firstRun.classes.class_id)
                      )
                  );
                }
                if (firstRun.jockey && firstRun.jockey["@_id"]) {
                  const jockeyId = firstRun.jockey["@_id"];
                  if (!jockeyData.has(jockeyId)) {
                    jockeyData.set(jockeyId, {
                      name: firstRun.jockey["@_name"],
                      startPrices: { M: [], P: [], C: [] },
                      openPrices: { M: [], P: [], C: [] },
                    });
                  }
                  if (firstRun.decimalprices["@_starting"]) {
                    jockeyData
                      .get(jockeyId)
                      .startPrices[location].push(
                        parseFloat(firstRun.decimalprices["@_starting"])
                      );
                  }
                   
                  if (firstRun.decimalprices["@_opening"])
                    jockeyData
                      .get(jockeyId)
                      .openPrices[location].push(
                        parseFloat(firstRun.decimalprices["@_opening"])
                      );
                }
              }
            }
          });

          const means = {
            startMeans: {},
            openMeans: {},
            ratingMeans: {},
          };
          for (const location of locations) {
            means.startMeans[location] = calculateMean(
              results.startPrices[location]
            );
            means.openMeans[location] = calculateMean(
              results.openPrices[location]
            );
            means.ratingMeans[location] = calculateMean(
              results.ratings[location]
            );
          }

          trainerData.push({
            trainer_id,
            ...means,
          });
        })
      );

      console.log(`Processed ${trainerData.length} trainers`);
    }
   
    // Calculate global stats from trainerData
    const globalStartStats = {
      M: calculateStats(trainerData.map(t => t.startMeans.M)),
      P: calculateStats(trainerData.map(t => t.startMeans.P)),
      C: calculateStats(trainerData.map(t => t.startMeans.C)),
    };
    
    const globalJockeyStartStats = {
      M: calculateStats(Array.from(jockeyData.values()).map(j => calculateMean(j.startPrices.M))),
      P: calculateStats(Array.from(jockeyData.values()).map(j => calculateMean(j.startPrices.P))),
      C: calculateStats(Array.from(jockeyData.values()).map(j => calculateMean(j.startPrices.C))),
    };

    await Promise.all(
      trainerData.map(async (trainer) => {
        await centaur.trainers.updateOne(
          { TRN_TRAINER_ID: trainer.trainer_id },
          {
            $set: {
              TRN_RATINGS: {
                startPriceMeans: trainer.startMeans,
                openPriceMeans: trainer.openMeans,
                ratingMeans: trainer.ratingMeans,
                startPriceStats: globalStartStats,
              },
            },
          }
        );
      })
    );

    console.log("jockeyData", jockeyData.size);
    const processedJockeyData = Array.from(jockeyData.entries()).map(
      ([jockeyId, data]) => ({
        jockeyId,
        name: data.name,
        startPriceMeans: {
          M: calculateMean(data.startPrices.M),
          P: calculateMean(data.startPrices.P),
          C: calculateMean(data.startPrices.C),
        },
        openPriceMeans: {
          M: calculateMean(data.openPrices.M),
          P: calculateMean(data.openPrices.P),
          C: calculateMean(data.openPrices.C),
        }
      })
    );
    await Promise.all(
      processedJockeyData.map(async (jockey) => {
        await centaur.jockeys.updateOne(
          { JOC_JOCKEY_ID: jockey.jockeyId },
          {
            $set: {
              JOC_RATINGS: {
                startPriceMeans: jockey.startPriceMeans,
                openPriceMeans: jockey.openPriceMeans,
                startPriceStats: globalJockeyStartStats,
              },
            },
          }
        );
      })
    );
  } catch (error) {
    console.log(error);
    if (con) await helper.closeDBConnection(con);
  } finally {
    if (con) await helper.closeDBConnection(con);
  }
};