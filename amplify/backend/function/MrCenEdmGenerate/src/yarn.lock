# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@aws-crypto/crc32@5.2.0":
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/crc32/-/crc32-5.2.0.tgz#cfcc22570949c98c6689cfcbd2d693d36cdae2e1"
  integrity sha512-nLbCWqQNgUiwwtFsen1AdzAtvuLRsQS8rYgMuxCrdKf9kOssamGLuPwyTY9wyYblNr9+1XM8v6zoDTPPSIeANg==
  dependencies:
    "@aws-crypto/util" "^5.2.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^2.6.2"

"@aws-crypto/crc32c@5.2.0":
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/crc32c/-/crc32c-5.2.0.tgz#4e34aab7f419307821509a98b9b08e84e0c1917e"
  integrity sha512-+iWb8qaHLYKrNvGRbiYRHSdKRWhto5XlZUEBwDjYNf+ly5SVYG6zEoYIdxvf5R3zyeP16w4PLBn3rH1xc74Rag==
  dependencies:
    "@aws-crypto/util" "^5.2.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^2.6.2"

"@aws-crypto/sha1-browser@5.2.0":
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/sha1-browser/-/sha1-browser-5.2.0.tgz#b0ee2d2821d3861f017e965ef3b4cb38e3b6a0f4"
  integrity sha512-OH6lveCFfcDjX4dbAvCFSYUjJZjDr/3XJ3xHtjn3Oj5b9RjojQo8npoLeA/bNwkOkrSQ0wgrHzXk4tDRxGKJeg==
  dependencies:
    "@aws-crypto/supports-web-crypto" "^5.2.0"
    "@aws-crypto/util" "^5.2.0"
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-locate-window" "^3.0.0"
    "@smithy/util-utf8" "^2.0.0"
    tslib "^2.6.2"

"@aws-crypto/sha256-browser@5.2.0":
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/sha256-browser/-/sha256-browser-5.2.0.tgz#153895ef1dba6f9fce38af550e0ef58988eb649e"
  integrity sha512-AXfN/lGotSQwu6HNcEsIASo7kWXZ5HYWvfOmSNKDsEqC4OashTp8alTmaz+F7TC2L083SFv5RdB+qU3Vs1kZqw==
  dependencies:
    "@aws-crypto/sha256-js" "^5.2.0"
    "@aws-crypto/supports-web-crypto" "^5.2.0"
    "@aws-crypto/util" "^5.2.0"
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-locate-window" "^3.0.0"
    "@smithy/util-utf8" "^2.0.0"
    tslib "^2.6.2"

"@aws-crypto/sha256-js@5.2.0", "@aws-crypto/sha256-js@^5.2.0":
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/sha256-js/-/sha256-js-5.2.0.tgz#c4fdb773fdbed9a664fc1a95724e206cf3860042"
  integrity sha512-FFQQyu7edu4ufvIZ+OadFpHHOt+eSTBaYaki44c+akjg7qZg9oOQeLlk77F6tSYqjDAFClrHJk9tMf0HdVyOvA==
  dependencies:
    "@aws-crypto/util" "^5.2.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^2.6.2"

"@aws-crypto/supports-web-crypto@^5.2.0":
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/supports-web-crypto/-/supports-web-crypto-5.2.0.tgz#a1e399af29269be08e695109aa15da0a07b5b5fb"
  integrity sha512-iAvUotm021kM33eCdNfwIN//F77/IADDSs58i+MDaOqFrVjZo9bAal0NK7HurRuWLLpF1iLX7gbWrjHjeo+YFg==
  dependencies:
    tslib "^2.6.2"

"@aws-crypto/util@5.2.0", "@aws-crypto/util@^5.2.0":
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/@aws-crypto/util/-/util-5.2.0.tgz#71284c9cffe7927ddadac793c14f14886d3876da"
  integrity sha512-4RkU9EsI6ZpBve5fseQlGNUWKMa1RLPQ1dnjnQoe07ldfIzcsGb5hC5W0Dm7u423KWzawlrpbjXBrXCEv9zazQ==
  dependencies:
    "@aws-sdk/types" "^3.222.0"
    "@smithy/util-utf8" "^2.0.0"
    tslib "^2.6.2"

"@aws-lambda-powertools/commons@^1.18.1":
  version "1.18.1"
  resolved "https://registry.yarnpkg.com/@aws-lambda-powertools/commons/-/commons-1.18.1.tgz#fcfdef39639105a7b2b5363e4bcade9d277f5468"
  integrity sha512-gFRgQ2GJDghKvf+fXvT0kQVftgOT05W+hCa7RkfZj6HSjVAO+9DZZeJL3JK1HcsLAjWRj7W9ra0/MqB3Abf+PQ==

"@aws-lambda-powertools/logger@^1.5.1":
  version "1.18.1"
  resolved "https://registry.yarnpkg.com/@aws-lambda-powertools/logger/-/logger-1.18.1.tgz#6388ddbafca6b3f65277b7a364df45f426f5c592"
  integrity sha512-GsSMqaFXCSz+llSOn2CVNMoN+j/jNsS6JP2Opy9myU0tvg7PeuU3+rN24vKyibUwpxM466IzWFBSJkYdm0bqVw==
  dependencies:
    "@aws-lambda-powertools/commons" "^1.18.1"
    lodash.merge "^4.6.2"

"@aws-lambda-powertools/tracer@^1.5.1":
  version "1.18.1"
  resolved "https://registry.yarnpkg.com/@aws-lambda-powertools/tracer/-/tracer-1.18.1.tgz#9a6c618abb195d0e2cc25b2cd36469f9d63317bd"
  integrity sha512-bMLBtdEFNmLUR9RJvBULR6XJD0XopUhhS1mlpeQlm2BCPIN3gLbqAlJK8dMXyAw8GCpLpHaziCo2+7a/AIh7lA==
  dependencies:
    "@aws-lambda-powertools/commons" "^1.18.1"
    aws-xray-sdk-core "^3.5.3"

"@aws-sdk/client-api-gateway@^3.54.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-api-gateway/-/client-api-gateway-3.723.0.tgz#fa610cf78f7208a1205fdd4f65161f178563a3fd"
  integrity sha512-oDAQ75J0EtNabV5qViWs0E4/dKwyUJrMg4CJ1IwfFzRFzGphro6gJ/cffAez+R1hnlxSXPA6dtmxvo3D2LHVvg==
  dependencies:
    "@aws-crypto/sha256-browser" "5.2.0"
    "@aws-crypto/sha256-js" "5.2.0"
    "@aws-sdk/client-sso-oidc" "3.723.0"
    "@aws-sdk/client-sts" "3.723.0"
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/credential-provider-node" "3.723.0"
    "@aws-sdk/middleware-host-header" "3.723.0"
    "@aws-sdk/middleware-logger" "3.723.0"
    "@aws-sdk/middleware-recursion-detection" "3.723.0"
    "@aws-sdk/middleware-sdk-api-gateway" "3.723.0"
    "@aws-sdk/middleware-user-agent" "3.723.0"
    "@aws-sdk/region-config-resolver" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@aws-sdk/util-endpoints" "3.723.0"
    "@aws-sdk/util-user-agent-browser" "3.723.0"
    "@aws-sdk/util-user-agent-node" "3.723.0"
    "@smithy/config-resolver" "^4.0.0"
    "@smithy/core" "^3.0.0"
    "@smithy/fetch-http-handler" "^5.0.0"
    "@smithy/hash-node" "^4.0.0"
    "@smithy/invalid-dependency" "^4.0.0"
    "@smithy/middleware-content-length" "^4.0.0"
    "@smithy/middleware-endpoint" "^4.0.0"
    "@smithy/middleware-retry" "^4.0.0"
    "@smithy/middleware-serde" "^4.0.0"
    "@smithy/middleware-stack" "^4.0.0"
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/node-http-handler" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/smithy-client" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/url-parser" "^4.0.0"
    "@smithy/util-base64" "^4.0.0"
    "@smithy/util-body-length-browser" "^4.0.0"
    "@smithy/util-body-length-node" "^4.0.0"
    "@smithy/util-defaults-mode-browser" "^4.0.0"
    "@smithy/util-defaults-mode-node" "^4.0.0"
    "@smithy/util-endpoints" "^3.0.0"
    "@smithy/util-middleware" "^4.0.0"
    "@smithy/util-retry" "^4.0.0"
    "@smithy/util-stream" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/client-cognito-identity@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-cognito-identity/-/client-cognito-identity-3.723.0.tgz#c8278c78efecfe5b1330f33977b15cf32d4ce8e0"
  integrity sha512-SEJGNcbfpLM7fqS5s0dLgj7jFCm2IBc1Qluehvpf4+lL1dymGwZAK7S4Ii6gPZhliEj0n48YE6Dg0UkxR2iRag==
  dependencies:
    "@aws-crypto/sha256-browser" "5.2.0"
    "@aws-crypto/sha256-js" "5.2.0"
    "@aws-sdk/client-sso-oidc" "3.723.0"
    "@aws-sdk/client-sts" "3.723.0"
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/credential-provider-node" "3.723.0"
    "@aws-sdk/middleware-host-header" "3.723.0"
    "@aws-sdk/middleware-logger" "3.723.0"
    "@aws-sdk/middleware-recursion-detection" "3.723.0"
    "@aws-sdk/middleware-user-agent" "3.723.0"
    "@aws-sdk/region-config-resolver" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@aws-sdk/util-endpoints" "3.723.0"
    "@aws-sdk/util-user-agent-browser" "3.723.0"
    "@aws-sdk/util-user-agent-node" "3.723.0"
    "@smithy/config-resolver" "^4.0.0"
    "@smithy/core" "^3.0.0"
    "@smithy/fetch-http-handler" "^5.0.0"
    "@smithy/hash-node" "^4.0.0"
    "@smithy/invalid-dependency" "^4.0.0"
    "@smithy/middleware-content-length" "^4.0.0"
    "@smithy/middleware-endpoint" "^4.0.0"
    "@smithy/middleware-retry" "^4.0.0"
    "@smithy/middleware-serde" "^4.0.0"
    "@smithy/middleware-stack" "^4.0.0"
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/node-http-handler" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/smithy-client" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/url-parser" "^4.0.0"
    "@smithy/util-base64" "^4.0.0"
    "@smithy/util-body-length-browser" "^4.0.0"
    "@smithy/util-body-length-node" "^4.0.0"
    "@smithy/util-defaults-mode-browser" "^4.0.0"
    "@smithy/util-defaults-mode-node" "^4.0.0"
    "@smithy/util-endpoints" "^3.0.0"
    "@smithy/util-middleware" "^4.0.0"
    "@smithy/util-retry" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/client-s3@^3.282.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-s3/-/client-s3-3.723.0.tgz#0bde044e1737f527080cc1d156596db62055ebdf"
  integrity sha512-uJkSBWeAbEORApCSc8ZlD8nmmJVZnklauSR+GLnG19ZiHQl3ib6IzT4zdnMHrrIXqVttwkyC8eT703ZUDVaacw==
  dependencies:
    "@aws-crypto/sha1-browser" "5.2.0"
    "@aws-crypto/sha256-browser" "5.2.0"
    "@aws-crypto/sha256-js" "5.2.0"
    "@aws-sdk/client-sso-oidc" "3.723.0"
    "@aws-sdk/client-sts" "3.723.0"
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/credential-provider-node" "3.723.0"
    "@aws-sdk/middleware-bucket-endpoint" "3.723.0"
    "@aws-sdk/middleware-expect-continue" "3.723.0"
    "@aws-sdk/middleware-flexible-checksums" "3.723.0"
    "@aws-sdk/middleware-host-header" "3.723.0"
    "@aws-sdk/middleware-location-constraint" "3.723.0"
    "@aws-sdk/middleware-logger" "3.723.0"
    "@aws-sdk/middleware-recursion-detection" "3.723.0"
    "@aws-sdk/middleware-sdk-s3" "3.723.0"
    "@aws-sdk/middleware-ssec" "3.723.0"
    "@aws-sdk/middleware-user-agent" "3.723.0"
    "@aws-sdk/region-config-resolver" "3.723.0"
    "@aws-sdk/signature-v4-multi-region" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@aws-sdk/util-endpoints" "3.723.0"
    "@aws-sdk/util-user-agent-browser" "3.723.0"
    "@aws-sdk/util-user-agent-node" "3.723.0"
    "@aws-sdk/xml-builder" "3.723.0"
    "@smithy/config-resolver" "^4.0.0"
    "@smithy/core" "^3.0.0"
    "@smithy/eventstream-serde-browser" "^4.0.0"
    "@smithy/eventstream-serde-config-resolver" "^4.0.0"
    "@smithy/eventstream-serde-node" "^4.0.0"
    "@smithy/fetch-http-handler" "^5.0.0"
    "@smithy/hash-blob-browser" "^4.0.0"
    "@smithy/hash-node" "^4.0.0"
    "@smithy/hash-stream-node" "^4.0.0"
    "@smithy/invalid-dependency" "^4.0.0"
    "@smithy/md5-js" "^4.0.0"
    "@smithy/middleware-content-length" "^4.0.0"
    "@smithy/middleware-endpoint" "^4.0.0"
    "@smithy/middleware-retry" "^4.0.0"
    "@smithy/middleware-serde" "^4.0.0"
    "@smithy/middleware-stack" "^4.0.0"
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/node-http-handler" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/smithy-client" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/url-parser" "^4.0.0"
    "@smithy/util-base64" "^4.0.0"
    "@smithy/util-body-length-browser" "^4.0.0"
    "@smithy/util-body-length-node" "^4.0.0"
    "@smithy/util-defaults-mode-browser" "^4.0.0"
    "@smithy/util-defaults-mode-node" "^4.0.0"
    "@smithy/util-endpoints" "^3.0.0"
    "@smithy/util-middleware" "^4.0.0"
    "@smithy/util-retry" "^4.0.0"
    "@smithy/util-stream" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    "@smithy/util-waiter" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/client-secrets-manager@^3.282.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-secrets-manager/-/client-secrets-manager-3.723.0.tgz#9597b9c55cf53db4db0a4580b0444237d00caf47"
  integrity sha512-Zh+j0J9iog4c9l8re9EXvS3/+ylVwbJIFbqDJUvqszdCrTAFQGaGVdxoJND5WC5Ggr6Syiy7ZDj0p+yrEQOObA==
  dependencies:
    "@aws-crypto/sha256-browser" "5.2.0"
    "@aws-crypto/sha256-js" "5.2.0"
    "@aws-sdk/client-sso-oidc" "3.723.0"
    "@aws-sdk/client-sts" "3.723.0"
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/credential-provider-node" "3.723.0"
    "@aws-sdk/middleware-host-header" "3.723.0"
    "@aws-sdk/middleware-logger" "3.723.0"
    "@aws-sdk/middleware-recursion-detection" "3.723.0"
    "@aws-sdk/middleware-user-agent" "3.723.0"
    "@aws-sdk/region-config-resolver" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@aws-sdk/util-endpoints" "3.723.0"
    "@aws-sdk/util-user-agent-browser" "3.723.0"
    "@aws-sdk/util-user-agent-node" "3.723.0"
    "@smithy/config-resolver" "^4.0.0"
    "@smithy/core" "^3.0.0"
    "@smithy/fetch-http-handler" "^5.0.0"
    "@smithy/hash-node" "^4.0.0"
    "@smithy/invalid-dependency" "^4.0.0"
    "@smithy/middleware-content-length" "^4.0.0"
    "@smithy/middleware-endpoint" "^4.0.0"
    "@smithy/middleware-retry" "^4.0.0"
    "@smithy/middleware-serde" "^4.0.0"
    "@smithy/middleware-stack" "^4.0.0"
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/node-http-handler" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/smithy-client" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/url-parser" "^4.0.0"
    "@smithy/util-base64" "^4.0.0"
    "@smithy/util-body-length-browser" "^4.0.0"
    "@smithy/util-body-length-node" "^4.0.0"
    "@smithy/util-defaults-mode-browser" "^4.0.0"
    "@smithy/util-defaults-mode-node" "^4.0.0"
    "@smithy/util-endpoints" "^3.0.0"
    "@smithy/util-middleware" "^4.0.0"
    "@smithy/util-retry" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    "@types/uuid" "^9.0.1"
    tslib "^2.6.2"
    uuid "^9.0.1"

"@aws-sdk/client-sso-oidc@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-sso-oidc/-/client-sso-oidc-3.723.0.tgz#d2111164c2563dead8c87291f0c6073ebebe1dde"
  integrity sha512-9IH90m4bnHogBctVna2FnXaIGVORncfdxcqeEIovOxjIJJyHDmEAtA7B91dAM4sruddTbVzOYnqfPVst3odCbA==
  dependencies:
    "@aws-crypto/sha256-browser" "5.2.0"
    "@aws-crypto/sha256-js" "5.2.0"
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/credential-provider-node" "3.723.0"
    "@aws-sdk/middleware-host-header" "3.723.0"
    "@aws-sdk/middleware-logger" "3.723.0"
    "@aws-sdk/middleware-recursion-detection" "3.723.0"
    "@aws-sdk/middleware-user-agent" "3.723.0"
    "@aws-sdk/region-config-resolver" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@aws-sdk/util-endpoints" "3.723.0"
    "@aws-sdk/util-user-agent-browser" "3.723.0"
    "@aws-sdk/util-user-agent-node" "3.723.0"
    "@smithy/config-resolver" "^4.0.0"
    "@smithy/core" "^3.0.0"
    "@smithy/fetch-http-handler" "^5.0.0"
    "@smithy/hash-node" "^4.0.0"
    "@smithy/invalid-dependency" "^4.0.0"
    "@smithy/middleware-content-length" "^4.0.0"
    "@smithy/middleware-endpoint" "^4.0.0"
    "@smithy/middleware-retry" "^4.0.0"
    "@smithy/middleware-serde" "^4.0.0"
    "@smithy/middleware-stack" "^4.0.0"
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/node-http-handler" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/smithy-client" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/url-parser" "^4.0.0"
    "@smithy/util-base64" "^4.0.0"
    "@smithy/util-body-length-browser" "^4.0.0"
    "@smithy/util-body-length-node" "^4.0.0"
    "@smithy/util-defaults-mode-browser" "^4.0.0"
    "@smithy/util-defaults-mode-node" "^4.0.0"
    "@smithy/util-endpoints" "^3.0.0"
    "@smithy/util-middleware" "^4.0.0"
    "@smithy/util-retry" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/client-sso@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-sso/-/client-sso-3.723.0.tgz#4fb8c88a9cb45456bb84c716d39b0f2638bde395"
  integrity sha512-r1ddZDb8yPmdofX1gQ4m8oqKozgkgVONLlAuSprGObbyMy8bYt1Psxu+GjnwMmgVu3vlF069PHyW1ndrBiL1zA==
  dependencies:
    "@aws-crypto/sha256-browser" "5.2.0"
    "@aws-crypto/sha256-js" "5.2.0"
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/middleware-host-header" "3.723.0"
    "@aws-sdk/middleware-logger" "3.723.0"
    "@aws-sdk/middleware-recursion-detection" "3.723.0"
    "@aws-sdk/middleware-user-agent" "3.723.0"
    "@aws-sdk/region-config-resolver" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@aws-sdk/util-endpoints" "3.723.0"
    "@aws-sdk/util-user-agent-browser" "3.723.0"
    "@aws-sdk/util-user-agent-node" "3.723.0"
    "@smithy/config-resolver" "^4.0.0"
    "@smithy/core" "^3.0.0"
    "@smithy/fetch-http-handler" "^5.0.0"
    "@smithy/hash-node" "^4.0.0"
    "@smithy/invalid-dependency" "^4.0.0"
    "@smithy/middleware-content-length" "^4.0.0"
    "@smithy/middleware-endpoint" "^4.0.0"
    "@smithy/middleware-retry" "^4.0.0"
    "@smithy/middleware-serde" "^4.0.0"
    "@smithy/middleware-stack" "^4.0.0"
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/node-http-handler" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/smithy-client" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/url-parser" "^4.0.0"
    "@smithy/util-base64" "^4.0.0"
    "@smithy/util-body-length-browser" "^4.0.0"
    "@smithy/util-body-length-node" "^4.0.0"
    "@smithy/util-defaults-mode-browser" "^4.0.0"
    "@smithy/util-defaults-mode-node" "^4.0.0"
    "@smithy/util-endpoints" "^3.0.0"
    "@smithy/util-middleware" "^4.0.0"
    "@smithy/util-retry" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/client-sts@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/client-sts/-/client-sts-3.723.0.tgz#****************************************"
  integrity sha512-YyN8x4MI/jMb4LpHsLf+VYqvbColMK8aZeGWVk2fTFsmt8lpTYGaGC1yybSwGX42mZ4W8ucu8SAYSbUraJZEjA==
  dependencies:
    "@aws-crypto/sha256-browser" "5.2.0"
    "@aws-crypto/sha256-js" "5.2.0"
    "@aws-sdk/client-sso-oidc" "3.723.0"
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/credential-provider-node" "3.723.0"
    "@aws-sdk/middleware-host-header" "3.723.0"
    "@aws-sdk/middleware-logger" "3.723.0"
    "@aws-sdk/middleware-recursion-detection" "3.723.0"
    "@aws-sdk/middleware-user-agent" "3.723.0"
    "@aws-sdk/region-config-resolver" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@aws-sdk/util-endpoints" "3.723.0"
    "@aws-sdk/util-user-agent-browser" "3.723.0"
    "@aws-sdk/util-user-agent-node" "3.723.0"
    "@smithy/config-resolver" "^4.0.0"
    "@smithy/core" "^3.0.0"
    "@smithy/fetch-http-handler" "^5.0.0"
    "@smithy/hash-node" "^4.0.0"
    "@smithy/invalid-dependency" "^4.0.0"
    "@smithy/middleware-content-length" "^4.0.0"
    "@smithy/middleware-endpoint" "^4.0.0"
    "@smithy/middleware-retry" "^4.0.0"
    "@smithy/middleware-serde" "^4.0.0"
    "@smithy/middleware-stack" "^4.0.0"
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/node-http-handler" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/smithy-client" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/url-parser" "^4.0.0"
    "@smithy/util-base64" "^4.0.0"
    "@smithy/util-body-length-browser" "^4.0.0"
    "@smithy/util-body-length-node" "^4.0.0"
    "@smithy/util-defaults-mode-browser" "^4.0.0"
    "@smithy/util-defaults-mode-node" "^4.0.0"
    "@smithy/util-endpoints" "^3.0.0"
    "@smithy/util-middleware" "^4.0.0"
    "@smithy/util-retry" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/core@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/core/-/core-3.723.0.tgz#7a441b1362fa22609f80ede42d4e069829b9b4d1"
  integrity sha512-UraXNmvqj3vScSsTkjMwQkhei30BhXlW5WxX6JacMKVtl95c7z0qOXquTWeTalYkFfulfdirUhvSZrl+hcyqTw==
  dependencies:
    "@aws-sdk/types" "3.723.0"
    "@smithy/core" "^3.0.0"
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/property-provider" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/signature-v4" "^5.0.0"
    "@smithy/smithy-client" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/util-middleware" "^4.0.0"
    fast-xml-parser "4.4.1"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-cognito-identity@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-cognito-identity/-/credential-provider-cognito-identity-3.723.0.tgz#416c1032518d248f964cfdcccd1df780b1524a89"
  integrity sha512-QsI3Y9isqJAttxtGiPgm/moa/SWJl4EaCfwBLDTI/gZpYdzYkqeYf6r52mhCZ0tQm2CHvXrt1eHiTqD9jINhEA==
  dependencies:
    "@aws-sdk/client-cognito-identity" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@smithy/property-provider" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-env@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-env/-/credential-provider-env-3.723.0.tgz#7d85014d21ce50f9f6a108c5c673e87c54860eaa"
  integrity sha512-OuH2yULYUHTVDUotBoP/9AEUIJPn81GQ/YBtZLoo2QyezRJ2QiO/1epVtbJlhNZRwXrToLEDmQGA2QfC8c7pbA==
  dependencies:
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@smithy/property-provider" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-http@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-http/-/credential-provider-http-3.723.0.tgz#3b5db3225bb6dd97fecf22e18c06c3567eb1bce4"
  integrity sha512-DTsKC6xo/kz/ZSs1IcdbQMTgiYbpGTGEd83kngFc1bzmw7AmK92DBZKNZpumf8R/UfSpTcj9zzUUmrWz1kD0eQ==
  dependencies:
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@smithy/fetch-http-handler" "^5.0.0"
    "@smithy/node-http-handler" "^4.0.0"
    "@smithy/property-provider" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/smithy-client" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/util-stream" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-ini@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-ini/-/credential-provider-ini-3.723.0.tgz#3dc8e8d88d0c66a7ba890b5c510ced86fd98c066"
  integrity sha512-fWRLksuSG851e7Iu+ltMrQTM7C/5iI9OkxAmCYblcCetAzjTRmMB2arku0Z83D8edIZEQtOJMt5oQ9KNg43pzg==
  dependencies:
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/credential-provider-env" "3.723.0"
    "@aws-sdk/credential-provider-http" "3.723.0"
    "@aws-sdk/credential-provider-process" "3.723.0"
    "@aws-sdk/credential-provider-sso" "3.723.0"
    "@aws-sdk/credential-provider-web-identity" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@smithy/credential-provider-imds" "^4.0.0"
    "@smithy/property-provider" "^4.0.0"
    "@smithy/shared-ini-file-loader" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-node@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-node/-/credential-provider-node-3.723.0.tgz#9e136a8c6df2324ff0d82e18f8ec22181bb0f25b"
  integrity sha512-OyLHt+aY+rkuRejigcxviS5RLUBcqbxhDTSNfP8dp9I+1SP610qRLpTIROvtKwXZssFcATpPfgikFtVYRrihXQ==
  dependencies:
    "@aws-sdk/credential-provider-env" "3.723.0"
    "@aws-sdk/credential-provider-http" "3.723.0"
    "@aws-sdk/credential-provider-ini" "3.723.0"
    "@aws-sdk/credential-provider-process" "3.723.0"
    "@aws-sdk/credential-provider-sso" "3.723.0"
    "@aws-sdk/credential-provider-web-identity" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@smithy/credential-provider-imds" "^4.0.0"
    "@smithy/property-provider" "^4.0.0"
    "@smithy/shared-ini-file-loader" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-process@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-process/-/credential-provider-process-3.723.0.tgz#32bc55573b0a8f31e69b15939202d266adbbe711"
  integrity sha512-fgupvUjz1+jeoCBA7GMv0L6xEk92IN6VdF4YcFhsgRHlHvNgm7ayaoKQg7pz2JAAhG/3jPX6fp0ASNy+xOhmPA==
  dependencies:
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@smithy/property-provider" "^4.0.0"
    "@smithy/shared-ini-file-loader" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-sso@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-sso/-/credential-provider-sso-3.723.0.tgz#b05a9bff698de12be9b929802cd85538adfccc36"
  integrity sha512-laCnxrk0pgUegU+ib6rj1/Uv51wei+cH8crvBJddybc8EDn7Qht61tCvBwf3o33qUDC+ZWZZewlpSebf+J+tBw==
  dependencies:
    "@aws-sdk/client-sso" "3.723.0"
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/token-providers" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@smithy/property-provider" "^4.0.0"
    "@smithy/shared-ini-file-loader" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/credential-provider-web-identity@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-provider-web-identity/-/credential-provider-web-identity-3.723.0.tgz#5c17ea243b05b4dca0584db597ac68d8509dd754"
  integrity sha512-tl7pojbFbr3qLcOE6xWaNCf1zEfZrIdSJtOPeSXfV/thFMMAvIjgf3YN6Zo1a6cxGee8zrV/C8PgOH33n+Ev/A==
  dependencies:
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@smithy/property-provider" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/credential-providers@^3.186.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/credential-providers/-/credential-providers-3.723.0.tgz#82d6aae0a3eeba54807daa928f1d7a0a8193a757"
  integrity sha512-poDyQ5/O8fdhVP+Nr3nNYS9jBpmrwy9G7ipMYceHbztiBrAmFBsB3dyFKytRM1+z5Gz3icOHamv5ZinDhyBWsA==
  dependencies:
    "@aws-sdk/client-cognito-identity" "3.723.0"
    "@aws-sdk/client-sso" "3.723.0"
    "@aws-sdk/client-sts" "3.723.0"
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/credential-provider-cognito-identity" "3.723.0"
    "@aws-sdk/credential-provider-env" "3.723.0"
    "@aws-sdk/credential-provider-http" "3.723.0"
    "@aws-sdk/credential-provider-ini" "3.723.0"
    "@aws-sdk/credential-provider-node" "3.723.0"
    "@aws-sdk/credential-provider-process" "3.723.0"
    "@aws-sdk/credential-provider-sso" "3.723.0"
    "@aws-sdk/credential-provider-web-identity" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@smithy/credential-provider-imds" "^4.0.0"
    "@smithy/property-provider" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-bucket-endpoint@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-bucket-endpoint/-/middleware-bucket-endpoint-3.723.0.tgz#a8f2d474d90066319d729b089b4b691b2ff18d6d"
  integrity sha512-OmKSXwSlXyW+zg+xq4hUf7V4VF5/fa4LHu1JzeBlomrKX3/NnqhnJn7760GXoDr16AT+dP7nvv35Ofp91umEAg==
  dependencies:
    "@aws-sdk/types" "3.723.0"
    "@aws-sdk/util-arn-parser" "3.723.0"
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/util-config-provider" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-expect-continue@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-expect-continue/-/middleware-expect-continue-3.723.0.tgz#59addac9b4cdc958ea1e06de9863db657e9c8e43"
  integrity sha512-w/O0EkIzkiqvGu7U8Ke7tue0V0HYM5dZQrz6nVU+R8T2LddWJ+njEIHU4Wh8aHPLQXdZA5NQumv0xLPdEutykw==
  dependencies:
    "@aws-sdk/types" "3.723.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-flexible-checksums@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-flexible-checksums/-/middleware-flexible-checksums-3.723.0.tgz#612ec13c4ba5dc906793172ece02a95059fffcc6"
  integrity sha512-JY76mrUCLa0FHeMZp8X9+KK6uEuZaRZaQrlgq6zkXX/3udukH0T3YdFC+Y9uw5ddbiwZ5+KwgmlhnPpiXKfP4g==
  dependencies:
    "@aws-crypto/crc32" "5.2.0"
    "@aws-crypto/crc32c" "5.2.0"
    "@aws-crypto/util" "5.2.0"
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@smithy/is-array-buffer" "^4.0.0"
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/util-middleware" "^4.0.0"
    "@smithy/util-stream" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-host-header@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-host-header/-/middleware-host-header-3.723.0.tgz#f043689755e5b45ee6500b0d0a7090d9b4a864f7"
  integrity sha512-LLVzLvk299pd7v4jN9yOSaWDZDfH0SnBPb6q+FDPaOCMGBY8kuwQso7e/ozIKSmZHRMGO3IZrflasHM+rI+2YQ==
  dependencies:
    "@aws-sdk/types" "3.723.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-location-constraint@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-location-constraint/-/middleware-location-constraint-3.723.0.tgz#364e875a511d97431b6d337878c8a9bd5e2fdf64"
  integrity sha512-inp9tyrdRWjGOMu1rzli8i2gTo0P4X6L7nNRXNTKfyPNZcBimZ4H0H1B671JofSI5isaklVy5r4pvv2VjjLSHw==
  dependencies:
    "@aws-sdk/types" "3.723.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-logger@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-logger/-/middleware-logger-3.723.0.tgz#e8718056fc2d73a0d51308cad20676228be26652"
  integrity sha512-chASQfDG5NJ8s5smydOEnNK7N0gDMyuPbx7dYYcm1t/PKtnVfvWF+DHCTrRC2Ej76gLJVCVizlAJKM8v8Kg3cg==
  dependencies:
    "@aws-sdk/types" "3.723.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-recursion-detection@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.723.0.tgz#b4557c7f554492f56eeb0cbf5bc02dac7ef102a8"
  integrity sha512-7usZMtoynT9/jxL/rkuDOFQ0C2mhXl4yCm67Rg7GNTstl67u7w5WN1aIRImMeztaKlw8ExjoTyo6WTs1Kceh7A==
  dependencies:
    "@aws-sdk/types" "3.723.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-sdk-api-gateway@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-sdk-api-gateway/-/middleware-sdk-api-gateway-3.723.0.tgz#a9bac255d8e1407c918f8a9891387d8bf4c50509"
  integrity sha512-+iUPbxmsAfO4cRUQrs+qmEvSUhFG61roNVefcD0vbBEMXHQdghLSU36BVzIirk13uhPVOBpEDHmIb0Qs8W+7Fw==
  dependencies:
    "@aws-sdk/types" "3.723.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-sdk-s3@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-sdk-s3/-/middleware-sdk-s3-3.723.0.tgz#d323c24b2268933bf51353d5215fa8baadaf8837"
  integrity sha512-wfjOvNJVp8LDWhq4wO5jtSMb8Vgf4tNlR7QTEQfoYc6AGU3WlK5xyUQcpfcpwytEhQTN9u0cJLQpSyXDO+qSCw==
  dependencies:
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@aws-sdk/util-arn-parser" "3.723.0"
    "@smithy/core" "^3.0.0"
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/signature-v4" "^5.0.0"
    "@smithy/smithy-client" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/util-config-provider" "^4.0.0"
    "@smithy/util-middleware" "^4.0.0"
    "@smithy/util-stream" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-ssec@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-ssec/-/middleware-ssec-3.723.0.tgz#b4adb65eb4ac029ee8b566f373b1d54aecbbd7ad"
  integrity sha512-Bs+8RAeSMik6ZYCGSDJzJieGsDDh2fRbh1HQG94T8kpwBXVxMYihm6e9Xp2cyl+w9fyyCnh0IdCKChP/DvrdhA==
  dependencies:
    "@aws-sdk/types" "3.723.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/middleware-user-agent@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.723.0.tgz#a989ddebd490e8fa4fc7d3d6f12bd5c81afc7ae7"
  integrity sha512-AY5H2vD3IRElplBO4DCyRMNnOG/4/cb0tsHyLe1HJy0hdUF6eY5z/VVjKJoKbbDk7ui9euyOBWslXxDyLmyPWg==
  dependencies:
    "@aws-sdk/core" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@aws-sdk/util-endpoints" "3.723.0"
    "@smithy/core" "^3.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/region-config-resolver@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/region-config-resolver/-/region-config-resolver-3.723.0.tgz#07b7ee4788ec7a7f5638bbbe0f9f7565125caf22"
  integrity sha512-tGF/Cvch3uQjZIj34LY2mg8M2Dr4kYG8VU8Yd0dFnB1ybOEOveIK/9ypUo9ycZpB9oO6q01KRe5ijBaxNueUQg==
  dependencies:
    "@aws-sdk/types" "3.723.0"
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/util-config-provider" "^4.0.0"
    "@smithy/util-middleware" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/signature-v4-multi-region@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/signature-v4-multi-region/-/signature-v4-multi-region-3.723.0.tgz#1de81c7ee98410dabbb22978bc5d4540c51a8afa"
  integrity sha512-lJlVAa5Sl589qO8lwMLVUtnlF1Q7I+6k1Iomv2goY9d1bRl4q2N5Pit2qJVr2AMW0sceQXeh23i2a/CKOqVAdg==
  dependencies:
    "@aws-sdk/middleware-sdk-s3" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/signature-v4" "^5.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/token-providers@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/token-providers/-/token-providers-3.723.0.tgz#ae173a18783886e592212abb820d28cbdb9d9237"
  integrity sha512-hniWi1x4JHVwKElANh9afKIMUhAutHVBRD8zo6usr0PAoj+Waf220+1ULS74GXtLXAPCiNXl5Og+PHA7xT8ElQ==
  dependencies:
    "@aws-sdk/types" "3.723.0"
    "@smithy/property-provider" "^4.0.0"
    "@smithy/shared-ini-file-loader" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/types@3.723.0", "@aws-sdk/types@^3.222.0", "@aws-sdk/types@^3.4.1":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/types/-/types-3.723.0.tgz#f0c5a6024a73470421c469b6c1dd5bc4b8fb851b"
  integrity sha512-LmK3kwiMZG1y5g3LGihT9mNkeNOmwEyPk6HGcJqh0wOSV4QpWoKu2epyKE4MLQNUUlz2kOVbVbOrwmI6ZcteuA==
  dependencies:
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/util-arn-parser@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-arn-parser/-/util-arn-parser-3.723.0.tgz#e9bff2b13918a92d60e0012101dad60ed7db292c"
  integrity sha512-ZhEfvUwNliOQROcAk34WJWVYTlTa4694kSVhDSjW6lE1bMataPnIN8A0ycukEzBXmd8ZSoBcQLn6lKGl7XIJ5w==
  dependencies:
    tslib "^2.6.2"

"@aws-sdk/util-endpoints@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-endpoints/-/util-endpoints-3.723.0.tgz#de645ddebf29e40582a651351935bdf995820a94"
  integrity sha512-vR1ZfAUvrTtdA1Q78QxgR8TFgi2gzk+N4EmNjbyR5hHmeOXuaKRdhbNQAzLPYVe1aNUpoiy9cl8mWkg9SrNHBw==
  dependencies:
    "@aws-sdk/types" "3.723.0"
    "@smithy/types" "^4.0.0"
    "@smithy/util-endpoints" "^3.0.0"
    tslib "^2.6.2"

"@aws-sdk/util-locate-window@^3.0.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-locate-window/-/util-locate-window-3.723.0.tgz#174551bfdd2eb36d3c16e7023fd7e7ee96ad0fa9"
  integrity sha512-Yf2CS10BqK688DRsrKI/EO6B8ff5J86NXe4C+VCysK7UOgN0l1zOTeTukZ3H8Q9tYYX3oaF1961o8vRkFm7Nmw==
  dependencies:
    tslib "^2.6.2"

"@aws-sdk/util-user-agent-browser@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.723.0.tgz#64b0b4413c1be1585f95c3e2606429cc9f86df83"
  integrity sha512-Wh9I6j2jLhNFq6fmXydIpqD1WyQLyTfSxjW9B+PXSnPyk3jtQW8AKQur7p97rO8LAUzVI0bv8kb3ZzDEVbquIg==
  dependencies:
    "@aws-sdk/types" "3.723.0"
    "@smithy/types" "^4.0.0"
    bowser "^2.11.0"
    tslib "^2.6.2"

"@aws-sdk/util-user-agent-node@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.723.0.tgz#289831fd85edce37eb600caea84d12456a8a997c"
  integrity sha512-uCtW5sGq8jCwA9w57TvVRIwNnPbSDD1lJaTIgotf7Jit2bTrYR64thgMy/drL5yU5aHOdFIQljqn/5aDXLtTJw==
  dependencies:
    "@aws-sdk/middleware-user-agent" "3.723.0"
    "@aws-sdk/types" "3.723.0"
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@aws-sdk/xml-builder@3.723.0":
  version "3.723.0"
  resolved "https://registry.yarnpkg.com/@aws-sdk/xml-builder/-/xml-builder-3.723.0.tgz#989580d65086985b82f05eaea0ee46d78a510398"
  integrity sha512-5xK2SqGU1mzzsOeemy7cy3fGKxR1sEpUs4pEiIjaT0OIvU+fZaDVUEYWOqsgns6wI90XZEQJlXtI8uAHX/do5Q==
  dependencies:
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@dazn/lambda-powertools-correlation-ids@^1.28.1":
  version "1.28.1"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-correlation-ids/-/lambda-powertools-correlation-ids-1.28.1.tgz#ff0b94de104154cbdf5825e9f2a5a789c4cb6e92"
  integrity sha512-/RZUT5ZlVcQxsWi+OolEwXUIsXHqffNeZ+eY4Je23s9VcztuqHuHeyrlJh1m5Kg76EsvQTq+5b1xvjf3J/6A1Q==

"@dazn/lambda-powertools-logger@^1.28.1":
  version "1.28.1"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-logger/-/lambda-powertools-logger-1.28.1.tgz#ac638e6e13552ac4e3a35613436f8a132e9bfe05"
  integrity sha512-vfnKgEwW/jv4PSkqRsEWPWLY5fkcjCnLrSZpca2Exh7pIUWZQN3FqLdpqs23caa+qtOCJ2JM8toa21uwSzYMLw==
  dependencies:
    "@dazn/lambda-powertools-correlation-ids" "^1.28.1"

"@dazn/lambda-powertools-middleware-correlation-ids@^1.29.0":
  version "1.29.0"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-middleware-correlation-ids/-/lambda-powertools-middleware-correlation-ids-1.29.0.tgz#e03e8184e2a3673a77d18d02576b44646caa060c"
  integrity sha512-kXOOKzEMKz6nYHUQo2GUvTqnQeXo1U6/RI87xUjSeztcjHaDZ0Jw6plUepZD+YawjfsVIrHKfnZrlN909utttA==
  dependencies:
    "@dazn/lambda-powertools-correlation-ids" "^1.28.1"
    "@dazn/lambda-powertools-logger" "^1.28.1"

"@dazn/lambda-powertools-middleware-log-timeout@^1.29.0":
  version "1.29.0"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-middleware-log-timeout/-/lambda-powertools-middleware-log-timeout-1.29.0.tgz#027b9fcaa0413b5d0e0261f16721be31079f9e9c"
  integrity sha512-BJv3DQdcuOCBfp93cFv3LgCcCBhwh4s8COmw4x+c3cEdkY6zajo9tHAikFea8Fv9ShDXAcUgnPpkv8EFMbAH+w==
  dependencies:
    "@dazn/lambda-powertools-logger" "^1.28.1"

"@dazn/lambda-powertools-middleware-sample-logging@^1.29.0":
  version "1.29.0"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-middleware-sample-logging/-/lambda-powertools-middleware-sample-logging-1.29.0.tgz#a0b403f7387e202b47df1bdc975c5e0ba09fd46f"
  integrity sha512-VHe3bSw0ch5Ql5tA3XvCta8db1Nr6NaSJ0Oj2oqQU+F15WJfqPD+reeKMgj3F1z8lJqXWAea3aD4nQT0PCTt6Q==
  dependencies:
    "@dazn/lambda-powertools-correlation-ids" "^1.28.1"
    "@dazn/lambda-powertools-logger" "^1.28.1"

"@dazn/lambda-powertools-pattern-basic@^1.29.0":
  version "1.29.0"
  resolved "https://registry.yarnpkg.com/@dazn/lambda-powertools-pattern-basic/-/lambda-powertools-pattern-basic-1.29.0.tgz#d97d47730588cb93dc115402fbab12e4492c6948"
  integrity sha512-HYmu9eKVRYNu5Q2CYuOl3UmBMAfpHzvNJFRdR8f8F5DJLktsexapk1sDjZZq4bP1ZmduuSbG/mUN9nmtkCRWYw==
  dependencies:
    "@dazn/lambda-powertools-middleware-correlation-ids" "^1.29.0"
    "@dazn/lambda-powertools-middleware-log-timeout" "^1.29.0"
    "@dazn/lambda-powertools-middleware-sample-logging" "^1.29.0"
    "@middy/core" "^2.1.0"

"@mediality/centaur@../../centaurappCentaurAppCommonLayer/lib/nodejs":
  version "1.0.31"
  dependencies:
    "@aws-lambda-powertools/logger" "^1.5.1"
    "@aws-lambda-powertools/tracer" "^1.5.1"
    "@aws-sdk/client-api-gateway" "^3.54.0"
    "@aws-sdk/client-s3" "^3.282.0"
    "@aws-sdk/client-secrets-manager" "^3.282.0"
    "@dazn/lambda-powertools-logger" "^1.28.1"
    "@dazn/lambda-powertools-pattern-basic" "^1.29.0"
    "@mediality/centaur" "./"
    aws-sdk "^2.1324.0"
    aws-xray-sdk "^3.3.4"
    aws-xray-sdk-core "^3.3.4"
    axios "^1.6.7"
    basic-ftp "^5.0.1"
    fast-xml-parser "^4.0.1"
    fs "^0.0.1-security"
    fs-extra "^10.0.0"
    install "^0.13.0"
    moment "^2.29.1"
    mongoose "^6.1.3"
    pify "^5.0.0"
    uuid "^8.3.2"
    uuid-by-string "^3.0.4"
    validator "^13.7.0"
    xml2js "^0.4.23"
    xmlbuilder2 "^3.0.2"

"@mediality/centaur@./":
  version "2.0.0"

"@middy/core@^2.1.0":
  version "2.5.7"
  resolved "https://registry.yarnpkg.com/@middy/core/-/core-2.5.7.tgz#a1b3eff68881ff66b14b5051255791f7cbd3b471"
  integrity sha512-KX5Ud0SP+pol6PGkYtMCH4goHobs1XJo3OvEUwdiZUIjZgo56Q08nLu5N7Bs6P+FwGTQHA+hlQ3I5SZbfpO/jg==

"@mongodb-js/saslprep@^1.1.0":
  version "1.1.9"
  resolved "https://registry.yarnpkg.com/@mongodb-js/saslprep/-/saslprep-1.1.9.tgz#e974bab8eca9faa88677d4ea4da8d09a52069004"
  integrity sha512-tVkljjeEaAhCqTzajSdgbQ6gE6f3oneVwa3iXR6csiEwXXOFsiC6Uh9iAjAhXPtqa/XMDHWjjeNH/77m/Yq2dw==
  dependencies:
    sparse-bitfield "^3.0.3"

"@oozcitak/dom@1.15.10":
  version "1.15.10"
  resolved "https://registry.yarnpkg.com/@oozcitak/dom/-/dom-1.15.10.tgz#dca7289f2b292cff2a901ea4fbbcc0a1ab0b05c2"
  integrity sha512-0JT29/LaxVgRcGKvHmSrUTEvZ8BXvZhGl2LASRUgHqDTC1M5g1pLmVv56IYNyt3bG2CUjDkc67wnyZC14pbQrQ==
  dependencies:
    "@oozcitak/infra" "1.0.8"
    "@oozcitak/url" "1.0.4"
    "@oozcitak/util" "8.3.8"

"@oozcitak/infra@1.0.8":
  version "1.0.8"
  resolved "https://registry.yarnpkg.com/@oozcitak/infra/-/infra-1.0.8.tgz#b0b089421f7d0f6878687608301fbaba837a7d17"
  integrity sha512-JRAUc9VR6IGHOL7OGF+yrvs0LO8SlqGnPAMqyzOuFZPSZSXI7Xf2O9+awQPSMXgIWGtgUf/dA6Hs6X6ySEaWTg==
  dependencies:
    "@oozcitak/util" "8.3.8"

"@oozcitak/url@1.0.4":
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/@oozcitak/url/-/url-1.0.4.tgz#ca8b1c876319cf5a648dfa1123600a6aa5cda6ba"
  integrity sha512-kDcD8y+y3FCSOvnBI6HJgl00viO/nGbQoCINmQ0h98OhnGITrWR3bOGfwYCthgcrV8AnTJz8MzslTQbC3SOAmw==
  dependencies:
    "@oozcitak/infra" "1.0.8"
    "@oozcitak/util" "8.3.8"

"@oozcitak/util@8.3.8":
  version "8.3.8"
  resolved "https://registry.yarnpkg.com/@oozcitak/util/-/util-8.3.8.tgz#10f65fe1891fd8cde4957360835e78fd1936bfdd"
  integrity sha512-T8TbSnGsxo6TDBJx/Sgv/BlVJL3tshxZP7Aq5R1mSnM5OcHY2dQaxLMu2+E8u3gN0MLOzdjurqN4ZRVuzQycOQ==

"@smithy/abort-controller@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/abort-controller/-/abort-controller-4.0.0.tgz#c28e4e39191fde8ce93a595cd89f646dcb597632"
  integrity sha512-xFNL1ZfluscKiVI0qlPEnu7pL1UgNNIzQdjTPkaO7JCJtIkbArPYNtqbxohuNaQdksJ01Tn1wLbDA5oIp62P8w==
  dependencies:
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/chunked-blob-reader-native@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/chunked-blob-reader-native/-/chunked-blob-reader-native-4.0.0.tgz#33cbba6deb8a3c516f98444f65061784f7cd7f8c"
  integrity sha512-R9wM2yPmfEMsUmlMlIgSzOyICs0x9uu7UTHoccMyt7BWw8shcGM8HqB355+BZCPBcySvbTYMs62EgEQkNxz2ig==
  dependencies:
    "@smithy/util-base64" "^4.0.0"
    tslib "^2.6.2"

"@smithy/chunked-blob-reader@^5.0.0":
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/chunked-blob-reader/-/chunked-blob-reader-5.0.0.tgz#3f6ea5ff4e2b2eacf74cefd737aa0ba869b2e0f6"
  integrity sha512-+sKqDBQqb036hh4NPaUiEkYFkTUGYzRsn3EuFhyfQfMy6oGHEUJDurLP9Ufb5dasr/XiAmPNMr6wa9afjQB+Gw==
  dependencies:
    tslib "^2.6.2"

"@smithy/config-resolver@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/config-resolver/-/config-resolver-4.0.0.tgz#0f24a0f47fcbc8749bf5cb0cbfa19a291073bd59"
  integrity sha512-29pIDlUY/a9+ChJPAarPiD9cU8fBtBh0wFnmnhj7j5AhgMzc+uyXdfzmziH6xx2jzw54waSP3HfnFkTANZuPYA==
  dependencies:
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/util-config-provider" "^4.0.0"
    "@smithy/util-middleware" "^4.0.0"
    tslib "^2.6.2"

"@smithy/core@^3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/core/-/core-3.0.0.tgz#087f4da0100b824b6ec8b8c2ef74f6decdd61ce2"
  integrity sha512-pKaas7RWvPljJ8uByCeBa10rtbVJCy4N/Fr7OSPxFezcyG0SQuXWnESZqzXj7m2+A+kPzG6fKyP4wrKidl2Ikg==
  dependencies:
    "@smithy/middleware-serde" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/util-body-length-browser" "^4.0.0"
    "@smithy/util-middleware" "^4.0.0"
    "@smithy/util-stream" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@smithy/credential-provider-imds@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/credential-provider-imds/-/credential-provider-imds-4.0.0.tgz#3fbee0d9203462a195cc3e9ac886907e997c38a0"
  integrity sha512-+hTShyZHiq2AVFOxJja3k6O17DKU6TaZbwr2y1OH5HQtUw2a+7O3mMR+10LVmc39ef72SAj+uFX0IW9rJGaLQQ==
  dependencies:
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/property-provider" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/url-parser" "^4.0.0"
    tslib "^2.6.2"

"@smithy/eventstream-codec@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-codec/-/eventstream-codec-4.0.0.tgz#82881a8e1b10acb01f288e85970fc14bee8f13ad"
  integrity sha512-YvKUUOo3qehqOxNrkax3YKXF1v0ff475FhDgbBmF8Bo0oOOpsXZyltjQnwBzIeTYo446ZPV85KM3kY4YoxUNOg==
  dependencies:
    "@aws-crypto/crc32" "5.2.0"
    "@smithy/types" "^4.0.0"
    "@smithy/util-hex-encoding" "^4.0.0"
    tslib "^2.6.2"

"@smithy/eventstream-serde-browser@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-browser/-/eventstream-serde-browser-4.0.0.tgz#b5b3deffb0a3995c2091db25bee88475ed365be2"
  integrity sha512-YRwsVPJU/DN1VshH8tKs4CxY66HLhmDSw6oZDM2LVIgHODsqpJBcRdEfcnb97ULmgyFrWxTjL9UXpyKPuJXQRA==
  dependencies:
    "@smithy/eventstream-serde-universal" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/eventstream-serde-config-resolver@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-config-resolver/-/eventstream-serde-config-resolver-4.0.0.tgz#580b8d9c5c14925f76a66630636dcfc0567058ec"
  integrity sha512-OZ/aK9LHsZch0VZ6bnf+dPD80kJripnZnkc36QNymnej49VkHJLSNJxsM0pwt53FA6+fUYYMMT0DVDTH1Msq2g==
  dependencies:
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/eventstream-serde-node@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-node/-/eventstream-serde-node-4.0.0.tgz#c3ea67b93567aa924e6e3e6fea44df7077b74ed7"
  integrity sha512-10b4F+zXbzxZHKuP+m2st/C+rEGK7FUut1dNSRw6DQCCfaTUecJGCoHPCmk2CRvuMTzunVpS1BKLMk839318VQ==
  dependencies:
    "@smithy/eventstream-serde-universal" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/eventstream-serde-universal@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/eventstream-serde-universal/-/eventstream-serde-universal-4.0.0.tgz#10325634e7b94625e11c6e65f0dd21413d3624eb"
  integrity sha512-HEhZpf731J3oFYJtaKO3dnV6stIjA+lJwXuXGu/WbSgicDWGAOITUwTt9ynldEFsnFkNu9b/C4ebXnJA16xSCA==
  dependencies:
    "@smithy/eventstream-codec" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/fetch-http-handler@^5.0.0":
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/fetch-http-handler/-/fetch-http-handler-5.0.0.tgz#f432a59e9fd6a066364e7159914e2fec0e475632"
  integrity sha512-jUEq+4056uqsDLRqQb1fm48rrSMBYcBxVvODfiP37ORcV5n9xWJQsINWcIffyYxWTM5K0Y/GOfhSQGDtWpAPpQ==
  dependencies:
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/querystring-builder" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/util-base64" "^4.0.0"
    tslib "^2.6.2"

"@smithy/hash-blob-browser@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/hash-blob-browser/-/hash-blob-browser-4.0.0.tgz#34cd5ef0fb85fb90cbdc81cd4e105273181b3d30"
  integrity sha512-JBXNC2YCDlm9uqP/eQJbK6auahAaq4HndJC2PURxWPRUDjbXDRJS5Npfi+7zSxKOSOWxXCG/3dLE5D8znI9l/w==
  dependencies:
    "@smithy/chunked-blob-reader" "^5.0.0"
    "@smithy/chunked-blob-reader-native" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/hash-node@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/hash-node/-/hash-node-4.0.0.tgz#1f6244f830f80ce196966e8ad4578bd60d1057d1"
  integrity sha512-25OxGYGnG3JPEOTk4iFE03bfmoC6GXUQ4L13z4cNdsS3mkncH22AGSDRfKwwEqutNUxXQZWVy9f72Fm59C9qlg==
  dependencies:
    "@smithy/types" "^4.0.0"
    "@smithy/util-buffer-from" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@smithy/hash-stream-node@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/hash-stream-node/-/hash-stream-node-4.0.0.tgz#319626a797fe086db3df45841ae4c270f9fd9f0c"
  integrity sha512-MRgYnr9atik1c02mdgjjJkNK5A8IvRRlpa/zOdA8PxmQtBCwjODKzobyI166uamxrL20wg7vuKoVSAjQU4IXfw==
  dependencies:
    "@smithy/types" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@smithy/invalid-dependency@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/invalid-dependency/-/invalid-dependency-4.0.0.tgz#69180b347b122b1ae46691697d0eb5ff8ed777ff"
  integrity sha512-0GTyet02HX/sPctEhOExY+3HI7hwkVwOoJg0XnItTJ+Xw7JMuL9FOxALTmKVIV6+wg0kF6veLeg72hVSbD9UCw==
  dependencies:
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/is-array-buffer@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/is-array-buffer/-/is-array-buffer-2.2.0.tgz#f84f0d9f9a36601a9ca9381688bd1b726fd39111"
  integrity sha512-GGP3O9QFD24uGeAXYUjwSTXARoqpZykHadOmA8G5vfJPK0/DC67qa//0qvqrJzL1xc8WQWX7/yc7fwudjPHPhA==
  dependencies:
    tslib "^2.6.2"

"@smithy/is-array-buffer@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/is-array-buffer/-/is-array-buffer-4.0.0.tgz#55a939029321fec462bcc574890075cd63e94206"
  integrity sha512-saYhF8ZZNoJDTvJBEWgeBccCg+yvp1CX+ed12yORU3NilJScfc6gfch2oVb4QgxZrGUx3/ZJlb+c/dJbyupxlw==
  dependencies:
    tslib "^2.6.2"

"@smithy/md5-js@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/md5-js/-/md5-js-4.0.0.tgz#e86165c65e66115476d932be9e3936f74ab4fcd8"
  integrity sha512-NUjbK+M1RNd0J/mM3eh4Yw5SfUrJBsIAea/H5dvc8tirxWFHFDUHJ/CK40/vtY3niiYnygWjZZ+ISydray6Raw==
  dependencies:
    "@smithy/types" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@smithy/middleware-content-length@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-content-length/-/middleware-content-length-4.0.0.tgz#b860215d325ee8484b729f315d9b85d84d6d5c34"
  integrity sha512-nM1RJqLwkSCidumGK8WwNEZ0a0D/4LkwqdPna+QmHrdPoAK6WGLyZFosdMpsAW1OIbDLWGa+r37Mo4Vth4S4kQ==
  dependencies:
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/middleware-endpoint@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-endpoint/-/middleware-endpoint-4.0.0.tgz#fe727b28452bc3fe85399521daab7600966e4174"
  integrity sha512-/f6z5SqUurmqemhBZNhM0c+C7QW0AY/zJpic//sbdu26q98HSPAI/xvzStjYq+UhtWeAe/jaX6gamdL/2r3W1g==
  dependencies:
    "@smithy/core" "^3.0.0"
    "@smithy/middleware-serde" "^4.0.0"
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/shared-ini-file-loader" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/url-parser" "^4.0.0"
    "@smithy/util-middleware" "^4.0.0"
    tslib "^2.6.2"

"@smithy/middleware-retry@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-retry/-/middleware-retry-4.0.0.tgz#35c6ad71a6d662ed2d718bb5ca0b87593cf1433a"
  integrity sha512-K6tsFp3Ik44H3694a+LWoXLV8mqy8zn6/vTw2feU72MaIzi51EHMVNNxxpL6e2GI6oxw8FFRGWgGn8+wQRrHZQ==
  dependencies:
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/service-error-classification" "^4.0.0"
    "@smithy/smithy-client" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/util-middleware" "^4.0.0"
    "@smithy/util-retry" "^4.0.0"
    tslib "^2.6.2"
    uuid "^9.0.1"

"@smithy/middleware-serde@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-serde/-/middleware-serde-4.0.0.tgz#cbb22e84f297e7089f988364325d36d508791fd8"
  integrity sha512-aW4Zo8Cm988RCvhysErzqrQ4YPKgZFhajvgPoZnsWIDaZfT419J17Ahr13Lul3kqGad2dCz7YOrXd7r+UAEj/w==
  dependencies:
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/middleware-stack@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/middleware-stack/-/middleware-stack-4.0.0.tgz#d4d818fc70d44a76a616f990bee4075608a46f0e"
  integrity sha512-4NFaX88RmgVrCyJv/3RsSdqMwxzI/EQa8nvhUDVxmLUMRS2JUdHnliD6IwKuqIwIzz+E1aZK3EhSHUM4HXp3ww==
  dependencies:
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/node-config-provider@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/node-config-provider/-/node-config-provider-4.0.0.tgz#950c451951ddd4e22c4e5ba1178893ade8124303"
  integrity sha512-Crp9rg1ewjqgM2i7pWSpNhfbBa0usyKGDVQLEXTOpu6trFqq3BFLLCgbCE1S18h6mxqKnOqUONq3nWOxUk75XA==
  dependencies:
    "@smithy/property-provider" "^4.0.0"
    "@smithy/shared-ini-file-loader" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/node-http-handler@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/node-http-handler/-/node-http-handler-4.0.0.tgz#d90cdb19cc25c05a077278c07e776a6949944791"
  integrity sha512-WvumtEaFyxaI95zmj6eYlF/vCFCKNyru3P/UUHCUS9BjvajUtNckH2cY3bBfi+qqMPX5gha4g26lcOlE/wPz/Q==
  dependencies:
    "@smithy/abort-controller" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/querystring-builder" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/property-provider@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/property-provider/-/property-provider-4.0.0.tgz#5e328d086a867646b147d55a8551aec9d2e318b2"
  integrity sha512-AJSvY1k3SdM0stGrIjL8/FIjXO7X9I7KkznXDmr76RGz+yvaDHLsLm2hSHyzAlmwEQnHaafSU2dwaV0JcnR/4w==
  dependencies:
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/protocol-http@^5.0.0":
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/protocol-http/-/protocol-http-5.0.0.tgz#bd395e6b6271dcebdef5d846a5dab050fc9c57c9"
  integrity sha512-laAcIHWq9GQ5VdAS71DUrCj5HUHZ/89Ee+HRTLhFR5/E3toBlnZfPG+kqBajwfEB5aSdRuKslfzl5Dzrn3pr8A==
  dependencies:
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/querystring-builder@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/querystring-builder/-/querystring-builder-4.0.0.tgz#7eb2d94d5f9f5d7ef3a003c90af83a1ee734ee42"
  integrity sha512-kMqPDRf+/hwm+Dmk8AQCaYTJxNWWpNdJJteeMm0jwDbmRDqSqHQ7oLEVzvOnbWJu1poVtOhv6v7jsbyx9JASsw==
  dependencies:
    "@smithy/types" "^4.0.0"
    "@smithy/util-uri-escape" "^4.0.0"
    tslib "^2.6.2"

"@smithy/querystring-parser@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/querystring-parser/-/querystring-parser-4.0.0.tgz#d21b7d1396d863d3498a72d561955b5dc77670d7"
  integrity sha512-SbogL1PNEmm28ya0eK2S0EZEbYwe0qpaqSGrODm+uYS6dQ7pekPLVNXjBRuuLIAT26ZF2wTsp6X7AVRBNZd8qw==
  dependencies:
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/service-error-classification@^2.0.4":
  version "2.1.5"
  resolved "https://registry.yarnpkg.com/@smithy/service-error-classification/-/service-error-classification-2.1.5.tgz#0568a977cc0db36299d8703a5d8609c1f600c005"
  integrity sha512-uBDTIBBEdAQryvHdc5W8sS5YX7RQzF683XrHePVdFmAgKiMofU15FLSM0/HU03hKTnazdNRFa0YHS7+ArwoUSQ==
  dependencies:
    "@smithy/types" "^2.12.0"

"@smithy/service-error-classification@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/service-error-classification/-/service-error-classification-4.0.0.tgz#91eca0ac1a454e2166ee1f86577cdc5308115778"
  integrity sha512-hIZreT6aXSG0PK/psT1S+kfeGTnYnRRlf7rU3yDmH/crSVjTbS/5h5w2J7eO2ODrQb3xfhJcYxQBREdwsZk6TA==
  dependencies:
    "@smithy/types" "^4.0.0"

"@smithy/shared-ini-file-loader@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/shared-ini-file-loader/-/shared-ini-file-loader-4.0.0.tgz#ee8334f935138c92e11699bc81be5d1f76e6dce3"
  integrity sha512-Ktupe8msp2GPaKKVfiz3NNUNnslJiGGRoVh3BDpm/RChkQ5INQpqmTc2taE0XChNYumNynLfb3keekIPaiaZeg==
  dependencies:
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/signature-v4@^5.0.0":
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/signature-v4/-/signature-v4-5.0.0.tgz#176ab46d0e161f5e7a08aa3b39930fb0e154ee15"
  integrity sha512-zqcOR1sZTuoA6K3PBNwzu4YgT1pmIwz47tYpgaJjBTfGUIMtcjUaXKtuSKEScdv+0wx45/PbXz0//hk80fky3w==
  dependencies:
    "@smithy/is-array-buffer" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/util-hex-encoding" "^4.0.0"
    "@smithy/util-middleware" "^4.0.0"
    "@smithy/util-uri-escape" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@smithy/smithy-client@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/smithy-client/-/smithy-client-4.0.0.tgz#06bff6c99bfe4b9332a299721c367327eae98c92"
  integrity sha512-AgcZ6B+JuqArYioAbaYrCpTCjYsD3/1hPSXntbN2ipsfc4hE+72RFZevUPYgsKxpy3G+QxuLfqm11i3+oX4oSA==
  dependencies:
    "@smithy/core" "^3.0.0"
    "@smithy/middleware-endpoint" "^4.0.0"
    "@smithy/middleware-stack" "^4.0.0"
    "@smithy/protocol-http" "^5.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/util-stream" "^4.0.0"
    tslib "^2.6.2"

"@smithy/types@^2.12.0":
  version "2.12.0"
  resolved "https://registry.yarnpkg.com/@smithy/types/-/types-2.12.0.tgz#c44845f8ba07e5e8c88eda5aed7e6a0c462da041"
  integrity sha512-QwYgloJ0sVNBeBuBs65cIkTbfzV/Q6ZNPCJ99EICFEdJYG50nGIY/uYXp+TbsdJReIuPr0a0kXmCvren3MbRRw==
  dependencies:
    tslib "^2.6.2"

"@smithy/types@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/types/-/types-4.0.0.tgz#7458c1c4dde3c6cf23221370acf5acd03215de6e"
  integrity sha512-aNwIGSOgDOhtTRY/rrn2aeuQeKw/IFrQ998yK5l6Ah853WeWIEmFPs/EO4OpfADEdcK+igWnZytm/oUgkLgUYg==
  dependencies:
    tslib "^2.6.2"

"@smithy/url-parser@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/url-parser/-/url-parser-4.0.0.tgz#c379b812860f88a435164cd5d3d940009e464ea7"
  integrity sha512-2iPpuLoH0hCKpLtqVgilHtpPKsmHihbkwBm3h3RPuEctdmuiOlFRZ2ZI8IHSwl0o4ff5IdyyJ0yu/2tS9KpUug==
  dependencies:
    "@smithy/querystring-parser" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/util-base64@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-base64/-/util-base64-4.0.0.tgz#8345f1b837e5f636e5f8470c4d1706ae0c6d0358"
  integrity sha512-CvHfCmO2mchox9kjrtzoHkWHxjHZzaFojLc8quxXY7WAAMAg43nuxwv95tATVgQFNDwd4M9S1qFzj40Ul41Kmg==
  dependencies:
    "@smithy/util-buffer-from" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@smithy/util-body-length-browser@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-body-length-browser/-/util-body-length-browser-4.0.0.tgz#965d19109a4b1e5fe7a43f813522cce718036ded"
  integrity sha512-sNi3DL0/k64/LO3A256M+m3CDdG6V7WKWHdAiBBMUN8S3hK3aMPhwnPik2A/a2ONN+9doY9UxaLfgqsIRg69QA==
  dependencies:
    tslib "^2.6.2"

"@smithy/util-body-length-node@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-body-length-node/-/util-body-length-node-4.0.0.tgz#3db245f6844a9b1e218e30c93305bfe2ffa473b3"
  integrity sha512-q0iDP3VsZzqJyje8xJWEJCNIu3lktUGVoSy1KB0UWym2CL1siV3artm+u1DFYTLejpsrdGyCSWBdGNjJzfDPjg==
  dependencies:
    tslib "^2.6.2"

"@smithy/util-buffer-from@^2.2.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-buffer-from/-/util-buffer-from-2.2.0.tgz#6fc88585165ec73f8681d426d96de5d402021e4b"
  integrity sha512-IJdWBbTcMQ6DA0gdNhh/BwrLkDR+ADW5Kr1aZmd4k3DIF6ezMV4R2NIAmT08wQJ3yUK82thHWmC/TnK/wpMMIA==
  dependencies:
    "@smithy/is-array-buffer" "^2.2.0"
    tslib "^2.6.2"

"@smithy/util-buffer-from@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-buffer-from/-/util-buffer-from-4.0.0.tgz#b23b7deb4f3923e84ef50c8b2c5863d0dbf6c0b9"
  integrity sha512-9TOQ7781sZvddgO8nxueKi3+yGvkY35kotA0Y6BWRajAv8jjmigQ1sBwz0UX47pQMYXJPahSKEKYFgt+rXdcug==
  dependencies:
    "@smithy/is-array-buffer" "^4.0.0"
    tslib "^2.6.2"

"@smithy/util-config-provider@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-config-provider/-/util-config-provider-4.0.0.tgz#e0c7c8124c7fba0b696f78f0bd0ccb060997d45e"
  integrity sha512-L1RBVzLyfE8OXH+1hsJ8p+acNUSirQnWQ6/EgpchV88G6zGBTDPdXiiExei6Z1wR2RxYvxY/XLw6AMNCCt8H3w==
  dependencies:
    tslib "^2.6.2"

"@smithy/util-defaults-mode-browser@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-4.0.0.tgz#925046f4c18607b7af409be914b13159dbf04c68"
  integrity sha512-7wqsXkzaJkpSqV+Ca95pN9yQutXvhaKeCxGGmjWnRGXY1fW/yR7wr1ouNnUYCJuTS8MvmB61xp5Qdj8YMgIA2Q==
  dependencies:
    "@smithy/property-provider" "^4.0.0"
    "@smithy/smithy-client" "^4.0.0"
    "@smithy/types" "^4.0.0"
    bowser "^2.11.0"
    tslib "^2.6.2"

"@smithy/util-defaults-mode-node@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-4.0.0.tgz#110cfa24dbc7ebcdbde71770bb7cc2daa19ea8dd"
  integrity sha512-P8VK885kiRT6TEtvcQvz+L/+xIhrDhCmM664ToUtrshFSBhwGYaJWlQNAH9fXlMhwnNvR+tmh1KngKJIgQP6bw==
  dependencies:
    "@smithy/config-resolver" "^4.0.0"
    "@smithy/credential-provider-imds" "^4.0.0"
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/property-provider" "^4.0.0"
    "@smithy/smithy-client" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/util-endpoints@^3.0.0":
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-endpoints/-/util-endpoints-3.0.0.tgz#d1abf9ac6311e0876b444845f1b2ef47e953b5ce"
  integrity sha512-kyOKbkg77lsIVN2jC08uEWm3s16eK1YdVDyi/nKeBDbUnjR30dmTEga79E5tiu5OEgTAdngNswA9V+L6xa65sA==
  dependencies:
    "@smithy/node-config-provider" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/util-hex-encoding@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-hex-encoding/-/util-hex-encoding-4.0.0.tgz#dd449a6452cffb37c5b1807ec2525bb4be551e8d"
  integrity sha512-Yk5mLhHtfIgW2W2WQZWSg5kuMZCVbvhFmC7rV4IO2QqnZdbEFPmQnCcGMAX2z/8Qj3B9hYYNjZOhWym+RwhePw==
  dependencies:
    tslib "^2.6.2"

"@smithy/util-middleware@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-middleware/-/util-middleware-4.0.0.tgz#0401ada0ebd61af1c0965c6e15e379fe6ebab27f"
  integrity sha512-ncuvK6ekpDqtASHg7jx3d3nrkD2BsTzUmeVgvtepuHGxtySY8qUlb4SiNRdxHYcv3pL2SwdXs70RwKBU0edW5w==
  dependencies:
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/util-retry@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-retry/-/util-retry-4.0.0.tgz#c08f1894278afce4ab3c00b55883f13026f04222"
  integrity sha512-64WFoC19NVuHh3HQO2QbGw+n6GzQ6VH/drxwXLOU3GDLKxUUzIR9XNm9aTVqh8/7R+y+DgITiv5LpX5XdOy73A==
  dependencies:
    "@smithy/service-error-classification" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@smithy/util-stream@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-stream/-/util-stream-4.0.0.tgz#c604497ae110216e92ccd586a40b8cf4fa8d9b62"
  integrity sha512-ctcLq8Ogi2FQuGy2RxJXGGrozhFEb4p9FawB5SpTNAkNQWbNHcwrGcVSVI3FtdQtkNAINLiEdMnrx+UN/mafvw==
  dependencies:
    "@smithy/fetch-http-handler" "^5.0.0"
    "@smithy/node-http-handler" "^4.0.0"
    "@smithy/types" "^4.0.0"
    "@smithy/util-base64" "^4.0.0"
    "@smithy/util-buffer-from" "^4.0.0"
    "@smithy/util-hex-encoding" "^4.0.0"
    "@smithy/util-utf8" "^4.0.0"
    tslib "^2.6.2"

"@smithy/util-uri-escape@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-uri-escape/-/util-uri-escape-4.0.0.tgz#a96c160c76f3552458a44d8081fade519d214737"
  integrity sha512-77yfbCbQMtgtTylO9itEAdpPXSog3ZxMe09AEhm0dU0NLTalV70ghDZFR+Nfi1C60jnJoh/Re4090/DuZh2Omg==
  dependencies:
    tslib "^2.6.2"

"@smithy/util-utf8@^2.0.0":
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-utf8/-/util-utf8-2.3.0.tgz#dd96d7640363259924a214313c3cf16e7dd329c5"
  integrity sha512-R8Rdn8Hy72KKcebgLiv8jQcQkXoLMOGGv5uI1/k0l+snqkOzQ1R0ChUBCxWMlBsFMekWjq0wRudIweFs7sKT5A==
  dependencies:
    "@smithy/util-buffer-from" "^2.2.0"
    tslib "^2.6.2"

"@smithy/util-utf8@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-utf8/-/util-utf8-4.0.0.tgz#09ca2d9965e5849e72e347c130f2a29d5c0c863c"
  integrity sha512-b+zebfKCfRdgNJDknHCob3O7FpeYQN6ZG6YLExMcasDHsCXlsXCEuiPZeLnJLpwa5dvPetGlnGCiMHuLwGvFow==
  dependencies:
    "@smithy/util-buffer-from" "^4.0.0"
    tslib "^2.6.2"

"@smithy/util-waiter@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@smithy/util-waiter/-/util-waiter-4.0.0.tgz#66f2cd8096c6b405f92b8fe0c714070a0a47b251"
  integrity sha512-C8dSfGmZH0ecrmnJOw4aDXJ47krihnUtee8eDZ2fA+28wTjD4TVM3L/bBQYnBBlDVWcYzldLV7loPRsPc1kERg==
  dependencies:
    "@smithy/abort-controller" "^4.0.0"
    "@smithy/types" "^4.0.0"
    tslib "^2.6.2"

"@types/aws-lambda@^8.10.92":
  version "8.10.141"
  resolved "https://registry.yarnpkg.com/@types/aws-lambda/-/aws-lambda-8.10.141.tgz#f4d2fddf2bda51c4b2885b1abc3d1e9a1d6d2a7a"
  integrity sha512-SMWlRBukG9KV8ZNjwemp2AzDibp/czIAeKKTw09nCPbWxVskIxactCJCGOp4y6I1hCMY7T7UGfySvBLXNeUbEw==

"@types/body-parser@*":
  version "1.19.5"
  resolved "https://registry.yarnpkg.com/@types/body-parser/-/body-parser-1.19.5.tgz#04ce9a3b677dc8bd681a17da1ab9835dc9d3ede4"
  integrity sha512-fB3Zu92ucau0iQ0JMCFQE7b/dv8Ot07NI3KaZIkIUNXq82k4eBAqUaneXfleGY9JWskeS9y+u0nXMyspcuQrCg==
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/cls-hooked@^4.3.3":
  version "4.3.9"
  resolved "https://registry.yarnpkg.com/@types/cls-hooked/-/cls-hooked-4.3.9.tgz#2cff2f7ca961d53213ef626f96afa986a66d3fad"
  integrity sha512-CMtHMz6Q/dkfcHarq9nioXH8BDPP+v5xvd+N90lBQ2bdmu06UvnLDqxTKoOJzz4SzIwb/x9i4UXGAAcnUDuIvg==
  dependencies:
    "@types/node" "*"

"@types/connect@*":
  version "3.4.38"
  resolved "https://registry.yarnpkg.com/@types/connect/-/connect-3.4.38.tgz#5ba7f3bc4fbbdeaff8dded952e5ff2cc53f8d858"
  integrity sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==
  dependencies:
    "@types/node" "*"

"@types/express-serve-static-core@^5.0.0":
  version "5.0.3"
  resolved "https://registry.yarnpkg.com/@types/express-serve-static-core/-/express-serve-static-core-5.0.3.tgz#04174d3f0836863467b7fbcbbbcd69441d205715"
  integrity sha512-JEhMNwUJt7bw728CydvYzntD0XJeTmDnvwLlbfbAhE7Tbslm/ax6bdIiUwTgeVlZTsJQPwZwKpAkyDtIjsvx3g==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*":
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/@types/express/-/express-5.0.0.tgz#13a7d1f75295e90d19ed6e74cab3678488eaa96c"
  integrity sha512-DvZriSMehGHL1ZNLzi6MidnsDhUZM/x2pRdDIKdwbUNqqwHxMlRdkxtn6/EPKyqKpHqTl/4nRZsRNLpZxZRpPQ==
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^5.0.0"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/http-errors@*":
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/@types/http-errors/-/http-errors-2.0.4.tgz#7eb47726c391b7345a6ec35ad7f4de469cf5ba4f"
  integrity sha512-D0CFMMtydbJAegzOyHjtiKPLlvnm3iTZyZRSZoLq2mRhDdmLfIWOCYPfQJ4cu2erKghU++QvjcUjp/5h7hESpA==

"@types/mime@^1":
  version "1.3.5"
  resolved "https://registry.yarnpkg.com/@types/mime/-/mime-1.3.5.tgz#1ef302e01cf7d2b5a0fa526790c9123bf1d06690"
  integrity sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==

"@types/mysql@*":
  version "2.15.26"
  resolved "https://registry.yarnpkg.com/@types/mysql/-/mysql-2.15.26.tgz#f0de1484b9e2354d587e7d2bd17a873cc8300836"
  integrity sha512-DSLCOXhkvfS5WNNPbfn2KdICAmk8lLc+/PNvnPnF7gOdMZCxopXduqv0OQ13y/yA/zXTSikZZqVgybUxOEg6YQ==
  dependencies:
    "@types/node" "*"

"@types/node@*":
  version "22.10.5"
  resolved "https://registry.yarnpkg.com/@types/node/-/node-22.10.5.tgz#95af89a3fb74a2bb41ef9927f206e6472026e48b"
  integrity sha512-F8Q+SeGimwOo86fiovQh8qiXfFEh2/ocYv7tU5pJ3EXMSSxk1Joj5wefpFK2fHTf/N6HKGSxIDBT9f3gCxXPkQ==
  dependencies:
    undici-types "~6.20.0"

"@types/pg@*":
  version "8.11.10"
  resolved "https://registry.yarnpkg.com/@types/pg/-/pg-8.11.10.tgz#b8fb2b2b759d452fe3ec182beadd382563b63291"
  integrity sha512-LczQUW4dbOQzsH2RQ5qoeJ6qJPdrcM/DcMLoqWQkMLMsq83J5lAX3LXjdkWdpscFy67JSOWDnh7Ny/sPFykmkg==
  dependencies:
    "@types/node" "*"
    pg-protocol "*"
    pg-types "^4.0.1"

"@types/qs@*":
  version "6.9.17"
  resolved "https://registry.yarnpkg.com/@types/qs/-/qs-6.9.17.tgz#fc560f60946d0aeff2f914eb41679659d3310e1a"
  integrity sha512-rX4/bPcfmvxHDv0XjfJELTTr+iB+tn032nPILqHm5wbthUUUuVtNGGqzhya9XUxjTP8Fpr0qYgSZZKxGY++svQ==

"@types/range-parser@*":
  version "1.2.7"
  resolved "https://registry.yarnpkg.com/@types/range-parser/-/range-parser-1.2.7.tgz#50ae4353eaaddc04044279812f52c8c65857dbcb"
  integrity sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==

"@types/send@*":
  version "0.17.4"
  resolved "https://registry.yarnpkg.com/@types/send/-/send-0.17.4.tgz#6619cd24e7270793702e4e6a4b958a9010cfc57a"
  integrity sha512-x2EM6TJOybec7c52BX0ZspPodMsQUd5L6PRwOunVyVUhXiBSKf3AezDL8Dgvgt5o0UfKNfuA0eMLr2wLT4AiBA==
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-static@*":
  version "1.15.7"
  resolved "https://registry.yarnpkg.com/@types/serve-static/-/serve-static-1.15.7.tgz#22174bbd74fb97fe303109738e9b5c2f3064f714"
  integrity sha512-W8Ym+h8nhuRwaKPaDw34QUkwsGi6Rc4yYqvKFo5rm2FUEhCFbzVWrxXUxuKK8TASjWsysJY0nsmNCGhCOIsrOw==
  dependencies:
    "@types/http-errors" "*"
    "@types/node" "*"
    "@types/send" "*"

"@types/uuid@^9.0.1":
  version "9.0.8"
  resolved "https://registry.yarnpkg.com/@types/uuid/-/uuid-9.0.8.tgz#7545ba4fc3c003d6c756f651f3bf163d8f0f29ba"
  integrity sha512-jg+97EGIcY9AGHJJRaaPVgetKDsrTgbRjQ5Msgjh/DQKEFl0DtyRr/VCOyD1T2R1MNeWPK/u7JoGhlDZnKBAfA==

"@types/webidl-conversions@*":
  version "7.0.3"
  resolved "https://registry.yarnpkg.com/@types/webidl-conversions/-/webidl-conversions-7.0.3.tgz#1306dbfa53768bcbcfc95a1c8cde367975581859"
  integrity sha512-CiJJvcRtIgzadHCYXw7dqEnMNRjhGZlYK05Mj9OyktqV8uVT8fD2BFOB7S1uwBE3Kj2Z+4UyPmFw/Ixgw/LAlA==

"@types/whatwg-url@^8.2.1":
  version "8.2.2"
  resolved "https://registry.yarnpkg.com/@types/whatwg-url/-/whatwg-url-8.2.2.tgz#749d5b3873e845897ada99be4448041d4cc39e63"
  integrity sha512-FtQu10RWgn3D9U4aazdwIE2yzphmTJREDqNdODHrbrZmmMqI0vMheC/6NE/J1Yveaj8H+ela+YwWTjq5PGmuhA==
  dependencies:
    "@types/node" "*"
    "@types/webidl-conversions" "*"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.yarnpkg.com/argparse/-/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==
  dependencies:
    sprintf-js "~1.0.2"

async-hook-jl@^1.7.6:
  version "1.7.6"
  resolved "https://registry.yarnpkg.com/async-hook-jl/-/async-hook-jl-1.7.6.tgz#4fd25c2f864dbaf279c610d73bf97b1b28595e68"
  integrity sha512-gFaHkFfSxTjvoxDMYqDuGHlcRyUuamF8s+ZTtJdDzqjws4mCt7v0vuV79/E2Wr2/riMQgtG4/yUtXWs1gZ7JMg==
  dependencies:
    stack-chain "^1.3.7"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

atomic-batcher@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/atomic-batcher/-/atomic-batcher-1.0.2.tgz#d16901d10ccec59516c197b9ccd8930689b813b4"
  integrity sha512-EFGCRj4kLX1dHv1cDzTk+xbjBFj1GnJDpui52YmEcxxHHEWjYyT6l51U7n6WQ28osZH4S9gSybxe56Vm7vB61Q==

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz#a5cc375d6a03c2efc87a553f3e0b1522def14846"
  integrity sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==
  dependencies:
    possible-typed-array-names "^1.0.0"

aws-sdk@^2.1324.0:
  version "2.1692.0"
  resolved "https://registry.yarnpkg.com/aws-sdk/-/aws-sdk-2.1692.0.tgz#9dac5f7bfcc5ab45825cc8591b12753aa7d2902c"
  integrity sha512-x511uiJ/57FIsbgUe5csJ13k3uzu25uWQE+XqfBis/sB0SFoiElJWXRkgEAUh0U6n40eT3ay5Ue4oPkRMu1LYw==
  dependencies:
    buffer "4.9.2"
    events "1.1.1"
    ieee754 "1.1.13"
    jmespath "0.16.0"
    querystring "0.2.0"
    sax "1.2.1"
    url "0.10.3"
    util "^0.12.4"
    uuid "8.0.0"
    xml2js "0.6.2"

aws-xray-sdk-core@3.10.2, aws-xray-sdk-core@^3.3.4, aws-xray-sdk-core@^3.5.3:
  version "3.10.2"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk-core/-/aws-xray-sdk-core-3.10.2.tgz#59cb76dedb17cd9ec94bc47206a5410531029480"
  integrity sha512-hAFEB+Stqm4FoQmIuyw5AzGVJh3BSfvLjK7IK4YYRXXLt1Oq9KS6pv2samYgRTTTXsxhmVpDjiYF3Xo/gfXIXA==
  dependencies:
    "@aws-sdk/types" "^3.4.1"
    "@smithy/service-error-classification" "^2.0.4"
    "@types/cls-hooked" "^4.3.3"
    atomic-batcher "^1.0.2"
    cls-hooked "^4.2.2"
    semver "^7.5.3"

aws-xray-sdk-express@3.10.2:
  version "3.10.2"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk-express/-/aws-xray-sdk-express-3.10.2.tgz#78eb4bd5584292816ec8aeb445698d3eb3922cf0"
  integrity sha512-1NJnQNTmRcQSl0hdwIdQ+3UXhzeXTYR+yyY87bmzDDjHer1GPGQYeYRhkpCGLx8bRF7JpYlHoclA1RVhH3pWMw==
  dependencies:
    "@types/express" "*"

aws-xray-sdk-mysql@3.10.2:
  version "3.10.2"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk-mysql/-/aws-xray-sdk-mysql-3.10.2.tgz#edd4ba39f996d7802acc48c0cc365135921fcedc"
  integrity sha512-TybN+z0XIWbputEy57T+jQEp5OKFs0daRrQ0t8aU+jAJfpuVgN69WoQrV/LFj2HITVL+ISMQsaUR5dvxWY+ZYQ==
  dependencies:
    "@types/mysql" "*"

aws-xray-sdk-postgres@3.10.2:
  version "3.10.2"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk-postgres/-/aws-xray-sdk-postgres-3.10.2.tgz#1855f24d8c9aeff6470cd7c18bfe9a5582c7b9d0"
  integrity sha512-ku17VjrT25BpCYUb5Vqcfh8pkc62SaVIeritCH2No85LOzU9jl0oPUhlRG4NzHkdmIJuUtLP1IxQlwIGAQgreg==
  dependencies:
    "@types/pg" "*"

aws-xray-sdk@^3.3.4:
  version "3.10.2"
  resolved "https://registry.yarnpkg.com/aws-xray-sdk/-/aws-xray-sdk-3.10.2.tgz#1b3264a33be2d3a86fdaecdf30903f5f208e0776"
  integrity sha512-T9Qwq65hUQo4GtZ7WPAzpLGd7y8bDKODlJkKAYsQKMcUIIpMPYWsSxd38ZLy3uwTY0ErkrG6Pqmc5Zs1p0BmZg==
  dependencies:
    aws-xray-sdk-core "3.10.2"
    aws-xray-sdk-express "3.10.2"
    aws-xray-sdk-mysql "3.10.2"
    aws-xray-sdk-postgres "3.10.2"

axios@^1.6.7:
  version "1.7.9"
  resolved "https://registry.yarnpkg.com/axios/-/axios-1.7.9.tgz#d7d071380c132a24accda1b2cfc1535b79ec650a"
  integrity sha512-LhLcE7Hbiryz8oMDdDptSrWowmB4Bl6RCt6sIJKpRB4XtVf0iEgewX3au/pJqm+Py1kCASkb/FFKjxQaLtxJvw==
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

base64-js@^1.0.2, base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://registry.yarnpkg.com/base64-js/-/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

basic-ftp@^5.0.1:
  version "5.0.5"
  resolved "https://registry.yarnpkg.com/basic-ftp/-/basic-ftp-5.0.5.tgz#14a474f5fffecca1f4f406f1c26b18f800225ac0"
  integrity sha512-4Bcg1P8xhUuqcii/S0Z9wiHIrQVPMermM1any+MX5GeGD7faD3/msQUDGLol9wOcz4/jbg/WJnGqoJF6LiBdtg==

bowser@^2.11.0:
  version "2.11.0"
  resolved "https://registry.yarnpkg.com/bowser/-/bowser-2.11.0.tgz#5ca3c35757a7aa5771500c70a73a9f91ef420a8f"
  integrity sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==

bson@^4.7.2:
  version "4.7.2"
  resolved "https://registry.yarnpkg.com/bson/-/bson-4.7.2.tgz#320f4ad0eaf5312dd9b45dc369cc48945e2a5f2e"
  integrity sha512-Ry9wCtIZ5kGqkJoi6aD8KjxFZEx78guTQDnpXWiNthsxzrxAK/i8E6pCHAIZTbaEFWcOCvbecMukfK7XUvyLpQ==
  dependencies:
    buffer "^5.6.0"

buffer@4.9.2:
  version "4.9.2"
  resolved "https://registry.yarnpkg.com/buffer/-/buffer-4.9.2.tgz#230ead344002988644841ab0244af8c44bbe3ef8"
  integrity sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

buffer@^5.6.0:
  version "5.7.1"
  resolved "https://registry.yarnpkg.com/buffer/-/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.1.tgz#32e5892e6361b29b0b545ba6f7763378daca2840"
  integrity sha512-BhYE+WDaywFg2TBWYNXAE+8B1ATnThNBqXHP5nQu0jWJdVvY2hvkpyB3qOmtmDePiS5/BDQ8wASEWGMWRG148g==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.8:
  version "1.0.8"
  resolved "https://registry.yarnpkg.com/call-bind/-/call-bind-1.0.8.tgz#0736a9660f537e3388826f440d5ec45f744eaa4c"
  integrity sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2, call-bound@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/call-bound/-/call-bound-1.0.3.tgz#41cfd032b593e39176a71533ab4f384aa04fd681"
  integrity sha512-YTd+6wGlNlPxSuri7Y6X8tY2dmm12UMH66RpKMhiX6rsk5wXXnYgbUcOt8kiS31/AjfoTOvCsE+w8nZQLQnzHA==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    get-intrinsic "^1.2.6"

cls-hooked@^4.2.2:
  version "4.2.2"
  resolved "https://registry.yarnpkg.com/cls-hooked/-/cls-hooked-4.2.2.tgz#ad2e9a4092680cdaffeb2d3551da0e225eae1908"
  integrity sha512-J4Xj5f5wq/4jAvcdgoGsL3G103BtWpZrMo8NEinRltN+xpTZdI+M38pyQqhuFU/P792xkMFvnKSf+Lm81U1bxw==
  dependencies:
    async-hook-jl "^1.7.6"
    emitter-listener "^1.0.1"
    semver "^5.4.1"

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.yarnpkg.com/combined-stream/-/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

debug@4.x:
  version "4.4.0"
  resolved "https://registry.yarnpkg.com/debug/-/debug-4.4.0.tgz#2b3f2aea2ffeb776477460267377dc8710faba8a"
  integrity sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==
  dependencies:
    ms "^2.1.3"

define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/define-data-property/-/define-data-property-1.1.4.tgz#894dc141bb7d3060ae4366f6a0107e68fbe48c5e"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/dunder-proto/-/dunder-proto-1.0.1.tgz#d7ae667e1dc83482f8b70fd0f6eefc50da30f58a"
  integrity sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

emitter-listener@^1.0.1:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/emitter-listener/-/emitter-listener-1.1.2.tgz#56b140e8f6992375b3d7cb2cab1cc7432d9632e8"
  integrity sha512-Bt1sBAGFHY9DKY+4/2cV6izcKJUf5T7/gkdmkxzX/qv9CcGH8xSwVRW5mtX03SWJtRTWSOpzCuWN9rBFYZepZQ==
  dependencies:
    shimmer "^1.2.0"

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/es-define-property/-/es-define-property-1.0.1.tgz#983eb2f9a6724e9303f61addf011c72e09e0b0fa"
  integrity sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/es-errors/-/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-object-atoms@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/es-object-atoms/-/es-object-atoms-1.0.0.tgz#ddb55cd47ac2e240701260bc2a8e31ecb643d941"
  integrity sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw==
  dependencies:
    es-errors "^1.3.0"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/esprima/-/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==

events@1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/events/-/events-1.1.1.tgz#9ebdb7635ad099c70dcc4c2a1f5004288e8bd924"
  integrity sha512-kEcvvCBByWXGnZy6JUlgAp2gBIUjfCAV6P6TgT1/aaQKcmuAEC4OZTV1I4EWQLz2gxZw76atuVyvHhTxvi0Flw==

fast-xml-parser@4.4.1:
  version "4.4.1"
  resolved "https://registry.yarnpkg.com/fast-xml-parser/-/fast-xml-parser-4.4.1.tgz#86dbf3f18edf8739326447bcaac31b4ae7f6514f"
  integrity sha512-xkjOecfnKGkSsOwtZ5Pz7Us/T6mrbPQrq0nh+aCO5V9nk5NLWmasAHumTKjiPJPWANe+kAZ84Jc8ooJkzZ88Sw==
  dependencies:
    strnum "^1.0.5"

fast-xml-parser@^4.0.1:
  version "4.5.1"
  resolved "https://registry.yarnpkg.com/fast-xml-parser/-/fast-xml-parser-4.5.1.tgz#a7e665ff79b7919100a5202f23984b6150f9b31e"
  integrity sha512-y655CeyUQ+jj7KBbYMc4FG01V8ZQqjN+gDYGJ50RtfsUB8iG9AmwmwoAgeKLJdmueKKMrH1RJ7yXHTSoczdv5w==
  dependencies:
    strnum "^1.0.5"

follow-redirects@^1.15.6:
  version "1.15.9"
  resolved "https://registry.yarnpkg.com/follow-redirects/-/follow-redirects-1.15.9.tgz#a604fa10e443bf98ca94228d9eebcc2e8a2c8ee1"
  integrity sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==

for-each@^0.3.3:
  version "0.3.3"
  resolved "https://registry.yarnpkg.com/for-each/-/for-each-0.3.3.tgz#69b447e88a0a5d32c3e7084f3f1710034b21376e"
  integrity sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==
  dependencies:
    is-callable "^1.1.3"

form-data@^4.0.0:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/form-data/-/form-data-4.0.1.tgz#ba1076daaaa5bfd7e99c1a6cb02aa0a5cff90d48"
  integrity sha512-tzN8e4TX8+kkxGPK8D5u0FNmjPUjw3lwC9lSLxxoB/+GtsJG91CO8bSWy73APlgAZzZbXEYZJuxjkHH2w+Ezhw==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

fs-extra@^10.0.0:
  version "10.1.0"
  resolved "https://registry.yarnpkg.com/fs-extra/-/fs-extra-10.1.0.tgz#02873cfbc4084dde127eaa5f9905eef2325d1abf"
  integrity sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs@^0.0.1-security:
  version "0.0.1-security"
  resolved "https://registry.yarnpkg.com/fs/-/fs-0.0.1-security.tgz#8a7bd37186b6dddf3813f23858b57ecaaf5e41d4"
  integrity sha512-3XY9e1pP0CVEUCdj5BmfIZxRBTSDycnbqhIOGec9QYtmVH2fbLpj86CFWkrNOkt/Fvty4KZG5lTglL9j/gJ87w==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/function-bind/-/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

get-intrinsic@^1.2.4, get-intrinsic@^1.2.6:
  version "1.2.7"
  resolved "https://registry.yarnpkg.com/get-intrinsic/-/get-intrinsic-1.2.7.tgz#dcfcb33d3272e15f445d15124bc0a216189b9044"
  integrity sha512-VW6Pxhsrk0KAOqs3WEd0klDiF/+V7gQOpAvY1jVU/LHmaD/kQO4523aiJuikX/QAKYiW6x8Jh+RJej1almdtCA==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    function-bind "^1.1.2"
    get-proto "^1.0.0"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-proto@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/get-proto/-/get-proto-1.0.1.tgz#150b3f2743869ef3e851ec0c49d15b1d14d00ee1"
  integrity sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/gopd/-/gopd-1.2.0.tgz#89f56b8217bdbc8802bd299df6d7f1081d7e51a1"
  integrity sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==

graceful-fs@^4.1.6, graceful-fs@^4.2.0:
  version "4.2.11"
  resolved "https://registry.yarnpkg.com/graceful-fs/-/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz#963ed7d071dc7bf5f084c5bfbe0d1b6222586854"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/has-symbols/-/has-symbols-1.1.0.tgz#fc9c6a783a084951d0b971fe1018de813707a338"
  integrity sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==

has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/has-tostringtag/-/has-tostringtag-1.0.2.tgz#2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc"
  integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/hasown/-/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

ieee754@1.1.13:
  version "1.1.13"
  resolved "https://registry.yarnpkg.com/ieee754/-/ieee754-1.1.13.tgz#ec168558e95aa181fd87d37f55c32bbcb6708b84"
  integrity sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg==

ieee754@^1.1.13, ieee754@^1.1.4:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/ieee754/-/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==

inherits@^2.0.3:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

install@^0.13.0:
  version "0.13.0"
  resolved "https://registry.yarnpkg.com/install/-/install-0.13.0.tgz#6af6e9da9dd0987de2ab420f78e60d9c17260776"
  integrity sha512-zDml/jzr2PKU9I8J/xyZBQn8rPCAY//UOYNmR01XwNwyfhEWObo2SWfSl1+0tm1u6PhxLwDnfsT/6jB7OUxqFA==

ip-address@^9.0.5:
  version "9.0.5"
  resolved "https://registry.yarnpkg.com/ip-address/-/ip-address-9.0.5.tgz#117a960819b08780c3bd1f14ef3c1cc1d3f3ea5a"
  integrity sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g==
  dependencies:
    jsbn "1.1.0"
    sprintf-js "^1.1.3"

is-arguments@^1.0.4:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/is-arguments/-/is-arguments-1.2.0.tgz#ad58c6aecf563b78ef2bf04df540da8f5d7d8e1b"
  integrity sha512-7bVbi0huj/wrIAOzb8U1aszg9kdi3KN/CyU19CTI7tAoZYEZoL9yCDXpbXN+uPsuWnP02cyug1gleqq+TU+YCA==
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-callable@^1.1.3:
  version "1.2.7"
  resolved "https://registry.yarnpkg.com/is-callable/-/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==

is-generator-function@^1.0.7:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/is-generator-function/-/is-generator-function-1.1.0.tgz#bf3eeda931201394f57b5dba2800f91a238309ca"
  integrity sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==
  dependencies:
    call-bound "^1.0.3"
    get-proto "^1.0.0"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-regex@^1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/is-regex/-/is-regex-1.2.1.tgz#76d70a3ed10ef9be48eb577887d74205bf0cad22"
  integrity sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-typed-array@^1.1.3:
  version "1.1.15"
  resolved "https://registry.yarnpkg.com/is-typed-array/-/is-typed-array-1.1.15.tgz#4bfb4a45b61cee83a5a46fba778e4e8d59c0ce0b"
  integrity sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==
  dependencies:
    which-typed-array "^1.1.16"

isarray@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==

jmespath@0.16.0:
  version "0.16.0"
  resolved "https://registry.yarnpkg.com/jmespath/-/jmespath-0.16.0.tgz#b15b0a85dfd4d930d43e69ed605943c802785076"
  integrity sha512-9FzQjJ7MATs1tSpnco1K6ayiYE3figslrXA72G2HQ/n76RzvYlofyi5QM+iX4YRs/pu3yzxlVQSST23+dMDknw==

js-md5@^0.7.3:
  version "0.7.3"
  resolved "https://registry.yarnpkg.com/js-md5/-/js-md5-0.7.3.tgz#b4f2fbb0b327455f598d6727e38ec272cd09c3f2"
  integrity sha512-ZC41vPSTLKGwIRjqDh8DfXoCrdQIyBgspJVPXHBGu4nZlAEvG3nf+jO9avM9RmLiGakg7vz974ms99nEV0tmTQ==

js-sha1@^0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/js-sha1/-/js-sha1-0.6.0.tgz#adbee10f0e8e18aa07cdea807cf08e9183dbc7f9"
  integrity sha512-01gwBFreYydzmU9BmZxpVk6svJJHrVxEN3IOiGl6VO93bVKYETJ0sIth6DASI6mIFdt7NmfX9UiByRzsYHGU9w==

js-yaml@3.14.1:
  version "3.14.1"
  resolved "https://registry.yarnpkg.com/js-yaml/-/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/jsbn/-/jsbn-1.1.0.tgz#b01307cb29b618a1ed26ec79e911f803c4da0040"
  integrity sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A==

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.yarnpkg.com/jsonfile/-/jsonfile-6.1.0.tgz#bc55b2634793c679ec6403094eb13698a6ec0aae"
  integrity sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

kareem@2.5.1:
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/kareem/-/kareem-2.5.1.tgz#7b8203e11819a8e77a34b3517d3ead206764d15d"
  integrity sha512-7jFxRVm+jD+rkq3kY0iZDJfsO2/t4BBPeEb2qKn2lR/9KhuksYk5hxzfRYWMPV8P/x2d0kHD306YyWLzjjH+uA==

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.yarnpkg.com/lodash.merge/-/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/math-intrinsics/-/math-intrinsics-1.1.0.tgz#a0dd74be81e2aa5c2f27e65ce283605ee4e2b7f9"
  integrity sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==

memory-pager@^1.0.2:
  version "1.5.0"
  resolved "https://registry.yarnpkg.com/memory-pager/-/memory-pager-1.5.0.tgz#d8751655d22d384682741c972f2c3d6dfa3e66b5"
  integrity sha512-ZS4Bp4r/Zoeq6+NLJpP+0Zzm0pR8whtGPf1XExKLJBAczGMnSi3It14OiNCStjQjM6NU1okjQGSxgEZN8eBYKg==

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12:
  version "2.1.35"
  resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

moment@^2.29.1:
  version "2.30.1"
  resolved "https://registry.yarnpkg.com/moment/-/moment-2.30.1.tgz#f8c91c07b7a786e30c59926df530b4eac96974ae"
  integrity sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==

mongodb-connection-string-url@^2.6.0:
  version "2.6.0"
  resolved "https://registry.yarnpkg.com/mongodb-connection-string-url/-/mongodb-connection-string-url-2.6.0.tgz#57901bf352372abdde812c81be47b75c6b2ec5cf"
  integrity sha512-WvTZlI9ab0QYtTYnuMLgobULWhokRjtC7db9LtcVfJ+Hsnyr5eo6ZtNAt3Ly24XZScGMelOcGtm7lSn0332tPQ==
  dependencies:
    "@types/whatwg-url" "^8.2.1"
    whatwg-url "^11.0.0"

mongodb@4.17.2:
  version "4.17.2"
  resolved "https://registry.yarnpkg.com/mongodb/-/mongodb-4.17.2.tgz#237c0534e36a3449bd74c6bf6d32f87a1ca7200c"
  integrity sha512-mLV7SEiov2LHleRJPMPrK2PMyhXFZt2UQLC4VD4pnth3jMjYKHhtqfwwkkvS/NXuo/Fp3vbhaNcXrIDaLRb9Tg==
  dependencies:
    bson "^4.7.2"
    mongodb-connection-string-url "^2.6.0"
    socks "^2.7.1"
  optionalDependencies:
    "@aws-sdk/credential-providers" "^3.186.0"
    "@mongodb-js/saslprep" "^1.1.0"

mongoose@^6.1.3:
  version "6.13.5"
  resolved "https://registry.yarnpkg.com/mongoose/-/mongoose-6.13.5.tgz#33e628d259787221e8c6ecc740189896f35c7edc"
  integrity sha512-podJEaIF/5N2mQymkyyUzN2NeL/68MOyYjf3O0zsgCU2B2Omnhg6NhGHVavt9ZH/VxOrwKE9XphbuHDFK+T06g==
  dependencies:
    bson "^4.7.2"
    kareem "2.5.1"
    mongodb "4.17.2"
    mpath "0.9.0"
    mquery "4.0.3"
    ms "2.1.3"
    sift "16.0.1"

mpath@0.9.0:
  version "0.9.0"
  resolved "https://registry.yarnpkg.com/mpath/-/mpath-0.9.0.tgz#0c122fe107846e31fc58c75b09c35514b3871904"
  integrity sha512-ikJRQTk8hw5DEoFVxHG1Gn9T/xcjtdnOKIU1JTmGjZZlg9LST2mBLmcX3/ICIbgJydT2GOc15RnNy5mHmzfSew==

mquery@4.0.3:
  version "4.0.3"
  resolved "https://registry.yarnpkg.com/mquery/-/mquery-4.0.3.tgz#4d15f938e6247d773a942c912d9748bd1965f89d"
  integrity sha512-J5heI+P08I6VJ2Ky3+33IpCdAvlYGTSUjwTPxkAr8i8EoduPMBX2OY/wa3IKZIQl7MU4SbFk8ndgSKyB/cl1zA==
  dependencies:
    debug "4.x"

ms@2.1.3, ms@^2.1.3:
  version "2.1.3"
  resolved "https://registry.yarnpkg.com/ms/-/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

obuf@~1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/obuf/-/obuf-1.1.2.tgz#09bea3343d41859ebd446292d11c9d4db619084e"
  integrity sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==

pg-int8@1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/pg-int8/-/pg-int8-1.0.1.tgz#943bd463bf5b71b4170115f80f8efc9a0c0eb78c"
  integrity sha512-WCtabS6t3c8SkpDBUlb1kjOs7l66xsGdKpIPZsg4wR+B3+u9UAum2odSsF9tnvxg80h4ZxLWMy4pRjOsFIqQpw==

pg-numeric@1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/pg-numeric/-/pg-numeric-1.0.2.tgz#816d9a44026086ae8ae74839acd6a09b0636aa3a"
  integrity sha512-BM/Thnrw5jm2kKLE5uJkXqqExRUY/toLHda65XgFTBTFYZyopbKjBe29Ii3RbkvlsMoFwD+tHeGaCjjv0gHlyw==

pg-protocol@*:
  version "1.7.0"
  resolved "https://registry.yarnpkg.com/pg-protocol/-/pg-protocol-1.7.0.tgz#ec037c87c20515372692edac8b63cf4405448a93"
  integrity sha512-hTK/mE36i8fDDhgDFjy6xNOG+LCorxLG3WO17tku+ij6sVHXh1jQUJ8hYAnRhNla4QVD2H8er/FOjc/+EgC6yQ==

pg-types@^4.0.1:
  version "4.0.2"
  resolved "https://registry.yarnpkg.com/pg-types/-/pg-types-4.0.2.tgz#399209a57c326f162461faa870145bb0f918b76d"
  integrity sha512-cRL3JpS3lKMGsKaWndugWQoLOCoP+Cic8oseVcbr0qhPzYD5DWXK+RZ9LY9wxRf7RQia4SCwQlXk0q6FCPrVng==
  dependencies:
    pg-int8 "1.0.1"
    pg-numeric "1.0.2"
    postgres-array "~3.0.1"
    postgres-bytea "~3.0.0"
    postgres-date "~2.1.0"
    postgres-interval "^3.0.0"
    postgres-range "^1.1.1"

pify@^5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/pify/-/pify-5.0.0.tgz#1f5eca3f5e87ebec28cc6d54a0e4aaf00acc127f"
  integrity sha512-eW/gHNMlxdSP6dmG6uJip6FXN0EQBwm2clYYd8Wul42Cwu/DK8HEftzsapcNdYe2MfLiIwZqsDk2RDEsTE79hA==

possible-typed-array-names@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/possible-typed-array-names/-/possible-typed-array-names-1.0.0.tgz#89bb63c6fada2c3e90adc4a647beeeb39cc7bf8f"
  integrity sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==

postgres-array@~3.0.1:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/postgres-array/-/postgres-array-3.0.2.tgz#68d6182cb0f7f152a7e60dc6a6889ed74b0a5f98"
  integrity sha512-6faShkdFugNQCLwucjPcY5ARoW1SlbnrZjmGl0IrrqewpvxvhSLHimCVzqeuULCbG0fQv7Dtk1yDbG3xv7Veog==

postgres-bytea@~3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/postgres-bytea/-/postgres-bytea-3.0.0.tgz#9048dc461ac7ba70a6a42d109221619ecd1cb089"
  integrity sha512-CNd4jim9RFPkObHSjVHlVrxoVQXz7quwNFpz7RY1okNNme49+sVyiTvTRobiLV548Hx/hb1BG+iE7h9493WzFw==
  dependencies:
    obuf "~1.1.2"

postgres-date@~2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/postgres-date/-/postgres-date-2.1.0.tgz#b85d3c1fb6fb3c6c8db1e9942a13a3bf625189d0"
  integrity sha512-K7Juri8gtgXVcDfZttFKVmhglp7epKb1K4pgrkLxehjqkrgPhfG6OO8LHLkfaqkbpjNRnra018XwAr1yQFWGcA==

postgres-interval@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/postgres-interval/-/postgres-interval-3.0.0.tgz#baf7a8b3ebab19b7f38f07566c7aab0962f0c86a"
  integrity sha512-BSNDnbyZCXSxgA+1f5UU2GmwhoI0aU5yMxRGO8CdFEcY2BQF9xm/7MqKnYoM1nJDk8nONNWDk9WeSmePFhQdlw==

postgres-range@^1.1.1:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/postgres-range/-/postgres-range-1.1.4.tgz#a59c5f9520909bcec5e63e8cf913a92e4c952863"
  integrity sha512-i/hbxIE9803Alj/6ytL7UHQxRvZkI9O4Sy+J3HGc4F4oo/2eQAjTSNJ0bfxyse3bH0nuVesCk+3IRLaMtG3H6w==

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz#e102f16ca355424865755d2c9e8ea4f24d58c3e2"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"
  integrity sha512-RofWgt/7fL5wP1Y7fxE7/EmTLzQVnB0ycyibJ0OOHIlJqTNzglYFxVwETOcIoJqJmpDXJ9xImDv+Fq34F/d4Dw==

punycode@^2.1.1:
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==

querystring@0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/querystring/-/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"
  integrity sha512-X/xY82scca2tau62i9mDyU9K+I+djTMUsvwf7xnUX5GLvVzgJybOJf4Y6o9Zx3oJK/LSXg5tTZBjwzqVPaPO2g==

safe-regex-test@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/safe-regex-test/-/safe-regex-test-1.1.0.tgz#7f87dfb67a3150782eaaf18583ff5d1711ac10c1"
  integrity sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-regex "^1.2.1"

sax@1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/sax/-/sax-1.2.1.tgz#7b8e656190b228e81a66aea748480d828cd2d37a"
  integrity sha512-8I2a3LovHTOpm7NV5yOyO8IHqgVsfK4+UuySrXU8YXkSRX7k6hCV9b3HrkKCr3nMpgj+0bmocaJJWpvp1oc7ZA==

sax@>=0.6.0:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/sax/-/sax-1.4.1.tgz#44cc8988377f126304d3b3fc1010c733b929ef0f"
  integrity sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==

semver@^5.4.1:
  version "5.7.2"
  resolved "https://registry.yarnpkg.com/semver/-/semver-5.7.2.tgz#48d55db737c3287cd4835e17fa13feace1c41ef8"
  integrity sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==

semver@^7.5.3:
  version "7.6.3"
  resolved "https://registry.yarnpkg.com/semver/-/semver-7.6.3.tgz#980f7b5550bc175fb4dc09403085627f9eb33143"
  integrity sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==

set-function-length@^1.2.2:
  version "1.2.2"
  resolved "https://registry.yarnpkg.com/set-function-length/-/set-function-length-1.2.2.tgz#aac72314198eaed975cf77b2c3b6b880695e5449"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

shimmer@^1.2.0:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/shimmer/-/shimmer-1.2.1.tgz#610859f7de327b587efebf501fb43117f9aff337"
  integrity sha512-sQTKC1Re/rM6XyFM6fIAGHRPVGvyXfgzIDvzoq608vM+jeyVD0Tu1E6Np0Kc2zAIFWIj963V2800iF/9LPieQw==

sift@16.0.1:
  version "16.0.1"
  resolved "https://registry.yarnpkg.com/sift/-/sift-16.0.1.tgz#e9c2ccc72191585008cf3e36fc447b2d2633a053"
  integrity sha512-Wv6BjQ5zbhW7VFefWusVP33T/EM0vYikCaQ2qR8yULbsilAT8/wQaXvuQ3ptGLpoKx+lihJE3y2UTgKDyyNHZQ==

smart-buffer@^4.2.0:
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/smart-buffer/-/smart-buffer-4.2.0.tgz#6e1d71fa4f18c05f7d0ff216dd16a481d0e8d9ae"
  integrity sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==

socks@^2.7.1:
  version "2.8.3"
  resolved "https://registry.yarnpkg.com/socks/-/socks-2.8.3.tgz#1ebd0f09c52ba95a09750afe3f3f9f724a800cb5"
  integrity sha512-l5x7VUUWbjVFbafGLxPWkYsHIhEvmF85tbIeFZWc8ZPtoMyybuEhL7Jye/ooC4/d48FgOjSJXgsF/AJPYCW8Zw==
  dependencies:
    ip-address "^9.0.5"
    smart-buffer "^4.2.0"

sparse-bitfield@^3.0.3:
  version "3.0.3"
  resolved "https://registry.yarnpkg.com/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz#ff4ae6e68656056ba4b3e792ab3334d38273ca11"
  integrity sha512-kvzhi7vqKTfkh0PZU+2D2PIllw2ymqJKujUcyPMd9Y75Nv4nPbGJZXNhxsgdQab2BmlDct1YnfQCguEvHr7VsQ==
  dependencies:
    memory-pager "^1.0.2"

sprintf-js@^1.1.3:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/sprintf-js/-/sprintf-js-1.1.3.tgz#4914b903a2f8b685d17fdf78a70e917e872e444a"
  integrity sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==

stack-chain@^1.3.7:
  version "1.3.7"
  resolved "https://registry.yarnpkg.com/stack-chain/-/stack-chain-1.3.7.tgz#d192c9ff4ea6a22c94c4dd459171e3f00cea1285"
  integrity sha512-D8cWtWVdIe/jBA7v5p5Hwl5yOSOrmZPWDPe2KxQ5UAGD+nxbxU0lKXA4h85Ta6+qgdKVL3vUxsbIZjc1kBG7ug==

strnum@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/strnum/-/strnum-1.0.5.tgz#5c4e829fe15ad4ff0d20c3db5ac97b73c9b072db"
  integrity sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==

tr46@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/tr46/-/tr46-3.0.0.tgz#555c4e297a950617e8eeddef633c87d4d9d6cbf9"
  integrity sha512-l7FvfAHlcmulp8kr+flpQZmVwtu7nfRV7NZujtN0OqES8EL4O4e0qqzL0DC5gAvx/ZC/9lk6rhcUwYvkBnBnYA==
  dependencies:
    punycode "^2.1.1"

tslib@^2.6.2:
  version "2.8.1"
  resolved "https://registry.yarnpkg.com/tslib/-/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

undici-types@~6.20.0:
  version "6.20.0"
  resolved "https://registry.yarnpkg.com/undici-types/-/undici-types-6.20.0.tgz#8171bf22c1f588d1554d55bf204bc624af388433"
  integrity sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/universalify/-/universalify-2.0.1.tgz#168efc2180964e6386d061e094df61afe239b18d"
  integrity sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==

url@0.10.3:
  version "0.10.3"
  resolved "https://registry.yarnpkg.com/url/-/url-0.10.3.tgz#021e4d9c7705f21bbf37d03ceb58767402774c64"
  integrity sha512-hzSUW2q06EqL1gKM/a+obYHLIO6ct2hwPuviqTTOcfFVc61UbfJ2Q32+uGL/HCPxKqrdGB5QUwIe7UqlDgwsOQ==
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

util@^0.12.4:
  version "0.12.5"
  resolved "https://registry.yarnpkg.com/util/-/util-0.12.5.tgz#5f17a6059b73db61a875668781a1c2b136bd6fbc"
  integrity sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

uuid-by-string@^3.0.4:
  version "3.0.7"
  resolved "https://registry.yarnpkg.com/uuid-by-string/-/uuid-by-string-3.0.7.tgz#3c9b7e60c3d4a1bf5da5dfb2601721acc813d8fc"
  integrity sha512-9xf+GAcwzLLGL2Z2Vb7hmi7jWIAKSiuaI5cLFsKw1IIlm7S5VpqvdJ5S7N36hqdy0v7DAwnnENJVAeev57/H1A==
  dependencies:
    js-md5 "^0.7.3"
    js-sha1 "^0.6.0"

uuid@8.0.0:
  version "8.0.0"
  resolved "https://registry.yarnpkg.com/uuid/-/uuid-8.0.0.tgz#bc6ccf91b5ff0ac07bbcdbf1c7c4e150db4dbb6c"
  integrity sha512-jOXGuXZAWdsTH7eZLtyXMqUb9EcWMGZNbL9YcGBJl4MH4nrxHmZJhEHvyLFrkxo+28uLb/NYRcStH48fnD0Vzw==

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://registry.yarnpkg.com/uuid/-/uuid-8.3.2.tgz#80d5b5ced271bb9af6c445f21a1a04c606cefbe2"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

uuid@^9.0.1:
  version "9.0.1"
  resolved "https://registry.yarnpkg.com/uuid/-/uuid-9.0.1.tgz#e188d4c8853cc722220392c424cd637f32293f30"
  integrity sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==

validator@^13.7.0:
  version "13.12.0"
  resolved "https://registry.yarnpkg.com/validator/-/validator-13.12.0.tgz#7d78e76ba85504da3fee4fd1922b385914d4b35f"
  integrity sha512-c1Q0mCiPlgdTVVVIJIrBuxNicYE+t/7oKeI9MWLj3fh/uq2Pxh/3eeWbVZ4OcGW1TUf53At0njHw5SMdA3tmMg==

webidl-conversions@^7.0.0:
  version "7.0.0"
  resolved "https://registry.yarnpkg.com/webidl-conversions/-/webidl-conversions-7.0.0.tgz#256b4e1882be7debbf01d05f0aa2039778ea080a"
  integrity sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==

whatwg-url@^11.0.0:
  version "11.0.0"
  resolved "https://registry.yarnpkg.com/whatwg-url/-/whatwg-url-11.0.0.tgz#0a849eebb5faf2119b901bb76fd795c2848d4018"
  integrity sha512-RKT8HExMpoYx4igMiVMY83lN6UeITKJlBQ+vR/8ZJ8OCdSiN3RwCq+9gH0+Xzj0+5IrM6i4j/6LuvzbZIQgEcQ==
  dependencies:
    tr46 "^3.0.0"
    webidl-conversions "^7.0.0"

which-typed-array@^1.1.16, which-typed-array@^1.1.2:
  version "1.1.18"
  resolved "https://registry.yarnpkg.com/which-typed-array/-/which-typed-array-1.1.18.tgz#df2389ebf3fbb246a71390e90730a9edb6ce17ad"
  integrity sha512-qEcY+KJYlWyLH9vNbsr6/5j59AXk5ni5aakf8ldzBvGde6Iz4sxZGkJyWSAueTG7QhOvNRYb1lDdFmL5Td0QKA==
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

xml2js@0.6.2:
  version "0.6.2"
  resolved "https://registry.yarnpkg.com/xml2js/-/xml2js-0.6.2.tgz#dd0b630083aa09c161e25a4d0901e2b2a929b499"
  integrity sha512-T4rieHaC1EXcES0Kxxj4JWgaUQHDk+qwHcYOCFHfiwKz7tOVPLq7Hjq9dM1WCMhylqMEfP7hMcOIChvotiZegA==
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xml2js@^0.4.23:
  version "0.4.23"
  resolved "https://registry.yarnpkg.com/xml2js/-/xml2js-0.4.23.tgz#a0c69516752421eb2ac758ee4d4ccf58843eac66"
  integrity sha512-ySPiMjM0+pLDftHgXY4By0uswI3SPKLDw/i3UXbnO8M/p28zqexCUoPmQFrYD+/1BzhGJSs2i1ERWKJAtiLrug==
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder2@^3.0.2:
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/xmlbuilder2/-/xmlbuilder2-3.1.1.tgz#b977ef8a6fb27a1ea7ffa7d850d2c007ff343bc0"
  integrity sha512-WCSfbfZnQDdLQLiMdGUQpMxxckeQ4oZNMNhLVkcekTu7xhD4tuUDyAPoY8CwXvBYE6LwBHd6QW2WZXlOWr1vCw==
  dependencies:
    "@oozcitak/dom" "1.15.10"
    "@oozcitak/infra" "1.0.8"
    "@oozcitak/util" "8.3.8"
    js-yaml "3.14.1"

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "https://registry.yarnpkg.com/xmlbuilder/-/xmlbuilder-11.0.1.tgz#be9bae1c8a046e76b31127726347d0ad7002beb3"
  integrity sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==
