const helper = require("./library/helper");
const centaur = require("@mediality/centaur");
const moment = require("moment");
let con;

exports.handler = async (event) => {
    try {
        con = await helper.openDBConnection();
        let edmMeetings = [], meetRange = [], response = {}, filename = '', daterange = '';
        
        if (event.queryStringParameters && event.queryStringParameters.range){
            meetRange = event.queryStringParameters.range.split("-to-");
            if (meetRange[0] && meetRange[1]){
                edmMeetings = await centaur.processed_meetings.find({
                    $and: [
                        { meetingState: { $in: ["NSW", "ACT", "VIC", "SA", "QLD", "TAS", "NT", "WA"] } },
                        { meetingStage: { $in: ["Results", "InterimResults", "Interim Results"] } }
                    ]
                })
                .where("meetingDate").gte(meetRange[0])
                .where("meetingDate").lte(meetRange[1])
                .select("processedMeetingData inputMeetingData meetingState")
                .lean();
            }
            if (moment(meetRange[0]).diff(moment(meetRange[1]), 'days') > 360) {
                filename = `${moment(meetRange[0]).format("YYYY")}_${moment(meetRange[1]).format("YYYY")}season.json`;
            } else {
                filename = `${meetRange[0]}_${meetRange[1]}edmData.json`;
            }
            daterange = `${meetRange[0]}_${meetRange[1]}`;
        } else {
            let fromDate = moment().subtract(6,'d').format('YYYY-MM-DD');
            if (event.queryStringParameters && event.queryStringParameters.since) {
                fromDate = event.queryStringParameters.since;
            }
            meetRange.push(fromDate);
            edmMeetings = await centaur.processed_meetings.find({
                $and: [
                    { meetingState: { $in: ["NSW", "ACT", "VIC", "SA", "QLD", "TAS", "NT", "WA"] } },
                    { meetingStage: { $in: ["Results", "InterimResults", "Interim Results"] } }
                ]
            })
            .where("meetingDate").gte(fromDate)
            .select("processedMeetingData inputMeetingData meetingState")
            .lean();
            if (moment(fromDate).diff(moment(), 'days') > 7) {
                filename = `${moment(fromDate).format("YYYY-MM-DD")}_${moment().format("YYYY-MM-DD")}period.json`;
            } else {
                filename = 'weeklyData.json';
            }
            daterange = `${moment(fromDate).format("YYYY-MM-DD")}_${moment().dayOfYear(moment().dayOfYear()).format("YYYY-MM-DD")}`;
        }

        let jsonDoc = {
            dateRange: daterange,
            allUp : {
                meetings : 0,
                races : 0,
                horses : 0,
                purse : 0
            },
            meetingBreakdown : {
                metro : 0,
                provincial : 0,
                country : 0
            },
            raceBreakdown : {
                metro : 0,
                provincial : 0,
                country : 0,
                wetTrack : 0,
                dryTrack : 0
            },
            starterBreakdown : {
                metro : 0,
                provincial : 0,
                country : 0,
                scratched : 0
            },
            prizeBreakdown : {
                metro : 0,
                provincial : 0,
                country : 0
            },
            tabNonTab : {
                tabMeetings : 0,
                tabRaces : 0,
                tabRunners : 0,
                nonTabMeetings : 0,
                nonTabRaces : 0,
                nonTabRunners : 0,
                trialMeetings : 0,
                trialRaces : 0,
                trialRunners : 0,
            },
            prizePurse : {
                metro : {
                    prize : 0,
                    raceNo : 0,
                    raceName : "",
                    track : "",
                    state : "",
                    day : ""
                },
                provincial : {
                    prize : 0,
                    raceNo : 0,
                    raceName : "",
                    track : "",
                    state : "",
                    day : ""
                },
                country : {
                    prize : 0,
                    raceNo : 0,
                    raceName : "",
                    track : "",
                    state : "",
                    day : ""
                }
            },
            theHorses : {
                firstStarts : 0,
                twoYearOlds : 0,
                imports : 0,
                importsWon : 0,
                maidens : {
                    starters : 0,
                    races : 0 
                },
                countryOfOrigin : []
            },
            breeding : {
                totalSires : 0,
                ausSires : 0,
                winningSires : 0
            },
            jockeys : {
                totalUnique : 0,
                totalApprentices : 0,
                totalWinningApprentices: 0,
                apprenticeWins : 0,
                avgRidesPerMeet : 0,
                winnerUnique : 0,
                placerUnique : 0
            },
            trainers : {
                totalUnique : 0,
                totalPartners : 0,
                totalNonPartners : 0,
                winnerUnique : 0,
                placerUnique : 0
            },
            markets : {
                avgStartPrice : 0,
                favsLast : 0,
                favOnDry : 0,
                favOnWet : 0,
                favsWon : {
                    total : 0,
                    percent : 0
                },
                favs2nd : {
                    total : 0,
                    percent : 0
                },
                favs3rd :{
                    total : 0,
                    percent : 0
                },
                favsUnplaced : {
                    total : 0,
                    percent : 0
                },
                favOddsOnWin :{
                    total : 0,
                    totalWins: 0,
                    percent : 0
                }
            },
            bits:{
                longestOfSix : 0,
                twelveYOstarts: 0,
                averageAge : 0,
                barriers: {
                    total : 0,
                    barrier : 0
                },
                tabNumber: {
                    total : 0,
                    tabNumber : 0
                },
                greysOnWet : {
                    total : 0,
                    wins : 0
                },
                biggestMargin : {
                    margin : 0,
                    name : "",
                    raceNumber : 0,
                    trackName : "",
                    state : "",
                    day : ""
                },
                longestOddsWinner : {
                    odds : 0,
                    name : "",
                    raceNumber : 0,
                    trackName : "",
                    state : "",
                    day : ""
                },
                maidenBreaks : []
            }
        };

        let uniqueEntities = {
            sires : [],
            winSires : [],
            jockeys : [],
            jockApprentices : [],
            jockApprenticesWinners : [],
            jockAvgRides : [],
            jocksWinners : [],
            jocksPlacers : [],
            trainers : [],
            trainPartners : [],
            trainNonPartners : [],
            trainWinners : [],
            trainPlacers : [],
            winAvg : [],
            favWinPct : [],
            fav2ndPct : [],
            fav3rdPct : [],
            favNoPlace : [],
            oddsOnWin : [],
            starterAge : [],
            barriers : {},
            tabNumbers : {},
            countryOfOrigin: {}
        };

        for (const meeting of edmMeetings){
            const the_meet = meeting.processedMeetingData.meeting;
            const input_meet = meeting.inputMeetingData;
            const tabStatus = the_meet.tab_indicator;

            if (tabStatus === "T") jsonDoc.tabNonTab.tabMeetings++;
            else if (tabStatus === "N") jsonDoc.tabNonTab.nonTabMeetings++;
            else if (tabStatus === "Trial") jsonDoc.tabNonTab.trialMeetings++;

            if (tabStatus !== "Trial") {
                jsonDoc.allUp.meetings++;
                let joxRides = {};
                const meetLocation = 
                    the_meet.track["@_location"] == "M" ? "metro" : 
                    the_meet.track["@_location"] == "P" ? "provincial" : 
                    the_meet.track["@_location"] == "C" ? "country" : 
                    "";
                jsonDoc.meetingBreakdown[meetLocation]++;
                for (const [index, race] of the_meet.races.race.entries()){
                    const inputRace = input_meet.races.race[index];
                    if (['Abandoned','Acceptances','FinalFields'].includes(race.race_stage)) continue;
                    let maiden_race = false;
                    let big_purse = false;
                    jsonDoc.allUp.races++;
                    jsonDoc.raceBreakdown[meetLocation]++;
                    if (tabStatus === "T") jsonDoc.tabNonTab.tabRaces++;
                    else if (tabStatus === "N") jsonDoc.tabNonTab.nonTabRaces++;
                    try{
                        for (const prize of race.prizes.prize){
                            if (prize["@_type"] === "total_value") {
                                jsonDoc.allUp.purse += parseInt(prize["@_value"]);
                                jsonDoc.prizeBreakdown[meetLocation] += parseInt(prize["@_value"]);
                                if (parseInt(prize["@_value"]) > jsonDoc.prizePurse[meetLocation].prize){
                                    jsonDoc.prizePurse[meetLocation] = {
                                        prize : parseInt(prize["@_value"]),
                                        raceNo : race["@_number"],
                                        raceName : race["@_name"],
                                        track : the_meet.track["@_name"],
                                        state : meeting.meetingState,
                                        day : moment(the_meet.date,"DD/MM/YYYY").format("dddd")
                                    };
                                    big_purse = true;
                                }
                            }
                        }
                    } catch(err){
                        // Errors silently handled
                    }
                    if (race.track_condition && helper.wetOrDry(race.track_condition) === "wet") jsonDoc.raceBreakdown.wetTrack += 1;
                    else if (race.track_condition && helper.wetOrDry(race.track_condition) === "dry") jsonDoc.raceBreakdown.dryTrack += 1;
                    if (race.classes.class_id === 30 || race.classes.second_class_id === 30 || race.classes.third_class_id === 30){
                        jsonDoc.theHorses.maidens.races++;
                        maiden_race = true;
                    }
                    let priceFin = [];
                    for (const [horseIndex, horse] of race.horses.horse.entries()){
                        const inputHorse = inputRace.horses.horse[horseIndex];
                        try{
                            if (horse.scratched){
                                jsonDoc.starterBreakdown.scratched++;
                            } else {
                                let isImport = false;
                                let isGreyOnWet = false;
                                let isApprentice = false;
                                jsonDoc.allUp.horses += 1;
                                jsonDoc.starterBreakdown[meetLocation] += 1;
                                uniqueEntities.starterAge.push(horse["@_age"]);
                                if (tabStatus === "T") jsonDoc.tabNonTab.tabRunners += 1;
                                else if (tabStatus === "N") jsonDoc.tabNonTab.nonTabRunners += 1;
                                if (horse.last_four_starts === "") jsonDoc.theHorses.firstStarts += 1;
                                if (maiden_race) jsonDoc.theHorses.maidens.starters += 1;
                                if (horse["@_age"] === 2) jsonDoc.theHorses.twoYearOlds += 1;
                                uniqueEntities.countryOfOrigin[horse["@_country"]] = ((uniqueEntities.countryOfOrigin[horse["@_country"]]) ?? 0) + 1;

                                // Log horses with empty or non-empty error_log
                                if (inputHorse.error_log.length > 0) {
                                    console.log(` ${horse["@_name"]} non-empty error_log: Meeting ID ${meeting._id}, Horse ID ${horse["@_id"]} `);
                                } else {
                                    console.log(`${horse["@_name"]}  empty error_log: Meeting ID ${meeting._id}, Horse ID ${horse["@_id"]} `);
                                }

                                // INSERT FORM CHECK FOR IMPORT
                                if (
                                    horse.error_log.length > 0 &&
                                    horse.error_log.findIndex(element => {
                                        if (typeof element === 'string') {
                                            return element.includes("last registered form");
                                        } else if (typeof element === 'object' && element.message) {
                                            return element.message.includes("last registered form");
                                        } else {
                                            return false;
                                        }
                                    }) > -1
                                ) {
                                    const form_horse = await centaur.form.findOne({horse_id:horse["@_id"]}).lean();
                                    if (helper.checkForOseasRuns(form_horse.form,meetRange[0])) {
                                        jsonDoc.theHorses.imports += 1;
                                        isImport = true;
                                    }
                                }

                                if (!uniqueEntities.sires.includes(horse.sire["@_id"])) uniqueEntities.sires.push(horse.sire["@_id"]);
                                if (horse.jockey["@_id"] && horse.jockey["@_id"] !== 0){
                                    if (!uniqueEntities.jockeys.includes(horse.jockey["@_id"])) uniqueEntities.jockeys.push(horse.jockey["@_id"]);
                                    if (horse.jockey["@_apprentice_indicator"] == "Y") {
                                        isApprentice = true;
                                        if (!uniqueEntities.jockApprentices.includes(horse.jockey["@_id"])) {
                                            uniqueEntities.jockApprentices.push(horse.jockey["@_id"]);
                                        }
                                    }
                                    joxRides[horse.jockey["@_id"].toString()] = (joxRides[horse.jockey["@_id"].toString()] ?? 0) + 1;
                                }
                                if (horse.trainer["@_id"] && horse.trainer["@_id"] !== 0){
                                    if (!uniqueEntities.trainers.includes(horse.trainer["@_id"])) uniqueEntities.trainers.push(horse.trainer["@_id"]);
                                    if ((horse.trainer["@_name"].split(" & ")[1] || horse.trainer["@_name"].split(" and ")[1] || horse.trainer["@_name"].split(" And ")[1])) {
                                        if (!uniqueEntities.trainPartners.includes(horse.trainer["@_id"])) uniqueEntities.trainPartners.push(horse.trainer["@_id"]);
                                    } else if (!uniqueEntities.trainNonPartners.includes(horse.trainer["@_id"])) {
                                        uniqueEntities.trainNonPartners.push(horse.trainer["@_id"]);
                                    }
                                }

                                if (horse["@_colour"].includes("gr") && race.track_condition && helper.wetOrDry(race.track_condition) === "wet"){
                                    // is grey horse on wet track
                                    isGreyOnWet = true;
                                    jsonDoc.bits.greysOnWet.total += 1;
                                }
                                if (horse["@_age"] > 11) jsonDoc.bits.twelveYOstarts += 1;

                                if (horse.finish_position === 1){
                                    if (horse.sire["@_country"] === "AUS") jsonDoc.breeding.ausSires += 1;
                                    if (!uniqueEntities.winSires.includes(horse.sire["@_id"])) uniqueEntities.winSires.push(horse.sire["@_id"]);
                                    if (horse.jockey["@_id"] && horse.jockey["@_id"] !== 0){
                                        if (!uniqueEntities.jocksWinners.includes(horse.jockey["@_id"])) uniqueEntities.jocksWinners.push(horse.jockey["@_id"]);
                                    }
                                    if (horse.trainer["@_id"] && horse.trainer["@_id"] !== 0){
                                        if (!uniqueEntities.trainWinners.includes(horse.trainer["@_id"])) uniqueEntities.trainWinners.push(horse.trainer["@_id"]); 
                                    }
                                    if (isApprentice) jsonDoc.jockeys.apprenticeWins +=1;
                                    if (tabStatus === "T") uniqueEntities.winAvg.push(parseFloat(horse.decimalprices["@_starting"]));
                                    if (isImport) {jsonDoc.theHorses.importsWon += 1;
                                        console.log('yes')
                                        console.log(JSON.stringify(horse));
                                    }
                                    if (isGreyOnWet) jsonDoc.bits.greysOnWet.wins += 1;
                                    uniqueEntities.tabNumbers[horse.tab_number.toString()] = ((uniqueEntities.tabNumbers[horse.tab_number.toString()]) ?? 0) + 1;
                                    uniqueEntities.barriers[horse.barrier.toString()] = ((uniqueEntities.barriers[horse.barrier.toString()]) ?? 0) + 1;

                                    if (race.race_type === "Flat" && horse.beaten_margin && horse.beaten_margin > jsonDoc.bits.biggestMargin.margin){
                                        jsonDoc.bits.biggestMargin = {
                                            margin : horse.beaten_margin,
                                            name : horse["@_name"],
                                            raceNumber : race["@_number"],
                                            distance : race.distance["@_metres"],
                                            condition : helper.conditionSelect(race.track_condition),
                                            trackName : the_meet.track["@_name"],
                                            state : meeting.meetingState,
                                            day : moment(the_meet.date,"DD/MM/YYYY").format("dddd")
                                        };
                                    }

                                    if (race.race_type === "Flat" && horse.decimalprices && horse.decimalprices["@_starting"] && parseFloat(horse.decimalprices["@_starting"]) > jsonDoc.bits.longestOddsWinner.odds){
                                        jsonDoc.bits.longestOddsWinner = {
                                            odds : parseFloat(horse.decimalprices["@_starting"]),
                                            name : horse["@_name"],
                                            raceNumber : race["@_number"],
                                            trackName : the_meet.track["@_name"],
                                            state : meeting.meetingState,
                                            day : moment(the_meet.date,"DD/MM/YYYY").format("dddd")
                                        };
                                    }

                                    if (isApprentice && !uniqueEntities.jockApprenticesWinners.includes(horse.jockey["@_id"])) {
                                        uniqueEntities.jockApprenticesWinners.push(horse.jockey["@_id"]);
                                    }

                                    if (horse.statistics && 
                                        horse.statistics.statistic && 
                                        horse.statistics.statistic[0]["@_total"] > 24 && 
                                        horse.statistics.statistic[0]["@_firsts"] === 0){
                                            jsonDoc.bits.maidenBreaks.push(`${horse["@_name"]} (${the_meet.track["@_name"]} ${meeting.meetingState})`)
                                        }

                                    if (big_purse) jsonDoc.prizePurse[meetLocation].winner = horse["@_name"];
                                }
                                if (horse.finish_position < 4){
                                    if (horse.jockey["@_id"] && horse.jockey["@_id"] !== 0){
                                        if (!uniqueEntities.jocksPlacers.includes(horse.jockey["@_id"])) uniqueEntities.jocksPlacers.push(horse.jockey["@_id"]);
                                    }
                                    if (horse.trainer["@_id"] && horse.trainer["@_id"] !== 0){
                                        if (!uniqueEntities.trainPlacers.includes(horse.trainer["@_id"])) uniqueEntities.trainPlacers.push(horse.trainer["@_id"]); 
                                    }
                                }
                                if (horse.decimalprices && horse.decimalprices["@_starting"]) priceFin.push({price:parseFloat(horse.decimalprices["@_starting"]),finish:horse.finish_position})
                            }
                        } catch(err){
                            console.log(err);

                        }
                    }
                    if (priceFin.length > 0){
                        priceFin.sort((a,b) => a.price - b.price)
                        const favPrice = priceFin[0].price;
                        let favsArray = [];
                        for (const price of priceFin){
                            if (price.price === favPrice) favsArray.push(price);
                        }
                        for (const fav of favsArray){
                            switch (fav.finish) {
                                case 1:
                                    uniqueEntities.favWinPct.push(1);
                                    uniqueEntities.fav2ndPct.push(0);
                                    uniqueEntities.fav3rdPct.push(0);
                                    uniqueEntities.favNoPlace.push(0);
                                    jsonDoc.markets.favsWon.total+=1;
                                    if (race.track_condition && helper.wetOrDry(race.track_condition) === "wet") jsonDoc.markets.favOnWet+=1;
                                    else if (race.track_condition && helper.wetOrDry(race.track_condition) === "dry") jsonDoc.markets.favOnDry+=1;  
                                    break;
                                case 2:
                                    uniqueEntities.favWinPct.push(0);
                                    uniqueEntities.fav2ndPct.push(1);
                                    uniqueEntities.fav3rdPct.push(0);
                                    uniqueEntities.favNoPlace.push(0);
                                    jsonDoc.markets.favs2nd.total+=1;
                                    break;
                                case 3:
                                    uniqueEntities.favWinPct.push(0);
                                    uniqueEntities.fav2ndPct.push(0);
                                    uniqueEntities.fav3rdPct.push(1);
                                    uniqueEntities.favNoPlace.push(0);
                                    jsonDoc.markets.favs3rd.total+=1;
                                    break;
                                default:
                                    uniqueEntities.favWinPct.push(0);
                                    uniqueEntities.fav2ndPct.push(0);
                                    uniqueEntities.fav3rdPct.push(0);
                                    uniqueEntities.favNoPlace.push(1);
                                    jsonDoc.markets.favsUnplaced.total+=1;
                                    if (fav.finish === race.starters) jsonDoc.markets.favsLast += 1;
                                    break;
                            } 
                            if (fav.price < 2){
                                if (fav.finish === 1) {
                                    jsonDoc.markets.favOddsOnWin.totalWins+=1;
                                    uniqueEntities.oddsOnWin.push(1);
                                }
                                else uniqueEntities.oddsOnWin.push(0);
                                jsonDoc.markets.favOddsOnWin.total+=1;
                            } 
                        }
                        if (priceFin.length < 7 && priceFin[priceFin.length - 1].finish == 1){
                            jsonDoc.bits.longestOfSix += 1;
                        }
                    }
                }
                for (const key in joxRides){
                    uniqueEntities.jockAvgRides.push(joxRides[key]);
                }
            } else {
                for (const race of the_meet.races.race){
                    jsonDoc.tabNonTab.trialRaces += 1;
                    for (const horse of race.horses.horse){
                        if (!horse.scratched) jsonDoc.tabNonTab.trialRunners += 1;
                    }
                }
            }
        }
        for (const barr in uniqueEntities.barriers){
            if (uniqueEntities.barriers[barr] > jsonDoc.bits.barriers.total){
                jsonDoc.bits.barriers = {
                    total : uniqueEntities.barriers[barr],
                    barrier : barr
                }
            }
        }

        for (const tabNo in uniqueEntities.tabNumbers){
            if (uniqueEntities.tabNumbers[tabNo] > jsonDoc.bits.tabNumber.total){
                jsonDoc.bits.tabNumber = {
                    total : uniqueEntities.tabNumbers[tabNo],
                    tabNumber : tabNo
                }
            }
        }
        for (const key in uniqueEntities.countryOfOrigin){
            jsonDoc.theHorses.countryOfOrigin.push({country:key,total:uniqueEntities.countryOfOrigin[key]})
            jsonDoc.theHorses.countryOfOrigin.sort((a,b) => b.total - a.total)
        }
        jsonDoc.jockeys.avgRidesPerMeet = Math.round(((uniqueEntities.jockAvgRides.reduce((pv, cv) => pv + cv, 0)) / uniqueEntities.jockAvgRides.length) * 10) / 10;
        jsonDoc.bits.averageAge = Math.round(((uniqueEntities.starterAge.reduce((pv, cv) => pv + cv, 0)) / uniqueEntities.starterAge.length) * 10) / 10;
        jsonDoc.breeding.totalSires = uniqueEntities.sires.length;
        jsonDoc.breeding.winningSires = uniqueEntities.winSires.length;
        jsonDoc.jockeys.totalUnique = uniqueEntities.jockeys.length;
        jsonDoc.jockeys.totalApprentices = uniqueEntities.jockApprentices.length;
        jsonDoc.jockeys.totalWinningApprentices = uniqueEntities.jockApprenticesWinners.length;
        jsonDoc.jockeys.winnerUnique = uniqueEntities.jocksWinners.length;
        jsonDoc.jockeys.placerUnique = uniqueEntities.jocksPlacers.length;
        jsonDoc.trainers.totalUnique = uniqueEntities.trainers.length;
        jsonDoc.trainers.totalPartners = uniqueEntities.trainPartners.length;
        jsonDoc.trainers.totalNonPartners = uniqueEntities.trainNonPartners.length;
        jsonDoc.trainers.winnerUnique = uniqueEntities.trainWinners.length;
        jsonDoc.trainers.placerUnique = uniqueEntities.trainPlacers.length;
        jsonDoc.markets.avgStartPrice = Math.round(((uniqueEntities.winAvg.reduce((pv, cv) => pv + cv, 0)) / uniqueEntities.winAvg.length) * 100) / 100;
        jsonDoc.markets.favsWon.percent = Math.round(((uniqueEntities.favWinPct.reduce((pv, cv) => pv + cv, 0)) / uniqueEntities.favWinPct.length) * 10000) / 100;
        jsonDoc.markets.favs2nd.percent = Math.round(((uniqueEntities.fav2ndPct.reduce((pv, cv) => pv + cv, 0)) / uniqueEntities.fav2ndPct.length) * 10000) / 100;
        jsonDoc.markets.favs3rd.percent = Math.round(((uniqueEntities.fav3rdPct.reduce((pv, cv) => pv + cv, 0)) / uniqueEntities.fav3rdPct.length) * 10000) / 100;
        jsonDoc.markets.favsUnplaced.percent = Math.round(((uniqueEntities.favNoPlace.reduce((pv, cv) => pv + cv, 0)) / uniqueEntities.favNoPlace.length) * 10000) / 100;
        jsonDoc.markets.favOddsOnWin.percent = Math.round(((uniqueEntities.oddsOnWin.reduce((pv, cv) => pv + cv, 0)) / uniqueEntities.oddsOnWin.length) * 10000) / 100;
        if (jsonDoc.bits.maidenBreaks.length === 0) jsonDoc.bits.maidenBreaks.push("none");

        if (edmMeetings.length > 0){
            response = {
                statusCode: 200,
                headers: { "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Headers": "*" },
                body: JSON.stringify(jsonDoc)
            }

        } else {
            response = {
                statusCode: 422,
                headers: { "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Headers": "*" },
                body: JSON.stringify({
                    "message": `${event.queryStringParamaters} not recognised input`
                })
            }
        }
        
        await sendToS3(jsonDoc,filename)
        return response;

    } catch (err) {
        await helper.closeDBConnection(con);
    } finally {
        await helper.closeDBConnection(con);
    }
};

const sendToS3 = async (jsonData, filename) => {
    let bucket = 'silks.medialityracing.com.au';
    if (process.env.ENV === 'prd') bucket = 'mr-assets-public';
    try {
        const AWS = require('aws-sdk');
        const S3 = new AWS.S3({ region: process.env.AWS_REGION });
        const params = {
            Bucket: bucket,
            Key: `edm/${filename}`,
            Body: JSON.stringify(jsonData),
            ContentType: 'application/json',
        };

        await S3.upload(params).promise();
        return true;
    } catch (err) {
        return false;
    }
};
