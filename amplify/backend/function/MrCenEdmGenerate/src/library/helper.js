const util = require('util');
const moment = require('moment')


const inspectObject = async (jsonObject) => {
  console.log(
    util.inspect(
      jsonObject,
      { showHidden: false, depth: null, colors: true }
    )
  )
}


const getSecrets = async (secretName) => {
  const AWS = require('aws-sdk');
  var secretsmanager = new AWS.SecretsManager();
  var params = {
    SecretId: secretName,
  };
  const fetchSecretString = await secretsmanager.getSecretValue(params).promise();
  var aws_secrets = JSON.parse(fetchSecretString.SecretString)
  return aws_secrets
}

const openDBConnection = async (env) => {
  const local = require("../local");
  const mongoose = require('mongoose');

  if (env == "local") {
      //Local environment

      var cert_path = './rds-cert/global-bundle.pem'
      console.log('Running Locally')
      DB_USERNAME = local.DB_USERNAME
      DB_PASSWORD = local.DB_PASSWORD
      DB_URL = local.DB_URL
      DB_NAME = local.DB_NAME
      CONNECTION_STRING = "mongodb://" + DB_USERNAME + ":" + DB_PASSWORD + "@" + DB_URL + "/" + DB_NAME
      CONNECTION_STRING = "mongodb://root:Test.123!@127.0.0.1:27000/dev-centaur?directConnection=true&ssl=true&retrywrites=false&tls=true"
      await mongoose.connect(CONNECTION_STRING, {
          connectTimeoutMS: 1000,
          tlsCAFile: cert_path,
          directConnection: true,
          ssl: true,
          sslValidate: false,
          maxPoolSize: 10

      }).then(
          () => {
              console.log('Connected to Local')
              response = "Connect to Local"
          },
          err => {
              console.log('Not connected:' + err)
              response = "Not Connected to Local"
          });

  } else {
      console.log('Running Online')
      var aws_secrets = await getSecrets(process.env.centaurSecrets)
      DB_USERNAME = aws_secrets.DB_USERNAME
      DB_PASSWORD = aws_secrets.DB_PASSWORD
      DB_URL = aws_secrets.DB_URL
      DB_NAME = aws_secrets.DB_NAME
      CONNECTION_STRING = "mongodb://" + DB_USERNAME + ":" + DB_PASSWORD + "@" + DB_URL + "/" + DB_NAME + "?tls=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
      await mongoose.connect(CONNECTION_STRING, {
          connectTimeoutMS: 1000,
          tlsCAFile: cert_path,
          directConnection: true,
          ssl: true,
          sslValidate: false,
          maxPoolSize: 10
      }).then(
          () => {
              console.log('Connected to Online')
              response = "Connect Online"
          },
          err => {
              console.log('Not connected:' + err)
              response = "Not Connected Online"
          });
  }
  return mongoose
}

const closeDBConnection = async (con) => {

  con.connection.close()
  console.log("Connection Closed")

}


const wetOrDry = (trackCond) => {
  if (trackCond.includes("Synthetic")) return "none"
  if (["H","S","O"].includes(trackCond.slice(0,1))) return "wet"
  if (["F","G","M","D"].includes(trackCond.slice(0,1))) return "dry"
  return "none"

}

const checkForOseasRuns = (data,startdate) => {
  for (const raceEvent of data) {
      if (moment(raceEvent.meeting_date).diff(moment(startdate), "days") > 0) continue
      if (
          (
            raceEvent.classes && 
            raceEvent.classes.class_id && 
            raceEvent.classes.class_id === 90
          ) || (
            raceEvent.classes && 
            raceEvent.classes.second_class_id || 
            raceEvent.classes.second_class_id === 90
          ) || (
            raceEvent.classes && 
            raceEvent.classes.third_class_id || 
            raceEvent.classes.third_class_id === 90
          )
        ){
        continue
      }
      if (
            raceEvent.track &&
            raceEvent.track["@_country"] &&
            (
                raceEvent.track["@_country"] === "AUS"
            )
        ){
            return false
        }
    if (
            raceEvent.track &&
            raceEvent.track["@_state_no"] &&
            raceEvent.track["@_state_no"] < 9
        ){
            return false
        }
  }
  return true
}

const conditionSelect = (condition) => {
  var trackCond = "";
  switch (condition) {
    case "Y0":
      trackCond = "Synthetic";
      break;
    case "F1":
      trackCond = "Firm(1)";
      break;
    case "F2":
      trackCond = "Firm(2)";
      break;
    case "M1":
      trackCond = "Firm(1)";
      break;
    case "M2":
      trackCond = "Firm(2)";
      break;
    case "G3":
      trackCond = "Good(3)";
      break;
    case "G4":
      trackCond = "Good(4)";
      break;
    case "O5":
      trackCond = "Soft(5)";
      break;
    case "O6":
      trackCond = "Soft(6)";
      break;
    case "O7":
      trackCond = "Soft(7)";
      break;
    case "H8":
      trackCond = "Heavy(8)";
      break;
    case "H9":
      trackCond = "Heavy(9)";
      break;
    case "H10":
      trackCond = "Heavy(10)";
      break;
    case "H11":
      trackCond = "Heavy(11)";
      break;
    case "F0":
      trackCond = "Firm";
      break;
    case "M0":
      trackCond = "Firm";
      break;
    case "G0":
      trackCond = "Good";
      break;
    case "O0":
      trackCond = "Soft";
      break;
    case "S0":
      trackCond = "Soft";
      break;
    case "H0":
      trackCond = "Heavy";
      break;
    default:
      trackCond = condition;
      break;
  }
  return trackCond;
};

module.exports = {
  openDBConnection,
  closeDBConnection,
  getSecrets,
  inspectObject,
  wetOrDry,
  checkForOseasRuns,
  conditionSelect
};
