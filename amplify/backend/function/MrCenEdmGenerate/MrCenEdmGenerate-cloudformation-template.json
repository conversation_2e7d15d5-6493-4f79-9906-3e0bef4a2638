{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"Linux\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.8.2\",\"stackType\":\"function-Lambda\",\"metadata\":{}}", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "s3Key": {"Type": "String"}, "functioncentaurappCentaurAppCommonLayerArn": {"Type": "String", "Default": "functioncentaurappCentaurAppCommonLayerArn"}, "region": {"Type": "String"}, "env": {"Type": "String"}, "awsLocal": {"Type": "String"}, "centaurSecrets": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "MrCenEdmGenerate", {"Fn::Join": ["", ["MrCenEdmGenerate", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"REGION": {"Ref": "AWS::Region"}, "ENV": {"Ref": "env"}, "AWS_LOCAL": {"Ref": "awsLocal"}, "centaurSecrets": {"Ref": "centaurSecrets"}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "MemorySize": 8192, "Layers": [{"Ref": "functioncentaurappCentaurAppCommonLayerArn"}], "VpcConfig": {"SecurityGroupIds": ["{{resolve:ssm:centaurLambdaDbSecurityGroup}}"], "SubnetIds": ["{{resolve:ssm:centaurVpcAppSubnet0}}", "{{resolve:ssm:centaurVpcAppSubnet1}}", "{{resolve:ssm:centaurVpcAppSubnet2}}"]}, "Timeout": 900}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "centaurappLambdaRole5886ec99", {"Fn::Join": ["", ["centaurappLambdaRole5886ec99", "-", {"Ref": "env"}]]}]}, "Path": "/", "ManagedPolicyArns": ["arn:aws:iam::aws:policy/AmazonDocDBFullAccess", "arn:aws:iam::aws:policy/AmazonS3FullAccess", "arn:aws:iam::aws:policy/SecretsManagerReadWrite", "arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess", "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"], "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}, "ScheduledRule": {"Type": "AWS::Events::Rule", "Properties": {"Description": "Schedule rule for Lambda", "ScheduleExpression": "cron(0 20 ? * SUN *)", "State": "ENABLED", "Targets": [{"Arn": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}, "Id": {"Ref": "LambdaFunction"}}]}}, "CloudWatchEvent": {"Type": "AWS::Events::Rule", "Properties": {"Description": "Schedule rule for Lambda", "ScheduleExpression": {"Ref": "CloudWatchRule"}, "State": "ENABLED", "Targets": [{"Arn": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}, "Id": {"Ref": "LambdaFunction"}}]}}, "PermissionForEventsToInvokeLambda": {"Type": "AWS::Lambda::Permission", "Properties": {"FunctionName": {"Ref": "LambdaFunction"}, "Action": "lambda:InvokeFunction", "Principal": "events.amazonaws.com", "SourceArn": {"Fn::GetAtt": ["CloudWatchEvent", "<PERSON><PERSON>"]}}}, "InvocationErrorAlarm": {"Type": "AWS::CloudWatch::Alarm", "DependsOn": ["LambdaFunction"], "Properties": {"AlarmActions": [{"Fn::Sub": ["arn:aws:sns:${region}:${account}:MrCenAlarms-${environment}", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "environment": {"Ref": "env"}}]}], "AlarmName": {"Fn::Join": ["", ["LambdaError-", {"Ref": "LambdaFunction"}]]}, "ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "FunctionName", "Value": {"Ref": "LambdaFunction"}}], "EvaluationPeriods": 1, "MetricName": "Errors", "Namespace": "AWS/Lambda", "Period": 60, "Statistic": "Maximum", "Threshold": 1, "TreatMissingData": "notBreaching"}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}, "CloudWatchEventRule": {"Value": {"Ref": "CloudWatchEvent"}}}}