{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.10.0\",\"stackType\":\"custom-customCloudformation\",\"metadata\":{}}", "Parameters": {"env": {"Type": "String"}, "sqsQueueName": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"SQS": {"Type": "AWS::SQS::Queue", "Properties": {"QueueName": {"Fn::If": ["ShouldNotCreateEnvResources", {"Ref": "sqsQueueName"}, {"Fn::Join": ["", [{"Ref": "sqsQueueName"}, "-", {"Ref": "env"}]]}]}}}}, "Outputs": {"Name": {"Value": {"Ref": "SQS"}}, "Arn": {"Value": {"Fn::GetAtt": ["SQS", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}}}