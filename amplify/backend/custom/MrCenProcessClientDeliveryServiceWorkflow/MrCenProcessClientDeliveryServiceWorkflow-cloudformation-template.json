{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.10.0\",\"stackType\":\"custom-customCloudformation\",\"metadata\":{}}", "Parameters": {"env": {"Type": "String"}, "stepFunctionName": {"Type": "String"}}, "Resources": {"ProcessWorkflowCustomPolicy": {"Type": "AWS::IAM::Policy", "Properties": {"Roles": [{"Ref": "ProcessClientDeliveryServiceWorkflowExecutionRole"}], "PolicyName": {"Fn::Join": ["-", [{"Ref": "stepFunctionName"}, "CustomPolicy", {"Ref": "env"}]]}, "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["lambda:InvokeFunction"], "Resource": "*"}]}}}, "ProcessWorkflowDistributedModePolicy": {"Type": "AWS::IAM::Policy", "Properties": {"Roles": [{"Ref": "ProcessClientDeliveryServiceWorkflowExecutionRole"}], "PolicyName": {"Fn::Join": ["-", [{"Ref": "stepFunctionName"}, "DistributedModePolicy", {"Ref": "env"}]]}, "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["states:StartExecution"], "Resource": {"Fn::Sub": ["arn:aws:states:${region}:${account}:stateMachine:${stepFunctionName}-${env}", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "stepFunctionName": {"Ref": "stepFunctionName"}, "env": {"Ref": "env"}}]}}, {"Effect": "Allow", "Action": ["states:DescribeExecution", "states:StopExecution"], "Resource": {"Fn::Sub": ["arn:aws:states:${region}:${account}:execution:${stepFunctionName}-${env}", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "stepFunctionName": {"Ref": "stepFunctionName"}, "env": {"Ref": "env"}}]}}]}}}, "ProcessClientDeliveryServiceWorkflowExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "states.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": ["arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess", "arn:aws:iam::aws:policy/CloudWatchLogsFullAccess", "arn:aws:iam::aws:policy/AWSLambdaExecute"]}}, "ProcessClientDeliveryServiceWorkflowStateMachineLogGroup": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": {"Fn::Join": ["", ["/aws/vendedlogs/states/", {"Fn::Join": ["-", [{"Ref": "stepFunctionName"}, {"Ref": "env"}]]}]]}, "RetentionInDays": 14}}, "ProcessClientDeliveryServiceWorkflowStateMachine": {"Type": "AWS::StepFunctions::StateMachine", "Properties": {"DefinitionString": "{\"Comment\":\"A Step functions workflow to process files for Mediality Business Operations, receive the xml files path and it will be filtered based on the clients permission\",\"StartAt\":\"Get Delivery Manifest\",\"States\":{\"Get Delivery Manifest\":{\"Type\":\"Task\",\"Resource\":\"arn:aws:states:::lambda:invoke\",\"OutputPath\":\"$.Payload\",\"Parameters\":{\"Payload.$\":\"$\",\"FunctionName.$\":\"$.functions.MrCenLambdaGetDeliveryManifest\"},\"Retry\":[{\"ErrorEquals\":[\"Lambda.ServiceException\",\"Lambda.AWSLambdaException\",\"Lambda.SdkClientException\",\"Lambda.TooManyRequestsException\"],\"IntervalSeconds\":2,\"MaxAttempts\":6,\"BackoffRate\":2}],\"Next\":\"Client Delivery Service Map\"},\"Client Delivery Service Map\":{\"Type\":\"Map\",\"ItemProcessor\":{\"ProcessorConfig\":{\"Mode\":\"DISTRIBUTED\",\"ExecutionType\":\"EXPRESS\"},\"StartAt\":\"Client Delivery Service\",\"States\":{\"Client Delivery Service\":{\"Type\":\"Task\",\"Resource\":\"arn:aws:states:::lambda:invoke\",\"OutputPath\":\"$.Payload\",\"Parameters\":{\"Payload.$\":\"$\",\"FunctionName.$\":\"$.functions.MrCenLambdaClientDeliveryService\"},\"Retry\":[{\"ErrorEquals\":[\"Lambda.ServiceException\",\"Lambda.AWSLambdaException\",\"Lambda.SdkClientException\",\"Lambda.TooManyRequestsException\"],\"IntervalSeconds\":2,\"MaxAttempts\":6,\"BackoffRate\":2}],\"End\":true}}},\"End\":true,\"InputPath\":\"$.permissions\",\"MaxConcurrency\":1000,\"Label\":\"ClientDeliveryServiceXMLProcessing\"}}}", "LoggingConfiguration": {"Destinations": [{"CloudWatchLogsLogGroup": {"LogGroupArn": {"Fn::GetAtt": ["ProcessClientDeliveryServiceWorkflowStateMachineLogGroup", "<PERSON><PERSON>"]}}}], "IncludeExecutionData": true, "Level": "ALL"}, "RoleArn": {"Fn::GetAtt": ["ProcessClientDeliveryServiceWorkflowExecutionRole", "<PERSON><PERSON>"]}, "StateMachineName": {"Fn::Join": ["-", [{"Ref": "stepFunctionName"}, {"Ref": "env"}]]}, "StateMachineType": "STANDARD", "TracingConfiguration": {"Enabled": "true"}}}, "FailedStepFunctionAlarm": {"Type": "AWS::CloudWatch::Alarm", "DependsOn": ["ProcessClientDeliveryServiceWorkflowStateMachine"], "Properties": {"AlarmActions": [{"Fn::Sub": ["arn:aws:sns:${region}:${account}:MrCenAlarms-${environment}", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "environment": {"Ref": "env"}}]}], "AlarmName": {"Fn::Join": ["", ["StepFunctionError-", {"Ref": "ProcessClientDeliveryServiceWorkflowStateMachine"}]]}, "ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "StateMachineArn", "Value": {"Ref": "ProcessClientDeliveryServiceWorkflowStateMachine"}}], "EvaluationPeriods": 1, "MetricName": "ExecutionsFailed", "Namespace": "AWS/States", "Period": 900, "Statistic": "Sum", "Threshold": 1, "TreatMissingData": "notBreaching"}}}, "Outputs": {"Name": {"Value": {"Fn::GetAtt": ["ProcessClientDeliveryServiceWorkflowStateMachine", "Name"]}}, "Arn": {"Value": {"Ref": "ProcessClientDeliveryServiceWorkflowStateMachine"}}}}