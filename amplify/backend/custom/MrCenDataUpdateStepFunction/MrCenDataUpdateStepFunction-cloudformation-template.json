{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.10.0\",\"stackType\":\"custom-customCloudformation\",\"metadata\":{}}", "Parameters": {"env": {"Type": "String"}, "stepFunctionName": {"Type": "String"}}, "Resources": {"ProcessWorkflowCustomPolicy": {"Type": "AWS::IAM::Policy", "Properties": {"Roles": [{"Ref": "UpdateNamesWorkflowExecutionRole"}], "PolicyName": {"Fn::Join": ["-", [{"Ref": "stepFunctionName"}, "CustomPolicy", {"Ref": "env"}]]}, "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["lambda:InvokeFunction"], "Resource": "*"}]}}}, "UpdateNamesWorkflowExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "states.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": ["arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess", "arn:aws:iam::aws:policy/CloudWatchLogsFullAccess", "arn:aws:iam::aws:policy/AWSLambdaExecute"]}}, "UpdateNamesWorkflowStateMachineLogGroup": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": {"Fn::Join": ["", ["/aws/vendedlogs/states/", {"Fn::Join": ["-", [{"Ref": "stepFunctionName"}, {"Ref": "env"}]]}]]}, "RetentionInDays": 14}}, "UpdateNamesWorkflowStateMachine": {"Type": "AWS::StepFunctions::StateMachine", "Properties": {"DefinitionString": {"Fn::Sub": "{\n    \"StartAt\": \"Update Names\",\n    \"States\": {\n      \"Update Names\": {\n        \"Type\": \"Map\",\n        \"ItemsPath\": \"$\",\n        \"End\": true,\n        \"Iterator\": {\n          \"StartAt\": \"Update Names Portion\",\n          \"States\": {\n            \"Update Names Portion\": {\n              \"Type\": \"Task\",\n              \"Resource\": \"arn:aws:states:::lambda:invoke\",\n              \"OutputPath\": \"$.Payload\",\n              \"Parameters\": {\n                \"Payload.$\": \"$\",\n                \"FunctionName\": \"MrCenLambdaHorseNameUpdate-${env}\"\n              },\n              \"Retry\": [\n                {\n                  \"ErrorEquals\": [\n                    \"Lambda.ServiceException\",\n                    \"Lambda.AWSLambdaException\",\n                    \"Lambda.SdkClientException\"\n                  ],\n                  \"IntervalSeconds\": 2,\n                  \"MaxAttempts\": 6,\n                  \"BackoffRate\": 2\n                }\n              ],\n              \"End\": true\n            }\n          }\n        },\n        \"MaxConcurrency\": 1\n      }\n    },\n    \"Comment\": \"A Step functions workflow to update entity names in lambda size portions\",\n    \"TimeoutSeconds\": 1500\n  }\n"}, "LoggingConfiguration": {"Destinations": [{"CloudWatchLogsLogGroup": {"LogGroupArn": {"Fn::GetAtt": ["UpdateNamesWorkflowStateMachineLogGroup", "<PERSON><PERSON>"]}}}], "IncludeExecutionData": true, "Level": "ALL"}, "RoleArn": {"Fn::GetAtt": ["UpdateNamesWorkflowExecutionRole", "<PERSON><PERSON>"]}, "StateMachineName": {"Fn::Join": ["-", [{"Ref": "stepFunctionName"}, {"Ref": "env"}]]}, "StateMachineType": "STANDARD", "TracingConfiguration": {"Enabled": "true"}}}, "FailedStepFunctionAlarm": {"Type": "AWS::CloudWatch::Alarm", "DependsOn": ["UpdateNamesWorkflowStateMachine"], "Properties": {"AlarmActions": [{"Fn::Sub": ["arn:aws:sns:${region}:${account}:MrCenAlarms-${environment}", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "environment": {"Ref": "env"}}]}], "AlarmName": {"Fn::Join": ["", ["StepFunctionError-", {"Ref": "UpdateNamesWorkflowStateMachine"}]]}, "ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "StateMachineArn", "Value": {"Ref": "UpdateNamesWorkflowStateMachine"}}], "EvaluationPeriods": 1, "MetricName": "ExecutionsFailed", "Namespace": "AWS/States", "Period": 900, "Statistic": "Sum", "Threshold": 1, "TreatMissingData": "notBreaching"}}}, "Outputs": {"Name": {"Value": {"Fn::GetAtt": ["UpdateNamesWorkflowStateMachine", "Name"]}}, "Arn": {"Value": {"Ref": "UpdateNamesWorkflowStateMachine"}}}}