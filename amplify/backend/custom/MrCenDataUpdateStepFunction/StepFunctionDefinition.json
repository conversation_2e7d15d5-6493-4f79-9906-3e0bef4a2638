{"Comment": "A state machine to invoke a Lambda function with each year and jox", "StartAt": "MapState", "States": {"MapState": {"Type": "Map", "ItemsPath": "$.uniqueMonths", "MaxConcurrency": 1, "Parameters": {"year.$": "$$.Map.Item.Value.year", "month.$": "$$.Map.Item.Value.month", "jox.$": "$.jox"}, "Iterator": {"StartAt": "InvokeLambda", "States": {"InvokeLambda": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"FunctionName": "MRCenLambdaJockeyUpdate-<PERSON><PERSON><PERSON>", "Payload": {"year.$": "$.year", "month.$": "$.month", "jox.$": "$.jox"}}, "End": true}}}, "End": true}}, "TimeoutSeconds": 12000}