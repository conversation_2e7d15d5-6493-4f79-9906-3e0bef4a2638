{"StartAt": "Update Entities", "States": {"Update Entities": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "MrManualTriggerUpdate-${env}"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Generate Statistics"}, "Generate Statistics": {"Type": "Map", "ItemsPath": "$.raceArray", "Iterator": {"StartAt": "Generate Statistics Data", "States": {"Generate Statistics Data": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "MrCenLambdaProcessStats-${env}"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "Next": "Compare and End", "MaxConcurrency": 5, "ResultPath": null}, "Compare and End": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "MRCentCompareMeeting-${env}"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}, "Comment": "A Step functions workflow to process files for Mediality Business Operations, Inputs sources should be enriched with file types of Nominations, Weights, Acceptances, FinalFields, Results", "TimeoutSeconds": 1500}