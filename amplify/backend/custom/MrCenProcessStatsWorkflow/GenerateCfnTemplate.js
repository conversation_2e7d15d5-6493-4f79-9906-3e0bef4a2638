const fs = require('fs')

fs.readFile('StepFunctionDefinition.json', 'utf8', function (err, data) {
  if (err) {
    return console.log(err);
  }
  var result = data.replace(/michael|dev/g, '${env}');
  var result = JSON.stringify(result);
  var result = JSON.parse(result);

  fs.readFile('MrCenProcessStatsWorkflow-cloudformation-template.json', 'utf8', function (err, cfndata) {
    if (err) {
      return console.log(err);
    }
    cfndata = JSON.parse(cfndata);
    cfndata.Resources.ProcessStatsWorkflowStateMachine.Properties.DefinitionString['Fn::Sub'] = result;
    // console.log(cfndata.Resources.ProcessWorkflowStateMachine.Properties.DefinitionString);

    fs.writeFile('MrCenProcessStatsWorkflow-cloudformation-template.json', JSON.stringify(cfndata, null, 2), 'utf8', function (err) {
       if (err) return console.log(err);
    });
  });
});
