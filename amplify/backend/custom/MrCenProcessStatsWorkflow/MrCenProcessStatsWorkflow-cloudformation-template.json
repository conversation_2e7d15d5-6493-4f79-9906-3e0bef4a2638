{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.10.0\",\"stackType\":\"custom-customCloudformation\",\"metadata\":{}}", "Parameters": {"env": {"Type": "String"}, "stepFunctionName": {"Type": "String"}}, "Resources": {"ProcessWorkflowCustomPolicy": {"Type": "AWS::IAM::Policy", "Properties": {"Roles": [{"Ref": "ProcessStatsWorkflowExecutionRole"}], "PolicyName": {"Fn::Join": ["-", [{"Ref": "stepFunctionName"}, "CustomPolicy", {"Ref": "env"}]]}, "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["lambda:InvokeFunction"], "Resource": "*"}]}}}, "ProcessStatsWorkflowExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "states.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": ["arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess", "arn:aws:iam::aws:policy/CloudWatchLogsFullAccess", "arn:aws:iam::aws:policy/AWSLambdaExecute"]}}, "ProcessStatsWorkflowStateMachineLogGroup": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": {"Fn::Join": ["", ["/aws/vendedlogs/states/", {"Fn::Join": ["-", [{"Ref": "stepFunctionName"}, {"Ref": "env"}]]}]]}, "RetentionInDays": 14}}, "ProcessStatsWorkflowStateMachine": {"Type": "AWS::StepFunctions::StateMachine", "Properties": {"DefinitionString": {"Fn::Sub": "{\n    \"StartAt\": \"Update Entities\",\n    \"States\": {\n      \"Update Entities\": {\n        \"Type\": \"Task\",\n        \"Resource\": \"arn:aws:states:::lambda:invoke\",\n        \"OutputPath\": \"$.Payload\",\n        \"Parameters\": {\n          \"Payload.$\": \"$\",\n          \"FunctionName\": \"MrManualTriggerUpdate-${env}\"\n        },\n        \"Retry\": [\n          {\n            \"ErrorEquals\": [\n              \"Lambda.ServiceException\",\n              \"Lambda.AWSLambdaException\",\n              \"Lambda.SdkClientException\"\n            ],\n            \"IntervalSeconds\": 2,\n            \"MaxAttempts\": 6,\n            \"BackoffRate\": 2\n          }\n        ],\n        \"Next\": \"Generate Statistics\"\n      },\n      \"Generate Statistics\": {\n        \"Type\": \"Map\",\n        \"ItemsPath\": \"$.raceArray\",\n        \"Iterator\": {\n          \"StartAt\": \"Generate Statistics Data\",\n          \"States\": {\n            \"Generate Statistics Data\": {\n              \"Type\": \"Task\",\n              \"Resource\": \"arn:aws:states:::lambda:invoke\",\n              \"OutputPath\": \"$.Payload\",\n              \"Parameters\": {\n                \"Payload.$\": \"$\",\n                \"FunctionName\": \"MrCenLambdaProcessStats-${env}\"\n              },\n              \"Retry\": [\n                {\n                  \"ErrorEquals\": [\n                    \"Lambda.ServiceException\",\n                    \"Lambda.AWSLambdaException\",\n                    \"Lambda.SdkClientException\"\n                  ],\n                  \"IntervalSeconds\": 2,\n                  \"MaxAttempts\": 6,\n                  \"BackoffRate\": 2\n                }\n              ],\n              \"End\": true\n            }\n          }\n        },\n        \"Next\": \"Compare and End\",\n        \"MaxConcurrency\": 5,\n        \"ResultPath\": null\n      },\n      \"Compare and End\": {\n        \"Type\": \"Task\",\n        \"Resource\": \"arn:aws:states:::lambda:invoke\",\n        \"OutputPath\": \"$.Payload\",\n        \"Parameters\": {\n          \"Payload.$\": \"$\",\n          \"FunctionName\": \"MRCentCompareMeeting-${env}\"\n        },\n        \"Retry\": [\n          {\n            \"ErrorEquals\": [\n              \"Lambda.ServiceException\",\n              \"Lambda.AWSLambdaException\",\n              \"Lambda.SdkClientException\"\n            ],\n            \"IntervalSeconds\": 2,\n            \"MaxAttempts\": 6,\n            \"BackoffRate\": 2\n          }\n        ],\n        \"End\": true\n      }\n    },\n    \"Comment\": \"A Step functions workflow to process files for Mediality Business Operations, Inputs sources should be enriched with file types of Nominations, Weights, Acceptances, FinalFields, Results\",\n    \"TimeoutSeconds\": 1500\n  }\n"}, "LoggingConfiguration": {"Destinations": [{"CloudWatchLogsLogGroup": {"LogGroupArn": {"Fn::GetAtt": ["ProcessStatsWorkflowStateMachineLogGroup", "<PERSON><PERSON>"]}}}], "IncludeExecutionData": true, "Level": "ALL"}, "RoleArn": {"Fn::GetAtt": ["ProcessStatsWorkflowExecutionRole", "<PERSON><PERSON>"]}, "StateMachineName": {"Fn::Join": ["-", [{"Ref": "stepFunctionName"}, {"Ref": "env"}]]}, "StateMachineType": "STANDARD", "TracingConfiguration": {"Enabled": "true"}}}, "FailedStepFunctionAlarm": {"Type": "AWS::CloudWatch::Alarm", "DependsOn": ["ProcessStatsWorkflowStateMachine"], "Properties": {"AlarmActions": [{"Fn::Sub": ["arn:aws:sns:${region}:${account}:MrCenAlarms-${environment}", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "environment": {"Ref": "env"}}]}], "AlarmName": {"Fn::Join": ["", ["StepFunctionError-", {"Ref": "ProcessStatsWorkflowStateMachine"}]]}, "ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "StateMachineArn", "Value": {"Ref": "ProcessStatsWorkflowStateMachine"}}], "EvaluationPeriods": 1, "MetricName": "ExecutionsFailed", "Namespace": "AWS/States", "Period": 900, "Statistic": "Sum", "Threshold": 1, "TreatMissingData": "notBreaching"}}}, "Outputs": {"Name": {"Value": {"Fn::GetAtt": ["ProcessStatsWorkflowStateMachine", "Name"]}}, "Arn": {"Value": {"Ref": "ProcessStatsWorkflowStateMachine"}}}}