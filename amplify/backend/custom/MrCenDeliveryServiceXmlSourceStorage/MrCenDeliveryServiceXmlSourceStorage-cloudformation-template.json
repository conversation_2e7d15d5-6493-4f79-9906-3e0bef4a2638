{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.10.0\",\"stackType\":\"custom-customCloudformation\",\"metadata\":{}}", "Parameters": {"env": {"Type": "String"}, "bucketName": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"XmlSourceStorage": {"Type": "AWS::S3::<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Properties": {"BucketName": {"Fn::If": ["ShouldNotCreateEnvResources", {"Ref": "bucketName"}, {"Fn::Join": ["", [{"Ref": "bucketName"}, "-", {"Ref": "env"}]]}]}, "NotificationConfiguration": {"EventBridgeConfiguration": {"EventBridgeEnabled": true}}, "PublicAccessBlockConfiguration": {"BlockPublicAcls": true, "BlockPublicPolicy": true, "IgnorePublicAcls": true, "RestrictPublicBuckets": true}, "VersioningConfiguration": {"Status": "Enabled"}}}}, "Outputs": {"bucketName": {"Value": {"Ref": "XmlSourceStorage"}, "Export": {"Name": {"Fn::Join": ["", ["MrCenDeliveryServiceSourceXmlStorageBucketName", "-", {"Ref": "env"}]]}}}, "bucketArn": {"Value": {"Fn::GetAtt": ["XmlSourceStorage", "<PERSON><PERSON>"]}, "Export": {"Name": {"Fn::Join": ["", ["MrCenDeliveryServiceSourceXmlStorageBucketArn", "-", {"Ref": "env"}]]}}}}}