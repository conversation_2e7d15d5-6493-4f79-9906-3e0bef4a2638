{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.10.0\",\"stackType\":\"custom-customCloudformation\",\"metadata\":{}}", "Parameters": {"env": {"Type": "String"}, "bucketName": {"Type": "String"}, "sqsQueueName": {"Type": "String"}, "supplierBucketName": {"Type": "String"}, "snsTopicName": {"Type": "String"}, "lambdaName": {"Type": "String", "Description": "The ARN of the Lambda function to notify"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"SupplierStorage": {"Type": "AWS::S3::<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Properties": {"BucketName": {"Fn::If": ["ShouldNotCreateEnvResources", {"Ref": "supplierBucketName"}, {"Fn::Join": ["", [{"Ref": "supplierBucketName"}, "-", {"Ref": "env"}]]}]}, "PublicAccessBlockConfiguration": {"BlockPublicAcls": true, "BlockPublicPolicy": true, "IgnorePublicAcls": true, "RestrictPublicBuckets": true}}}, "FileStorage": {"Type": "AWS::S3::<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Properties": {"BucketName": {"Fn::If": ["ShouldNotCreateEnvResources", {"Ref": "bucketName"}, {"Fn::Join": ["", [{"Ref": "bucketName"}, "-", {"Ref": "env"}]]}]}, "NotificationConfiguration": {"TopicConfigurations": [{"Event": "s3:ObjectCreated:*", "Filter": {"S3Key": {"Rules": [{"Name": "suffix", "Value": ".xml"}, {"Name": "prefix", "Value": "unprocessed/"}]}}, "Topic": {"Ref": "SNSTopic"}}, {"Event": "s3:ObjectCreated:*", "Filter": {"S3Key": {"Rules": [{"Name": "suffix", "Value": ".json"}, {"Name": "prefix", "Value": "unprocessed/"}]}}, "Topic": {"Ref": "SNSTopic"}}, {"Event": "s3:ObjectCreated:*", "Filter": {"S3Key": {"Rules": [{"Name": "suffix", "Value": ".csv"}, {"Name": "prefix", "Value": "unprocessed/"}]}}, "Topic": {"Ref": "SNSTopic"}}]}, "PublicAccessBlockConfiguration": {"BlockPublicAcls": true, "BlockPublicPolicy": true, "IgnorePublicAcls": true, "RestrictPublicBuckets": true}, "VersioningConfiguration": {"Status": "Enabled"}}, "DependsOn": ["SNSTopic", "SQS", "SNSTopicPolicy", "SQSPolicy"]}, "SNSTopic": {"Type": "AWS::SNS::Topic", "Properties": {"TopicName": {"Fn::If": ["ShouldNotCreateEnvResources", {"Ref": "snsTopicName"}, {"Fn::Join": ["", [{"Ref": "snsTopicName"}, "-", {"Ref": "env"}]]}]}}}, "SNSTopicPolicy": {"Type": "AWS::SNS::TopicPolicy", "Properties": {"Topics": [{"Ref": "SNSTopic"}], "PolicyDocument": {"Statement": [{"Sid": "AllowS3BucketToPublish", "Effect": "Allow", "Principal": {"Service": "s3.amazonaws.com"}, "Action": "sns:Publish", "Resource": {"Ref": "SNSTopic"}, "Condition": {"ArnLike": {"aws:SourceArn": {"Fn::Join": ["", ["arn:aws:s3:::", {"Fn::Join": ["-", [{"Ref": "bucketName"}, {"Ref": "env"}]]}]]}}}}]}}}, "SQS": {"Type": "AWS::SQS::Queue", "Properties": {"QueueName": {"Fn::If": ["ShouldNotCreateEnvResources", {"Ref": "sqsQueueName"}, {"Fn::Join": ["", [{"Ref": "sqsQueueName"}, "-", {"Ref": "env"}]]}]}}}, "SQSSubscription": {"Type": "AWS::SNS::Subscription", "Properties": {"Protocol": "sqs", "Endpoint": {"Fn::GetAtt": ["SQS", "<PERSON><PERSON>"]}, "TopicArn": {"Ref": "SNSTopic"}}}, "SQSPolicy": {"Type": "AWS::SQS::QueuePolicy", "Properties": {"Queues": [{"Ref": "SQS"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Sid": "AllowSNSMessages", "Effect": "Allow", "Principal": {"Service": "sns.amazonaws.com"}, "Action": "sqs:SendMessage", "Resource": {"Fn::GetAtt": ["SQS", "<PERSON><PERSON>"]}, "Condition": {"ArnEquals": {"aws:SourceArn": {"Ref": "SNSTopic"}}}}]}}}, "LambdaPermission": {"Type": "AWS::Lambda::Permission", "Properties": {"FunctionName": {"Fn::Join": [":", ["arn:aws:lambda", {"Ref": "AWS::Region"}, {"Ref": "AWS::AccountId"}, "function", {"Fn::Join": ["-", [{"Ref": "lambdaName"}, {"Ref": "env"}]]}]]}, "Action": "lambda:InvokeFunction", "Principal": "sns.amazonaws.com", "SourceArn": {"Ref": "SNSTopic"}}}, "LambdaSubscription": {"Type": "AWS::SNS::Subscription", "Properties": {"Protocol": "lambda", "Endpoint": {"Fn::Sub": ["arn:aws:lambda:${Region}:${AccountId}:function:${lambdaName}-${env}", {"Region": {"Ref": "AWS::Region"}, "AccountId": {"Ref": "AWS::AccountId"}, "lambdaName": {"Ref": "lambdaName"}, "env": {"Ref": "env"}}]}, "TopicArn": {"Ref": "SNSTopic"}}}}, "Outputs": {"supplierBucketName": {"Value": {"Ref": "SupplierStorage"}}, "supplierBucketArn": {"Value": {"Fn::GetAtt": ["SupplierStorage", "<PERSON><PERSON>"]}}, "bucketName": {"Value": {"Ref": "FileStorage"}}, "bucketArn": {"Value": {"Fn::GetAtt": ["FileStorage", "<PERSON><PERSON>"]}}, "bucketRegion": {"Value": {"Ref": "AWS::Region"}}, "sqsName": {"Value": {"Ref": "SQS"}}, "sqsArn": {"Value": {"Fn::GetAtt": ["SQS", "<PERSON><PERSON>"]}}, "sqsRegion": {"Value": {"Ref": "AWS::Region"}}, "SNSTopicARN": {"Description": "ARN of the SNS Topic", "Value": {"Ref": "SNSTopic"}}}}