{"dontuse": {}, "LambdaPermission": {"Type": "AWS::Lambda::Permission", "Properties": {"FunctionName": {"Fn::Join": [":", ["arn:aws:lambda", {"Ref": "AWS::Region"}, {"Ref": "AWS::AccountId"}, "function", {"Fn::Join": ["-", [{"Ref": "lambdaName"}, {"Ref": "env"}]]}]]}, "Action": "lambda:InvokeFunction", "Principal": "sns.amazonaws.com", "SourceArn": {"Ref": "SNSTopic"}}}, "LambdaSubscription": {"Type": "AWS::SNS::Subscription", "Properties": {"Protocol": "lambda", "Endpoint": {"Fn::Sub": ["arn:aws:lambda:${Region}:${AccountId}:function:${lambdaName}-${env}", {"Region": {"Ref": "AWS::Region"}, "AccountId": {"Ref": "AWS::AccountId"}, "lambdaName": {"Ref": "lambdaName"}, "env": {"Ref": "env"}}]}, "TopicArn": {"Ref": "SNSTopic"}}}}