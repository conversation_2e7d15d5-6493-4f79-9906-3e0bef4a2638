{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.10.0\",\"stackType\":\"custom-customCloudformation\",\"metadata\":{}}", "Parameters": {"env": {"Type": "String"}, "bucketName": {"Type": "String"}, "expirationInDays": {"Type": "Number"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"XmlOutputStorage": {"Type": "AWS::S3::<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Properties": {"BucketName": {"Fn::If": ["ShouldNotCreateEnvResources", {"Ref": "bucketName"}, {"Fn::Join": ["", [{"Ref": "bucketName"}, "-", {"Ref": "env"}]]}]}, "LifecycleConfiguration": {"Rules": [{"Id": "XML Output file expiration", "ExpirationInDays": {"Ref": "expirationInDays"}, "Status": "Enabled"}]}, "PublicAccessBlockConfiguration": {"BlockPublicAcls": true, "BlockPublicPolicy": true, "IgnorePublicAcls": true, "RestrictPublicBuckets": true}}}}, "Outputs": {"bucketName": {"Value": {"Ref": "XmlOutputStorage"}}, "bucketArn": {"Value": {"Fn::GetAtt": ["XmlOutputStorage", "<PERSON><PERSON>"]}}}}