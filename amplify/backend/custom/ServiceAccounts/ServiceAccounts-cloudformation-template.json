{"AWSTemplateFormatVersion": "2010-09-09", "Parameters": {"env": {"Type": "String"}, "customMrCenFileStoragesupplierBucketName": {"Type": "String", "Description": "Input parameter describing supplierBucketName attribute for custom/MrCenFileStorage resource"}, "customMrCenFileStoragebucketName": {"Type": "String", "Description": "Input parameter describing bucketName attribute for custom/MrCenFileStorage resource"}, "customEnvironmentCreate": {"Type": "String", "Description": "Custom environment parameter to create IAM Users, if environment doesn't match Dev, Stg, and Prd", "Default": ""}, "customMrCenFileStoragesupplierBucketArn": {"Type": "String", "Description": "Input parameter describing supplierBucketArn attribute for custom/MrCenFileStorage resource"}, "customMrCenFileStoragebucketArn": {"Type": "String", "Description": "Input parameter describing bucketArn attribute for custom/MrCenFileStorage resource"}, "customMrCenFileStoragebucketRegion": {"Type": "String", "Description": "Input parameter describing bucketRegion attribute for custom/MrCenFileStorage resource"}, "customMrCenFileStoragesqsName": {"Type": "String", "Description": "Input parameter describing sqsName attribute for custom/MrCenFileStorage resource"}, "customMrCenFileStoragesqsArn": {"Type": "String", "Description": "Input parameter describing sqsArn attribute for custom/MrCenFileStorage resource"}, "customMrCenFileStoragesqsRegion": {"Type": "String", "Description": "Input parameter describing sqsRegion attribute for custom/MrCenFileStorage resource"}, "customMrCenDeliveryServiceXmlOutputStoragebucketName": {"Type": "String", "Description": "Input parameter describing bucketName attribute for custom/MrCenDeliveryServiceXmlOutputStorage resource"}, "customMrCenDeliveryServiceXmlOutputStoragebucketArn": {"Type": "String", "Description": "Input parameter describing bucketArn attribute for custom/MrCenDeliveryServiceXmlOutputStorage resource"}}, "Conditions": {"IsCustomEnv": {"Fn::Equals": [{"Ref": "env"}, {"Ref": "customEnvironmentCreate"}]}, "IsDev": {"Fn::Equals": [{"Ref": "env"}, "dev"]}, "IsStg": {"Fn::Equals": [{"Ref": "env"}, "stg"]}, "IsPrd": {"Fn::Equals": [{"Ref": "env"}, "prd"]}, "IsValidateEnvironmentToCreate": {"Fn::Or": [{"Condition": "<PERSON><PERSON><PERSON>"}, {"Condition": "IsStg"}, {"Condition": "IsPrd"}]}, "IsValidateEnvironmentToTest": {"Condition": "IsCustomEnv"}}, "Resources": {"TisdbUser": {"Type": "AWS::IAM::User", "Condition": "IsValidateEnvironmentToCreate", "Properties": {"Policies": [{"PolicyName": {"Fn::Join": ["", ["Tisdb", "User", "Policy"]]}, "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Sid": "ListContentsCentaurStorage", "Effect": "Allow", "Action": "s3:ListBucket", "Resource": [{"Fn::Join": ["", ["arn:aws:s3:::", {"Ref": "customMrCenFileStoragebucketName"}]]}], "Condition": {"StringEquals": {"s3:prefix": ["", "unprocessed"]}}}, {"Sid": "ListBucketContentsCentaurStorage", "Effect": "Allow", "Action": "s3:ListBucket", "Resource": [{"Fn::Join": ["", ["arn:aws:s3:::", {"Ref": "customMrCenFileStoragebucketName"}]]}], "Condition": {"StringLike": {"s3:prefix": ["unprocessed/*"]}}}, {"Sid": "AllowGetAndPutObjects", "Effect": "Allow", "Action": ["s3:GetObject", "s3:PutObject"], "Resource": [{"Fn::Join": ["", ["arn:aws:s3:::", {"Ref": "customMrCenFileStoragebucketName"}, "/unprocessed/*"]]}]}]}}], "Tags": [{"Key": "Description", "Value": "A Services Account User for TISDB"}, {"Key": "Managed By", "Value": "Amplify"}, {"Key": "Notes", "Value": "User and Policy creation is managed all other components are outside of CFN ie Key Rotation"}], "UserName": "amplify-service-account-tisdb"}}, "RatingsUser": {"Type": "AWS::IAM::User", "Condition": "IsValidateEnvironmentToCreate", "Properties": {"Policies": [{"PolicyName": {"Fn::Join": ["", ["Ratings", "User", "Policy"]]}, "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Sid": "ListBucketContents", "Effect": "Allow", "Action": "s3:ListBucket", "Resource": [{"Fn::Join": ["", ["arn:aws:s3:::", {"Ref": "customMrCenFileStoragesupplierBucketName"}]]}], "Condition": {"StringEquals": {"s3:prefix": ["", "ratings"]}}}, {"Sid": "ListBucketContentsRatings", "Effect": "Allow", "Action": "s3:ListBucket", "Resource": [{"Fn::Join": ["", ["arn:aws:s3:::", {"Ref": "customMrCenFileStoragesupplierBucketName"}]]}], "Condition": {"StringLike": {"s3:prefix": ["ratings/*"]}}}, {"Sid": "AllowGetAndPutObjects", "Effect": "Allow", "Action": ["s3:GetObject", "s3:PutObject"], "Resource": [{"Fn::Join": ["", ["arn:aws:s3:::", {"Ref": "customMrCenFileStoragesupplierBucketName"}, "/ratings/*"]]}]}]}}], "Tags": [{"Key": "Description", "Value": "A Services Account User for Ratings"}, {"Key": "Managed By", "Value": "Amplify"}, {"Key": "Notes", "Value": "User and Policy creation is managed all other components are outside of CFN ie Key Rotation"}], "UserName": "amplify-service-account-ratings"}}, "FilesComUser": {"Type": "AWS::IAM::User", "Condition": "IsValidateEnvironmentToTest", "Properties": {"Policies": [{"PolicyName": {"Fn::Join": ["", ["Files.com", "User", "Policy", "-", {"Ref": "env"}]]}, "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["s3:ListBucket"], "Resource": [{"Ref": "customMrCenDeliveryServiceXmlOutputStoragebucketArn"}]}, {"Effect": "Allow", "Action": ["s3:PutObject", "s3:GetObject", "s3:DeleteObject"], "Resource": [{"Fn::Join": ["", [{"Ref": "customMrCenDeliveryServiceXmlOutputStoragebucketArn"}, "/*"]]}]}]}}], "Tags": [{"Key": "Description", "Value": "A Services Account User for Files.com"}, {"Key": "Managed By", "Value": "Amplify"}], "UserName": {"Fn::Join": ["", ["amplify-service-account-files.com", "-", {"Ref": "env"}]]}}}}, "Outputs": {}, "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.10.0\",\"stackType\":\"custom-customCloudformation\",\"metadata\":{}}"}