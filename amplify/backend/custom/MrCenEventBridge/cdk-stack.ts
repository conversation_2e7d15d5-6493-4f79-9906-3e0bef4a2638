import * as cdk from '@aws-cdk/core';
import * as AmplifyHelpers from '@aws-amplify/cli-extensibility-helper';
import {AmplifyDependentResourcesAttributes} from '../../types/amplify-dependent-resources-ref';
import * as targets from '@aws-cdk/aws-events-targets';
import * as events from '@aws-cdk/aws-events';
import * as stepFunctions from '@aws-cdk/aws-stepfunctions';
import * as logs from '@aws-cdk/aws-logs';
import {RetentionDays} from '@aws-cdk/aws-logs';
import {EventPattern} from "@aws-cdk/aws-events/lib/event-pattern";
import {CloudWatchLogsTarget} from "./lib/CloudwatchLogsTarget";

export class cdkStack extends cdk.Stack {
  constructor(scope: cdk.Construct, id: string, props?: cdk.StackProps, amplifyResourceProps?: AmplifyHelpers.AmplifyResourceProps) {
    super(scope, id, props);
    /* Do not remove - Amplify CLI automatically injects the current deployment environment in this input parameter */
    new cdk.CfnParameter(this, 'env', {
      type: 'String',
      description: 'Current Amplify CLI env name',
    });

    const projectEnv: string = cdk.Fn.ref('env');

    const dependencies:AmplifyDependentResourcesAttributes = AmplifyHelpers.addResourceDependency(this,
        amplifyResourceProps.category,
        amplifyResourceProps.resourceName,
        [
          { category: "custom", resourceName: "MrCenProcessClientDeliveryServiceWorkflow" },
          { category: "custom", resourceName: "MrCenDeliveryServiceXmlOutputStorage" },
          { category: "custom", resourceName: "MrCenDeliveryServiceXmlSourceStorage" },
          { category: "function", resourceName: "MrCenLambdaGetDeliveryManifest" },
          { category: "function", resourceName: "MrCenLambdaClientDeliveryService" },
        ]
    );

    const eventPattern: EventPattern = {
      source: ["aws.s3"],
      detailType: ["Object Created"],
      detail: {
        bucket: {
          name: [cdk.Fn.ref(dependencies.custom.MrCenDeliveryServiceXmlSourceStorage.bucketName)]
        },
        object: {
          key: [{
            "suffix": ".xml"
          }]
        }
      }
    };

    const bus = events.EventBus.fromEventBusName(this, 'default-bus', 'default');
    bus.archive('MrCenArchive', {
      archiveName: `MrCenArchive-${projectEnv}`,
      description: 'Centaur Event Bridge Archive',
      eventPattern,
      retention: cdk.Duration.days(14),
    });

    const s3MasterXmlOutputName = 'MrCenS3MasterXmlFileOutput';
    const s3MasterXmlOutputFileRule = new events.Rule(this, 'MrCenS3MasterXmlFileOutput', {
      ruleName: `${s3MasterXmlOutputName}-${projectEnv}`,
      description: "Get the xml files from xml output s3 bucket",
      eventPattern,
    });

    const destinationS3BucketFullName = cdk.Fn.ref(dependencies.custom.MrCenDeliveryServiceXmlOutputStorage.bucketName)

    const deliveryServiceStepFunction = stepFunctions.StateMachine.fromStateMachineArn(this, 'deliveryServiceStepFunction', cdk.Fn.ref(dependencies.custom.MrCenProcessClientDeliveryServiceWorkflow.Arn));

    s3MasterXmlOutputFileRule.addTarget(new targets.SfnStateMachine(deliveryServiceStepFunction, {
      input: events.RuleTargetInput.fromObject({
        s3: {
          source: {
            bucketName: events.EventField.fromPath('$.detail.bucket.name'),
            objectKey: events.EventField.fromPath('$.detail.object.key'),
          },
          destination: {
            bucketName: destinationS3BucketFullName,
          }
        },
        functions: {
          MrCenLambdaGetDeliveryManifest: cdk.Fn.ref(dependencies.function.MrCenLambdaGetDeliveryManifest.Arn),
          MrCenLambdaClientDeliveryService: cdk.Fn.ref(dependencies.function.MrCenLambdaClientDeliveryService.Arn),
        },
      })
    }))

    const logGroup = new logs.LogGroup(this, 'deliveryServiceLogGroup', {
      logGroupName: `/aws/events/${s3MasterXmlOutputName}-${projectEnv}`,
      retention: RetentionDays.THREE_MONTHS,
    });

    s3MasterXmlOutputFileRule.addTarget(new CloudWatchLogsTarget(this, 'deliveryServiceLogGroupTarget', logGroup));
  }
}
