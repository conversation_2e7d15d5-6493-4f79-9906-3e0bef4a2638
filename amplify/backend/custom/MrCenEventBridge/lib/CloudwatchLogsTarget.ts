import * as cdk from '@aws-cdk/core';
import * as logs from '@aws-cdk/aws-logs';
import * as events from '@aws-cdk/aws-events';

export class CloudWatchLogsTarget extends cdk.Construct {
    private logGroup: logs.ILogGroup;

    constructor(scope: cdk.Construct, id: string, logGroup: logs.ILogGroup) {
        super(scope, id);
        this.logGroup = logGroup;
    }

    public bind(rule: events.IRule, id?: string): events.RuleTargetConfig {
        return {
            id: '',
            // we can't use logGroup.logArn because it has `:*` on the end, and it's a token
            // so we can't just remove the suffix with string replacement operations
            arn: `arn:aws:logs:${this.logGroup.stack.region}:${this.logGroup.stack.account}:log-group:${this.logGroup.logGroupName}`,
        }
    }
}
