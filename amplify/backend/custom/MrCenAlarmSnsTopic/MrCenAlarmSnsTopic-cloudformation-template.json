{"AWSTemplateFormatVersion": "2010-09-09", "Parameters": {"env": {"Type": "String"}, "alarmsTopicName": {"Type": "String"}, "alarmsSubscriptionEmailProd": {"Type": "String"}, "alarmsSubscriptionEmailTest": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}, "CreateJiraAlerts": {"Fn::Equals": [{"Ref": "env"}, "prd"]}}, "Resources": {"AlarmsTopic": {"Type": "AWS::SNS::Topic", "Properties": {"TopicName": {"Fn::If": ["ShouldNotCreateEnvResources", {"Ref": "alarmsTopicName"}, {"Fn::Join": ["", [{"Ref": "alarmsTopicName"}, "-", {"Ref": "env"}]]}]}, "Subscription": [{"Endpoint": {"Fn::If": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", {"Ref": "alarmsSubscriptionEmailProd"}, {"Ref": "alarmsSubscriptionEmailTest"}]}, "Protocol": "email"}]}}}, "Outputs": {"Region": {"Value": {"Ref": "AWS::Region"}}, "Arn": {"Value": {"Ref": "AlarmsTopic"}}}, "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.10.0\",\"stackType\":\"custom-customCloudformation\",\"metadata\":{}}"}