{"StartAt": "Parse received XML and Generate JSON file", "States": {"Parse received XML and Generate JSON file": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"FunctionName": "MrCenLambdaProcessMeeting-ab<PERSON><PERSON>", "Payload.$": "$"}, "Next": "Check Meeting Stage"}, "Check Meeting Stage": {"Type": "Choice", "Choices": [{"Or": [{"Variable": "$.stage", "StringEquals": "notProcessing"}, {"Variable": "$.meetingStage", "StringEquals": "GearChanges"}, {"Variable": "$.meetingStage", "StringEquals": "RegistrationAU"}, {"Variable": "$.meetingStage", "StringEquals": "<PERSON><PERSON><PERSON><PERSON>"}, {"Variable": "$.meetingStage", "StringEquals": "Ratings"}, {"Variable": "$.meetingStage", "StringEquals": "Sectionals"}, {"Variable": "$.meetingStage", "StringEquals": "Results"}, {"Variable": "$.meetingStage", "StringEquals": "InterimResults"}], "Next": "Process Other Files"}], "Default": "Create Or Update Meeting and Generate Race Array"}, "Process Other Files": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "MrCenLambdaProcessMeeting-ab<PERSON><PERSON>"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}, "Create Or Update Meeting and Generate Race Array": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"FunctionName": "MrCenLambdaProcessMeeting-ab<PERSON><PERSON>", "Payload.$": "$"}, "Next": "Generate Each Race"}, "Generate Each Race": {"Type": "Map", "ItemsPath": "$.meetingData.races", "Iterator": {"StartAt": "Generate Race Level Data", "States": {"Generate Race Level Data": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "MrCenLambdaProcessMeeting-ab<PERSON><PERSON>"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "MaxConcurrency": 1, "Next": "Trigger Statistics and Error Generation", "ResultPath": null}, "Trigger Statistics and Error Generation": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "MrCenLambdaPostProcessMeeting-ab<PERSON><PERSON>"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}, "Comment": "A Step functions workflow to process files for Mediality Business Operations, Inputs sources should be enriched with file types of Nominations, Weights, Acceptances, FinalFields, Results", "TimeoutSeconds": 1500}