{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.10.0\",\"stackType\":\"stepFunction-Step Function\",\"metadata\":{}}", "Parameters": {"env": {"Type": "String"}}, "Resources": {"ProcessMeetingWorkflowCustomPolicy": {"Type": "AWS::IAM::Policy", "Properties": {"Roles": [{"Ref": "ProcessMeetingWorkflowExecutionRole"}], "PolicyName": {"Fn::Join": ["-", ["MrCenProcessMeetingWorkflow", "CustomPolicy", {"Ref": "env"}]]}, "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["lambda:InvokeFunction"], "Resource": "*"}]}}}, "ProcessMeetingWorkflowExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "states.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "ManagedPolicyArns": ["arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess", "arn:aws:iam::aws:policy/CloudWatchLogsFullAccess", "arn:aws:iam::aws:policy/AWSLambdaExecute"]}}, "ProcessMeetingWorkflowStateMachineLogGroup": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": {"Fn::Join": ["", ["/aws/vendedlogs/states/", {"Fn::Join": ["-", ["MrCenProcessMeetingWorkflow", {"Ref": "env"}]]}]]}, "RetentionInDays": 14}}, "ProcessMeetingWorkflowStateMachine": {"Type": "AWS::StepFunctions::StateMachine", "Properties": {"DefinitionString": {"Fn::Sub": "{\n    \"StartAt\": \"Parse received XML and Generate JSON file\",\n    \"States\": {\n      \"Parse received XML and Generate JSON file\": {\n        \"Type\": \"Task\",\n        \"Resource\": \"arn:aws:states:::lambda:invoke\",\n        \"OutputPath\": \"$.Payload\",\n        \"Parameters\": {\n          \"FunctionName\": \"MrCenLambdaProcessMeeting-${env}\",\n          \"Payload.$\": \"$\"\n        },\n        \"Next\": \"Check Meeting Stage\"\n      },\n      \"Check Meeting Stage\": {\n        \"Type\": \"Choice\",\n        \"Choices\": [\n          {\n            \"Or\": [\n              {\n                \"Variable\": \"$.stage\",\n                \"StringEquals\": \"notProcessing\"\n              },\n              {\n                \"Variable\": \"$.meetingStage\",\n                \"StringEquals\": \"GearChanges\"\n              },\n              {\n                \"Variable\": \"$.meetingStage\",\n                \"StringEquals\": \"RegistrationAU\"\n              },\n              {\n                \"Variable\": \"$.meetingStage\",\n                \"StringEquals\": \"Scratchings\"\n              },\n              {\n                \"Variable\": \"$.meetingStage\",\n                \"StringEquals\": \"Ratings\"\n              },\n              {\n                \"Variable\": \"$.meetingStage\",\n                \"StringEquals\": \"Results\"\n              },\n              {\n                \"Variable\": \"$.meetingStage\",\n                \"StringEquals\": \"InterimResults\"\n              }\n            ],\n            \"Next\": \"Process Other Files\"\n          }\n        ],\n        \"Default\": \"Create Or Update Meeting and Generate Race Array\"\n      },\n      \"Process Other Files\": {\n        \"Type\": \"Task\",\n        \"Resource\": \"arn:aws:states:::lambda:invoke\",\n        \"OutputPath\": \"$.Payload\",\n        \"Parameters\": {\n          \"Payload.$\": \"$\",\n          \"FunctionName\": \"MrCenLambdaProcessMeeting-${env}\"\n        },\n        \"Retry\": [\n          {\n            \"ErrorEquals\": [\n              \"Lambda.ServiceException\",\n              \"Lambda.AWSLambdaException\",\n              \"Lambda.SdkClientException\"\n            ],\n            \"IntervalSeconds\": 2,\n            \"MaxAttempts\": 6,\n            \"BackoffRate\": 2\n          }\n        ],\n        \"End\": true\n      },\n      \"Create Or Update Meeting and Generate Race Array\": {\n        \"Type\": \"Task\",\n        \"Resource\": \"arn:aws:states:::lambda:invoke\",\n        \"OutputPath\": \"$.Payload\",\n        \"Parameters\": {\n          \"FunctionName\": \"MrCenLambdaProcessMeeting-${env}\",\n          \"Payload.$\": \"$\"\n        },\n        \"Next\": \"Generate Each Race\"\n      },\n      \"Generate Each Race\": {\n        \"Type\": \"Map\",\n        \"ItemsPath\": \"$.meetingData.races\",\n        \"Iterator\": {\n          \"StartAt\": \"Generate Race Level Data\",\n          \"States\": {\n            \"Generate Race Level Data\": {\n              \"Type\": \"Task\",\n              \"Resource\": \"arn:aws:states:::lambda:invoke\",\n              \"OutputPath\": \"$.Payload\",\n              \"Parameters\": {\n                \"Payload.$\": \"$\",\n                \"FunctionName\": \"MrCenLambdaProcessMeeting-${env}\"\n              },\n              \"Retry\": [\n                {\n                  \"ErrorEquals\": [\n                    \"Lambda.ServiceException\",\n                    \"Lambda.AWSLambdaException\",\n                    \"Lambda.SdkClientException\"\n                  ],\n                  \"IntervalSeconds\": 2,\n                  \"MaxAttempts\": 6,\n                  \"BackoffRate\": 2\n                }\n              ],\n              \"End\": true\n            }\n          }\n        },\n        \"MaxConcurrency\": 1,\n        \"Next\": \"Trigger Statistics and Error Generation\",\n        \"ResultPath\": null\n      },\n      \"Trigger Statistics and Error Generation\": {\n        \"Type\": \"Task\",\n        \"Resource\": \"arn:aws:states:::lambda:invoke\",\n        \"OutputPath\": \"$.Payload\",\n        \"Parameters\": {\n          \"Payload.$\": \"$\",\n          \"FunctionName\": \"MrCenLambdaPostProcessMeeting-${env}\"\n        },\n        \"Retry\": [\n          {\n            \"ErrorEquals\": [\n              \"Lambda.ServiceException\",\n              \"Lambda.AWSLambdaException\",\n              \"Lambda.SdkClientException\"\n            ],\n            \"IntervalSeconds\": 2,\n            \"MaxAttempts\": 6,\n            \"BackoffRate\": 2\n          }\n        ],\n        \"End\": true\n      }\n    },\n    \"Comment\": \"A Step functions workflow to process files for Mediality Business Operations, Inputs sources should be enriched with file types of Nominations, Weights, Acceptances, FinalFields, Results\",\n    \"TimeoutSeconds\": 1500\n  }\n"}, "LoggingConfiguration": {"Destinations": [{"CloudWatchLogsLogGroup": {"LogGroupArn": {"Fn::GetAtt": ["ProcessMeetingWorkflowStateMachineLogGroup", "<PERSON><PERSON>"]}}}], "IncludeExecutionData": true, "Level": "ALL"}, "RoleArn": {"Fn::GetAtt": ["ProcessMeetingWorkflowExecutionRole", "<PERSON><PERSON>"]}, "StateMachineName": {"Fn::Join": ["-", ["MrCenProcessMeetingWorkflow", {"Ref": "env"}]]}, "StateMachineType": "STANDARD", "TracingConfiguration": {"Enabled": "true"}}}, "FailedStepFunctionAlarm": {"Type": "AWS::CloudWatch::Alarm", "DependsOn": ["ProcessMeetingWorkflowStateMachine"], "Properties": {"AlarmActions": [{"Fn::Sub": ["arn:aws:sns:${region}:${account}:MrCenAlarms-${environment}", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "environment": {"Ref": "env"}}]}], "AlarmName": {"Fn::Join": ["", ["StepFunctionError-", {"Ref": "ProcessMeetingWorkflowStateMachine"}]]}, "ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "StateMachineArn", "Value": {"Ref": "ProcessMeetingWorkflowStateMachine"}}], "EvaluationPeriods": 1, "MetricName": "ExecutionsFailed", "Namespace": "AWS/States", "Period": 900, "Statistic": "Sum", "Threshold": 2, "TreatMissingData": "notBreaching"}}}, "Outputs": {"Name": {"Value": {"Fn::GetAtt": ["ProcessMeetingWorkflowStateMachine", "Name"]}}, "Arn": {"Value": {"Ref": "ProcessMeetingWorkflowStateMachine"}}}}