const fs = require('fs')

fs.readFile('StepFunctionDefinition.json', 'utf8', function (err, data) {
  if (err) {
    return console.log(err);
  }
  var result = data.replace(/abhinav/g, '${env}');
  // result = JSON.stringify(result);
  // result = JSON.parse(result);

  fs.readFile('MrCenProcessMeetingWorkflow-cloudformation-template.json', 'utf8', function (err, cfndata) {
    if (err) {
      return console.log(err);
    }
    cfndata = JSON.parse(cfndata);

    cfndata.Resources.ProcessMeetingWorkflowStateMachine.Properties.DefinitionString['Fn::Sub'] = result;
    // console.log(cfndata.Resources.ProcessWorkflowStateMachine.Properties.DefinitionString);

    fs.writeFile('MrCenProcessMeetingWorkflow-cloudformation-template.json', JSON.stringify(cfndata, null, 2), 'utf8', function (err) {
       if (err) return console.log(err);
    });
  });
});
