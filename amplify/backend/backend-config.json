{"api": {"MrCenApiGateway": {"dependsOn": [{"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "MrCenLambdaApi"}, {"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "MrCenLambdaApiAdmin"}, {"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "MrCenLambdaDeliveryFunction"}, {"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "Mr<PERSON>enLambdaComment"}, {"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "MrManualTriggerUpdate"}, {"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "MrCenEdmGenerate"}, {"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "MrPegasusLambdaCommentGen"}], "providerPlugin": "awscloudformation", "service": "API Gateway"}}, "auth": {"centaurapp": {"dependsOn": [], "frontendAuthConfig": {"mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "passwordProtectionSettings": {"passwordPolicyCharacters": [], "passwordPolicyMinLength": 8}, "signupAttributes": ["EMAIL"], "socialProviders": [], "usernameAttributes": [], "verificationMechanisms": ["EMAIL"]}, "providerPlugin": "awscloudformation", "service": "Cognito", "serviceType": "managed"}}, "custom": {"MrCenAlarmSnsTopic": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "customCloudformation"}, "MrCenDataUpdateStepFunction": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "customCloudformation"}, "MrCenDeadLetterQueue": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "customCloudformation"}, "MrCenDeliveryServiceXmlOutputStorage": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "customCloudformation"}, "MrCenDeliveryServiceXmlSourceStorage": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "customCloudformation"}, "MrCenEventBridge": {"dependsOn": [{"attributes": ["Name", "<PERSON><PERSON>"], "category": "custom", "resourceName": "MrCenProcessClientDeliveryServiceWorkflow"}, {"attributes": ["bucketName", "bucketArn"], "category": "custom", "resourceName": "MrCenDeliveryServiceXmlOutputStorage"}, {"attributes": ["bucketName", "bucketArn"], "category": "custom", "resourceName": "MrCenDeliveryServiceXmlSourceStorage"}, {"attributes": ["Name", "<PERSON><PERSON>", "Region", "LambdaExecutionRole", "LambdaExecutionRoleArn"], "category": "function", "resourceName": "MrCenLambdaGetDeliveryManifest"}, {"attributes": ["Name", "<PERSON><PERSON>", "Region", "LambdaExecutionRole", "LambdaExecutionRoleArn"], "category": "function", "resourceName": "MrCenLambdaClientDeliveryService"}], "providerPlugin": "awscloudformation", "service": "customCDK"}, "MrCenFileStorage": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "customCloudformation"}, "MrCenProcessClientDeliveryServiceWorkflow": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "customCloudformation"}, "MrCenProcessStatsWorkflow": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "customCloudformation"}, "ServiceAccounts": {"dependsOn": [{"attributes": ["bucketName", "bucketArn"], "category": "custom", "resourceName": "MrCenDeliveryServiceXmlOutputStorage"}, {"attributes": ["supplierBucketName", "supplierBucketArn", "bucketName", "bucketArn", "bucketRegion", "sqsName", "sqsArn", "sqsRegion"], "category": "custom", "resourceName": "MrCenFileStorage"}], "providerPlugin": "awscloudformation", "service": "customCloudformation"}}, "function": {"MRCenFileArchive": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MRCenLambdaCronSendFormXML": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MRCenLambdaHorseNameUpdate": {"build": true, "dependsOn": [{"attributes": ["Name"], "category": "function", "resourceName": "MrCenDocDbScheduler"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MRCenLambdaJockeyUpdate": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MRCenProcessRating": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MRCentCompareMeeting": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenChargebeeAPI": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenDocDbScheduler": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenEdmGenerate": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenLambdaApi": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenLambdaApiAdmin": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenLambdaClientDeliveryService": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}, {"attributes": ["bucketName", "bucketArn"], "category": "custom", "resourceName": "MrCenDeliveryServiceXmlSourceStorage"}, {"attributes": ["bucketName", "bucketArn"], "category": "custom", "resourceName": "MrCenDeliveryServiceXmlOutputStorage"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenLambdaComment": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenLambdaDBcleanup": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenLambdaDeliveryFunction": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenLambdaGetDeliveryManifest": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenLambdaInputFileLoad": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenLambdaPollerFunction": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenLambdaPollerTIS": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenLambdaPostProcessMeeting": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenLambdaProcessMeeting": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenLambdaProcessStats": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrCenLambdaStepFunctionExecutor": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrManualTriggerUpdate": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "MrPegasusLambdaCommentGen": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "centaurappCentaurAppCommonLayer": {"build": true, "providerPlugin": "awscloudformation", "service": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "parameters": {"AMPLIFY_function_MRCenFileArchive_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MRCenFileArchive"}]}, "AMPLIFY_function_MRCenFileArchive_region": {"usedBy": [{"category": "function", "resourceName": "MRCenFileArchive"}]}, "AMPLIFY_function_MRCenFileArchive_s3Key": {"usedBy": [{"category": "function", "resourceName": "MRCenFileArchive"}]}, "AMPLIFY_function_MRCenLambdaCronSendFormXML_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MRCenLambdaCronSendFormXML"}]}, "AMPLIFY_function_MRCenLambdaCronSendFormXML_s3Key": {"usedBy": [{"category": "function", "resourceName": "MRCenLambdaCronSendFormXML"}]}, "AMPLIFY_function_MRCenLambdaHorseNameUpdate_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MRCenLambdaHorseNameUpdate"}]}, "AMPLIFY_function_MRCenLambdaHorseNameUpdate_s3Key": {"usedBy": [{"category": "function", "resourceName": "MRCenLambdaHorseNameUpdate"}]}, "AMPLIFY_function_MRCenLambdaJockeyUpdate_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MRCenLambdaJockeyUpdate"}]}, "AMPLIFY_function_MRCenLambdaJockeyUpdate_s3Key": {"usedBy": [{"category": "function", "resourceName": "MRCenLambdaJockeyUpdate"}]}, "AMPLIFY_function_MRCenProcessRating_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MRCenProcessRating"}]}, "AMPLIFY_function_MRCenProcessRating_s3Key": {"usedBy": [{"category": "function", "resourceName": "MRCenProcessRating"}]}, "AMPLIFY_function_MRCentCompareMeeting_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MRCentCompareMeeting"}]}, "AMPLIFY_function_MRCentCompareMeeting_s3Key": {"usedBy": [{"category": "function", "resourceName": "MRCentCompareMeeting"}]}, "AMPLIFY_function_MrCenChargebeeAPI_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrCenChargebeeAPI"}]}, "AMPLIFY_function_MrCenChargebeeAPI_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrCenChargebeeAPI"}]}, "AMPLIFY_function_MrCenDocDbScheduler_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrCenDocDbScheduler"}]}, "AMPLIFY_function_MrCenDocDbScheduler_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrCenDocDbScheduler"}]}, "AMPLIFY_function_MrCenEdmGenerate_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrCenEdmGenerate"}]}, "AMPLIFY_function_MrCenEdmGenerate_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrCenEdmGenerate"}]}, "AMPLIFY_function_MrCenLambdaApiAdmin_awsLocal": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaApiAdmin"}]}, "AMPLIFY_function_MrCenLambdaApiAdmin_centaurSecrets": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaApiAdmin"}]}, "AMPLIFY_function_MrCenLambdaApiAdmin_customerApiGateway": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaApiAdmin"}]}, "AMPLIFY_function_MrCenLambdaApiAdmin_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaApiAdmin"}]}, "AMPLIFY_function_MrCenLambdaApiAdmin_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaApiAdmin"}]}, "AMPLIFY_function_MrCenLambdaApi_awsLocal": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaApi"}]}, "AMPLIFY_function_MrCenLambdaApi_centaurSecrets": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaApi"}]}, "AMPLIFY_function_MrCenLambdaApi_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaApi"}]}, "AMPLIFY_function_MrCenLambdaApi_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaApi"}]}, "AMPLIFY_function_MrCenLambdaClientDeliveryService_centaurSecrets": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaClientDeliveryService"}]}, "AMPLIFY_function_MrCenLambdaClientDeliveryService_centaurSecretsId": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaClientDeliveryService"}]}, "AMPLIFY_function_MrCenLambdaClientDeliveryService_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaClientDeliveryService"}]}, "AMPLIFY_function_MrCenLambdaClientDeliveryService_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaClientDeliveryService"}]}, "AMPLIFY_function_MrCenLambdaComment_awsLocal": {"usedBy": [{"category": "function", "resourceName": "Mr<PERSON>enLambdaComment"}]}, "AMPLIFY_function_MrCenLambdaComment_centaurSecrets": {"usedBy": [{"category": "function", "resourceName": "Mr<PERSON>enLambdaComment"}]}, "AMPLIFY_function_MrCenLambdaComment_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "Mr<PERSON>enLambdaComment"}]}, "AMPLIFY_function_MrCenLambdaComment_s3Key": {"usedBy": [{"category": "function", "resourceName": "Mr<PERSON>enLambdaComment"}]}, "AMPLIFY_function_MrCenLambdaComment_secretsPathAmplifyAppId": {"usedBy": [{"category": "function", "resourceName": "Mr<PERSON>enLambdaComment"}]}, "AMPLIFY_function_MrCenLambdaDBcleanup_awsLocal": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaDBcleanup"}]}, "AMPLIFY_function_MrCenLambdaDBcleanup_centaurSecrets": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaDBcleanup"}]}, "AMPLIFY_function_MrCenLambdaDBcleanup_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaDBcleanup"}]}, "AMPLIFY_function_MrCenLambdaDBcleanup_env": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaDBcleanup"}]}, "AMPLIFY_function_MrCenLambdaDBcleanup_region": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaDBcleanup"}]}, "AMPLIFY_function_MrCenLambdaDBcleanup_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaDBcleanup"}]}, "AMPLIFY_function_MrCenLambdaDeliveryFunction_centaurSecrets": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaDeliveryFunction"}]}, "AMPLIFY_function_MrCenLambdaDeliveryFunction_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaDeliveryFunction"}]}, "AMPLIFY_function_MrCenLambdaDeliveryFunction_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaDeliveryFunction"}]}, "AMPLIFY_function_MrCenLambdaGetDeliveryManifest_centaurSecrets": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaGetDeliveryManifest"}]}, "AMPLIFY_function_MrCenLambdaGetDeliveryManifest_centaurSecretsId": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaGetDeliveryManifest"}]}, "AMPLIFY_function_MrCenLambdaGetDeliveryManifest_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaGetDeliveryManifest"}]}, "AMPLIFY_function_MrCenLambdaGetDeliveryManifest_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaGetDeliveryManifest"}]}, "AMPLIFY_function_MrCenLambdaInputFileLoad_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaInputFileLoad"}]}, "AMPLIFY_function_MrCenLambdaInputFileLoad_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaInputFileLoad"}]}, "AMPLIFY_function_MrCenLambdaPollerFunction_awsLocal": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaPollerFunction"}]}, "AMPLIFY_function_MrCenLambdaPollerFunction_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaPollerFunction"}]}, "AMPLIFY_function_MrCenLambdaPollerFunction_fileProcessLimit": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaPollerFunction"}]}, "AMPLIFY_function_MrCenLambdaPollerFunction_mockSftpData": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaPollerFunction"}]}, "AMPLIFY_function_MrCenLambdaPollerFunction_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaPollerFunction"}]}, "AMPLIFY_function_MrCenLambdaPollerFunction_timeDiff": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaPollerFunction"}]}, "AMPLIFY_function_MrCenLambdaPollerTIS_awsLocal": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaPollerTIS"}]}, "AMPLIFY_function_MrCenLambdaPollerTIS_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaPollerTIS"}]}, "AMPLIFY_function_MrCenLambdaPollerTIS_fileProcessLimit": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaPollerTIS"}]}, "AMPLIFY_function_MrCenLambdaPollerTIS_mockSftpData": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaPollerTIS"}]}, "AMPLIFY_function_MrCenLambdaPollerTIS_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaPollerTIS"}]}, "AMPLIFY_function_MrCenLambdaPollerTIS_timeDiff": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaPollerTIS"}]}, "AMPLIFY_function_MrCenLambdaPostProcessMeeting_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaPostProcessMeeting"}]}, "AMPLIFY_function_MrCenLambdaPostProcessMeeting_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaPostProcessMeeting"}]}, "AMPLIFY_function_MrCenLambdaProcessMeeting_awsLocal": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaProcessMeeting"}]}, "AMPLIFY_function_MrCenLambdaProcessMeeting_centaurSecrets": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaProcessMeeting"}]}, "AMPLIFY_function_MrCenLambdaProcessMeeting_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaProcessMeeting"}]}, "AMPLIFY_function_MrCenLambdaProcessMeeting_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaProcessMeeting"}]}, "AMPLIFY_function_MrCenLambdaProcessStats_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaProcessStats"}]}, "AMPLIFY_function_MrCenLambdaProcessStats_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaProcessStats"}]}, "AMPLIFY_function_MrCenLambdaStepFunctionExecutor_awsLocal": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaStepFunctionExecutor"}]}, "AMPLIFY_function_MrCenLambdaStepFunctionExecutor_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaStepFunctionExecutor"}]}, "AMPLIFY_function_MrCenLambdaStepFunctionExecutor_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrCenLambdaStepFunctionExecutor"}]}, "AMPLIFY_function_MrManualTriggerUpdate_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrManualTriggerUpdate"}]}, "AMPLIFY_function_MrManualTriggerUpdate_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrManualTriggerUpdate"}]}, "AMPLIFY_function_MrPegasusLambdaCommentGen_awsLocal": {"usedBy": [{"category": "function", "resourceName": "MrPegasusLambdaCommentGen"}]}, "AMPLIFY_function_MrPegasusLambdaCommentGen_centaurSecrets": {"usedBy": [{"category": "function", "resourceName": "MrPegasusLambdaCommentGen"}]}, "AMPLIFY_function_MrPegasusLambdaCommentGen_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "MrPegasusLambdaCommentGen"}]}, "AMPLIFY_function_MrPegasusLambdaCommentGen_s3Key": {"usedBy": [{"category": "function", "resourceName": "MrPegasusLambdaCommentGen"}]}, "AMPLIFY_function_MrPegasusLambdaCommentGen_secretsPathAmplifyAppId": {"usedBy": [{"category": "function", "resourceName": "MrPegasusLambdaCommentGen"}]}, "AMPLIFY_function_centaurappCentaurAppCommonLayer_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}]}, "AMPLIFY_function_centaurappCentaurAppCommonLayer_s3Key": {"usedBy": [{"category": "function", "resourceName": "centaurappCentaurAppCommonLayer"}]}}, "stepFunction": {"MrCenProcessMeetingWorkflow": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "Step Function"}}}