{"Description": "API Gateway policy stack created using Amplify CLI", "AWSTemplateFormatVersion": "2010-09-09", "Parameters": {"authRoleName": {"Type": "String"}, "unauthRoleName": {"Type": "String"}, "env": {"Type": "String"}, "MrCenApiGateway": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"PolicyAPIGWAuth1": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["execute-api:Invoke"], "Resource": [{"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/inputmeetings/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/inputmeetings"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/s3download/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/s3download"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/meeting/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/meeting"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/forms/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/forms"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/generatestats/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/generatestats"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/queues/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/queues"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/horse/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/horse"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/gen-meeting-error-count/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/gen-meeting-error-count"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/processedmeetings/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/processedmeetings"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/jockey/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/jockey"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/trainer/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/trainer"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/track/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/track"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/horse-form/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/horse-form"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/all/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/all"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/club/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/club"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/admin/generate-api-key/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/admin/generate-api-key"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/admin/client/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/admin/client"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/admin/horse/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/admin/horse"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/admin/ping/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/admin/ping"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/admin/search/horse/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/admin/search/horse"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/admin/search/form/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/admin/search/form"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/admin/search/jockey/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/admin/search/jockey"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/admin/search/track/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/admin/search/track"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/admin/search/trainer/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/admin/search/trainer"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/admin/search/club/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/admin/search/club"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/admin/search/horse-form/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/admin/search/horse-form"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/transfer-meeting/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/transfer-meeting"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/meeting-status/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/meeting-status"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/admin/files/list/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/admin/files/list"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/admin/queues/list/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/admin/queues/list"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/admin/files/download/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/admin/files/download"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/distribute/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/distribute"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/registrations/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/registrations"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/compare-meeting/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/compare-meeting"]]}]}]}, "Roles": [{"Ref": "authRoleName"}]}}, "PolicyAPIGWAuth2": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["execute-api:Invoke"], "Resource": [{"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/changelog/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/changelog"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/locks/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/locks"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/POST/claim/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/POST/claim"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/claim/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/claim"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/PUT/claim/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/PUT/claim"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/PATCH/claim/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/PATCH/claim"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/trigger<PERSON><PERSON>al<PERSON><PERSON>resh/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/triggerManualRefresh"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/formIndex/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/formIndex"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/edm/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/edm"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/admin/formIndex/search/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/GET/admin/formIndex/search"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/commentgenerate/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/commentgenerate"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/comments/*"]]}, {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "MrCenApiGateway"}, "/", {"Fn::If": ["ShouldNotCreateEnvResources", "Prod", {"Ref": "env"}]}, "/*/comments"]]}]}]}, "Roles": [{"Ref": "authRoleName"}]}}}}