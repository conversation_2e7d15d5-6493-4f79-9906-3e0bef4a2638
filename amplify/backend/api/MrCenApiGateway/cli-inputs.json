{"version": 1, "paths": {"/inputmeetings": {"name": "/inputmeetings", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/s3download": {"name": "/s3download", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/meeting": {"name": "/meeting", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/forms": {"name": "/forms", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/generatestats": {"name": "/generatestats", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/queues": {"name": "/queues", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/horse": {"name": "/horse", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/gen-meeting-error-count": {"name": "/gen-meeting-error-count", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/processedmeetings": {"name": "/processedmeetings", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/jockey": {"name": "/jockey", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/trainer": {"name": "/trainer", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/track": {"name": "/track", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/horse-form": {"name": "/horse-form", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/all": {"name": "/all", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/club": {"name": "/club", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/admin/generate-api-key": {"name": "/admin/generate-api-key", "lambdaFunction": "MrCenLambdaApiAdmin", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/admin/client": {"name": "/admin/client", "lambdaFunction": "MrCenLambdaApiAdmin", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/admin/horse": {"name": "/admin/horse", "lambdaFunction": "MrCenLambdaApiAdmin", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/admin/ping": {"name": "/admin/ping", "lambdaFunction": "MrCenLambdaApiAdmin", "permissions": {"setting": "private", "auth": ["read"]}}, "/admin/search/horse": {"name": "/admin/search/horse", "lambdaFunction": "MrCenLambdaApiAdmin", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/admin/search/form": {"name": "/admin/search/form", "lambdaFunction": "MrCenLambdaApiAdmin", "permissions": {"setting": "private", "auth": ["read"]}}, "/admin/search/jockey": {"name": "/admin/search/jockey", "lambdaFunction": "MrCenLambdaApiAdmin", "permissions": {"setting": "private", "auth": ["read"]}}, "/admin/search/track": {"name": "/admin/search/track", "lambdaFunction": "MrCenLambdaApiAdmin", "permissions": {"setting": "private", "auth": ["read"]}}, "/admin/search/trainer": {"name": "/admin/search/trainer", "lambdaFunction": "MrCenLambdaApiAdmin", "permissions": {"setting": "private", "auth": ["read"]}}, "/admin/search/club": {"name": "/admin/search/club", "lambdaFunction": "MrCenLambdaApiAdmin", "permissions": {"setting": "private", "auth": ["read"]}}, "/admin/search/horse-form": {"name": "/admin/search/horse-form", "lambdaFunction": "MrCenLambdaApiAdmin", "permissions": {"setting": "private", "auth": ["read"]}}, "/transfer-meeting": {"name": "/transfer-meeting", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/meeting-status": {"name": "/meeting-status", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/admin/files/list": {"name": "/admin/files/list", "lambdaFunction": "MrCenLambdaApiAdmin", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/admin/queues/list": {"name": "/admin/queues/list", "lambdaFunction": "MrCenLambdaApiAdmin", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/admin/files/download": {"name": "/admin/files/download", "lambdaFunction": "MrCenLambdaApiAdmin", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/distribute": {"name": "/distribute", "lambdaFunction": "MrCenLambdaDeliveryFunction", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/registrations": {"name": "/registrations", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/comment": {"name": "/comment", "lambdaFunction": "Mr<PERSON>enLambdaComment", "permissions": {"setting": "open"}}, "/compare-meeting": {"name": "/compare-meeting", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/changelog": {"name": "/changelog", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/locks": {"name": "/locks", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["read"]}}, "/claim": {"name": "/claim", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update"]}}, "/stats": {"name": "/stats", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "open"}}, "/fileHandler": {"name": "/fileHandler", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "open"}}, "/triggerManualRefresh": {"name": "/triggerManualRefresh", "lambdaFunction": "MrManualTriggerUpdate", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/formIndex": {"name": "/formIndex", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/edm": {"name": "/edm", "lambdaFunction": "MrCenEdmGenerate", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/admin/formIndex/search": {"name": "/admin/formIndex/search", "lambdaFunction": "MrCenLambdaApiAdmin", "permissions": {"setting": "private", "auth": ["read"]}}, "/commentgenerate": {"name": "/commentgenerate", "lambdaFunction": "MrPegasusLambdaCommentGen", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}, "/comments": {"name": "/comments", "lambdaFunction": "MrCenLambdaApi", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"]}}}}