export type AmplifyDependentResourcesAttributes = {
  "api": {
    "MrCenApiGateway": {
      "ApiId": "string",
      "ApiName": "string",
      "RootUrl": "string"
    }
  },
  "auth": {
    "centaurapp": {
      "AppClientID": "string",
      "AppClientIDWeb": "string",
      "IdentityPoolId": "string",
      "IdentityPoolName": "string",
      "UserPoolArn": "string",
      "UserPoolId": "string",
      "UserPoolName": "string"
    }
  },
  "custom": {
    "MrCenAlarmSnsTopic": {
      "Arn": "string",
      "Region": "string"
    },
    "MrCenDataUpdateStepFunction": {
      "Arn": "string",
      "Name": "string"
    },
    "MrCenDeadLetterQueue": {
      "Arn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenDeliveryServiceXmlOutputStorage": {
      "bucketArn": "string",
      "bucketName": "string"
    },
    "MrCenDeliveryServiceXmlSourceStorage": {
      "bucketArn": "string",
      "bucketName": "string"
    },
    "MrCenFileStorage": {
      "SNSTopicARN": "string",
      "bucketArn": "string",
      "bucketName": "string",
      "bucketRegion": "string",
      "sqsArn": "string",
      "sqsName": "string",
      "sqsRegion": "string",
      "supplierBucketArn": "string",
      "supplierBucketName": "string"
    },
    "MrCenProcessClientDeliveryServiceWorkflow": {
      "Arn": "string",
      "Name": "string"
    },
    "MrCenProcessStatsWorkflow": {
      "Arn": "string",
      "Name": "string"
    }
  },
  "function": {
    "MRCenFileArchive": {
      "Arn": "string",
      "CloudWatchEventRule": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MRCenLambdaCronSendFormXML": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MRCenLambdaHorseNameUpdate": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MRCenLambdaJockeyUpdate": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MRCenProcessRating": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MRCentCompareMeeting": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenChargebeeAPI": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenDocDbScheduler": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenEdmGenerate": {
      "Arn": "string",
      "CloudWatchEventRule": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenLambdaApi": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenLambdaApiAdmin": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenLambdaClientDeliveryService": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenLambdaComment": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenLambdaDBcleanup": {
      "Arn": "string",
      "CloudWatchEventRule": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenLambdaDeliveryFunction": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenLambdaGetDeliveryManifest": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenLambdaInputFileLoad": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenLambdaPollerFunction": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenLambdaPollerTIS": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenLambdaPostProcessMeeting": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenLambdaProcessMeeting": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenLambdaProcessStats": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrCenLambdaStepFunctionExecutor": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrManualTriggerUpdate": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "MrPegasusLambdaCommentGen": {
      "Arn": "string",
      "LambdaExecutionRole": "string",
      "LambdaExecutionRoleArn": "string",
      "Name": "string",
      "Region": "string"
    },
    "centaurappCentaurAppCommonLayer": {
      "Arn": "string"
    }
  },
  "stepFunction": {
    "MrCenProcessMeetingWorkflow": {
      "Arn": "string",
      "Name": "string"
    }
  }
}