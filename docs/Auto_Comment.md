# Auto Comment

- [Auto Comment](#auto-comment)
  - [1 Epic - Architecture and Design](#1-epic---architecture-and-design)
    - [1.1 Deep Dive](#11-deep-dive)
    - [1.2 Auto Comment and Racebook Record Design](#12-auto-comment-and-racebook-record-design)
    - [1.3 Data Migration](#13-data-migration)
  - [2. Epic - Comment](#2-epic---comment)
    - [2.1 Generate comments based on rules, rule groups](#21-generate-comments-based-on-rules-rule-groups)
    - [2.2 As a user I want to be able to Edit comments](#22-as-a-user-i-want-to-be-able-to-edit-comments)
  - [3 Epic - Rule Groups, Rules and Sentences](#3-epic---rule-groups-rules-and-sentences)
    - [3.1 As a user I want to be able to add/edit/delete/order rules](#31-as-a-user-i-want-to-be-able-to-addeditdeleteorder-rules)
    - [3.2 As a user I want to be able to add/edit/delete/order sentences](#32-as-a-user-i-want-to-be-able-to-addeditdeleteorder-sentences)
    - [3.3 Top Trainers and Top Jockeys](#33-top-trainers-and-top-jockeys)
  - [Sprint Planning](#sprint-planning)
    - [Sprint 1](#sprint-1)
    - [Sprint 2](#sprint-2)
    - [Sprint 3](#sprint-3)
    - [Sprint 4](#sprint-4)

## 1 Epic - Architecture and Design

​

### 1.1 Deep Dive

Task to understand existing application -- DONE
​

### 1.2 Auto Comment and Racebook Record Design

Story points - 8

- High Level Design documents on Cloud native solution for Auto Comment and Racebook Record platform

![Architecture_overview](images/Architecture_overview.svg "Architecture_overview")

- Solution Architecture for generating comments
  - User trigger will initiate the auto-comment generation via API. The processing service will ingest the data from the documentDB database and update the processMeetings collection with the auto-generated comment.

![SolnArch](images/auto_comment-SAD.png "Solution Architecture Diagram")

- Solution Architecture for generating race book records
  - User trigger will initiate the auto-comment generation via API. The processing service will ingest the data from the documentDB database and generate a race book record file and store it in the S3 bucket.

![SolnArch](images/auto_comment-RBR_SAD.png "Solution Architecture Diagram")

An alternative approach could be as follows.

![SolnArch](images/auto_comment-RBR_ALT_SAD.png "Solution Architecture Diagram")

### 1.3 Data Migration

Migrating the comment related tables to the documentDB.

## 2. Epic - Comment

Editing and generating comments
After comments are generated based on the rules, this epic contains stories to edit comments
​

### 2.1 Generate comments based on rules, rule groups

Story points - 13
Port logic from VB code to nodejs
​MPC-197
As an internal user, i want to be able to edit the auto-comment once generated

### 2.2 As a user I want to be able to Edit comments

Story points - 5
Edit the final generated comment on the screen
what is the UX for editing the auto-generated comment inline edit dialog - select/click comment to show entire comment.
​MPC-198 assigned to Nic and Abhi

## 3 Epic - Rule Groups, Rules and Sentences

​
This epic contains stories for maintaining the comment rules, rule groups required for comment generation
​
### 3.1 As a user I want to be able to add/edit/delete/order rules

Story points - 5
parameterize the rule fields, to ensure that it is configurable from backend.

Rules need to be editable in the backend.
Extra fields need to be editable. These extra fields needs to be decided upon.
All the fields that are editable right now in racinghai.app will be made editable in the new app.

### 3.2 As a user I want to be able to add/edit/delete/order sentences

Story points - 5+5
The user workflow and functionality is similar to racinghai.aap sentences webpage

### 3.3 Top Trainers and Top Jockeys

toptrainers and topjockeys tables as inputs
Story points - 5+3

## Sprint Planning

### Sprint 1

1.2 and starting 2.1 - 8+5 = 13

### Sprint 2

2.1, 2.2, 3.4 - 8+3+3 = 11

### Sprint 3

2.4 and Epic 3, 3.4 - 5+10+5 = 20

### Sprint 4

Stabilizations, tech debt
