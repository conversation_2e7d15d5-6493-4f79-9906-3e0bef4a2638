image: node:18.14.2
pipelines:
  pull-requests:
    "**":
      - parallel:
          - step:
              caches:
                - node
              name: "MrCenLambdaGetDeliveryManifest Test"
              script:
                - cd ./amplify/backend/function/centaurappCentaurAppCommonLayer/lib/nodejs && npm install
                - cd ../../../MrCenLambdaGetDeliveryManifest/src && npm install && npm test
          - step:
              caches:
                - node
              name: "MrCenLambdaClientDeliveryService Test"
              script:
                - cd ./amplify/backend/function/centaurappCentaurAppCommonLayer/lib/nodejs && npm install
                - cd ../../../MrCenLambdaClientDeliveryService/src && npm install && npm test
          - step:
              caches:
                - node
              name: "centaurappCentaurAppCommonLayer Test"
              script:
                - cd ./amplify/backend/function/centaurappCentaurAppCommonLayer/lib/nodejs && npm install && npm test
