# Environment Variables for Centaur Application
# Copy this file to .env.local and update the values as needed

# Report API URL - Override the default URL based on environment
# For staging environments
# VUE_APP_REPORT_API_URL=https://51erxl7hb6.execute-api.ap-southeast-2.amazonaws.com/report

# For production environment (update when production URL is available)
# VUE_APP_REPORT_API_URL=https://production-api-url.execute-api.ap-southeast-2.amazonaws.com/report

# Other environment-specific configurations can be added here
# VUE_APP_DEBUG=true
# VUE_APP_API_TIMEOUT=30000
