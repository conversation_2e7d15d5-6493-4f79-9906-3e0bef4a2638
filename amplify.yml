version: 1
backend:
  phases:
    # preBuild:
    #   commands:
    #     - rm -rf ~/.amplify
    #     - npm i -g @aws-amplify/cli@latest
    build:
      commands:
        - nvm use 18
        - npm --version
        # - npm install npm@latest -g
        - npm install npm@8.19.3 -g
        - amplifyPush --simple
frontend:
  phases:
    preBuild:
      commands:
        - npm ci
    build:
      commands:
        # Set environment-specific variables based on the branch/environment
        - |
          if [ "$AWS_BRANCH" = "prd" ]; then
            export VUE_APP_REPORT_API_URL="https://production-report-api.execute-api.ap-southeast-2.amazonaws.com/report"
          else
            export VUE_APP_REPORT_API_URL="https://51erxl7hb6.execute-api.ap-southeast-2.amazonaws.com/report"
          fi
        - npm run build
  artifacts:
    baseDirectory: dist
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
